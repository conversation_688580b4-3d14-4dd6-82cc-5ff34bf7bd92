package com.ggec.glasses.aimodelchat.aimodel.deepseek;

// 移动 Request/Response 后更新的导入路径
import com.ggec.glasses.aimodelchat.aimodel.deepseek.DeepSeekRequest;
import com.ggec.glasses.aimodelchat.aimodel.deepseek.DeepSeekResponse;
import com.ggec.glasses.aimodelchat.network.APIService; // 基础 APIService 保持在通用的 network 包中

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.Headers;
import retrofit2.http.POST;

/**
 * DeepSeek 特定的 Retrofit API 服务接口
 */
public interface DeepSeekAPIService extends APIService {

    @Headers({"Content-Type: application/json"})
    @POST("chat/completions")
    Call<DeepSeekResponse> createChatCompletion(
            // apiKey Header 仍在此处定义，但实际的 Key 由 RetrofitClient 的拦截器注入
            @Header("Authorization") String apiKey,
            @Body DeepSeekRequest request
    );
} 