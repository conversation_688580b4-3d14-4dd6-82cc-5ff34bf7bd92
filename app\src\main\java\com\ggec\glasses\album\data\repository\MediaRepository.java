package com.ggec.glasses.album.data.repository;

import android.content.Context;
import android.net.Uri;
import android.os.AsyncTask;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.ggec.glasses.album.data.db.MediaDatabase;
import com.ggec.glasses.album.data.entity.Album;
import com.ggec.glasses.album.data.entity.AlbumMedia;
import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.data.entity.MediaTag;
import com.ggec.glasses.album.data.entity.Tag;
import com.ggec.glasses.album.data.manager.MediaFileManager;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 媒体存储库，负责协调媒体相关的数据库操作和文件管理
 * (已重构，相册和标签管理已移至 AlbumRepository 和 TagRepository)
 */
public class MediaRepository {
    
    private static final String TAG = "MediaRepository";
    
    // 线程池，用于异步操作
    private final ExecutorService executor;
    
    // 数据库和DAO
    private final MediaDatabase database;
    private final MediaFileManager fileManager;
    
    // 上下文弱引用，避免内存泄漏
    private final WeakReference<Context> contextRef;
    
    // 单例模式
    private static MediaRepository INSTANCE;
    
    /**
     * 获取媒体存储库实例
     * @param context 应用上下文
     * @return 媒体存储库实例
     */
    public static synchronized MediaRepository getInstance(Context context) {
        if (INSTANCE == null) {
            INSTANCE = new MediaRepository(context.getApplicationContext());
        }
        return INSTANCE;
    }
    
    /**
     * 私有构造函数，初始化数据库和文件管理器
     * @param context 应用上下文
     */
    private MediaRepository(Context context) {
        database = MediaDatabase.getInstance(context);
        fileManager = new MediaFileManager(context);
        executor = Executors.newFixedThreadPool(4); // 创建4个线程的线程池
        contextRef = new WeakReference<>(context);
    }

    // --- 回调接口定义 ---
    public interface OnMediaSavedCallback {
        void onSuccess(Media media);
        void onError(String errorMessage);
    }

    public interface OnMediaDeletedCallback {
        void onSuccess();
        void onPartialSuccess(String message);
        void onError(String errorMessage);
    }

    public interface OnRecycleBinEmptiedCallback {
        void onSuccess();
        void onPartialSuccess(String message);
        void onError(String errorMessage);
    }
    
    /**
     * 保存媒体文件
     * @param sourceUri 源文件URI
     * @param mediaType 媒体类型
     * @param width 宽度
     * @param height 高度
     * @param duration 时长（视频专用）
     * @param mimeType MIME类型
     * @param callback 回调接口，通知操作结果
     */
    public void saveMedia(Uri sourceUri, String mediaType, int width, int height, 
                          long duration, String mimeType, OnMediaSavedCallback callback) {
        executor.execute(() -> {
            try {
                // 检查参数有效性
                if (sourceUri == null) {
                    if (callback != null) {
                        callback.onError("Source URI cannot be null");
                    }
                    return;
                }
                
                if (mediaType == null || (!mediaType.equals(MediaFileManager.TYPE_IMAGE) && 
                                         !mediaType.equals(MediaFileManager.TYPE_VIDEO))) {
                    if (callback != null) {
                        callback.onError("Invalid media type");
                    }
                    return;
                }
                
                // 1. 先检查URI是否对应已存在的媒体文件
                Media existingMedia = findExistingMediaByPath(sourceUri.toString());
                if (existingMedia != null && !existingMedia.isDeleted()) {
                    // 文件已存在于数据库，直接返回
                    Log.d(TAG, "Media already exists in database: " + existingMedia.getFilePath());
                    if (callback != null) {
                        callback.onSuccess(existingMedia);
                    }
                    return;
                }
                
                // 2. 将文件保存到内部存储
                File savedFile = fileManager.saveMediaFile(sourceUri, mediaType);
                if (savedFile == null) {
                    if (callback != null) {
                        callback.onError("Failed to save media file");
                    }
                    return;
                }
                
                // 3. 检查数据库中是否有相同路径的媒体（路径相同，但可能被标记为已删除）
                Media mediaByPath = findMediaByFilePath(savedFile.getAbsolutePath());
                if (mediaByPath != null) {
                    if (mediaByPath.isDeleted()) {
                        // 恢复被标记为已删除的媒体
                        mediaByPath.setDeleted(false);
                        mediaByPath.setModificationDate(new Date());
                        
                        // 如果元数据字段不存在或不包含当前URI，则更新
                        updateCustomMetadata(mediaByPath, sourceUri.toString());
                        
                        database.mediaDao().updateMedia(mediaByPath);
                        
                        if (callback != null) {
                            callback.onSuccess(mediaByPath);
                        }
                    } else {
                        // 媒体已存在且未被删除，直接返回
                        if (callback != null) {
                            callback.onSuccess(mediaByPath);
                        }
                    }
                    return;
                }
                
                // 4. 创建新的Media对象
                Media media = new Media();
                media.setFileName(savedFile.getName());
                media.setFilePath(savedFile.getAbsolutePath());
                media.setType(mediaType);
                media.setMimeType(mimeType != null ? mimeType : getDefaultMimeType(mediaType));
                media.setSize(savedFile.length());
                media.setWidth(width > 0 ? width : 0);
                media.setHeight(height > 0 ? height : 0);
                media.setDuration(duration > 0 ? duration : 0);
                media.setCreationDate(new Date());
                media.setModificationDate(new Date());
                media.setDeleted(false);
                
                // 创建并设置自定义元数据
                setInitialCustomMetadata(media, sourceUri.toString());
                
                // 5. 处理缩略图路径
                File thumbnail = fileManager.generateThumbnail(savedFile, mediaType);
                if (thumbnail != null) {
                    media.setThumbnailPath(thumbnail.getAbsolutePath());
                }
                
                // 6. 将Media对象保存到数据库
                long mediaId = database.mediaDao().insertMedia(media);
                
                // 回调通知保存成功
                if (callback != null) {
                    media.setId(mediaId);
                    callback.onSuccess(media);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error saving media", e);
                if (callback != null) {
                    callback.onError("Error saving media: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 创建并设置初始自定义元数据
     * @param media 媒体对象
     * @param sourceUri 源URI
     */
    private void setInitialCustomMetadata(Media media, String sourceUri) {
        try {
            JSONObject metadata = new JSONObject();
            metadata.put("sourceUri", sourceUri);
            metadata.put("importDate", new Date().getTime());
            media.setCustomMetadata(metadata.toString());
        } catch (JSONException e) {
            Log.e(TAG, "Error creating custom metadata", e);
        }
    }
    
    /**
     * 更新自定义元数据
     * @param media 媒体对象
     * @param sourceUri 源URI
     */
    private void updateCustomMetadata(Media media, String sourceUri) {
        try {
            JSONObject metadata;
            if (media.getCustomMetadata() != null && !media.getCustomMetadata().isEmpty()) {
                metadata = new JSONObject(media.getCustomMetadata());
            } else {
                metadata = new JSONObject();
            }
            
            // 如果元数据中不包含当前URI，则添加
            if (!metadata.has("sourceUri") || !metadata.getString("sourceUri").equals(sourceUri)) {
                metadata.put("sourceUri", sourceUri);
            }
            
            // 更新最后修改时间
            metadata.put("lastModified", new Date().getTime());
            
            media.setCustomMetadata(metadata.toString());
        } catch (JSONException e) {
            Log.e(TAG, "Error updating custom metadata", e);
        }
    }
    
    /**
     * 根据文件路径查找媒体
     * @param filePath 文件路径
     * @return 媒体对象，如果不存在则返回null
     */
    private Media findMediaByFilePath(String filePath) {
        return database.mediaDao().getMediaByFilePath(filePath);
    }
    
    /**
     * 根据URI路径查找媒体（用于检测重复导入）
     * @param uriPath URI路径
     * @return 媒体对象，如果不存在则返回null
     */
    private Media findExistingMediaByPath(String uriPath) {
        if (uriPath == null || uriPath.isEmpty()) {
            return null;
        }
        
        // 尝试通过原始URI查找
        try {
            List<Media> allMedia = database.mediaDao().getAllMedia();
            for (Media media : allMedia) {
                // 检查自定义元数据中是否存储了原始URI
                String metadata = media.getCustomMetadata();
                if (metadata != null && !metadata.isEmpty()) {
                    try {
                        JSONObject json = new JSONObject(metadata);
                        if (json.has("sourceUri") && json.getString("sourceUri").equals(uriPath)) {
                            return media;
                        }
                    } catch (JSONException e) {
                        Log.e(TAG, "Error parsing metadata JSON", e);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error finding media by URI", e);
        }
        
        return null;
    }
    
    /**
     * 获取默认的MIME类型
     * @param mediaType 媒体类型
     * @return 默认MIME类型
     */
    private String getDefaultMimeType(String mediaType) {
        if (MediaFileManager.TYPE_IMAGE.equals(mediaType)) {
            return "image/jpeg";
        } else if (MediaFileManager.TYPE_VIDEO.equals(mediaType)) {
            return "video/mp4";
        }
        return "application/octet-stream";
    }
    
    /**
     * 根据ID获取媒体对象
     * @param mediaId 媒体ID
     * @return 媒体对象，如果不存在则返回null
     */
    public Media getMediaById(long mediaId) {
        // 确保数据库操作在后台线程执行
        // 注意：这是一个同步调用，如果在主线程调用可能会阻塞。
        // 更好的做法是也提供异步版本或返回LiveData/Flow
        try {
            return executor.submit(() -> database.mediaDao().getMediaById(mediaId)).get();
        } catch (Exception e) {
            Log.e(TAG, "Error getting media by ID synchronously", e);
            return null;
        }
        // 或者直接调用 DAO（如果确定调用方在后台线程）:
        // return database.mediaDao().getMediaById(mediaId);
    }
    
    /**
     * 获取所有媒体LiveData
     * @return 包含媒体列表的LiveData
     */
    public LiveData<List<Media>> getAllMediaLive() {
        return database.mediaDao().getAllMediaLive();
    }
    
    /**
     * 根据类型获取媒体（LiveData版本）
     * @param type 媒体类型
     * @return 包含指定类型媒体列表的LiveData
     */
    public LiveData<List<Media>> getMediaByTypeLive(String type) {
        return database.mediaDao().getMediaByTypeLive(type);
    }
    
    /**
     * 获取已删除的媒体（回收站）
     * @return 包含已删除媒体列表的LiveData
     */
    public LiveData<List<Media>> getDeletedMedia() {
        return database.mediaDao().getDeletedMediaLive();
    }
    
    /**
     * 恢复已删除的媒体
     * @param mediaId 媒体ID
     */
    public void restoreMedia(long mediaId) {
        executor.execute(() -> database.mediaDao().restoreMedia(mediaId));
    }
    
    /**
     * 永久删除媒体文件及其数据库记录
     * @param mediaId 媒体ID
     * @param callback 回调接口，通知操作结果
     */
    public void permanentlyDeleteMedia(long mediaId, OnMediaDeletedCallback callback) {
        executor.execute(() -> {
            Media media = null;
            try {
                // 获取媒体对象
                 media = database.mediaDao().getMediaById(mediaId);
                if (media == null) {
                    if (callback != null) {
                        // 如果媒体记录不存在，也视为成功（幂等性）
                         callback.onSuccess();
                       // callback.onError("Media not found");
                    }
                    return;
                }
                
                // 删除文件
                boolean fileDeleted = fileManager.deleteMediaFile(media.getFilePath(), media.getType());
                
                // 删除数据库记录
                database.mediaDao().permanentlyDeleteMedia(mediaId);
                
                // 回调通知删除结果
                if (callback != null) {
                    if (fileDeleted) {
                        callback.onSuccess();
                    } else {
                        // 文件删除失败，但记录已删除，报告部分成功
                        Log.w(TAG, "Database record deleted but file deletion failed for: " + media.getFilePath());
                        callback.onPartialSuccess("Database record deleted but file deletion failed");
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error deleting media with ID: " + mediaId, e);
                if (callback != null) {
                    callback.onError("Error deleting media: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 清空回收站
     * @param callback 回调接口，通知操作结果
     */
    public void emptyRecycleBin(OnRecycleBinEmptiedCallback callback) {
        executor.execute(() -> {
            try {
                // 获取回收站中的所有媒体
                List<Media> deletedMedia = database.mediaDao().getDeletedMedia();
                 if (deletedMedia.isEmpty()) {
                     if (callback != null) callback.onSuccess();
                     return;
                 }

                // 删除文件
                int fileDeletedCount = 0;
                int failedFileDeletions = 0;
                for (Media media : deletedMedia) {
                    if (fileManager.deleteMediaFile(media.getFilePath(), media.getType())) {
                        fileDeletedCount++;
                    } else {
                        Log.w(TAG, "Failed to delete file while emptying recycle bin: " + media.getFilePath());
                        failedFileDeletions++;
                    }
                }
                
                // 清空回收站数据库记录
                database.mediaDao().emptyRecycleBin();
                
                // 回调通知结果
                if (callback != null) {
                    if (failedFileDeletions == 0) {
                        callback.onSuccess();
                    } else {
                        callback.onPartialSuccess(
                                "Emptied recycle bin. Deleted " + fileDeletedCount 
                                + " files successfully, failed to delete " + failedFileDeletions + " files.");
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error emptying recycle bin", e);
                if (callback != null) {
                    callback.onError("Error emptying recycle bin: " + e.getMessage());
                }
            }
        });
    }

    // --- 相册和标签相关方法已移除 ---
    
    /**
     * 清理临时文件
     */
    public void cleanTempFiles() {
        executor.execute(() -> fileManager.cleanTempFiles());
    }
    
    /**
     * 关闭存储库，释放资源
     */
    public void close() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        // 注意：不应在此处销毁数据库实例，它由 MediaDatabase 类管理
        // MediaDatabase.destroyInstance(); // 不应在这里调用
        INSTANCE = null;
    }
    
    /**
     * 清空所有媒体数据 (包括文件和数据库记录)
     * 注意：此方法只清空 Media 表和相关文件，不清空相册、标签等关联数据。
     */
    public void clearAllMedia() {
        executor.execute(() -> {
            try {
                // 获取所有媒体 (包括已删除的，以便清理文件)
                List<Media> allMedia = database.mediaDao().getAllMediaIncludingDeleted_InternalUseOnly(); // 假设有此方法
                
                // 删除文件
                int filesDeleted = 0;
                int filesFailed = 0;
                if (allMedia != null) {
                    for (Media media : allMedia) {
                        if (fileManager.deleteMediaFile(media.getFilePath(), media.getType())) {
                            filesDeleted++;
                        } else {
                             Log.w(TAG, "Failed to delete file while clearing all media: " + media.getFilePath());
                             filesFailed++;
                        }
                    }
                } else {
                     Log.w(TAG, "getAllMediaIncludingDeleted_InternalUseOnly returned null, cannot delete files.");
                }
                
                // 清空 Media 表
                database.mediaDao().clearAllMedia();
                
                Log.d(TAG, "Media table cleared. Files deleted: " + filesDeleted + ", Failed: " + filesFailed);
                // 可能需要添加回调通知完成
            } catch (Exception e) {
                Log.e(TAG, "Error clearing media table and files", e);
                // 可能需要添加回调通知错误
            }
        });
    }
    
    /**
     * 加载测试数据 (暂时保留在此)
     * @return 包含加载结果的LiveData
     */
    public MutableLiveData<LoadTestDataResult> loadTestData() {
        MutableLiveData<LoadTestDataResult> resultLiveData = new MutableLiveData<>();
        Context currentContext = contextRef.get();
        if (currentContext == null) {
             resultLiveData.postValue(new LoadTestDataResult(false, "Context is null"));
             return resultLiveData;
        }
        
        // 使用TestDataLoader导入测试数据
        com.ggec.glasses.album.util.TestDataLoader testDataLoader = new com.ggec.glasses.album.util.TestDataLoader(
                currentContext, this, fileManager);
        
        testDataLoader.loadAllTestData(new com.ggec.glasses.album.util.TestDataLoader.OnTestDataLoadedCallback() {
            @Override
            public void onSuccess() {
                resultLiveData.postValue(new LoadTestDataResult(true, null));
            }
            
            @Override
            public void onError(String errorMessage) {
                resultLiveData.postValue(new LoadTestDataResult(false, errorMessage));
            }
        });
        
        return resultLiveData;
    }
    
    /**
     * 加载测试数据的结果类
     */
    public static class LoadTestDataResult {
        private final boolean success;
        private final String errorMessage;
        
        public LoadTestDataResult(boolean success, String errorMessage) {
            this.success = success;
            this.errorMessage = errorMessage;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
    }
    
} 