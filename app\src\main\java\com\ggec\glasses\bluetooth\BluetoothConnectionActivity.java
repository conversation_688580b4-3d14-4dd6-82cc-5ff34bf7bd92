package com.ggec.glasses.bluetooth;

import android.Manifest;
import android.bluetooth.BluetoothDevice;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.glasses.R;
import com.ggec.glasses.bluetooth.adapter.BluetoothDeviceAdapter;
import com.ggec.glasses.bluetooth.adapter.GlassesBluetoothAdapter;
import com.ggec.glasses.bluetooth.bridge.BluetoothBridge;
import com.ggec.glasses.bluetooth.bridge.BluetoothBridgeFactory;
import com.ggec.glasses.bluetooth.manager.BluetoothManager;
import com.ggec.glasses.bluetooth.model.BluetoothDeviceModel;
import com.ggec.glasses.bluetooth.model.BluetoothDeviceType;
import com.ggec.glasses.bluetooth.receiver.BluetoothBroadcastReceiver;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 蓝牙连接活动
 */
public class BluetoothConnectionActivity extends AppCompatActivity implements BluetoothBroadcastReceiver.BluetoothStateListener {

    private static final String TAG = "BluetoothConnectionActivity";
    private static final int MAX_COLLAPSED_DEVICES = 3; // 折叠状态下显示的最大设备数量

    private ImageView ivBack;
    private TextView tvTitle;
    private ImageView ivRefresh; // 新增的刷新按钮
    private ConstraintLayout headerLayout;
    private NestedScrollView contentScrollView;
    
    // 已配对设备相关视图
    private TextView tvPairedDevicesTitle;
    private LinearLayout pairedDevicesCard;
    private RecyclerView rvPairedDevices;
    private TextView tvNoPairedDevices;
    private LinearLayout pairedDevicesExpandButton;
    private TextView tvPairedDevicesExpandText;
    private ImageView ivPairedDevicesExpandIcon;
    
    // 可用设备相关视图
    private TextView tvAvailableDevicesTitle;
    private LinearLayout availableDevicesCard;
    private RecyclerView rvAvailableDevices;
    private TextView tvSearchingDevices;
    private TextView tvNoAvailableDevices;
    
    // 设备适配器
    private BluetoothDeviceAdapter pairedDevicesAdapter;
    private BluetoothDeviceAdapter availableDevicesAdapter;
    
    // 设备列表数据
    private final List<BluetoothDeviceModel> pairedDevicesList = new ArrayList<>();
    private final List<BluetoothDeviceModel> availableDevicesList = new ArrayList<>();
    
    // 已配对设备的完整列表和折叠列表
    private List<BluetoothDeviceModel> fullPairedDevicesList = new ArrayList<>();
    private List<BluetoothDeviceModel> collapsedPairedDevicesList = new ArrayList<>();
    
    // 是否已展开已配对设备列表
    private boolean isPairedDevicesExpanded = false;
    
    // 蓝牙桥接
    private BluetoothBridge bluetoothBridge;
    
    // 刷新相关状态
    private boolean isRefreshing = false;
    
    // 是否使用模拟数据（仅UI开发阶段使用）
    private final boolean useMockData = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 设置为沉浸式状态栏
        setupImmersiveStatusBar();
        
        setContentView(R.layout.activity_bluetooth_connection);

        // 初始化列表
        fullPairedDevicesList = new ArrayList<>();
        collapsedPairedDevicesList = new ArrayList<>();

        // 初始化UI组件
        initViews();
        // 设置事件监听器
        setupListeners();
        // 设置窗口插入适配
        setupWindowInsets();
        
        // 初始化蓝牙相关
        initBluetooth();
        
        // 加载设备数据
        loadDevicesData();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        // 刷新设备列表
        refreshDevicesData();
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        
        // 停止设备发现
        stopDeviceDiscovery();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // 释放蓝牙资源
        if (bluetoothBridge != null) {
            bluetoothBridge.stopDeviceDiscovery();
            bluetoothBridge.unregisterReceivers();
            bluetoothBridge.release();
        }
    }

    /**
     * 设置沉浸式状态栏
     */
    private void setupImmersiveStatusBar() {
        // 不使用沉浸式状态栏，让内容不延伸到状态栏
        WindowCompat.setDecorFitsSystemWindows(getWindow(), true);
        
        // 设置状态栏颜色
        Window window = getWindow();
        window.setStatusBarColor(ContextCompat.getColor(this, R.color.comp_background_gray));
        
        // 设置状态栏图标为深色（因为背景是浅色）
        WindowInsetsControllerCompat windowInsetsController = 
                WindowCompat.getInsetsController(window, window.getDecorView());
        if (windowInsetsController != null) {
            windowInsetsController.setAppearanceLightStatusBars(true);
        }
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        // 初始化导航栏相关组件
        ivBack = findViewById(R.id.iv_back);
        tvTitle = findViewById(R.id.tv_title);
        ivRefresh = findViewById(R.id.iv_refresh); // 初始化刷新按钮
        headerLayout = findViewById(R.id.header_layout);
        
        // 初始化内容区域
        contentScrollView = findViewById(R.id.content_scroll_view);
        // 确保NestedScrollView可以滚动
        contentScrollView.setNestedScrollingEnabled(true);
        
        // 初始化已配对设备相关组件
        tvPairedDevicesTitle = findViewById(R.id.tv_paired_devices_title);
        pairedDevicesCard = findViewById(R.id.paired_devices_card);
        rvPairedDevices = findViewById(R.id.rv_paired_devices);
        tvNoPairedDevices = findViewById(R.id.tv_no_paired_devices);
        pairedDevicesExpandButton = findViewById(R.id.paired_devices_expand_button);
        tvPairedDevicesExpandText = findViewById(R.id.tv_paired_devices_expand_text);
        ivPairedDevicesExpandIcon = findViewById(R.id.iv_paired_devices_expand_icon);
        
        // 初始化可用设备相关组件
        tvAvailableDevicesTitle = findViewById(R.id.tv_available_devices_title);
        availableDevicesCard = findViewById(R.id.available_devices_card);
        rvAvailableDevices = findViewById(R.id.rv_available_devices);
        tvSearchingDevices = findViewById(R.id.tv_searching_devices);
        tvNoAvailableDevices = findViewById(R.id.tv_no_available_devices);
        
        // 设置标题文本
        tvTitle.setText(R.string.bluetooth_connection_title);
        
        // 初始化适配器
        pairedDevicesAdapter = new BluetoothDeviceAdapter(this, pairedDevicesList);
        availableDevicesAdapter = new BluetoothDeviceAdapter(this, availableDevicesList);
        
        // 配置RecyclerView的布局管理器
        rvPairedDevices.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        
        rvAvailableDevices.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        
        // 设置RecyclerView的适配器
        rvPairedDevices.setAdapter(pairedDevicesAdapter);
        rvAvailableDevices.setAdapter(availableDevicesAdapter);
        
        // 禁用RecyclerView的独立滚动
        rvPairedDevices.setNestedScrollingEnabled(false);
        rvAvailableDevices.setNestedScrollingEnabled(false);
        
        // 设置固定大小以提高性能
        rvPairedDevices.setHasFixedSize(true);
        rvAvailableDevices.setHasFixedSize(true);
        
        // 更新刷新按钮状态
        updateRefreshButton();
    }

    /**
     * 设置事件监听器
     */
    private void setupListeners() {
        // 返回按钮点击事件
        ivBack.setOnClickListener(v -> finish());
        
        // 刷新按钮点击事件
        ivRefresh.setOnClickListener(v -> {
            if (isRefreshing) {
                stopDeviceDiscovery();
            } else {
                // 清空可用设备列表
                availableDevicesList.clear();
                availableDevicesAdapter.notifyDataSetChanged();
                // 显示正在搜索提示
                tvSearchingDevices.setVisibility(View.VISIBLE);
                tvNoAvailableDevices.setVisibility(View.GONE);
                // 开始扫描
                startDeviceDiscovery();
            }
        });
        
        // 已配对设备点击事件
        pairedDevicesAdapter.setOnDeviceClickListener(device -> {
            Toast.makeText(this, "已配对设备: " + device.getName(), Toast.LENGTH_SHORT).show();
            connectToDevice(device);
        });
        
        // 可用设备点击事件
        availableDevicesAdapter.setOnDeviceClickListener(device -> {
            Toast.makeText(this, "可用设备: " + device.getName(), Toast.LENGTH_SHORT).show();
            pairWithDevice(device);
        });
        
        // 已配对设备展开按钮点击事件
        pairedDevicesExpandButton.setOnClickListener(v -> {
            if (isPairedDevicesExpanded) {
                collapsePairedDevices();
            } else {
                expandPairedDevices();
            }
        });
    }

    /**
     * 设置窗口插入适配
     */
    private void setupWindowInsets() {
        // 由于我们不再使用沉浸式状态栏，不需要特殊的Insets处理
        // 保留这个方法以便将来可能需要的功能扩展
    }

    /**
     * 初始化蓝牙相关功能
     */
    private void initBluetooth() {
        // 初始化蓝牙桥接
        bluetoothBridge = BluetoothBridgeFactory.createBridge(
                this,
                BluetoothBridgeFactory.BridgeType.REALTEK
        );
        
        // 设置设备发现监听器
        bluetoothBridge.setDeviceDiscoveryListener(new BluetoothBridge.DeviceDiscoveryListener() {
            @Override
            public void onDiscoveryStarted() {
                runOnUiThread(() -> {
                    isRefreshing = true;
                    updateRefreshButton();
                    tvSearchingDevices.setVisibility(View.VISIBLE);
                    tvNoAvailableDevices.setVisibility(View.GONE);
                });
            }
            
            @Override
            public void onDiscoveryFinished() {
                runOnUiThread(() -> {
                    isRefreshing = false;
                    updateRefreshButton();
                    tvSearchingDevices.setVisibility(View.GONE);
                    
                    if (availableDevicesList.isEmpty()) {
                        tvNoAvailableDevices.setVisibility(View.VISIBLE);
                    } else {
                        tvNoAvailableDevices.setVisibility(View.GONE);
                    }
                });
            }
            
            @Override
            public void onDeviceFound(BluetoothDeviceModel device) {
                runOnUiThread(() -> {
                    // 检查设备是否已配对
                    if (device.isPaired()) {
                        // 配对设备不在可用设备列表中显示，而是更新到已配对设备列表
                        boolean isNewPairedDevice = true;
                        for (int i = 0; i < pairedDevicesList.size(); i++) {
                            if (pairedDevicesList.get(i).getAddress().equals(device.getAddress())) {
                                pairedDevicesList.set(i, device);
                                isNewPairedDevice = false;
                                break;
                            }
                        }
                        
                        if (isNewPairedDevice) {
                            pairedDevicesList.add(device);
                            updatePairedDevicesList(pairedDevicesList);
                        } else {
                            pairedDevicesAdapter.notifyDataSetChanged();
                        }
                        
                        // 同时从可用设备列表中移除该设备（如果存在）
                        for (int i = 0; i < availableDevicesList.size(); i++) {
                            if (availableDevicesList.get(i).getAddress().equals(device.getAddress())) {
                                availableDevicesList.remove(i);
                                availableDevicesAdapter.notifyDataSetChanged();
                                break;
                            }
                        }
                    } else {
                        // 检查未配对设备是否已在列表中
                        boolean isNewAvailableDevice = true;
                        for (int i = 0; i < availableDevicesList.size(); i++) {
                            if (availableDevicesList.get(i).getAddress().equals(device.getAddress())) {
                                availableDevicesList.set(i, device);
                                isNewAvailableDevice = false;
                                break;
                            }
                        }
                        
                        if (isNewAvailableDevice) {
                            availableDevicesList.add(device);
                        }
                        
                        availableDevicesAdapter.notifyDataSetChanged();
                        
                        if (!availableDevicesList.isEmpty()) {
                            tvNoAvailableDevices.setVisibility(View.GONE);
                        }
                    }
                });
            }
        });
        
        // 设置连接监听器
        bluetoothBridge.setConnectionListener(new BluetoothBridge.ConnectionListener() {
            @Override
            public void onDeviceConnected(BluetoothDeviceModel device) {
                runOnUiThread(() -> {
                    Toast.makeText(BluetoothConnectionActivity.this, 
                            "已连接到设备: " + device.getName(), 
                            Toast.LENGTH_SHORT).show();
                    
                    // 刷新设备列表状态
                    refreshDevicesData();
                });
            }
            
            @Override
            public void onDeviceDisconnected(BluetoothDeviceModel device) {
                runOnUiThread(() -> {
                    Toast.makeText(BluetoothConnectionActivity.this, 
                            "设备已断开连接: " + device.getName(), 
                            Toast.LENGTH_SHORT).show();
                    
                    // 刷新设备列表状态
                    refreshDevicesData();
                });
            }
            
            @Override
            public void onBluetoothStateChanged(boolean enabled) {
                runOnUiThread(() -> {
                    if (!enabled) {
                        tvNoAvailableDevices.setText(R.string.bluetooth_disabled);
                        tvNoAvailableDevices.setVisibility(View.VISIBLE);
                        tvSearchingDevices.setVisibility(View.GONE);
                    } else {
                        tvNoAvailableDevices.setText(R.string.no_available_devices);
                        // 蓝牙启用时自动开始扫描
                        startDeviceDiscovery();
                    }
                });
            }
            
            @Override
            public void onDevicePaired(BluetoothDeviceModel device) {
                runOnUiThread(() -> {
                    Toast.makeText(BluetoothConnectionActivity.this, 
                            "设备已配对: " + device.getName(), 
                            Toast.LENGTH_SHORT).show();
                    
                    // 刷新已配对设备列表
                    refreshDevicesData();
                });
            }
        });
        
        // 注册接收器
        bluetoothBridge.registerReceivers();
    }
    
    /**
     * 加载设备数据
     */
    private void loadDevicesData() {
        if (useMockData) {
            // 使用模拟数据（开发测试用）
            loadMockDevices();
        } else {
            // 从蓝牙桥接获取实际设备数据
            refreshDevicesData();
        }
    }
    
    /**
     * 刷新设备数据
     */
    private void refreshDevicesData() {
        // 清空现有列表
        pairedDevicesList.clear();
        availableDevicesList.clear();
        
        // 更新UI先
        pairedDevicesAdapter.notifyDataSetChanged();
        availableDevicesAdapter.notifyDataSetChanged();
        
        // 更新UI状态
        tvSearchingDevices.setVisibility(View.GONE);
        tvNoAvailableDevices.setVisibility(View.VISIBLE);
        tvNoAvailableDevices.setText(R.string.no_available_devices);
        
        // 获取已配对设备
        List<BluetoothDeviceModel> pairedDevices = bluetoothBridge.getPairedDevices();
        if (pairedDevices != null && !pairedDevices.isEmpty()) {
            pairedDevicesList.addAll(pairedDevices);
            
            // 更新已配对设备UI
            tvNoPairedDevices.setVisibility(View.GONE);
            updatePairedDevicesList(pairedDevicesList);
        } else {
            tvNoPairedDevices.setVisibility(View.VISIBLE);
            pairedDevicesExpandButton.setVisibility(View.GONE);
        }
        
        // 开始扫描可用设备
        startDeviceDiscovery();
    }
    
    /**
     * 开始设备发现
     */
    private void startDeviceDiscovery() {
        if (!bluetoothBridge.isBluetoothEnabled()) {
            tvSearchingDevices.setVisibility(View.GONE);
            tvNoAvailableDevices.setText(R.string.bluetooth_disabled);
            tvNoAvailableDevices.setVisibility(View.VISIBLE);
            isRefreshing = false;
            updateRefreshButton();
            return;
        }
        
        // 开始发现设备
        if (bluetoothBridge.startDeviceDiscovery()) {
            isRefreshing = true;
            updateRefreshButton();
            tvSearchingDevices.setVisibility(View.VISIBLE);
            tvNoAvailableDevices.setVisibility(View.GONE);
        }
    }
    
    /**
     * 停止设备发现
     */
    private void stopDeviceDiscovery() {
        if (bluetoothBridge != null && bluetoothBridge.isDiscovering()) {
            bluetoothBridge.stopDeviceDiscovery();
        }
        isRefreshing = false;
        updateRefreshButton();
        tvSearchingDevices.setVisibility(View.GONE);
        
        if (availableDevicesList.isEmpty()) {
            tvNoAvailableDevices.setVisibility(View.VISIBLE);
        } else {
            tvNoAvailableDevices.setVisibility(View.GONE);
        }
    }
    
    /**
     * 更新刷新按钮状态
     */
    private void updateRefreshButton() {
        if (ivRefresh == null) return;
        
        if (isRefreshing) {
            // 显示停止图标
            ivRefresh.setImageResource(R.drawable.ic_stop);
            // 添加旋转动画
            if (ivRefresh.getAnimation() == null) {
                Animation rotateAnimation = new RotateAnimation(
                        0, 360,
                        Animation.RELATIVE_TO_SELF, 0.5f,
                        Animation.RELATIVE_TO_SELF, 0.5f
                );
                rotateAnimation.setDuration(1000);
                rotateAnimation.setRepeatCount(Animation.INFINITE);
                ivRefresh.startAnimation(rotateAnimation);
            }
        } else {
            // 显示刷新图标
            ivRefresh.setImageResource(R.drawable.ic_refresh);
            // 移除动画
            ivRefresh.clearAnimation();
        }
    }
    
    /**
     * 连接到设备
     * @param device 设备模型
     */
    private void connectToDevice(BluetoothDeviceModel device) {
        if (device != null) {
            bluetoothBridge.connectToDevice(device);
        }
    }
    
    /**
     * 与设备配对
     * @param device 设备模型
     */
    private void pairWithDevice(BluetoothDeviceModel device) {
        if (device != null) {
            bluetoothBridge.pairWithDevice(device);
        }
    }
    
    /**
     * 加载模拟设备数据（仅用于UI开发测试）
     */
    private void loadMockDevices() {
        // 模拟已配对设备
        List<BluetoothDeviceModel> mockPairedDevices = new ArrayList<>();
        mockPairedDevices.add(new BluetoothDeviceModel("我的眼镜", "11:22:33:44:55:66", BluetoothDeviceType.GLASSES, true));
        mockPairedDevices.add(new BluetoothDeviceModel("AirPods Pro", "AA:BB:CC:DD:EE:FF", BluetoothDeviceType.EARPHONE, true));
        mockPairedDevices.add(new BluetoothDeviceModel("小米手表", "12:34:56:78:90:AB", BluetoothDeviceType.WATCH, true));
        mockPairedDevices.add(new BluetoothDeviceModel("Samsung TV", "AB:CD:EF:12:34:56", BluetoothDeviceType.TV, true));
        mockPairedDevices.add(new BluetoothDeviceModel("笔记本电脑", "98:76:54:32:10:EF", BluetoothDeviceType.COMPUTER, true));
        
        // 更新已配对设备列表
        updatePairedDevicesList(mockPairedDevices);
        
        // 模拟可用设备
        List<BluetoothDeviceModel> mockAvailableDevices = new ArrayList<>();
        mockAvailableDevices.add(new BluetoothDeviceModel("新眼镜", "22:33:44:55:66:77", BluetoothDeviceType.GLASSES, false));
        mockAvailableDevices.add(new BluetoothDeviceModel("SONY耳机", "BB:CC:DD:EE:FF:00", BluetoothDeviceType.EARPHONE, false));
        mockAvailableDevices.add(new BluetoothDeviceModel("华为手表", "23:45:67:89:0A:BC", BluetoothDeviceType.WATCH, false));
        
        // 更新可用设备列表
        updateAvailableDevicesList(mockAvailableDevices);
    }
    
    /**
     * 更新已配对设备列表
     * @param devicesList 设备列表
     */
    private void updatePairedDevicesList(List<BluetoothDeviceModel> devicesList) {
        fullPairedDevicesList = new ArrayList<>(devicesList);
        
        if (fullPairedDevicesList.size() > MAX_COLLAPSED_DEVICES) {
            // 如果设备数量超过最大显示数，创建折叠列表
            collapsedPairedDevicesList = new ArrayList<>(fullPairedDevicesList.subList(0, MAX_COLLAPSED_DEVICES));
            pairedDevicesExpandButton.setVisibility(View.VISIBLE);
        } else {
            // 如果设备数量较少，不需要折叠
            collapsedPairedDevicesList = fullPairedDevicesList;
            pairedDevicesExpandButton.setVisibility(View.GONE);
        }
        
        // 根据当前展开状态更新列表
        pairedDevicesList.clear();
        if (isPairedDevicesExpanded) {
            pairedDevicesList.addAll(fullPairedDevicesList);
        } else {
            pairedDevicesList.addAll(collapsedPairedDevicesList);
        }
        
        pairedDevicesAdapter.notifyDataSetChanged();
        updatePairedDevicesExpandButton();
    }
    
    /**
     * 更新可用设备列表
     * @param devicesList 设备列表
     */
    private void updateAvailableDevicesList(List<BluetoothDeviceModel> devicesList) {
        availableDevicesList.clear();
        availableDevicesList.addAll(devicesList);
        availableDevicesAdapter.notifyDataSetChanged();
        
        // 更新UI状态
        if (availableDevicesList.isEmpty()) {
            tvNoAvailableDevices.setVisibility(View.VISIBLE);
        } else {
            tvNoAvailableDevices.setVisibility(View.GONE);
        }
    }
    
    /**
     * 展开已配对设备列表
     */
    private void expandPairedDevices() {
        isPairedDevicesExpanded = true;
        
        pairedDevicesList.clear();
        pairedDevicesList.addAll(fullPairedDevicesList);
        pairedDevicesAdapter.notifyDataSetChanged();
        
        updatePairedDevicesExpandButton();
    }
    
    /**
     * 折叠已配对设备列表
     */
    private void collapsePairedDevices() {
        isPairedDevicesExpanded = false;
        
        pairedDevicesList.clear();
        pairedDevicesList.addAll(collapsedPairedDevicesList);
        pairedDevicesAdapter.notifyDataSetChanged();
        
        updatePairedDevicesExpandButton();
    }
    
    /**
     * 更新已配对设备展开按钮
     */
    private void updatePairedDevicesExpandButton() {
        if (fullPairedDevicesList.size() <= MAX_COLLAPSED_DEVICES) {
            pairedDevicesExpandButton.setVisibility(View.GONE);
            return;
        }
        
        pairedDevicesExpandButton.setVisibility(View.VISIBLE);
        if (isPairedDevicesExpanded) {
            tvPairedDevicesExpandText.setText("收起");
            ivPairedDevicesExpandIcon.setImageResource(R.drawable.ic_public_arrow_up);
        } else {
            tvPairedDevicesExpandText.setText("更多");
            ivPairedDevicesExpandIcon.setImageResource(R.drawable.ic_public_arrow_down);
        }
    }
    
    //------------------------------蓝牙状态监听器回调（保留以向后兼容）------------------------------
    
    @Override
    public void onBluetoothAdapterStateChanged(boolean enabled) {
        // 通过桥接处理，此处不需要实际处理
    }
    
    @Override
    public void onBluetoothConnectionStateChanged(boolean isConnecting, boolean isConnected) {
        // 通过桥接处理
    }
    
    @Override
    public void onDeviceConnected(BluetoothDevice device) {
        // 通过桥接处理
    }
    
    @Override
    public void onDeviceDisconnected(BluetoothDevice device) {
        // 通过桥接处理
    }
    
    @Override
    public void onDeviceBondStateChanged(BluetoothDevice device, int bondState) {
        // 通过桥接处理
    }
} 
