package com.ggec.glasses.aimodelchat.aimodel.deepseek;

import java.util.List;

/**
 * DeepSeek API 请求体结构
 */
public class DeepSeekRequest {
    private String model; // 模型名称
    private List<Message> messages; // 消息列表
    private boolean stream = false; // 是否使用流式传输，默认为 false
    private float temperature; // 新增 temperature 字段

    public DeepSeekRequest(String model, List<Message> messages, float temperature) {
        this.model = model;
        this.messages = messages;
        this.temperature = temperature;
    }

    // Getters 和 Setters (可选，Gson 可以直接访问私有字段)

    /**
     * 内部消息结构
     */
    public static class Message {
        private String role; // 角色 (user 或 assistant)
        private String content; // 消息内容

        public Message(String role, String content) {
            this.role = role;
            this.content = content;
        }

        // Getters 和 Setters (可选)
    }
} 