package com.ggec.glasses.ai.data.util;

import android.content.Context;
import android.util.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.channels.FileChannel;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 数据库工具类，提供数据库辅助功能
 */
public class DatabaseUtils {
    
    private static final String TAG = "DatabaseUtils";
    
    /**
     * 备份数据库文件
     * @param context 应用上下文
     * @param dbName 数据库名称
     * @return 备份文件路径，失败返回null
     */
    public static String backupDatabase(Context context, String dbName) {
        try {
            File dbFile = context.getDatabasePath(dbName);
            File externalFilesDir = context.getExternalFilesDir(null);
            File backupDir = new File(externalFilesDir, "database_backups");
            
            if (!backupDir.exists()) {
                boolean created = backupDir.mkdirs();
                if (!created) {
                    Log.e(TAG, "无法创建备份目录");
                    return null;
                }
            }
            
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            String backupFileName = dbName + "_" + timestamp + ".db";
            File backupFile = new File(backupDir, backupFileName);
            
            FileInputStream fis = new FileInputStream(dbFile);
            FileOutputStream fos = new FileOutputStream(backupFile);
            FileChannel src = fis.getChannel();
            FileChannel dst = fos.getChannel();
            dst.transferFrom(src, 0, src.size());
            src.close();
            dst.close();
            fis.close();
            fos.close();
            
            Log.i(TAG, "数据库备份成功: " + backupFile.getAbsolutePath());
            return backupFile.getAbsolutePath();
        } catch (Exception e) {
            Log.e(TAG, "数据库备份失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 恢复数据库备份
     * @param context 应用上下文
     * @param dbName 数据库名称
     * @param backupPath 备份文件路径
     * @return 是否恢复成功
     */
    public static boolean restoreDatabase(Context context, String dbName, String backupPath) {
        try {
            File backupFile = new File(backupPath);
            if (!backupFile.exists()) {
                Log.e(TAG, "备份文件不存在: " + backupPath);
                return false;
            }
            
            File dbFile = context.getDatabasePath(dbName);
            
            // 先删除当前数据库文件
            if (dbFile.exists()) {
                boolean deleted = dbFile.delete();
                if (!deleted) {
                    Log.e(TAG, "无法删除当前数据库文件: " + dbFile.getAbsolutePath());
                    return false;
                }
            }
            
            // 复制备份文件
            FileInputStream fis = new FileInputStream(backupFile);
            FileOutputStream fos = new FileOutputStream(dbFile);
            FileChannel src = fis.getChannel();
            FileChannel dst = fos.getChannel();
            dst.transferFrom(src, 0, src.size());
            src.close();
            dst.close();
            fis.close();
            fos.close();
            
            Log.i(TAG, "数据库恢复成功: " + dbFile.getAbsolutePath());
            return true;
        } catch (Exception e) {
            Log.e(TAG, "数据库恢复失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取备份文件列表
     * @param context 应用上下文
     * @param dbName 数据库名称（可选，为null时返回所有备份）
     * @return 备份文件数组
     */
    public static File[] getBackupFiles(Context context, String dbName) {
        try {
            File externalFilesDir = context.getExternalFilesDir(null);
            File backupDir = new File(externalFilesDir, "database_backups");
            
            if (!backupDir.exists()) {
                return new File[0];
            }
            
            if (dbName != null) {
                return backupDir.listFiles((dir, name) -> name.startsWith(dbName) && name.endsWith(".db"));
            } else {
                return backupDir.listFiles((dir, name) -> name.endsWith(".db"));
            }
        } catch (Exception e) {
            Log.e(TAG, "获取备份文件列表失败: " + e.getMessage());
            return new File[0];
        }
    }
    
    /**
     * 删除备份文件
     * @param backupPath 备份文件路径
     * @return 是否删除成功
     */
    public static boolean deleteBackup(String backupPath) {
        try {
            File backupFile = new File(backupPath);
            if (!backupFile.exists()) {
                Log.e(TAG, "备份文件不存在: " + backupPath);
                return false;
            }
            
            boolean deleted = backupFile.delete();
            if (deleted) {
                Log.i(TAG, "备份文件删除成功: " + backupPath);
            } else {
                Log.e(TAG, "备份文件删除失败: " + backupPath);
            }
            
            return deleted;
        } catch (Exception e) {
            Log.e(TAG, "删除备份文件失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 清空数据库备份
     * @param context 应用上下文
     * @return 是否清空成功
     */
    public static boolean clearBackups(Context context) {
        try {
            File externalFilesDir = context.getExternalFilesDir(null);
            File backupDir = new File(externalFilesDir, "database_backups");
            
            if (!backupDir.exists()) {
                return true;
            }
            
            File[] backupFiles = backupDir.listFiles((dir, name) -> name.endsWith(".db"));
            if (backupFiles == null || backupFiles.length == 0) {
                return true;
            }
            
            boolean allDeleted = true;
            for (File backupFile : backupFiles) {
                boolean deleted = backupFile.delete();
                if (!deleted) {
                    Log.e(TAG, "备份文件删除失败: " + backupFile.getAbsolutePath());
                    allDeleted = false;
                }
            }
            
            return allDeleted;
        } catch (Exception e) {
            Log.e(TAG, "清空备份失败: " + e.getMessage());
            return false;
        }
    }
} 