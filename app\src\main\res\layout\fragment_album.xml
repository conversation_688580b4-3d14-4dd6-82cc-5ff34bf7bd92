<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginTop="36dp"
        android:background="@color/comp_background_gray"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="19dp"
            android:text="相册"
            android:textColor="@color/font_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 功能菜单按钮 -->
        <ImageView
            android:id="@+id/btn_more"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/btn_ripple_small_corner"
            android:clickable="true"
            android:contentDescription="更多选项"
            android:focusable="true"
            android:padding="12dp"
            android:src="@drawable/ic_public_more"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/icon_primary" />

    </androidx.constraintlayout.widget.ConstraintLayout>
    
    <!-- 多选模式顶部导航栏 -->
    <include
        android:id="@+id/multi_select_header_layout"
        layout="@layout/layout_album_multi_select_header"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        app:layout_constraintTop_toTopOf="@id/header_layout" />

    <!-- 媒体筛选标签栏 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:layout_marginTop="2dp"
        android:layout_marginHorizontal="19dp"
        android:background="@color/comp_background_gray"
        app:layout_constraintTop_toBottomOf="@id/header_layout"
        style="@style/PillTabLayoutStyle"
        app:tabPaddingStart="0dp"
        app:tabPaddingEnd="0dp"
        app:tabPadding="0dp" />

    <!-- 媒体内容区域 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tab_layout" />
        
    <!-- 多选模式底部操作栏 -->
    <include
        android:id="@+id/multi_select_toolbar"
        layout="@layout/layout_album_multi_select_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 