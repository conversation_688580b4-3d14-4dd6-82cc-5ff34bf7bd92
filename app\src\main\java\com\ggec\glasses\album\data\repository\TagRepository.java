package com.ggec.glasses.album.data.repository;

import android.content.Context;
import android.util.Log;

import androidx.lifecycle.LiveData;

import com.ggec.glasses.album.data.db.MediaDatabase;
import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.data.entity.MediaTag;
import com.ggec.glasses.album.data.entity.Tag;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 标签存储库，负责处理标签相关的数据库操作和逻辑
 */
public class TagRepository {

    private static final String TAG = "TagRepository";

    private final ExecutorService executor;
    private final MediaDatabase database;
    private final Context context; // 保留 Context 以便未来可能需要

    private static TagRepository INSTANCE;

    public static synchronized TagRepository getInstance(Context context) {
        if (INSTANCE == null) {
            INSTANCE = new TagRepository(context.getApplicationContext());
        }
        return INSTANCE;
    }

    private TagRepository(Context context) {
        this.context = context;
        database = MediaDatabase.getInstance(context);
        executor = Executors.newFixedThreadPool(2); // 使用单独的线程池
    }

    // --- 回调接口定义 ---
    public interface OnTagCreatedCallback {
        void onSuccess(Tag tag);
        void onError(String errorMessage);
    }

    public interface OnTagAddedToMediaCallback {
        void onSuccess();
        void onError(String errorMessage);
    }

    public interface OnTagRemovedFromMediaCallback {
        void onSuccess();
        void onError(String errorMessage);
    }


    // --- 标签 CRUD ---

    /**
     * 创建新标签
     * @param name 标签名称
     * @param colorCode 标签颜色代码
     * @param callback 回调接口，通知操作结果
     */
    public void createTag(String name, String colorCode, OnTagCreatedCallback callback) {
        executor.execute(() -> {
            try {
                // 检查是否已存在同名标签
                Tag existingTag = database.tagDao().getTagByName(name);
                if (existingTag != null) {
                    if (callback != null) {
                        callback.onError("Tag with this name already exists");
                    }
                    return;
                }

                // 创建并插入新标签
                Tag tag = new Tag(name, colorCode);
                long tagId = database.tagDao().insertTag(tag);

                // 回调通知结果
                if (callback != null) {
                    tag.setId(tagId);
                    callback.onSuccess(tag);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error creating tag", e);
                if (callback != null) {
                    callback.onError("Error creating tag: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 获取所有标签
     * @return 包含标签列表的LiveData
     */
    public LiveData<List<Tag>> getAllTags() {
        return database.tagDao().getAllTagsLive();
    }

    // --- 媒体-标签关系 ---

    /**
     * 为媒体添加标签
     * @param mediaId 媒体ID
     * @param tagId 标签ID
     * @param callback 回调接口，通知操作结果
     */
    public void addTagToMedia(long mediaId, long tagId, OnTagAddedToMediaCallback callback) {
        executor.execute(() -> {
            try {
                // 检查媒体和标签是否存在
                 Media media = database.mediaDao().getMediaById(mediaId);
                 Tag tag = database.tagDao().getTagById(tagId);

                if (media == null || tag == null) {
                    if (callback != null) {
                        callback.onError("Media or tag not found");
                    }
                    return;
                }

                // 检查媒体是否已有此标签
                boolean hasTag = database.mediaTagDao().hasMediaTag(mediaId, tagId);
                if (hasTag) {
                    if (callback != null) {
                       // callback.onError("Media already has this tag");
                       // 不认为是错误，操作未执行
                       callback.onSuccess();
                    }
                    return;
                }

                // 为媒体添加标签
                MediaTag mediaTag = new MediaTag(mediaId, tagId);
                database.mediaTagDao().insertMediaTag(mediaTag);

                // 回调通知结果
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error adding tag to media", e);
                if (callback != null) {
                    callback.onError("Error adding tag to media: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 从媒体中移除标签
     * @param mediaId 媒体ID
     * @param tagId 标签ID
     * @param callback 回调接口，通知操作结果
     */
    public void removeTagFromMedia(long mediaId, long tagId, OnTagRemovedFromMediaCallback callback) {
        executor.execute(() -> {
            try {
                // 检查媒体是否有此标签
                boolean hasTag = database.mediaTagDao().hasMediaTag(mediaId, tagId);
                if (!hasTag) {
                    if (callback != null) {
                        //callback.onError("Media does not have this tag");
                        // 不认为是错误，操作未执行
                        callback.onSuccess();
                    }
                    return;
                }

                // 从媒体中移除标签
                database.mediaTagDao().removeTagFromMedia(mediaId, tagId);

                // 回调通知结果
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error removing tag from media", e);
                if (callback != null) {
                    callback.onError("Error removing tag from media: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 获取媒体的所有标签
     * @param mediaId 媒体ID
     * @return 包含媒体标签列表的LiveData
     */
    public LiveData<List<Tag>> getTagsForMedia(long mediaId) {
        return database.mediaTagDao().getTagsForMediaLive(mediaId);
    }

    /**
     * 获取带有指定标签的所有媒体
     * @param tagId 标签ID
     * @return 包含带有指定标签的媒体列表的LiveData
     */
    public LiveData<List<Media>> getMediaWithTag(long tagId) {
        return database.mediaTagDao().getMediaWithTagLive(tagId);
    }

    /**
     * 关闭存储库，释放资源 (如果需要单独管理生命周期)
     */
    public void close() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        // 注意：不应在此处销毁数据库实例，它由 MediaDatabase 类管理
        INSTANCE = null;
    }
} 