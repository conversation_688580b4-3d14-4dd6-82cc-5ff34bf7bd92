package com.ggec.glasses.bluetooth.adapter;

import android.Manifest;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import com.ggec.glasses.bluetooth.manager.BluetoothConnectionManager;
import com.ggec.glasses.bluetooth.manager.BluetoothProfileManager;
import com.ggec.glasses.bluetooth.util.BluetoothDeviceUtils;

import java.util.List;
import java.util.Set;

/**
 * 蓝牙适配器包装类
 * 作为底层蓝牙操作的入口点
 * 重命名以避免与系统BluetoothAdapter类冲突
 */
public class GlassesBluetoothAdapter {
    
    private static final String TAG = "GlassesBluetoothAdapter";
    private static GlassesBluetoothAdapter instance;
    private final Context context;
    private final android.bluetooth.BluetoothAdapter nativeAdapter;
    
    // 蓝牙模块管理器
    private final BluetoothProfileManager profileManager;
    private final BluetoothConnectionManager connectionManager;
    
    /**
     * 私有构造函数
     * @param context 上下文
     */
    private GlassesBluetoothAdapter(Context context) {
        this.context = context.getApplicationContext();
        this.nativeAdapter = android.bluetooth.BluetoothAdapter.getDefaultAdapter();
        
        // 初始化管理器
        this.profileManager = new BluetoothProfileManager(context, nativeAdapter);
        this.connectionManager = new BluetoothConnectionManager(context, nativeAdapter, profileManager);
    }
    
    /**
     * 获取单例实例
     * @param context 上下文
     * @return BluetoothAdapter实例
     */
    public static synchronized GlassesBluetoothAdapter getInstance(Context context) {
        if (instance == null) {
            instance = new GlassesBluetoothAdapter(context);
        }
        return instance;
    }
    
    /**
     * 释放蓝牙配置文件资源
     */
    public void releaseProfiles() {
        profileManager.releaseProfiles();
    }
    
    /**
     * 获取设备当前连接状态
     * @param device 蓝牙设备
     * @return 如果任何Profile已连接则返回true，否则返回false
     */
    public boolean isDeviceConnected(BluetoothDevice device) {
        return BluetoothDeviceUtils.isDeviceConnected(context, device, profileManager);
    }
    
    /**
     * 检查设备是否正在连接
     * @param device 蓝牙设备
     * @return 如果任何Profile正在连接则返回true，否则返回false
     */
    public boolean isDeviceConnecting(BluetoothDevice device) {
        return BluetoothDeviceUtils.isDeviceConnecting(context, device, profileManager);
    }
    
    /**
     * 获取所有已连接的设备
     * @return 已连接的设备列表
     */
    public List<BluetoothDevice> getConnectedDevices() {
        return BluetoothDeviceUtils.getConnectedDevices(context, profileManager);
    }
    
    /**
     * 获取所有正在连接的设备
     * @return 正在连接的设备列表
     */
    public List<BluetoothDevice> getConnectingDevices() {
        return BluetoothDeviceUtils.getConnectingDevices(context, profileManager);
    }
    
    /**
     * 检查设备是否支持蓝牙
     * @return 如果支持则返回true，否则返回false
     */
    public boolean isSupported() {
        return nativeAdapter != null;
    }
    
    /**
     * 检查蓝牙是否已启用
     * @return 如果已启用则返回true，否则返回false
     */
    public boolean isEnabled() {
        if (nativeAdapter == null) {
            return false;
        }
        
        try {
            return nativeAdapter.isEnabled();
        } catch (SecurityException e) {
            Log.e(TAG, "检查蓝牙是否启用时出现安全异常", e);
            return false;
        }
    }
    
    /**
     * 启用蓝牙
     * @return 如果请求成功发送则返回true，否则返回false
     */
    public boolean enable() {
        if (nativeAdapter == null) {
            Log.e(TAG, "设备不支持蓝牙，无法启用");
            return false;
        }
        
        if (isEnabled()) {
            Log.d(TAG, "蓝牙已经启用");
            return true;
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    Log.e(TAG, "启用蓝牙失败：缺少BLUETOOTH_CONNECT权限");
                    return false;
                }
            }
            
            return nativeAdapter.enable();
        } catch (SecurityException e) {
            Log.e(TAG, "启用蓝牙时出现安全异常", e);
            return false;
        }
    }
    
    /**
     * 禁用蓝牙
     * @return 如果请求成功发送则返回true，否则返回false
     */
    public boolean disable() {
        if (nativeAdapter == null) {
            Log.e(TAG, "设备不支持蓝牙，无法禁用");
            return false;
        }
        
        if (!isEnabled()) {
            Log.d(TAG, "蓝牙已经禁用");
            return true;
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    Log.e(TAG, "禁用蓝牙失败：缺少BLUETOOTH_CONNECT权限");
                    return false;
                }
            }
            
            return nativeAdapter.disable();
        } catch (SecurityException e) {
            Log.e(TAG, "禁用蓝牙时出现安全异常", e);
            return false;
        }
    }
    
    /**
     * 获取已配对设备列表
     * @return 已配对设备集合
     */
    public Set<BluetoothDevice> getBondedDevices() {
        return BluetoothDeviceUtils.getBondedDevices(context, nativeAdapter);
    }
    
    /**
     * 获取启用蓝牙的Intent
     * @return 启用蓝牙Intent
     */
    public Intent getEnableBluetoothIntent() {
        return new Intent(android.bluetooth.BluetoothAdapter.ACTION_REQUEST_ENABLE);
    }
    
    /**
     * 获取蓝牙设置页面的Intent
     * @return 跳转到蓝牙设置的Intent
     */
    public Intent getBluetoothSettingsIntent() {
        return new Intent(Settings.ACTION_BLUETOOTH_SETTINGS);
    }
    
    /**
     * 获取设备蓝牙地址
     * @return 蓝牙地址，如果无法获取则返回null
     */
    public String getAddress() {
        if (nativeAdapter == null) {
            return null;
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    Log.e(TAG, "获取蓝牙地址失败：缺少BLUETOOTH_CONNECT权限");
                    return null;
                }
            }
            
            return nativeAdapter.getAddress();
        } catch (SecurityException e) {
            Log.e(TAG, "获取蓝牙地址时出现安全异常", e);
            return null;
        }
    }
    
    /**
     * 获取设备蓝牙名称
     * @return 蓝牙名称，如果无法获取则返回null
     */
    public String getName() {
        if (nativeAdapter == null) {
            return null;
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    Log.e(TAG, "获取蓝牙名称失败：缺少BLUETOOTH_CONNECT权限");
                    return null;
                }
            }
            
            return nativeAdapter.getName();
        } catch (SecurityException e) {
            Log.e(TAG, "获取蓝牙名称时出现安全异常", e);
            return null;
        }
    }
    
    /**
     * 检查是否至少有一个Profile已连接
     * @return 如果任一Profile已连接则返回true，否则返回false
     */
    public boolean isAnyProfileConnected() {
        return profileManager.isAnyProfileConnected();
    }
    
    /**
     * 获取本地蓝牙适配器
     * @return 本地蓝牙适配器
     */
    public android.bluetooth.BluetoothAdapter getNativeAdapter() {
        return nativeAdapter;
    }
    
    /**
     * 开始扫描蓝牙设备
     * @return 是否成功开始扫描
     */
    public boolean startDiscovery() {
        return connectionManager.startDiscovery();
    }
    
    /**
     * 取消蓝牙设备扫描
     * @return 是否成功取消扫描
     */
    public boolean cancelDiscovery() {
        return connectionManager.cancelDiscovery();
    }
    
    /**
     * 根据MAC地址获取蓝牙设备
     * @param address MAC地址
     * @return 蓝牙设备，如果未找到则返回null
     */
    public BluetoothDevice getDeviceByAddress(String address) {
        return BluetoothDeviceUtils.getDeviceByAddress(context, nativeAdapter, address);
    }
    
    /**
     * 连接到蓝牙设备
     * @param device 蓝牙设备
     * @return 是否成功开始连接
     */
    public boolean connectToDevice(BluetoothDevice device) {
        return connectionManager.connectToDevice(device);
    }
    
    /**
     * 与设备配对
     * @param device 蓝牙设备
     * @return 是否成功开始配对
     */
    public boolean pairDevice(BluetoothDevice device) {
        return connectionManager.pairDevice(device);
    }
    
    /**
     * 获取蓝牙配置文件管理器
     * @return 蓝牙配置文件管理器
     */
    public BluetoothProfileManager getProfileManager() {
        return profileManager;
    }
    
    /**
     * 获取蓝牙连接管理器
     * @return 蓝牙连接管理器
     */
    public BluetoothConnectionManager getConnectionManager() {
        return connectionManager;
    }
} 