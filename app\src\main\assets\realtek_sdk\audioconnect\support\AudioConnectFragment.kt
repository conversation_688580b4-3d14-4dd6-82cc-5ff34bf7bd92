/*
 * Copyright (c) 2024. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.sdk.audioconnect.support

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import com.realsil.sdk.support.base.BaseViewBindingFragment

/**
 * <AUTHOR>
 * @date 2024/06/21
 */
abstract class AudioConnectFragment<D : ViewBinding>(inflate: (LayoutInflater, ViewGroup?, Boolean) -> D) :
    BaseViewBindingFragment<D>(
        inflate
    ) {

    @JvmField
    var mDeviceAddress: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val intent = activity?.intent
        if (intent != null) {
            mDeviceAddress = intent.getStringExtra(EXTRA_KEY_BT_ADDR)!!
        }
    }

    companion object {
        const val EXTRA_KEY_BT_ADDR = "bt_addr"
    }
}