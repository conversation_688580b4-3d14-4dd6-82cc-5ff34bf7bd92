/*
 * Copyright (c) 2017-2022. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.bbpro;

import android.content.Context;

import com.realsil.sdk.audioconnect.tts.Language;
import com.realsil.sdk.support.preference.BaseSharedPrefes;


/**
 * preference for App
 * <AUTHOR>
 * @date 04/09/2017
 */

public class AppPref extends BaseSharedPrefes {
    public static final String PREFERENCE_NAME = "preferences_app";
    public static final String PREFERENCE_KEY_FIRST_START = "first_start";

    public static final String PREFERENCE_KEY_CURRENT_LANGUAGE = "current_language";
    public static final String PREFERENCE_KEY_SUPPORTED_LANGUAGE = "supported_language";


    private static volatile AppPref instance = null;

    private AppPref(Context context, String prefName) {
        super(context, prefName);
    }

    public static void initialize(Context context) {
        if (instance == null) {
            synchronized (AppPref.class) {
                if (instance == null) {
                    instance = new AppPref(context, PREFERENCE_NAME);
                }
            }
        }
    }

    public static AppPref getInstance() {
        return instance;
    }

    public int getCurrentLanuage() {
        if (!contains(PREFERENCE_KEY_CURRENT_LANGUAGE)) {
            setCurrentLanuage(Language.ENGLISH);
            return Language.ENGLISH;
        }
        return getInt(PREFERENCE_KEY_CURRENT_LANGUAGE,
                Language.ENGLISH);
    }

    public void setCurrentLanuage(int language) {
        set(PREFERENCE_KEY_CURRENT_LANGUAGE, language);
    }

    public int getSupportedLanuage() {
        return getInt(PREFERENCE_KEY_SUPPORTED_LANGUAGE, 0);
    }

    public void setSupportedLanuage(int language) {
        set(PREFERENCE_KEY_SUPPORTED_LANGUAGE, language);
    }

}
