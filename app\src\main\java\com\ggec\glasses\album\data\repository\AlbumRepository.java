package com.ggec.glasses.album.data.repository;

import android.content.Context;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.ggec.glasses.album.data.db.MediaDatabase;
import com.ggec.glasses.album.data.entity.Album;
import com.ggec.glasses.album.data.entity.AlbumMedia;
import com.ggec.glasses.album.data.entity.Media;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 相册存储库，负责处理相册相关的数据库操作和逻辑
 */
public class AlbumRepository {

    private static final String TAG = "AlbumRepository";

    private final ExecutorService executor;
    private final MediaDatabase database;
    private final Context context; // 保留 Context 以便未来可能需要

    private static AlbumRepository INSTANCE;

    public static synchronized AlbumRepository getInstance(Context context) {
        if (INSTANCE == null) {
            INSTANCE = new AlbumRepository(context.getApplicationContext());
        }
        return INSTANCE;
    }

    private AlbumRepository(Context context) {
        this.context = context;
        database = MediaDatabase.getInstance(context);
        executor = Executors.newFixedThreadPool(2); // 使用单独的线程池
    }

    // --- 回调接口定义 ---
    public interface OnAlbumCreatedCallback {
        void onSuccess(Album album);
        void onError(String errorMessage);
    }

    public interface OnAlbumDeletedCallback {
        void onSuccess();
        void onError(String errorMessage);
    }

    public interface OnMediaAddedToAlbumCallback {
        void onSuccess();
        void onError(String errorMessage);
    }

    public interface OnMediaRemovedFromAlbumCallback {
        void onSuccess();
        void onError(String errorMessage);
    }


    // --- 相册 CRUD ---

    /**
     * 创建新相册
     * @param name 相册名称
     * @param description 相册描述
     * @param callback 回调接口，通知操作结果
     */
    public void createAlbum(String name, String description, OnAlbumCreatedCallback callback) {
        executor.execute(() -> {
            try {
                // 检查是否已存在同名相册
                Album existingAlbum = database.albumDao().getAlbumByName(name);
                if (existingAlbum != null) {
                    if (callback != null) {
                        callback.onError("Album with this name already exists");
                    }
                    return;
                }

                // 创建并插入新相册
                Album album = new Album(name, description);
                long albumId = database.albumDao().insertAlbum(album);

                // 回调通知结果
                if (callback != null) {
                    album.setId(albumId);
                    callback.onSuccess(album);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error creating album", e);
                if (callback != null) {
                    callback.onError("Error creating album: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 获取所有相册
     * @return 包含相册列表的LiveData
     */
    public LiveData<List<Album>> getAllAlbums() {
        return database.albumDao().getAllAlbumsLive();
    }

    /**
     * 删除相册 (同时会删除 AlbumMedia 关联)
     * @param albumId 相册ID
     * @param callback 回调接口，通知操作结果
     */
    public void deleteAlbum(long albumId, OnAlbumDeletedCallback callback) {
        executor.execute(() -> {
            try {
                // 获取相册
                Album album = database.albumDao().getAlbumById(albumId);
                if (album == null) {
                    if (callback != null) {
                        callback.onError("Album not found");
                    }
                    return;
                }

                // 删除相册 (Room会自动处理级联删除 AlbumMedia)
                database.albumDao().deleteAlbum(album);

                // 回调通知结果
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error deleting album", e);
                if (callback != null) {
                    callback.onError("Error deleting album: " + e.getMessage());
                }
            }
        });
    }


    // --- 相册-媒体关系 ---

    /**
     * 将媒体添加到相册
     * @param albumId 相册ID
     * @param mediaId 媒体ID
     * @param callback 回调接口，通知操作结果
     */
    public void addMediaToAlbum(long albumId, long mediaId, OnMediaAddedToAlbumCallback callback) {
        executor.execute(() -> {
            try {
                // 检查媒体和相册是否存在
                // 注意：这里依赖 MediaDao，理想情况下可以通过 MediaRepository 检查，
                // 但为简化，暂时直接访问 DAO
                Media media = database.mediaDao().getMediaById(mediaId);
                Album album = database.albumDao().getAlbumById(albumId);

                if (media == null || album == null) {
                    if (callback != null) {
                        callback.onError("Media or album not found");
                    }
                    return;
                }

                // 检查媒体是否已在相册中
                boolean alreadyInAlbum = database.albumMediaDao().isMediaInAlbum(albumId, mediaId);
                if (alreadyInAlbum) {
                    if (callback != null) {
                        // 不认为是错误，但操作未执行
                         callback.onSuccess(); // 或者提供一个 onAlreadyExists 回调
                       // callback.onError("Media already in album");
                    }
                    return;
                }

                // 将媒体添加到相册
                AlbumMedia albumMedia = new AlbumMedia(albumId, mediaId);
                database.albumMediaDao().insertAlbumMedia(albumMedia);

                // 如果相册没有封面，则将此媒体设为封面
                if (album.getCoverMediaId() == null || album.getCoverMediaId() == 0) {
                     database.albumDao().updateAlbumCover(albumId, mediaId);
                }

                // 回调通知结果
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error adding media to album", e);
                if (callback != null) {
                    callback.onError("Error adding media to album: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 从相册中移除媒体
     * @param albumId 相册ID
     * @param mediaId 媒体ID
     * @param callback 回调接口，通知操作结果
     */
    public void removeMediaFromAlbum(long albumId, long mediaId, OnMediaRemovedFromAlbumCallback callback) {
        executor.execute(() -> {
            try {
                // 检查媒体是否在相册中
                boolean inAlbum = database.albumMediaDao().isMediaInAlbum(albumId, mediaId);
                if (!inAlbum) {
                    if (callback != null) {
                       // callback.onError("Media not in album");
                       // 不认为是错误，操作未执行
                       callback.onSuccess();
                    }
                    return;
                }

                // 从相册中移除媒体
                database.albumMediaDao().removeMediaFromAlbum(albumId, mediaId);

                // 检查是否需要更新相册封面
                Album album = database.albumDao().getAlbumById(albumId);
                if (album != null && album.getCoverMediaId() != null && album.getCoverMediaId() == mediaId) {
                    // 查找相册中的其他媒体作为新封面
                    List<Media> remainingMedia = database.albumMediaDao().getMediaInAlbum(albumId); // 获取剩余媒体
                    if (!remainingMedia.isEmpty()) {
                        database.albumDao().updateAlbumCover(albumId, remainingMedia.get(0).getId());
                    } else {
                        database.albumDao().updateAlbumCover(albumId, null); // 没有媒体，清除封面 ID
                    }
                }

                // 回调通知结果
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error removing media from album", e);
                if (callback != null) {
                    callback.onError("Error removing media from album: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 获取相册中的媒体
     * @param albumId 相册ID
     * @return 包含相册中媒体列表的LiveData
     */
    public LiveData<List<Media>> getMediaInAlbum(long albumId) {
        return database.albumMediaDao().getMediaInAlbumLive(albumId);
    }

    /**
     * 获取相册中指定类型的媒体
     * @param albumId 相册ID
     * @param type 媒体类型
     * @return 包含相册中指定类型媒体列表的LiveData
     */
    public LiveData<List<Media>> getMediaInAlbumByType(long albumId, String type) {
        return database.albumMediaDao().getMediaInAlbumByTypeLive(albumId, type);
    }

     /**
     * 关闭存储库，释放资源 (如果需要单独管理生命周期)
     */
    public void close() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        // 注意：不应在此处销毁数据库实例，它由 MediaDatabase 类管理
        INSTANCE = null;
    }
} 