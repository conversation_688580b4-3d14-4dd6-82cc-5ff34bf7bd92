package com.ggec.glasses.ai.data.db.repository;

import android.content.Context;
import android.os.AsyncTask;

import androidx.lifecycle.LiveData;

import com.ggec.glasses.ai.data.db.AIChatMessageHistory;
import com.ggec.glasses.ai.data.db.dao.ChatMessageDao;
import com.ggec.glasses.ai.data.db.entity.ChatMessage;
import com.ggec.glasses.ai.data.db.entity.MessageStatus;

import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 聊天消息存储库，作为数据访问层
 * 负责协调数据库访问和业务逻辑
 */
public class ChatMessageRepository {
    
    private final ChatMessageDao chatMessageDao;
    private final Executor executor;
    
    /**
     * 构造函数
     * @param context 应用上下文
     */
    public ChatMessageRepository(Context context) {
        AIChatMessageHistory database = AIChatMessageHistory.getInstance(context);
        chatMessageDao = database.chatMessageDao();
        executor = Executors.newFixedThreadPool(4); // 创建固定大小的线程池
    }
    
    /**
     * 插入消息
     * @param message 要插入的消息
     * @param callback 回调
     */
    public void insertMessage(ChatMessage message, RepositoryCallback<Long> callback) {
        executor.execute(() -> {
            try {
                long messageId = chatMessageDao.insert(message);
                if (callback != null) {
                    callback.onSuccess(messageId);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("插入消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 批量插入消息
     * @param messages 要插入的消息列表
     * @param callback 回调
     */
    public void insertMessages(List<ChatMessage> messages, RepositoryCallback<List<Long>> callback) {
        executor.execute(() -> {
            try {
                List<Long> messageIds = chatMessageDao.insertAll(messages);
                if (callback != null) {
                    callback.onSuccess(messageIds);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("批量插入消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 更新消息
     * @param message 要更新的消息
     * @param callback 回调
     */
    public void updateMessage(ChatMessage message, RepositoryCallback<Void> callback) {
        executor.execute(() -> {
            try {
                chatMessageDao.update(message);
                if (callback != null) {
                    callback.onSuccess(null);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("更新消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 删除消息
     * @param message 要删除的消息
     * @param callback 回调
     */
    public void deleteMessage(ChatMessage message, RepositoryCallback<Void> callback) {
        executor.execute(() -> {
            try {
                chatMessageDao.delete(message);
                if (callback != null) {
                    callback.onSuccess(null);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("删除消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 根据ID获取消息
     * @param messageId 消息ID
     * @param callback 回调
     */
    public void getMessageById(long messageId, RepositoryCallback<ChatMessage> callback) {
        executor.execute(() -> {
            try {
                ChatMessage message = chatMessageDao.getMessageById(messageId);
                if (callback != null) {
                    callback.onSuccess(message);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("获取消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 根据ID获取消息（LiveData版本）
     * @param messageId 消息ID
     * @return 包含消息对象的LiveData
     */
    public LiveData<ChatMessage> getMessageByIdLive(long messageId) {
        return chatMessageDao.getMessageByIdLive(messageId);
    }
    
    /**
     * 获取某个会话的所有消息
     * @param conversationId 会话ID
     * @param callback 回调
     */
    public void getMessagesByConversation(long conversationId, RepositoryCallback<List<ChatMessage>> callback) {
        executor.execute(() -> {
            try {
                List<ChatMessage> messages = chatMessageDao.getMessagesByConversation(conversationId);
                if (callback != null) {
                    callback.onSuccess(messages);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("获取会话消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 获取某个会话的所有消息（LiveData版本）
     * @param conversationId 会话ID
     * @return 包含消息列表的LiveData
     */
    public LiveData<List<ChatMessage>> getMessagesByConversationLive(long conversationId) {
        return chatMessageDao.getMessagesByConversationLive(conversationId);
    }
    
    /**
     * 根据状态获取消息
     * @param status 消息状态
     * @param callback 回调
     */
    public void getMessagesByStatus(MessageStatus status, RepositoryCallback<List<ChatMessage>> callback) {
        executor.execute(() -> {
            try {
                List<ChatMessage> messages = chatMessageDao.getMessagesByStatus(status.getValue());
                if (callback != null) {
                    callback.onSuccess(messages);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("获取状态消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 获取最新的N条消息
     * @param limit 消息数量限制
     * @param callback 回调
     */
    public void getLatestMessages(int limit, RepositoryCallback<List<ChatMessage>> callback) {
        executor.execute(() -> {
            try {
                List<ChatMessage> messages = chatMessageDao.getLatestMessages(limit);
                if (callback != null) {
                    callback.onSuccess(messages);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("获取最新消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 获取最新的N条消息（LiveData版本）
     * @param limit 消息数量限制
     * @return 包含最新消息的LiveData
     */
    public LiveData<List<ChatMessage>> getLatestMessagesLive(int limit) {
        return chatMessageDao.getLatestMessagesLive(limit);
    }
    
    /**
     * 更新消息状态
     * @param messageId 消息ID
     * @param status 新状态
     * @param callback 回调
     */
    public void updateMessageStatus(long messageId, MessageStatus status, RepositoryCallback<Void> callback) {
        executor.execute(() -> {
            try {
                chatMessageDao.updateMessageStatus(messageId, status.getValue());
                if (callback != null) {
                    callback.onSuccess(null);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("更新消息状态失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 删除指定会话的所有消息
     * @param conversationId 会话ID
     * @param callback 回调
     */
    public void deleteMessagesByConversation(long conversationId, RepositoryCallback<Void> callback) {
        executor.execute(() -> {
            try {
                chatMessageDao.deleteMessagesByConversation(conversationId);
                if (callback != null) {
                    callback.onSuccess(null);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("删除会话消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 删除所有消息
     * @param callback 回调
     */
    public void deleteAllMessages(RepositoryCallback<Void> callback) {
        executor.execute(() -> {
            try {
                chatMessageDao.deleteAllMessages();
                if (callback != null) {
                    callback.onSuccess(null);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("删除所有消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 搜索消息
     * @param query 搜索关键词
     * @param callback 回调
     */
    public void searchMessages(String query, RepositoryCallback<List<ChatMessage>> callback) {
        executor.execute(() -> {
            try {
                List<ChatMessage> messages = chatMessageDao.searchMessages(query);
                if (callback != null) {
                    callback.onSuccess(messages);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("搜索消息失败: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 搜索消息（LiveData版本）
     * @param query 搜索关键词
     * @return 包含搜索结果的LiveData
     */
    public LiveData<List<ChatMessage>> searchMessagesLive(String query) {
        return chatMessageDao.searchMessagesLive(query);
    }
} 