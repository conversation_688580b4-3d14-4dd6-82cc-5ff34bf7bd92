<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:id="@+id/video_player_root"
    android:clipChildren="false"
    android:clipToPadding="false">

    <!-- 居中的播放器容器，添加硬件加速和平滑淡入淡出效果 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/player_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintWidth_percent="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:hardwareAccelerated="true"
        android:clipChildren="false"
        android:clipToPadding="false">

        <!-- 播放器背景，确保无缝背景颜色过渡，始终为黑色 -->
        <View
            android:id="@+id/player_background"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/black"
            app:layout_constraintTop_toTopOf="@id/player_view"
            app:layout_constraintBottom_toBottomOf="@id/player_view"
            app:layout_constraintStart_toStartOf="@id/player_view"
            app:layout_constraintEnd_toEndOf="@id/player_view" />

        <!-- ExoPlayer的PlayerView，自适应视频尺寸 -->
        <com.google.android.exoplayer2.ui.PlayerView
            android:id="@+id/player_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="200dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:use_controller="true"
            app:controller_layout_id="@layout/custom_player_control_view"
            app:show_timeout="3000"
            app:show_buffering="when_playing"
            app:resize_mode="fit"
            app:surface_type="surface_view"
            app:shutter_background_color="@color/black"
            android:hardwareAccelerated="true"
            android:layerType="hardware" />

        <!-- 添加淡入淡出的过渡遮罩层，始终为黑色 -->
        <View
            android:id="@+id/transition_overlay"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/black"
            android:alpha="0"
            app:layout_constraintTop_toTopOf="@id/player_view"
            app:layout_constraintBottom_toBottomOf="@id/player_view"
            app:layout_constraintStart_toStartOf="@id/player_view"
            app:layout_constraintEnd_toEndOf="@id/player_view" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout> 