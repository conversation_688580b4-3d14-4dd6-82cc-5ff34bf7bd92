package com.ggec.glasses.album.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.ggec.glasses.album.data.entity.Media;

import java.util.List;

/**
 * 媒体数据访问对象接口
 */
@Dao
public interface MediaDao {
    
    /**
     * 插入一个媒体对象
     * @param media 要插入的媒体对象
     * @return 新插入记录的ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertMedia(Media media);
    
    /**
     * 批量插入媒体对象
     * @param mediaList 要插入的媒体对象列表
     * @return 新插入记录的ID列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insertMediaList(List<Media> mediaList);
    
    /**
     * 更新媒体对象
     * @param media 要更新的媒体对象
     */
    @Update
    void updateMedia(Media media);
    
    /**
     * 删除媒体对象
     * @param media 要删除的媒体对象
     */
    @Delete
    void deleteMedia(Media media);
    
    /**
     * 根据ID获取媒体对象
     * @param mediaId 媒体ID
     * @return 媒体对象
     */
    @Query("SELECT * FROM media WHERE id = :mediaId")
    Media getMediaById(long mediaId);
    
    /**
     * 根据ID获取媒体对象（LiveData版本）
     * @param mediaId 媒体ID
     * @return 包含媒体对象的LiveData
     */
    @Query("SELECT * FROM media WHERE id = :mediaId")
    LiveData<Media> getMediaByIdLive(long mediaId);
    
    /**
     * 获取所有未删除的媒体
     * @return 未删除的媒体列表
     */
    @Query("SELECT * FROM media WHERE is_deleted = 0 ORDER BY creation_date DESC")
    List<Media> getAllMedia();
    
    /**
     * 获取所有未删除的媒体（LiveData版本）
     * @return 包含未删除的媒体列表的LiveData
     */
    @Query("SELECT * FROM media WHERE is_deleted = 0 ORDER BY creation_date DESC")
    LiveData<List<Media>> getAllMediaLive();
    
    /**
     * 根据类型获取媒体
     * @param type 媒体类型（IMAGE或VIDEO）
     * @return 指定类型的媒体列表
     */
    @Query("SELECT * FROM media WHERE type = :type AND is_deleted = 0 ORDER BY creation_date DESC")
    List<Media> getMediaByType(String type);
    
    /**
     * 根据类型获取媒体（LiveData版本）
     * @param type 媒体类型（IMAGE或VIDEO）
     * @return 包含指定类型媒体列表的LiveData
     */
    @Query("SELECT * FROM media WHERE type = :type AND is_deleted = 0 ORDER BY creation_date DESC")
    LiveData<List<Media>> getMediaByTypeLive(String type);
    
    /**
     * 获取已删除的媒体（回收站）
     * @return 已删除的媒体列表
     */
    @Query("SELECT * FROM media WHERE is_deleted = 1 ORDER BY modification_date DESC")
    List<Media> getDeletedMedia();
    
    /**
     * 获取已删除的媒体（回收站，LiveData版本）
     * @return 包含已删除媒体列表的LiveData
     */
    @Query("SELECT * FROM media WHERE is_deleted = 1 ORDER BY modification_date DESC")
    LiveData<List<Media>> getDeletedMediaLive();
    
    /**
     * 根据文件路径查找媒体
     * @param filePath 文件路径
     * @return 匹配的媒体对象，如果不存在则返回null
     */
    @Query("SELECT * FROM media WHERE file_path = :filePath LIMIT 1")
    Media getMediaByFilePath(String filePath);
    
    /**
     * 标记媒体为已删除
     * @param mediaId 媒体ID
     */
    @Query("UPDATE media SET is_deleted = 1, modification_date = datetime('now') WHERE id = :mediaId")
    void markAsDeleted(long mediaId);
    
    /**
     * 恢复已删除的媒体
     * @param mediaId 媒体ID
     */
    @Query("UPDATE media SET is_deleted = 0, modification_date = datetime('now') WHERE id = :mediaId")
    void restoreMedia(long mediaId);
    
    /**
     * 永久删除媒体
     * @param mediaId 媒体ID
     */
    @Query("DELETE FROM media WHERE id = :mediaId")
    void permanentlyDeleteMedia(long mediaId);
    
    /**
     * 清空回收站
     */
    @Query("DELETE FROM media WHERE is_deleted = 1")
    void emptyRecycleBin();
    
    /**
     * 获取媒体总数
     * @return 未删除的媒体总数
     */
    @Query("SELECT COUNT(*) FROM media WHERE is_deleted = 0")
    int getMediaCount();
    
    /**
     * 获取指定类型的媒体总数
     * @param type 媒体类型
     * @return 指定类型的未删除媒体总数
     */
    @Query("SELECT COUNT(*) FROM media WHERE type = :type AND is_deleted = 0")
    int getMediaCountByType(String type);
    
    /**
     * 获取所有媒体，包括已删除的（仅限内部使用）
     * @return 所有媒体列表
     */
    @Query("SELECT * FROM media")
    List<Media> getAllMediaIncludingDeleted_InternalUseOnly();
    
    /**
     * 清空所有媒体数据
     */
    @Query("DELETE FROM media")
    void clearAllMedia();
} 