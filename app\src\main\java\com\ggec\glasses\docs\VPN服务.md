### 关键要点

- 研究表明，可以通过 Android 的 VpnService 创建 VPN 代理服务，确保 WiFi 连接开启，并允许与本地设备通过 WiFi 通信，同时让其他应用的互联网访问通过移动数据解决。
- 实现方法包括确定本地 WiFi 子网、计算排除本地子网的路由，并在 VPN 服务中处理数据包转发。
- 证据倾向于需要手动计算路由以排除本地子网，这可能涉及复杂的网络配置。

### 概述

要创建这样的 VPN 代理服务，我们需要确保 WiFi 保持连接，并允许本地设备通信，同时将其他应用的互联网流量路由到移动数据网络。这需要利用 Android 的 VpnService API，并进行一些网络配置。

#### 确定本地 WiFi 子网

首先，使用 WifiManager 获取 WiFi 连接信息，包括 IP 地址和子网掩码。然后，计算本地网络的地址和前缀长度。例如，如果 WiFi IP 是 ************，子网掩码是 *************，则本地子网是 ***********/24。

#### 配置 VPN 服务

使用 VpnService.Builder 设置 VPN 接口，添加路由以覆盖除本地子网外的所有 IP 地址（例如 0.0.0.0/0 除去 ***********/24）。这确保本地流量通过 WiFi，而互联网流量通过 VPN 接口。

#### 处理数据包转发

在 VPN 服务中，读取从 VPN 接口接收的数据包。对于目标为公共 IP 的数据包，使用绑定到移动数据网络的套接字转发。使用 ConnectivityManager 获取移动数据网络对象，并通过 network.bindSocket 绑定套接字，确保流量通过移动数据网络。

#### 确保本地通信

由于本地子网的流量未通过 VPN，它们会默认使用 WiFi 接口，与本地设备通信保持正常。

---

### 调查报告：详细实现 VPN 代理服务的步骤和考虑

本文详细探讨了如何在 Android 开发中创建 VPN 代理服务，确保 WiFi 连接开启，并允许与本地设备通过 WiFi 通信，同时让其他应用的互联网访问通过移动数据解决。这一过程涉及网络配置、路由计算和数据包转发，下面将逐步分析实现方法。

#### 背景与需求分析

用户希望在 Android 设备上实现以下功能：

- 保持 WiFi 连接开启，确保设备能通过 WiFi 与本地设备通信。
- 其他应用的互联网访问（即非本地通信）通过移动数据网络解决。

这要求我们区分本地流量（通常是私有 IP 范围，如 192.168.x.x）和互联网流量（公共 IP），并分别路由到不同的网络接口。Android 的 VpnService 提供了一个本地 VPN 接口，允许拦截和转发流量，这为实现上述需求提供了基础。

#### 实现步骤

##### 1. 确定本地 WiFi 子网

首先，需要获取设备的 WiFi 连接信息以确定本地子网。可以使用 WifiManager 获取当前 WiFi 的 IP 地址和子网掩码。例如：

- 通过 WifiManager.getConnectionInfo() 获取 WifiInfo，从中提取 IP 地址（例如 ************）。
- 子网掩码通常为 *************，对应前缀长度 /24。
- 计算网络地址：将 IP 地址与子网掩码按位与，得到网络地址（如 ***********）。前缀长度为 24，表示 ***********/24。

这一步确保我们知道本地通信的 IP 范围，例如 *********** 到 *************。

##### 2. 计算排除本地子网的路由

为了让本地流量通过 WiFi，而互联网流量通过 VPN（进而通过移动数据），需要在 VPN 服务中配置路由，覆盖除本地子网外的所有 IP 地址。这需要计算 0.0.0.0/0（所有 IP 地址）减去本地子网（如 ***********/24）的剩余路由。

这一过程涉及复杂的网络计算。例如，排除 ***********/24，可以通过以下方法：

- 使用 IP 地址库（如 [IPAddress](https://seancfoley.github.io/IPAddress/ipaddress.html)）计算补集路由。
- 或者手动实现算法：从 0.0.0.0/0 开始，递归分割前缀，直到找到不包含排除子网的最大前缀。例如：
    - 0.0.0.0/1（0.0.0.0-***************）不包含 ***********，可直接添加。
    - *********/1（*********-***************）包含 ***********，需进一步分割为 *********/2 和 *********/2，依此类推。

Stack Overflow 上提供了一个示例，排除 **************/21 需要添加 21 个路由，显示了这种计算的复杂性 ([Android VPNService Route Exclusion](https://stackoverflow.com/questions/14545800/android-vpnservice-route-exclusion))。

以下是可能的路由计算示例（假设排除 ***********/24）：

|路由地址|前缀长度|说明|
|---|---|---|
|0.0.0.0|1|覆盖 0.0.0.0-***************|
|*********|2|覆盖 *********-***************，但需进一步检查|
|*********|3|需分割，包含 ***********/24|

实际计算可能需要编程实现，推荐使用 IPAddress 库简化操作。

##### 3. 设置 VPN 服务

使用 VpnService.Builder 配置 VPN 接口：

- 设置会话名：builder.setSession("My VPN")。
- 添加 VPN 接口地址，例如 ********/24，确保不与本地 WiFi 子网冲突。
- 添加计算得到的路由：builder.addRoute("0.0.0.0", 0) 通常覆盖所有，但需替换为排除本地子网的路由列表。
- 建立 VPN 接口：ParcelFileDescriptor vpnInterface = builder.establish()。

这一步确保所有非本地子网的流量（即互联网流量）通过 VPN 接口。

##### 4. 处理数据包转发

VPN 服务启动后，会拦截通过 VPN 接口的流量。对于这些流量：

- 读取数据包，解析目标 IP 地址。由于路由配置，接收到的数据包目标为公共 IP。
- 使用 ConnectivityManager 获取移动数据网络：
    - Network[] networks = cm.getAllNetworks()，遍历找到 NetworkCapabilities.TRANSPORT_CELLULAR 的网络。
- 创建套接字并绑定到移动数据网络：
    - Socket socket = new Socket(); network.bindSocket(socket); socket.connect(new InetSocketAddress(destinationIp, destinationPort));。
- 发送数据包并处理响应，将响应写回 VPN 接口。

对于 TCP 连接，需要管理连接状态；对于 UDP，处理数据包即可。这一步确保互联网流量通过移动数据网络。

##### 5. 确保本地通信

由于 VPN 配置中未添加本地子网的路由（如 ***********/24），这些流量不会通过 VPN 接口，而是直接使用设备的默认网络接口，即 WiFi。这确保了与本地设备的通信通过 WiFi 正常进行。

##### 6. 附加考虑

- **DNS 处理**：DNS 查询可能通过 WiFi 或移动数据，取决于配置。如果 DNS 服务器在本地网络，建议让其通过 WiFi；如果在互联网，则通过 VPN 和移动数据。可以通过 addDnsServer 指定 DNS 服务器，但需注意路由配置。
- **网络绑定**：确保 VPN 服务自身连接（如需要外部服务器）不循环，通过 VpnService.protect 保护套接字。
- **WiFi 保持开启**：Android VPN 激活不会断开 WiFi，流量路由配置即可满足需求。

#### 技术细节与挑战

实现过程中可能遇到以下挑战：

- 路由计算复杂，尤其是在动态 WiFi 网络中，子网可能变化。
- 数据包转发需要处理 TCP/UDP 协议，涉及状态管理，可能需要参考开源项目如 ToyVpn。
- 移动数据网络可能不稳定，需考虑连接可靠性。

#### 结论

通过上述步骤，可以实现 VPN 代理服务，确保 WiFi 连接开启，支持本地设备通信，同时将其他应用的互联网流量路由到移动数据网络。这一方法依赖 Android 的 VpnService 和网络接口绑定功能，适合开发复杂网络应用的开发者。