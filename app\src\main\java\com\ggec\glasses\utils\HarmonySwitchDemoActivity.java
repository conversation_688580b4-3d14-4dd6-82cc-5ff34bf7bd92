package com.ggec.glasses.utils;

import android.os.Bundle;
import android.util.Log;
import android.widget.CompoundButton;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;

import com.ggec.glasses.R;

/**
 * 鸿蒙风格开关组件的演示Activity
 * 该Activity展示了如何使用HarmonySwitch和HarmonySwitchStyle
 */
public class HarmonySwitchDemoActivity extends AppCompatActivity {
    
    private static final String TAG = "HarmonySwitchDemo";
    
    private HarmonySwitch harmonySwitch1;
    private SwitchCompat harmonySwitch2;
    private SwitchCompat defaultSwitch;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.harmony_switch_demo);
        
        // 初始化视图
        initViews();
        
        // 设置事件监听
        setupListeners();
    }
    
    /**
     * 初始化视图组件
     */
    private void initViews() {
        harmonySwitch1 = findViewById(R.id.harmony_switch_1);
        harmonySwitch2 = findViewById(R.id.harmony_switch_2);
        defaultSwitch = findViewById(R.id.default_switch);
        
        // 设置初始状态
        harmonySwitch1.setChecked(true);
        harmonySwitch2.setChecked(true);
        defaultSwitch.setChecked(true);
    }
    
    /**
     * 设置开关事件监听器
     */
    private void setupListeners() {
        // 自定义HarmonySwitch组件的监听器
        harmonySwitch1.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Log.d(TAG, "HarmonySwitch组件: " + (isChecked ? "已开启" : "已关闭"));
            }
        });
        
        // 使用HarmonySwitchStyle样式的开关监听器
        harmonySwitch2.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Log.d(TAG, "使用HarmonySwitchStyle样式: " + (isChecked ? "已开启" : "已关闭"));
            }
        });
        
        // 默认开关样式监听器
        defaultSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Log.d(TAG, "默认开关样式: " + (isChecked ? "已开启" : "已关闭"));
            }
        });
    }
} 