package com.ggec.glasses.ai.data.db.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.TypeConverters;
import androidx.room.Ignore;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ggec.glasses.ai.data.ChatUIMessage;
import com.ggec.glasses.ai.data.db.converters.RoomConverters;

import java.util.Date;

/**
 * 聊天消息实体类，对应数据库中的chat_message表
 */
@Entity(tableName = "chat_message", indices = {
        @androidx.room.Index("conversation_id"),
        @androidx.room.Index("timestamp"),
        @androidx.room.Index("status")
})
@TypeConverters(RoomConverters.class)
public class ChatMessage {
    
    @PrimaryKey(autoGenerate = true)
    private long id;
    
    @ColumnInfo(name = "type")
    private int type; // 0: User, 1: AI
    
    @ColumnInfo(name = "content")
    private String content;
    
    @ColumnInfo(name = "timestamp")
    private Date timestamp;
    
    @ColumnInfo(name = "status")
    private MessageStatus status;
    
    @ColumnInfo(name = "conversation_id", defaultValue = "0")
    private long conversationId;
    
    @ColumnInfo(name = "reference_id", defaultValue = "0")
    private long referenceId;
    
    @ColumnInfo(name = "image_path")
    private String imagePath;
    
    @Nullable
    @ColumnInfo(name = "video_path")
    private String videoPath;
    
    /**
     * 默认构造函数
     */
    public ChatMessage() {
        this.timestamp = new Date();
        this.status = MessageStatus.SENDING;
        this.conversationId = 0;
        this.referenceId = 0;
    }
    
    /**
     * 创建一个新的消息
     * @param type 消息类型
     * @param content 消息内容
     */
    @Ignore
    public ChatMessage(int type, String content) {
        this();
        this.type = type;
        this.content = content;
    }
    
    /**
     * 创建一个新的消息
     * @param type 消息类型
     * @param content 消息内容
     * @param status 消息状态
     */
    @Ignore
    public ChatMessage(int type, String content, MessageStatus status) {
        this(type, content);
        this.status = status;
    }
    
    /**
     * 创建一个新的消息
     * @param type 消息类型
     * @param content 消息内容
     * @param timestamp 时间戳
     * @param status 消息状态
     */
    @Ignore
    public ChatMessage(int type, String content, Date timestamp, MessageStatus status) {
        this(type, content, status);
        this.timestamp = timestamp;
    }
    
    /**
     * 完整参数构造函数
     * @param type 消息类型
     * @param content 消息内容
     * @param timestamp 时间戳
     * @param status 消息状态
     * @param conversationId 会话ID
     * @param referenceId 引用消息ID
     */
    @Ignore
    public ChatMessage(int type, String content, Date timestamp, MessageStatus status, 
                       long conversationId, long referenceId) {
        this(type, content, timestamp, status);
        this.conversationId = conversationId;
        this.referenceId = referenceId;
    }
    
    // Getters and Setters
    
    public long getId() {
        return id;
    }
    
    public void setId(long id) {
        this.id = id;
    }
    
    public int getType() {
        return type;
    }
    
    public void setType(int type) {
        this.type = type;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Date getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }
    
    public MessageStatus getStatus() {
        return status;
    }
    
    public void setStatus(MessageStatus status) {
        this.status = status;
    }
    
    public long getConversationId() {
        return conversationId;
    }
    
    public void setConversationId(long conversationId) {
        this.conversationId = conversationId;
    }
    
    public long getReferenceId() {
        return referenceId;
    }
    
    public void setReferenceId(long referenceId) {
        this.referenceId = referenceId;
    }
    
    public String getImagePath() {
        return imagePath;
    }
    
    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }
    
    @Nullable
    public String getVideoPath() {
        return videoPath;
    }

    public void setVideoPath(@Nullable String videoPath) {
        this.videoPath = videoPath;
    }
    
    /**
     * 检查是否为用户消息
     * @return 是否为用户消息
     */
    public boolean isUserMessage() {
        return type == ChatUIMessage.TYPE_USER;
    }
    
    /**
     * 检查是否为AI消息
     * @return 是否为AI消息
     */
    public boolean isAIMessage() {
        return type == ChatUIMessage.TYPE_AI;
    }
    
    /**
     * 检查消息是否成功发送
     * @return 是否成功发送
     */
    public boolean isSuccessful() {
        return status == MessageStatus.SENT || 
               status == MessageStatus.RECEIVED || 
               status == MessageStatus.READ;
    }
    
    /**
     * 检查消息是否失败
     * @return 是否失败
     */
    public boolean isFailed() {
        return status == MessageStatus.FAILED;
    }
    
    /**
     * 检查消息是否正在发送
     * @return 是否正在发送
     */
    public boolean isSending() {
        return status == MessageStatus.SENDING;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ChatMessage that = (ChatMessage) o;
        
        if (id != that.id) return false;
        if (type != that.type) return false;
        if (conversationId != that.conversationId) return false;
        if (referenceId != that.referenceId) return false;
        if (content != null ? !content.equals(that.content) : that.content != null) return false;
        if (timestamp != null ? !timestamp.equals(that.timestamp) : that.timestamp != null) return false;
        if (imagePath != null ? !imagePath.equals(that.imagePath) : that.imagePath != null) return false;
        if (videoPath != null ? !videoPath.equals(that.videoPath) : that.videoPath != null) return false;
        return status == that.status;
    }
    
    @Override
    public int hashCode() {
        int result = (int) (id ^ (id >>> 32));
        result = 31 * result + type;
        result = 31 * result + (content != null ? content.hashCode() : 0);
        result = 31 * result + (timestamp != null ? timestamp.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (int) (conversationId ^ (conversationId >>> 32));
        result = 31 * result + (int) (referenceId ^ (referenceId >>> 32));
        result = 31 * result + (imagePath != null ? imagePath.hashCode() : 0);
        result = 31 * result + (videoPath != null ? videoPath.hashCode() : 0);
        return result;
    }
    
    @NonNull
    @Override
    public String toString() {
        return "ChatMessage{" +
                "id=" + id +
                ", type=" + (type == ChatUIMessage.TYPE_USER ? "USER" : "AI") +
                ", content='" + (content != null && content.length() > 20 ? 
                                content.substring(0, 20) + "..." : content) + '\'' +
                ", timestamp=" + timestamp +
                ", status=" + status +
                ", conversationId=" + conversationId +
                ", referenceId=" + referenceId +
                ", imagePath='" + imagePath + '\'' +
                ", videoPath='" + videoPath + '\'' +
                '}';
    }
    
    /**
     * 创建一个用户消息
     * @param content 消息内容
     * @param conversationId 会话ID
     * @return 用户消息实例
     */
    public static ChatMessage createUserMessage(String content, long conversationId) {
        ChatMessage message = new ChatMessage(ChatUIMessage.TYPE_USER, content);
        message.setConversationId(conversationId);
        return message;
    }
    
    /**
     * 创建一个用户消息
     * @param content 消息内容
     * @param conversationId 会话ID
     * @param timestamp 时间戳
     * @return 用户消息实例
     */
    public static ChatMessage createUserMessage(String content, long conversationId, Date timestamp) {
        ChatMessage message = new ChatMessage(ChatUIMessage.TYPE_USER, content);
        message.setConversationId(conversationId);
        message.setTimestamp(timestamp);
        return message;
    }
    
    /**
     * 创建一个AI消息
     * @param content 消息内容
     * @param conversationId 会话ID
     * @return AI消息实例
     */
    public static ChatMessage createAIMessage(String content, long conversationId) {
        ChatMessage message = new ChatMessage(ChatUIMessage.TYPE_AI, content);
        message.setConversationId(conversationId);
        return message;
    }
    
    /**
     * 创建一个AI消息
     * @param content 消息内容
     * @param conversationId 会话ID
     * @param timestamp 时间戳
     * @return AI消息实例
     */
    public static ChatMessage createAIMessage(String content, long conversationId, Date timestamp) {
        ChatMessage message = new ChatMessage(ChatUIMessage.TYPE_AI, content);
        message.setConversationId(conversationId);
        message.setTimestamp(timestamp);
        return message;
    }
} 