package com.ggec.glasses.ai.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.glasses.R;
import com.ggec.glasses.ai.data.ChatUIMessage;

/**
 * 消息列表适配器，用于在RecyclerView中显示消息
 */
public class MessageAdapter extends ListAdapter<ChatUIMessage, RecyclerView.ViewHolder> {
    
    public MessageAdapter() {
        super(new MessageDiffCallback());
    }
    
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        
        if (viewType == ChatUIMessage.TYPE_USER) {
            View view = inflater.inflate(R.layout.item_user_message, parent, false);
            return new UserMessageViewHolder(view);
        } else {
            View view = inflater.inflate(R.layout.item_ai_message, parent, false);
            return new AIMessageViewHolder(view);
        }
    }
    
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        ChatUIMessage message = getItem(position);
        
        if (holder instanceof UserMessageViewHolder) {
            ((UserMessageViewHolder) holder).bind(message);
        } else if (holder instanceof AIMessageViewHolder) {
            ((AIMessageViewHolder) holder).bind(message);
        }
    }
    
    @Override
    public int getItemViewType(int position) {
        return getItem(position).getType();
    }
    
    /**
     * 用户消息ViewHolder
     */
    static class UserMessageViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvMessage;
        
        UserMessageViewHolder(@NonNull View itemView) {
            super(itemView);
            tvMessage = itemView.findViewById(R.id.tv_message);
        }
        
        void bind(ChatUIMessage message) {
            tvMessage.setText(message.getContent());
        }
    }
    
    /**
     * AI消息ViewHolder
     */
    static class AIMessageViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvMessage;
        
        AIMessageViewHolder(@NonNull View itemView) {
            super(itemView);
            tvMessage = itemView.findViewById(R.id.tv_message);
        }
        
        void bind(ChatUIMessage message) {
            tvMessage.setText(message.getContent());
        }
    }
    
    /**
     * 消息差异回调，用于ListAdapter的高效更新
     */
    static class MessageDiffCallback extends DiffUtil.ItemCallback<ChatUIMessage> {
        @Override
        public boolean areItemsTheSame(@NonNull ChatUIMessage oldItem, @NonNull ChatUIMessage newItem) {
            // 在真实应用中，可能需要使用唯一ID来比较
            return oldItem.getTimestamp() == newItem.getTimestamp() && 
                   oldItem.getType() == newItem.getType();
        }
        
        @Override
        public boolean areContentsTheSame(@NonNull ChatUIMessage oldItem, @NonNull ChatUIMessage newItem) {
            return oldItem.getContent().equals(newItem.getContent()) && 
                   oldItem.getType() == newItem.getType();
        }
    }
} 