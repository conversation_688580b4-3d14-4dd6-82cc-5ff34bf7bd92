/*
 * Copyright (c) 2017-2023. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.bbpro.ui.settings

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.activityViewModels
import com.realsil.bbpro.databinding.FragmentSettingsBinding
import com.realsil.sample.audioconnect.hearing.eardetection.EarDetectionActivity
import com.realsil.sample.audioconnect.hearing.keymap.KeyMapActivity
import com.realsil.sample.audioconnect.hearing.listeningmode.ListeningModeActivity
import com.realsil.sample.audioconnect.smartwear.SmartGlassActivity
import com.realsil.sdk.audioconnect.support.AudioConnectActivity
import com.realsil.sdk.audioconnect.support.CapabilityCacheInfo
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.support.base.BaseViewBindingFragment


/**
 * <AUTHOR>
 * @date 13/08/2017
 */

class SettingsFragment : BaseViewBindingFragment<FragmentSettingsBinding>(FragmentSettingsBinding::inflate) {
    private val settingsViewModel: SettingsViewModel by activityViewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.itemEarDetection.setOnClickListener {
            val intent = Intent(activity, EarDetectionActivity::class.java)
            intent.putExtra(AudioConnectActivity.EXTRA_KEY_BT_ADDR, settingsViewModel.mDeviceAddress)
            settingsActivityResult.launch(intent)
        }

        binding.itemHearingSettings.setOnClickListener {
            val intent = Intent(activity, ListeningModeActivity::class.java)
            intent.putExtra(AudioConnectActivity.EXTRA_KEY_BT_ADDR, settingsViewModel.mDeviceAddress)
            settingsActivityResult.launch(intent)
        }

        binding.itemKeyMap.setOnClickListener {
            val intent = Intent(activity, KeyMapActivity::class.java)
            intent.putExtra(AudioConnectActivity.EXTRA_KEY_BT_ADDR, settingsViewModel.mDeviceAddress)
            settingsActivityResult.launch(intent)
        }

        binding.itemSmartWear.setOnClickListener {
            val intent = Intent(activity, SmartGlassActivity::class.java)
            intent.putExtra(AudioConnectActivity.EXTRA_KEY_BT_ADDR, settingsViewModel.mDeviceAddress)
            settingsActivityResult.launch(intent)
        }

        settingsViewModel.capabilityCacheInfoLiveData.observe(viewLifecycleOwner) {
            refresh(it)
        }
        refresh(settingsViewModel.capabilityCacheInfo)

    }

    private fun refresh(capabilityCacheInfo: CapabilityCacheInfo) {
        if (capabilityCacheInfo.earDetectionFeatureSupported) {
            binding.itemEarDetection.visibility = View.VISIBLE
        } else {
            binding.itemEarDetection.visibility = View.GONE
        }

        if (capabilityCacheInfo.realHearingEnhancementFeatureSupported) {
            binding.itemHearingSettings.visibility = View.VISIBLE
        } else {
            binding.itemHearingSettings.visibility = View.GONE
        }

        if (capabilityCacheInfo.keyMapFeatureSupported) {
            binding.itemKeyMap.visibility = View.VISIBLE
        } else {
            binding.itemKeyMap.visibility = View.GONE
        }
    }

    private val settingsActivityResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { it ->
            ZLogger.v("it.resultCode=${it.resultCode}")
            if (it.resultCode == Activity.RESULT_CANCELED) {
                if (!settingsViewModel.isConnected()) {
                    ZLogger.d(" connection is lost, close the window")
                    activity?.finish()
                }
            }
        }

    companion object {
        const val TAG = "SettingsFragment"
        private const val D = true

        fun newInstance(args: Bundle?): SettingsFragment {
            val fragment = SettingsFragment()
            if (args != null) {
                fragment.arguments = args
            }
            return fragment
        }

    }


}