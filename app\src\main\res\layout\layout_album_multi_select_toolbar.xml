<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/multi_select_toolbar"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:background="@color/comp_background_gray"
    android:visibility="gone">

    <!-- 下载按钮组 -->
    <LinearLayout
        android:id="@+id/btn_download_multi_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:background="@drawable/btn_ripple_small_corner"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_share_multi_group"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/btn_download_multi"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:contentDescription="下载"
            android:padding="5dp"
            android:src="@drawable/ic_public_download"
            app:tint="@color/icon_primary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="-2dp"
            android:text="下载"
            android:textColor="@color/font_primary"
            android:textSize="12sp" />
    </LinearLayout>

    <!-- 分享按钮组 -->
    <LinearLayout
        android:id="@+id/btn_share_multi_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:background="@drawable/btn_ripple_small_corner"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_delete_multi_group"
        app:layout_constraintStart_toEndOf="@+id/btn_download_multi_group"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/btn_share_multi"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:contentDescription="分享"
            android:padding="5dp"
            android:src="@drawable/ic_public_share"
            app:tint="@color/icon_primary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="-2dp"
            android:text="分享"
            android:textColor="@color/font_primary"
            android:textSize="12sp" />
    </LinearLayout>

    <!-- 删除按钮组 -->
    <LinearLayout
        android:id="@+id/btn_delete_multi_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:background="@drawable/btn_ripple_small_corner"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_more_multi_group"
        app:layout_constraintStart_toEndOf="@+id/btn_share_multi_group"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/btn_delete_multi"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:contentDescription="删除"
            android:padding="5dp"
            android:src="@drawable/ic_public_delete"
            app:tint="@color/icon_primary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="-2dp"
            android:text="删除"
            android:textColor="@color/font_primary"
            android:textSize="12sp" />
    </LinearLayout>

    <!-- 更多按钮组 -->
    <LinearLayout
        android:id="@+id/btn_more_multi_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:background="@drawable/btn_ripple_small_corner"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="3dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btn_delete_multi_group"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/btn_more_multi"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:contentDescription="更多"
            android:padding="5dp"
            android:src="@drawable/ic_public_more"
            app:tint="@color/icon_primary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="-2dp"
            android:text="更多"
            android:textColor="@color/font_primary"
            android:textSize="12sp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 