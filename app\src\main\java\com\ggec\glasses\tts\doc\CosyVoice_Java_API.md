语音合成CosyVoice Java API

调用模式
核心类（SpeechSynthesizer）提供了同步调用、异步调用和流式调用等接口。

请根据业务场景选择合适的调用模式：同步适用于短文本即时合成，异步适合长文本处理，流式适用于实时交互场景。

同步调用
同步提交语音合成任务，直接获取完整结果。

1、配置请求参数。

 
SpeechSynthesisParam param = SpeechSynthesisParam.builder()
    .model("cosyvoice-v1") // 指定模型
    .voice("longxiaochun") // 设置音色
    .build();

2、实例化核心类（SpeechSynthesizer）。
// 同步模式：禁用回调（第二个参数为null），禁用回调后，call 方法将阻塞线程直至返回完整音频数据
SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, null);  

3、同步调用。
ByteBuffer audio = synthesizer.call("今天天气怎么样？");  // 阻塞直至音频返回  
处理音频数据。

4、处理音频数据，例如保存到本地。

完整示例:
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

public class Main {
    private static String model = "cosyvoice-v1";
    private static String voice = "longxiaochun";

    public static void streamAudioDataToSpeaker() {
        SpeechSynthesisParam param =
                SpeechSynthesisParam.builder()
                        // 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将your-api-key替换为自己的API Key
                        // .apiKey("your-api-key")
                        .model(model)
                        .voice(voice)
                        .build();

        // 同步模式：禁用回调（第二个参数为null）
        SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, null);
        // 阻塞直至音频返回 
        ByteBuffer audio = synthesizer.call("今天天气怎么样？");
        // 将音频数据保存到本地文件“output.mp3”中
        File file = new File("output.mp3");
        System.out.println(
                "[Metric] requestId: "
                        + synthesizer.getLastRequestId()
                        + ", first package delay ms: "
                        + synthesizer.getFirstPackageDelay());
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(audio.array());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        streamAudioDataToSpeaker();
        System.exit(0);
    }
}


异步调用
异步提交语音合成任务，通过注册ResultCallback回调，逐帧接收实时语音分段数据。

1、配置请求参数。
SpeechSynthesisParam param = SpeechSynthesisParam.builder()
    .model("cosyvoice-v1") // 指定模型
    .voice("longxiaochun") // 设置音色
    .build();

2、实例化回调接口（ResultCallback）。

3、实例化核心类（SpeechSynthesizer）。
// 第二个参数传入回调即启用异步模式
SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, callback);  

4、异步调用。
语音合成的结果在回调接口中处理。
// 非阻塞调用，立即返回null（实际结果通过回调接口异步传递）
synthesizer.call("今天天气怎么样？"); 

完整示例

以下示例，展示如何使用同步接口调用语音大模型CosyVoice的音色“龙小淳（longxiaochun）”，将文案“今天天气怎么样”合成采样率为22050Hz，音频格式为MP3的音频。

 
import com.alibaba.dashscope.audio.tts.SpeechSynthesisResult;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.alibaba.dashscope.common.ResultCallback;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CountDownLatch;

class TimeUtils {
    private static final DateTimeFormatter formatter =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    public static String getTimestamp() {
        return LocalDateTime.now().format(formatter);
    }
}

public class Main {
    private static String model = "cosyvoice-v1";
    private static String voice = "longxiaochun";

    public static void streamAudioDataToSpeaker() {
        CountDownLatch latch = new CountDownLatch(1);

        // 实现回调接口ResultCallback
        ResultCallback<SpeechSynthesisResult> callback = new ResultCallback<SpeechSynthesisResult>() {
            @Override
            public void onEvent(SpeechSynthesisResult result) {
                // System.out.println("收到消息: " + result);
                if (result.getAudioFrame() != null) {
                    // 此处实现保存音频数据到本地的逻辑
                    System.out.println(TimeUtils.getTimestamp() + " 收到音频");
                }
            }

            @Override
            public void onComplete() {
                System.out.println(TimeUtils.getTimestamp() + " 收到Complete");
                latch.countDown();
            }

            @Override
            public void onError(Exception e) {
                System.out.println("收到错误: " + e.toString());
                latch.countDown();
            }
        };

        SpeechSynthesisParam param =
                SpeechSynthesisParam.builder()
                        // 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将your-api-key替换为自己的API Key
                        // .apiKey("your-api-key")
                        .model(model)
                        .voice(voice)
                        .build();
        // 第二个参数传入回调即启用异步模式
        SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, callback);
        // 非阻塞调用，立即返回null（实际结果通过回调接口异步传递）
        synthesizer.call("今天天气怎么样？");
        // 等待合成完成
        try {
            latch.await();
            // 等待播放线程全部播放完
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        System.out.println(
                "[Metric] requestId: "
                        + synthesizer.getLastRequestId()
                        + ", first package delay ms: "
                        + synthesizer.getFirstPackageDelay());
    }

    public static void main(String[] args) {
        streamAudioDataToSpeaker();
        System.exit(0);
    }
}


流式调用
分多次提交文本，通过注册ResultCallback回调，逐帧接收实时语音分段数据。

说明
- 流式输入时可多次调用streamingCall按顺序提交文本片段。服务端接收文本片段后自动进行分句：
    - 完整语句立即合成
    - 不完整语句缓存至完整后合成
    调用 streamingComplete 时，服务端会强制合成所有已接收但未处理的文本片段（包括未完成的句子）。

- 发送文本片段的间隔不得超过23秒，否则触发“request timeout after 23 seconds”异常。
    若无待发送文本，需及时调用 streamingComplete结束任务。
    服务端强制设定23秒超时机制，客户端无法修改该配置。

1、配置请求参数。
SpeechSynthesisParam param = SpeechSynthesisParam.builder()
    .model("cosyvoice-v1") // 指定模型
    .voice("longxiaochun") // 设置音色
    .build();

2、实例化回调接口（ResultCallback）。

3、实例化核心类（SpeechSynthesizer）。
SpeechSynthesizer synthesizer = new SpeechSynthesizer(
    param,     // 注入语音参数
    callback   // 注册事件监听器
);

4、分片提交文本。
多次调用核心类（SpeechSynthesizer）的streamingCall方法分片提交待合成文本。语音合成的结果在回调接口中处理。
List<String> textFragments = Arrays.asList("第一段文本", "第二段文本", "[结束标记]");  
for (String fragment : textFragments) {  
    synthesizer.streamingCall(fragment); 
}

5、文本提交完成后结束任务。
必须调用 streamingComplete() 结束流式任务，否则服务端可能不会返回缓存的剩余音频数据。
synthesizer.streamingComplete();

完整示例

 
import com.alibaba.dashscope.audio.tts.SpeechSynthesisResult;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisAudioFormat;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.alibaba.dashscope.common.ResultCallback;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CountDownLatch;

class TimeUtils {
    private static final DateTimeFormatter formatter =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    public static String getTimestamp() {
        return LocalDateTime.now().format(formatter);
    }
}


public class Main {
    private static String[] textArray = {"流式文本语音合成SDK，",
            "可以将输入的文本", "合成为语音二进制数据，", "相比于非流式语音合成，",
            "流式合成的优势在于实时性", "更强。用户在输入文本的同时",
            "可以听到接近同步的语音输出，", "极大地提升了交互体验，",
            "减少了用户等待时间。", "适用于调用大规模", "语言模型（LLM），以",
            "流式输入文本的方式", "进行语音合成的场景。"};
    private static String model = "cosyvoice-v1";
    private static String voice = "longxiaochun";

    public static void streamAudioDataToSpeaker() {
        CountDownLatch latch = new CountDownLatch(1);

        // 配置回调函数
        ResultCallback<SpeechSynthesisResult> callback = new ResultCallback<SpeechSynthesisResult>() {
            @Override
            public void onEvent(SpeechSynthesisResult result) {
                // System.out.println("收到消息: " + result);
                if (result.getAudioFrame() != null) {
                    // 此处实现处理音频数据的逻辑
                    System.out.println(TimeUtils.getTimestamp() + " 收到音频");
                }
            }

            @Override
            public void onComplete() {
                System.out.println(TimeUtils.getTimestamp() + " 收到Complete");
                latch.countDown();
            }

            @Override
            public void onError(Exception e) {
                System.out.println("收到错误: " + e.toString());
                latch.countDown();
            }
        };

        SpeechSynthesisParam param =
                SpeechSynthesisParam.builder()
                        // 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将your-api-key替换为自己的API Key
                        // .apiKey("your-api-key")
                        .model(model)
                        .voice(voice)
                        .format(SpeechSynthesisAudioFormat
                                .PCM_22050HZ_MONO_16BIT) // 流式合成使用PCM或者MP3
                        .build();
        SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, callback);
        // 带Callback的call方法将不会阻塞当前线程
        for (String text : textArray) {
            synthesizer.streamingCall(text);
        }
        synthesizer.streamingComplete();
        System.out.println(
                "[Metric] requestId: "
                        + synthesizer.getLastRequestId()
                        + ", first package delay ms: "
                        + synthesizer.getFirstPackageDelay());
        // 等待合成完成
        try {
            latch.await();
            // 等待播放线程全部播放完
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        streamAudioDataToSpeaker();
        System.exit(0);
    }
}


通过Flowable调用
Flowable是一个用于工作流和业务流程管理的开源框架，它基于Apache 2.0许可证发布。关于Flowable的使用，请参见Flowable API详情。

使用Flowable前需确保已集成RxJava库，并了解响应式编程基础概念。

- 非流式调用
以下示例展示了通过Flowable对象的blockingForEach接口，阻塞式地获取每次流式返回的SpeechSynthesisResult类型数据。

您也可以在Flowable的所有流式数据返回完成后，通过核心类（SpeechSynthesizer）的getAudioData来获取完整的合成结果。

完整示例

 
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.alibaba.dashscope.exception.NoApiKeyException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

class TimeUtils {
    private static final DateTimeFormatter formatter =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    public static String getTimestamp() {
        return LocalDateTime.now().format(formatter);
    }
}

public class Main {
    private static String model = "cosyvoice-v1";
    private static String voice = "longxiaochun";

    public static void streamAudioDataToSpeaker() throws NoApiKeyException {
        SpeechSynthesisParam param =
                SpeechSynthesisParam.builder()
                        // 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将your-api-key替换为自己的API Key
                        // .apiKey("your-api-key")
                        .model(model)
                        .voice(voice)
                        .build();
        SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, null);
        synthesizer.callAsFlowable("今天天气怎么样?").blockingForEach(result -> {
            // System.out.println("收到消息: " + result);
            if (result.getAudioFrame() != null) {
                // 此处实现处理音频数据的逻辑
                System.out.println(TimeUtils.getTimestamp() + " 收到音频");
            }
        });
        System.out.println(
                "[Metric] requestId: "
                        + synthesizer.getLastRequestId()
                        + ", first package delay ms: "
                        + synthesizer.getFirstPackageDelay());
    }

    public static void main(String[] args) throws NoApiKeyException {
        streamAudioDataToSpeaker();
        System.exit(0);
    }
}

- 流式调用

以下示例展示了通过Flowable对象作为输入参数，输入文本流。并通过Flowable对象作为返回值，利用的blockingForEach接口，阻塞式地获取每次流式返回的SpeechSynthesisResult类型数据。

您也可以在Flowable的所有流式数据返回完成后，通过核心类（SpeechSynthesizer）的getAudioData来获取完整的合成结果。

完整示例

 
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.alibaba.dashscope.exception.NoApiKeyException;
import io.reactivex.BackpressureStrategy;
import io.reactivex.Flowable;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

class TimeUtils {
    private static final DateTimeFormatter formatter =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    public static String getTimestamp() {
        return LocalDateTime.now().format(formatter);
    }
}

public class Main {
    private static String[] textArray = {"流式文本语音合成SDK，",
            "可以将输入的文本", "合成为语音二进制数据，", "相比于非流式语音合成，",
            "流式合成的优势在于实时性", "更强。用户在输入文本的同时",
            "可以听到接近同步的语音输出，", "极大地提升了交互体验，",
            "减少了用户等待时间。", "适用于调用大规模", "语言模型（LLM），以",
            "流式输入文本的方式", "进行语音合成的场景。"};
    private static String model = "cosyvoice-v1";
    private static String voice = "longxiaochun";

    public static void streamAudioDataToSpeaker() throws NoApiKeyException {
        // 模拟流式输入
        Flowable<String> textSource = Flowable.create(emitter -> {
            new Thread(() -> {
                for (int i = 0; i < textArray.length; i++) {
                    emitter.onNext(textArray[i]);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
                emitter.onComplete();
            }).start();
        }, BackpressureStrategy.BUFFER);

        SpeechSynthesisParam param =
                SpeechSynthesisParam.builder()
                        // 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将apiKey替换为自己的API Key
                        // .apiKey("yourApikey")
                        .model(model)
                        .voice(voice)
                        .build();
        SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, null);
        synthesizer.streamingCallAsFlowable(textSource).blockingForEach(result -> {
            if (result.getAudioFrame() != null) {
                // 此处实现播放音频的逻辑
                System.out.println(
                        TimeUtils.getTimestamp() +
                                " audio result length: " + result.getAudioFrame().capacity());
            }
        });
        System.out.println(
                "[Metric] requestId: "
                        + synthesizer.getLastRequestId()
                        + ", first package delay ms: "
                        + synthesizer.getFirstPackageDelay());
    }

    public static void main(String[] args) throws NoApiKeyException {
        streamAudioDataToSpeaker();
        System.exit(0);
    }
}

请求参数
请求参数通过SpeechSynthesisParam的链式方法进行配置。
示例
SpeechSynthesisParam param = SpeechSynthesisParam.builder()
    .model("cosyvoice-v1")
    .voice("longxiaochun")
    .format(SpeechSynthesisAudioFormat.WAV_8000HZ_MONO_16BIT)
    .volume(50)
    .speechRate(1.0)
    .pitchRate(1.0)
    .build();


|   |   |   |   |   |
|---|---|---|---|---|
|**参数**|**类型**|**默认值**|**是否必须**|**说明**|
|model|String|-|是|指定模型，支持`cosyvoice-v1`、`cosyvoice-v2`。|
|voice|String|-|是|指定音色名称。|
|format|enum|若未指定`format`，系统将根据`voice`参数自动选择该音色的推荐格式（如龙小淳默认使用MP3_22050HZ_MONO_256KBPS）。|否|指定音频编码格式及采样率：<br><br>- SpeechSynthesisAudioFormat.WAV_8000HZ_MONO_16BIT，代表音频格式为wav，采样率为8kHz<br>    <br>- SpeechSynthesisAudioFormat.WAV_16000HZ_MONO_16BIT，代表音频格式为wav，采样率为16kHz<br>    <br>- SpeechSynthesisAudioFormat.WAV_22050HZ_MONO_16BIT，代表音频格式为wav，采样率为22.05kHz<br>    <br>- SpeechSynthesisAudioFormat.WAV_24000HZ_MONO_16BIT，代表音频格式为wav，采样率为24kHz<br>    <br>- SpeechSynthesisAudioFormat.WAV_44100HZ_MONO_16BIT，代表音频格式为wav，采样率为44.1kHz<br>    <br>- SpeechSynthesisAudioFormat.WAV_48000HZ_MONO_16BIT，代表音频格式为wav，采样率为48kHz<br>    <br>- SpeechSynthesisAudioFormat.MP3_8000HZ_MONO_128KBPS，代表音频格式为mp3，采样率为8kHz<br>    <br>- SpeechSynthesisAudioFormat.MP3_16000HZ_MONO_128KBPS，代表音频格式为mp3，采样率为16kHz<br>    <br>- SpeechSynthesisAudioFormat.MP3_22050HZ_MONO_256KBPS，代表音频格式为mp3，采样率为22.05kHz<br>    <br>- SpeechSynthesisAudioFormat.MP3_24000HZ_MONO_256KBPS，代表音频格式为mp3，采样率为24kHz<br>    <br>- SpeechSynthesisAudioFormat.MP3_44100HZ_MONO_256KBPS，代表音频格式为mp3，采样率为44.1kHz<br>    <br>- SpeechSynthesisAudioFormat.MP3_48000HZ_MONO_256KBPS，代表音频格式为mp3，采样率为48kHz<br>    <br>- SpeechSynthesisAudioFormat.PCM_8000HZ_MONO_16BIT，代表音频格式为pcm，采样率为8kHz<br>    <br>- SpeechSynthesisAudioFormat.PCM_16000HZ_MONO_16BIT，代表音频格式为pcm，采样率为16kHz<br>    <br>- SpeechSynthesisAudioFormat.PCM_22050HZ_MONO_16BIT，代表音频格式为pcm，采样率为22.05kHz<br>    <br>- SpeechSynthesisAudioFormat.PCM_24000HZ_MONO_16BIT，代表音频格式为pcm，采样率为24kHz<br>    <br>- SpeechSynthesisAudioFormat.PCM_44100HZ_MONO_16BIT，代表音频格式为pcm，采样率为44.1kHz<br>    <br>- SpeechSynthesisAudioFormat.PCM_48000HZ_MONO_16BIT，代表音频格式为pcm，采样率为48kHz|
|volume|int|50|否|指定音量，取值范围：0~100。|
|speechRate|float|1.0|否|指定语速，取值范围：0.5~2。<br><br>- 0.5：表示默认语速的0.5倍速。<br>    <br>- 1：表示默认语速。默认语速是指模型默认输出的合成语速，语速会因音色不同而略有不同。约每秒钟4个字。<br>    <br>- 2：表示默认语速的2倍速。|
|pitchRate|float|1.0|否|指定语调，取值范围：0.5~2。|
|apiKey|String|-|否|指定用户API Key。如已将API Key配置到环境变量，则无须在代码中设置。|


响应结果
服务器返回二进制音频数据：

同步调用模式：对核心类（SpeechSynthesizer）的call方法返回的二进制音频数据进行处理。

异步调用或流式调用模式：对回调接口（ResultCallback）的onEvent方法的参数（SpeechSynthesisResult类型）进行处理。

SpeechSynthesisResult的关键接口如下：

|                                                     |        |         |                                      |
| --------------------------------------------------- | ------ | ------- | ------------------------------------ |
| **接口/方法**                                           | **参数** | **返回值** | **描述**                               |
| ```java<br>public ByteBuffer getAudioFrame()<br>``` | 无      | 二进制音频数据 | 返回当前流式合成片段的增量二进制音频数据，可能为空（当无新数据到达时）。 |


关键接口
回调接口（ResultCallback）
实现异步监听需继承ResultCallback抽象类，具备以下特征：

1.类型化处理：通过泛型SpeechSynthesisResult处理结构化数据。

2.事件驱动：专注处理有效业务数据，需实现以下方法：
 - onEvent(SpeechSynthesisResult result)：接收实时音频分片（可能为空，参见响应结果）。- onComplete()：任务成功完成时回调。
 - onError(Exception e)：异常处理，建议记录日志并释放资源。

示例

 
ResultCallback<SpeechSynthesisResult> callback = new ResultCallback<SpeechSynthesisResult>() {
    @Override
    public void onEvent(SpeechSynthesisResult result) {
        System.out.println("RequestId为：" + result.getRequestId());
        // 实时处理音频分片（如播放/写入缓冲）
    }

    @Override
    public void onComplete() {
        System.out.println("任务完成");
        // 处理合成结束逻辑（如释放播放器）
    }

    @Override
    public void onError(Exception e) {
        System.out.println("任务失败：" + e.getMessage());
        // 处理异常（网络错误/服务端错误码）
    }
};

| **接口/方法**                                       | **参数**                                                     | **返回值** | **描述**                                                     |
| --------------------------------------------------- | ------------------------------------------------------------ | ---------- | ------------------------------------------------------------ |
| `public void onEvent(SpeechSynthesisResult result)` | `result`：[响应结果](https://help.aliyun.com/zh/model-studio/developer-reference/cosyvoice-java-api#a727da82951p6) | 无         | 当服务端推送语音合成数据时被异步回调。                       |
| `public void onComplete()`                          | 无                                                           | 无         | 当任务完成后被异步回调。                                     |
| `public void onError(Exception e)`                  | `e`：异常信息                                                | 无         | 发生异常时该接口被异步回调。建议在`onError`方法中实现完整的异常日志记录和资源清理逻辑。 |


核心类（SpeechSynthesizer）
通过“import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;”方式引入，关键接口如下：


接口/方法	参数	返回值	描述
 public SpeechSynthesizer(SpeechSynthesisParam param, ResultCallback<SpeechSynthesisResult> callback)	param：语音合成请求参数callback：回调接口（设为null时启用同步模式）	SpeechSynthesizer实例	构造函数。
 public ByteBuffer call(String text)	text：待合成文本（UTF-8）	ByteBuffer或null	非流式发送待合成文本并获取语音合成结果。创建SpeechSynthesizer实例时，没有指定ResultCallback，则call方法会阻塞当前线程直到语音合成完成创建SpeechSynthesizer实例时，指定ResultCallback，则call方法会立刻返回null，并在回调函数中传递语音合成的结果。
 public void streamingCall(String text)	text：待合成文本，须为utf-8格式	无	流式发送待合成文本。您可以多次调用该接口，将待合成文本分多次发送给服务端。必须在所有streamingCall调用完成后执行streamingComplete、asyncStreamingComplete或者streamingCancel结束任务。
 public void streamingComplete() throws RuntimeException	无	无	结束流式任务。该方法阻塞调用线程直至发生以下条件之一：服务端完成最终音频合成（成功）流式会话异常中断（失败）达到10分钟超时阈值（自动解除阻塞）
 public void streamingComplete(long completeTimeoutMillis) throws RuntimeException	completeTimeoutMillis：等待时间，单位为毫秒	无	结束流式任务。该方法阻塞调用线程直至发生以下条件之一：服务端完成最终音频合成（成功）流式会话异常中断（失败）达到completeTimeoutMillis参数设置的超时时间（自动解除阻塞）completeTimeoutMillis为0时，表示无限等待。
 public void asyncStreamingComplete()	无	无	非阻塞结束流式任务。该方法不会阻塞当前线程，调用该方法后，在回调函数onEvent中会返回STREAM_INPUT_TTS_EVENT_SYNTHESIS_COMPLETE事件。
 public void streamingCancel()	无	无	立即结束流式任务。
 public Flowable<SpeechSynthesisResult> callAsFlowable(String text)	text：待合成文本，须为utf-8格式	合成结果，封装在Flowable<SpeechSynthesisResult>中	非流式文本输入实时转换为流式语音输出，合成结果在flowable中流式返回。
 public Flowable<SpeechSynthesisResult> streamingCallAsFlowable(Flowable<String> textStream)	textStream：封装了待合成文本的Flowable实例	合成结果，封装在Flowable<SpeechSynthesisResult>中	流式文本输入实时转换为流式语音输出，合成结果在flowable中流式返回。
 public void updateParamAndCallback(SpeechSynthesisParam param, ResultCallback<SpeechSynthesisResult> callback)	param：语音合成相关参数callback：回调接口，可以设为null	无	更新SpeechSynthesizer实例对应的SpeechSynthesisParam和ResultCallback配置。
 public String getLastRequestId()	无	当前上一个任务的request id	获取上一个任务的request id，在调用call、streamingCall、callAsFlowable、streamingCallAsFlowable开始新任务之后可以使用。
 public long getFirstPackageDelay()	无	当前任务首包延迟
