<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="180dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="10dp"
    android:background="@drawable/bg_popup_menu"
    android:elevation="4dp">

    <!-- 下载图片选项 -->
    <TextView
        android:id="@+id/btn_download_image"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:text="下载图片"
        android:textColor="@color/font_primary"
        android:textSize="17sp"
        android:textStyle="normal"
        android:background="@drawable/bg_menu_item_selector" />

    <!-- 下载视频选项 -->
    <TextView
        android:id="@+id/btn_download_video"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:text="下载视频"
        android:textColor="@color/font_primary"
        android:textSize="17sp"
        android:textStyle="normal"
        android:background="@drawable/bg_menu_item_selector" />

    <!-- 多选照片选项已删除 -->

    <!-- 加载测试数据选项 -->
    <TextView
        android:id="@+id/btn_load_test_data"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:text="加载测试数据"
        android:textColor="@color/font_primary"
        android:textSize="17sp"
        android:textStyle="normal"
        android:background="@drawable/bg_menu_item_selector" />

    <!-- 清空相册选项 -->
    <TextView
        android:id="@+id/btn_clear_album"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:text="@string/album_clear_album"
        android:textColor="@color/warning"
        android:textSize="17sp"
        android:textStyle="normal"
        android:background="@drawable/bg_menu_item_selector" />

</LinearLayout> 