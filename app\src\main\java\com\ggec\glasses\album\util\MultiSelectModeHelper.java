package com.ggec.glasses.album.util;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.ggec.glasses.fragments.AlbumFragment;

/**
 * 多选模式辅助类
 * 用于在相册模块内的不同Fragment之间共享多选模式状态
 */
public class MultiSelectModeHelper {
    
    private static final MutableLiveData<Boolean> multiSelectModeLiveData = new MutableLiveData<>(false);
    
    /**
     * 判断当前是否处于多选模式
     * @param fragment 当前Fragment
     * @return 是否处于多选模式
     */
    public static boolean isInMultiSelectMode(Fragment fragment) {
        if (fragment == null || fragment.getActivity() == null) {
            return false;
        }
        
        // 获取父级Fragment
        Fragment parentFragment = fragment.getParentFragment();
        if (parentFragment instanceof AlbumFragment) {
            // 如果父Fragment是AlbumFragment，直接调用其判断方法
            return ((AlbumFragment) parentFragment).isInMultiSelectMode();
        } else {
            // 遍历Activity中的Fragment，查找AlbumFragment
            FragmentActivity activity = fragment.getActivity();
            for (Fragment topFragment : activity.getSupportFragmentManager().getFragments()) {
                if (topFragment instanceof AlbumFragment) {
                    return ((AlbumFragment) topFragment).isInMultiSelectMode();
                }
            }
        }
        
        return false;
    }
    
    /**
     * 获取多选模式状态的LiveData
     * @param fragment 当前Fragment
     * @return 多选模式状态的LiveData
     */
    public static LiveData<Boolean> observeMultiSelectMode(Fragment fragment) {
        if (fragment == null || fragment.getActivity() == null) {
            return multiSelectModeLiveData;
        }
        
        // 获取父级Fragment
        Fragment parentFragment = fragment.getParentFragment();
        if (parentFragment instanceof AlbumFragment) {
            // 如果父Fragment是AlbumFragment，直接返回其LiveData
            return ((AlbumFragment) parentFragment).getMultiSelectModeLiveData();
        } else {
            // 遍历Activity中的Fragment，查找AlbumFragment
            FragmentActivity activity = fragment.getActivity();
            for (Fragment topFragment : activity.getSupportFragmentManager().getFragments()) {
                if (topFragment instanceof AlbumFragment) {
                    return ((AlbumFragment) topFragment).getMultiSelectModeLiveData();
                }
            }
        }
        
        return multiSelectModeLiveData;
    }
    
    /**
     * 设置多选模式状态
     * @param enabled 是否启用多选模式
     */
    public static void setMultiSelectMode(boolean enabled) {
        multiSelectModeLiveData.setValue(enabled);
    }
} 