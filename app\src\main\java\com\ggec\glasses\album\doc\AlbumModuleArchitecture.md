# 相册模块架构文档

## 1. 概述

相册模块是Glasses应用中负责管理、展示和操作媒体文件（包括图片和视频）的功能模块。该模块采用MVVM（Model-View-ViewModel）架构模式，结合Repository模式和Room数据库实现了媒体文件的完整生命周期管理。

## 2. 架构设计

### 2.1 整体架构

相册模块采用了现代Android应用开发推荐的MVVM架构，主要分为以下几层：

1. **UI层（View）**：包括Activity、Fragment和XML布局文件
2. **ViewModel层**：作为UI层和数据层之间的桥梁，管理UI相关的数据状态
3. **数据层（Model）**：包括Repository、数据实体类和数据访问对象（DAO）
4. **工具类**：提供各种辅助功能的实用工具类

### 2.2 目录结构

```
album/
├── activity/               # 活动类
├── adapter/                # 适配器类
├── data/                   # 数据相关类
│   ├── dao/                # 数据访问对象
│   ├── db/                 # 数据库
│   ├── entity/             # 数据实体类
│   ├── manager/            # 数据管理类
│   └── repository/         # 数据仓库类
├── docs/                   # 文档
├── fragments/              # 片段类
├── manager/                # 管理器类
├── util/                   # 工具类
└── viewmodel/              # 视图模型类
```

## 3. 核心组件

### 3.1 UI层

#### 3.1.1 Activity

- **MediaDisplayActivity**：
  - 功能：作为媒体展示的容器，负责管理界面和状态
  - 路径：`album/MediaDisplayActivity.java`
  - 职责：
    - 初始化并托管MediaDisplayFragment
    - 处理媒体对象的传递
    - 维护ViewModel的生命周期
    - 实现沉浸式状态栏和转场动画

#### 3.1.2 Fragment

- **MediaListFragment**：
  - 功能：展示媒体列表的Fragment
  - 路径：`album/fragments/MediaListFragment.java`
  - 职责：
    - 以网格形式展示媒体列表
    - 处理单选/多选模式切换
    - 导航到媒体详情页面

- **MediaDisplayFragment**：
  - 功能：展示媒体详情的Fragment
  - 路径：`album/fragments/MediaDisplayFragment.java`
  - 职责：
    - 托管MediaImageFragment或MediaVideoFragment
    - 处理媒体操作（导出、分享、删除等）
    - 管理UI操作层的显示隐藏

- **MediaImageFragment**：
  - 功能：图片展示的子Fragment
  - 路径：`album/fragments/MediaImageFragment.java`
  - 职责：
    - 使用PhotoView加载和展示图片
    - 支持图片缩放和平移操作

- **MediaVideoFragment**：
  - 功能：视频播放的子Fragment
  - 路径：`album/fragments/MediaVideoFragment.java`
  - 职责：
    - 使用ExoPlayer加载和播放视频
    - 支持视频控制（播放/暂停）
    - 处理视频加载状态
    - 响应操作层显示/隐藏事件，同步更新背景颜色

#### 3.1.3 Adapter

- **MediaAdapter**：
  - 功能：媒体列表的RecyclerView适配器
  - 路径：`album/adapter/MediaAdapter.java`
  - 职责：
    - 绑定媒体数据到视图
    - 处理项目点击事件
    - 管理多选模式下的项目选中状态

- **MediaPagerAdapter**：
  - 功能：媒体查看的ViewPager2适配器
  - 路径：`album/adapter/MediaPagerAdapter.java`
  - 职责：
    - 管理媒体详情的横向滑动

### 3.2 ViewModel层

- **MediaViewModel**：
  - 功能：媒体列表的视图模型
  - 路径：`album/viewmodel/MediaViewModel.java`
  - 职责：
    - 暴露媒体数据LiveData给UI
    - 处理媒体查询操作
    - 管理筛选条件

- **MediaDisplayViewModel**：
  - 功能：媒体详情的视图模型
  - 路径：`album/viewmodel/MediaDisplayViewModel.java`
  - 职责：
    - 管理当前展示的媒体数据
    - 处理媒体操作（删除、导出）
    - 管理操作层的可见性状态
    - 处理导航事件

### 3.3 数据层

#### 3.3.1 实体类

- **Media**：
  - 功能：媒体文件的数据实体类
  - 路径：`album/data/entity/Media.java`
  - 职责：
    - 定义媒体文件的属性（ID、路径、类型、尺寸等）
    - 实现Parcelable接口，支持Intent传递

- **Album**：
  - 功能：相册的数据实体类
  - 路径：`album/data/entity/Album.java`
  - 职责：
    - 定义相册属性（ID、名称、封面等）

- **AlbumMedia**、**MediaTag**、**Tag**：
  - 功能：相册与媒体、媒体与标签的关联实体类
  - 路径：`album/data/entity/`目录下
  - 职责：
    - 实现多对多关系的映射

#### 3.3.2 仓库类

- **MediaRepository**：
  - 功能：媒体数据仓库
  - 路径：`album/data/repository/MediaRepository.java`
  - 职责：
    - 协调媒体数据的数据库操作和文件系统操作
    - 提供统一的数据访问接口
    - 处理媒体文件的保存、删除、恢复等操作

- **AlbumRepository**：
  - 功能：相册数据仓库
  - 路径：`album/data/repository/AlbumRepository.java`
  - 职责：
    - 管理相册数据的CRUD操作
    - 处理相册与媒体的关联关系

- **TagRepository**：
  - 功能：标签数据仓库
  - 路径：`album/data/repository/TagRepository.java`
  - 职责：
    - 管理标签数据的CRUD操作
    - 处理媒体与标签的关联关系

### 3.4 管理器类

- **AlbumMediaExportManager**：
  - 功能：媒体导出管理器
  - 路径：`album/manager/AlbumMediaExportManager.java`
  - 职责：
    - 将内部应用媒体文件导出到系统相册
    - 兼容不同Android版本的媒体存储机制

- **MediaSelectionManager**：
  - 功能：媒体选择管理器
  - 路径：`album/manager/MediaSelectionManager.java`
  - 职责：
    - 管理多选模式下的选中状态
    - 提供选中项的统计和操作接口

- **MultiSelectModeManager**：
  - 功能：多选模式管理器
  - 路径：`album/manager/MultiSelectModeManager.java`
  - 职责：
    - 控制应用的多选模式状态
    - 管理多选模式下的工具栏显示

- **BatchMediaOperationManager**：
  - 功能：批量媒体操作管理器
  - 路径：`album/manager/BatchMediaOperationManager.java`
  - 职责：
    - 执行媒体批量操作（删除、导出等）
    - 处理批量操作的进度和结果

### 3.5 工具类

- **UIAnimationHelper**：
  - 功能：UI动画辅助类
  - 路径：`album/util/UIAnimationHelper.java`
  - 职责：
    - 封装媒体展示界面中的各种动画效果
    - 处理操作层显示/隐藏的动画
    - 管理背景颜色切换动画

- **VideoPlayerUtil**：
  - 功能：视频播放器工具类
  - 路径：`album/util/VideoPlayerUtil.java`
  - 职责：
    - 封装ExoPlayer的初始化和操作
    - 管理视频播放的生命周期

- **MultiSelectModeHelper**：
  - 功能：多选模式辅助类
  - 路径：`album/util/MultiSelectModeHelper.java`
  - 职责：
    - 提供多选模式的帮助方法
    - 处理Fragment中的多选模式状态管理

- **VideoFrameExtractor**：
  - 功能：视频帧提取工具
  - 路径：`album/util/VideoFrameExtractor.java`
  - 职责：
    - 从视频中提取缩略图帧

## 4. 交互流程

### 4.1 媒体浏览流程

1. `MediaListFragment` 从 `MediaViewModel` 获取媒体数据
2. 用户点击媒体项时，`MediaListFragment` 启动 `MediaDisplayActivity`
3. `MediaDisplayActivity` 初始化 `MediaDisplayViewModel` 并设置媒体数据
4. `MediaDisplayActivity` 加载 `MediaDisplayFragment`
5. `MediaDisplayFragment` 根据媒体类型加载 `MediaImageFragment` 或 `MediaVideoFragment`
6. 用户可以执行单个媒体操作（如下载、分享、删除）

### 4.2 多选操作流程

1. 用户长按媒体项触发多选模式
2. `MultiSelectModeHelper` 更新多选模式状态
3. `MediaAdapter` 根据多选模式更新UI
4. 用户选择多个媒体项，由 `MediaSelectionManager` 记录选择状态
5. 用户执行批量操作，由 `BatchMediaOperationManager` 处理

## 5. 优化设计

1. **职责分离**：
   - UI层仅负责界面展示和用户交互
   - ViewModel层处理UI状态和业务逻辑
   - Repository层处理数据获取和存储
   - Manager类负责特定功能的管理

2. **MVVM架构**：
   - 使用LiveData实现数据观察者模式
   - 使用ViewModel管理UI状态，确保配置更改时数据不丢失
   - UI组件观察ViewModel中的数据，实现单向数据流

3. **组件化设计**：
   - 各Fragment功能清晰明确
   - 使用子Fragment实现图片/视频展示的不同需求
   - 通过ViewModel实现组件间通信

4. **代码复用**：
   - 通过Helper类和Util类复用通用功能
   - 通过Manager类统一管理特定功能领域

## 6. 总结

相册模块采用了现代Android应用开发推荐的MVVM架构模式，结合Repository模式实现了清晰的职责分离。通过ViewModel连接UI和数据层，实现了高内聚低耦合的设计。模块化的设计使得代码易于维护和扩展，各组件之间的职责边界清晰。 