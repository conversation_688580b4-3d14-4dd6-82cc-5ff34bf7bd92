package com.ggec.glasses.album.manager;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;

import com.ggec.glasses.MainActivity;
import com.ggec.glasses.R;
import com.ggec.glasses.album.fragments.MediaListFragment;
import com.ggec.glasses.utils.DialogUtils;

import java.util.Set;

/**
 * 多选模式管理器
 * 负责管理相册多选模式的UI和交互
 */
public class MultiSelectModeManager {

    private static final String KEY_MULTI_SELECT_MODE = "key_multi_select_mode";
    private static final int ANIMATION_DURATION = 300; // 动画持续时间
    
    private Context context;
    private MultiSelectModeCallback callback;
    
    // UI组件
    private View headerLayout;                // 原始顶部导航栏
    private View multiSelectHeaderLayout;     // 多选模式顶部导航栏
    private View multiSelectToolbar;          // 多选模式底部工具栏
    private TextView tvSelectedCount;         // 已选择计数
    private ImageView btnSelectAllTop;        // 全选按钮
    private View btnSelectAllContainer;       // 全选按钮容器
    
    // 状态
    private boolean isMultiSelectMode = false;
    
    // 媒体选择管理器
    private final MediaSelectionManager selectionManager;
    
    /**
     * 多选模式回调接口
     */
    public interface MultiSelectModeCallback {
        /**
         * 当多选模式状态改变时回调
         * @param isInMultiSelectMode 是否处于多选模式
         */
        void onMultiSelectModeChanged(boolean isInMultiSelectMode);
    }
    
    /**
     * 构造函数
     * @param context 上下文
     * @param callback 回调接口
     * @param headerLayout 原始顶部导航栏
     * @param multiSelectHeaderLayout 多选模式顶部导航栏
     * @param multiSelectToolbar 多选模式底部工具栏
     */
    public MultiSelectModeManager(Context context, MultiSelectModeCallback callback,
                                 View headerLayout, View multiSelectHeaderLayout, View multiSelectToolbar) {
        this.context = context;
        this.callback = callback;
        this.headerLayout = headerLayout;
        this.multiSelectHeaderLayout = multiSelectHeaderLayout;
        this.multiSelectToolbar = multiSelectToolbar;
        
        // 获取已选择项计数文本视图
        this.tvSelectedCount = multiSelectHeaderLayout.findViewById(R.id.tv_selected_count);
        
        // 获取媒体选择管理器
        this.selectionManager = MediaSelectionManager.getInstance();
        
        // 初始化多选模式UI
        initMultiSelectUI();
        
        // 观察全选状态变化
        observeAllSelectedStates();
    }
    
    /**
     * 观察全选状态变化
     */
    private void observeAllSelectedStates() {
        // 观察总全选状态
        selectionManager.getTotalAllSelectedLiveData().observeForever(isAllSelected -> {
            if (btnSelectAllTop != null) {
                updateSelectAllButtonState(isAllSelected);
            }
        });
        
        // 观察选中数量
        selectionManager.getSelectedCountLiveData().observeForever(count -> {
            if (tvSelectedCount != null) {
                updateSelectedCount(count);
            }
        });
    }
    
    /**
     * 初始化多选模式UI
     */
    private void initMultiSelectUI() {
        // 初始化多选模式顶部导航栏
        initMultiSelectHeader();
        
        // 初始化多选模式底部工具栏
        initMultiSelectToolbar();
    }
    
    /**
     * 初始化多选模式顶部导航栏
     */
    private void initMultiSelectHeader() {
        // 获取全选按钮
        btnSelectAllTop = multiSelectHeaderLayout.findViewById(R.id.btn_select_all_top);
        btnSelectAllContainer = multiSelectHeaderLayout.findViewById(R.id.btn_select_all_container);
        
        // 设置全选按钮点击事件
        btnSelectAllContainer.setOnClickListener(v -> {
            boolean isAllSelected = selectionManager.toggleCurrentTabSelectAll();
            updateSelectAllButtonState(isAllSelected);
        });
        
        // 设置取消按钮点击事件
        View btnCancel = multiSelectHeaderLayout.findViewById(R.id.btn_cancel_select);
        btnCancel.setOnClickListener(v -> exitMultiSelectMode());
    }
    
    /**
     * 更新全选按钮状态
     * @param isAllSelected 是否全选
     */
    private void updateSelectAllButtonState(boolean isAllSelected) {
        if (btnSelectAllTop != null) {
            btnSelectAllTop.setSelected(isAllSelected);
        }
    }
    
    /**
     * 初始化多选模式底部工具栏
     */
    private void initMultiSelectToolbar() {
        // 设置下载按钮点击事件
        View btnDownloadMultiGroup = multiSelectToolbar.findViewById(R.id.btn_download_multi_group);
        btnDownloadMultiGroup.setOnClickListener(v -> {
            // 获取已选中的媒体ID集合
            Set<Long> selectedMediaIds = selectionManager.getSelectedMediaIds();
            if (selectedMediaIds.isEmpty()) {
                Toast.makeText(context, "请先选择要下载的媒体文件", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 显示下载中的提示
            Toast.makeText(context, "正在下载所选媒体文件...", Toast.LENGTH_SHORT).show();
            
            // 禁用按钮，防止重复操作
            btnDownloadMultiGroup.setEnabled(false);
            
            // 使用BatchMediaOperationManager批量下载媒体文件
            BatchMediaOperationManager.batchSaveMediaToGallery(context, selectedMediaIds, new BatchMediaOperationManager.BatchOperationCallback() {
                @Override
                public void onProgressUpdate(int current, int total) {
                    // 可以在UI上更新进度
                }
                
                @Override
                public void onComplete(int successCount, int failedCount) {
                    // 在主线程中显示结果
                    if (context instanceof android.app.Activity) {
                        ((android.app.Activity) context).runOnUiThread(() -> {
                            // 重新启用按钮
                            btnDownloadMultiGroup.setEnabled(true);
                            
                            if (failedCount == 0) {
                                Toast.makeText(context, "已成功下载 " + successCount + " 个媒体文件", Toast.LENGTH_SHORT).show();
                            } else {
                                Toast.makeText(context, 
                                    "下载完成：成功 " + successCount + " 个，失败 " + failedCount + " 个", 
                                    Toast.LENGTH_SHORT).show();
                            }
                            
                            // 下载完成后退出多选模式
                            exitMultiSelectMode();
                        });
                    }
                }
                
                @Override
                public void onError(String errorMessage) {
                    // 在主线程中显示错误信息
                    if (context instanceof android.app.Activity) {
                        ((android.app.Activity) context).runOnUiThread(() -> {
                            // 重新启用按钮
                            btnDownloadMultiGroup.setEnabled(true);
                            
                            Toast.makeText(context, "下载失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                        });
                    }
                }
            });
        });
        
        // 设置分享按钮点击事件
        View btnShareMultiGroup = multiSelectToolbar.findViewById(R.id.btn_share_multi_group);
        btnShareMultiGroup.setOnClickListener(v -> {
            Toast.makeText(context, "分享功能待开发", Toast.LENGTH_SHORT).show();
        });
        
        // 设置删除按钮点击事件
        View btnDeleteMultiGroup = multiSelectToolbar.findViewById(R.id.btn_delete_multi_group);
        btnDeleteMultiGroup.setOnClickListener(v -> {
            // 获取已选中的媒体ID集合
            Set<Long> selectedMediaIds = selectionManager.getSelectedMediaIds();
            if (selectedMediaIds.isEmpty()) {
                Toast.makeText(context, "请先选择要删除的媒体文件", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 显示确认对话框
            DialogUtils.showConfirmDialog(
                    context,
                    "确认删除",
                    "确定要删除所选的 " + selectedMediaIds.size() + " 个媒体文件吗？此操作将永久删除文件且无法恢复。",
                    "删除",
                    "取消",
                    (dialog, which) -> {
                        // 显示删除中的提示
                        Toast.makeText(context, "正在删除所选媒体文件...", Toast.LENGTH_SHORT).show();
                        
                        // 禁用按钮，防止重复操作
                        btnDeleteMultiGroup.setEnabled(false);
                        
                        // 使用BatchMediaOperationManager批量删除媒体文件
                        BatchMediaOperationManager.batchDeleteMedia(context, selectedMediaIds, new BatchMediaOperationManager.BatchOperationCallback() {
                            @Override
                            public void onProgressUpdate(int current, int total) {
                                // 可以在UI上更新进度
                            }
                            
                            @Override
                            public void onComplete(int successCount, int failedCount) {
                                // 在主线程中显示结果
                                if (context instanceof android.app.Activity) {
                                    ((android.app.Activity) context).runOnUiThread(() -> {
                                        // 重新启用按钮
                                        btnDeleteMultiGroup.setEnabled(true);
                                        
                                        if (failedCount == 0) {
                                            Toast.makeText(context, "已成功删除 " + successCount + " 个媒体文件", Toast.LENGTH_SHORT).show();
                                        } else {
                                            Toast.makeText(context, 
                                                "删除完成：成功 " + successCount + " 个，失败 " + failedCount + " 个", 
                                                Toast.LENGTH_SHORT).show();
                                        }
                                        
                                        // 删除完成后退出多选模式
                                        exitMultiSelectMode();
                                    });
                                }
                            }
                            
                            @Override
                            public void onError(String errorMessage) {
                                // 在主线程中显示错误信息
                                if (context instanceof android.app.Activity) {
                                    ((android.app.Activity) context).runOnUiThread(() -> {
                                        // 重新启用按钮
                                        btnDeleteMultiGroup.setEnabled(true);
                                        
                                        Toast.makeText(context, "删除失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                                    });
                                }
                            }
                        });
                    },
                    null
            );
        });
        
        // 设置更多按钮点击事件
        View btnMoreMultiGroup = multiSelectToolbar.findViewById(R.id.btn_more_multi_group);
        btnMoreMultiGroup.setOnClickListener(v -> {
            Toast.makeText(context, "更多功能待开发", Toast.LENGTH_SHORT).show();
        });
    }
    
    /**
     * 进入多选模式
     */
    public void enterMultiSelectMode() {
        if (isMultiSelectMode) {
            return;
        }
        
        isMultiSelectMode = true;
        
        // 回调通知状态变化
        if (callback != null) {
            callback.onMultiSelectModeChanged(true);
        }
        
        // 隐藏原始顶部导航栏（立即隐藏，不要动画）
        headerLayout.setVisibility(View.INVISIBLE);
        
        // 显示多选模式顶部导航栏（添加下滑动画）
        multiSelectHeaderLayout.setVisibility(View.VISIBLE);
        multiSelectHeaderLayout.setAlpha(0f);
        multiSelectHeaderLayout.setTranslationY(-multiSelectHeaderLayout.getHeight());
        multiSelectHeaderLayout.animate()
                .alpha(1f)
                .translationY(0)
                .setDuration(ANIMATION_DURATION)
                .setInterpolator(new DecelerateInterpolator())
                .start();
        
        // 显示多选模式底部工具栏（添加上滑动画）
        multiSelectToolbar.setVisibility(View.VISIBLE);
        multiSelectToolbar.setAlpha(0f);
        multiSelectToolbar.setTranslationY(multiSelectToolbar.getHeight());
        multiSelectToolbar.animate()
                .alpha(1f)
                .translationY(0)
                .setDuration(ANIMATION_DURATION)
                .setInterpolator(new DecelerateInterpolator())
                .start();
    }
    
    /**
     * 退出多选模式
     */
    public void exitMultiSelectMode() {
        if (!isMultiSelectMode) {
            return;
        }
        
        isMultiSelectMode = false;
        
        // 显示原始顶部导航栏（添加下滑动画）
        headerLayout.setVisibility(View.VISIBLE);
        headerLayout.setAlpha(0f);
        headerLayout.setTranslationY(-headerLayout.getHeight());
        headerLayout.animate()
                .alpha(1f)
                .translationY(0)
                .setDuration(ANIMATION_DURATION)
                .setInterpolator(new DecelerateInterpolator())
                .start();
        
        // 隐藏多选模式顶部导航栏（添加上滑动画）
        multiSelectHeaderLayout.animate()
                .alpha(0f)
                .translationY(-multiSelectHeaderLayout.getHeight())
                .setDuration(ANIMATION_DURATION)
                .setInterpolator(new AccelerateInterpolator())
                .withEndAction(() -> multiSelectHeaderLayout.setVisibility(View.GONE))
                .start();
        
        // 隐藏多选模式底部工具栏（添加下滑动画）
        multiSelectToolbar.animate()
                .alpha(0f)
                .translationY(multiSelectToolbar.getHeight())
                .setDuration(ANIMATION_DURATION)
                .setInterpolator(new AccelerateInterpolator())
                .withEndAction(() -> multiSelectToolbar.setVisibility(View.GONE))
                .start();
        
        // 清空选中状态
        selectionManager.clearSelection();
        
        // 回调通知状态变化
        if (callback != null) {
            callback.onMultiSelectModeChanged(false);
        }
    }
    
    /**
     * 更新选中数量
     * @param count 数量
     */
    public void updateSelectedCount(int count) {
        if (tvSelectedCount != null) {
            // 更新计数文本
            tvSelectedCount.setText(String.format("已选择 %d 项", count));
        }
    }
    
    /**
     * 恢复UI状态
     */
    public void restoreUIState() {
        if (isMultiSelectMode) {
            // 确保多选工具栏可见
            multiSelectToolbar.setVisibility(View.VISIBLE);
            multiSelectToolbar.setAlpha(1f);
            multiSelectToolbar.setTranslationY(0);
            
            // 确保多选模式顶部导航栏可见
            multiSelectHeaderLayout.setVisibility(View.VISIBLE);
            multiSelectHeaderLayout.setAlpha(1f);
            multiSelectHeaderLayout.setTranslationY(0);
            
            // 隐藏原始顶部导航栏
            headerLayout.setVisibility(View.INVISIBLE);
            
            // 回调通知状态变化
            if (callback != null) {
                callback.onMultiSelectModeChanged(true);
            }
        }
    }
    
    /**
     * 保存状态
     * @param outState Bundle对象
     */
    public void saveInstanceState(@NonNull Bundle outState) {
        outState.putBoolean(KEY_MULTI_SELECT_MODE, isMultiSelectMode);
    }
    
    /**
     * 恢复状态
     * @param savedInstanceState Bundle对象
     */
    public void restoreInstanceState(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            isMultiSelectMode = savedInstanceState.getBoolean(KEY_MULTI_SELECT_MODE, false);
        }
    }
    
    /**
     * 判断当前是否处于多选模式
     * @return 是否处于多选模式
     */
    public boolean isInMultiSelectMode() {
        return isMultiSelectMode;
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        // 移除监听
        selectionManager.getTotalAllSelectedLiveData().removeObserver(isAllSelected -> {
            if (btnSelectAllTop != null) {
                updateSelectAllButtonState(isAllSelected);
            }
        });
        
        selectionManager.getSelectedCountLiveData().removeObserver(count -> {
            if (tvSelectedCount != null) {
                updateSelectedCount(count);
            }
        });
    }
    
    /**
     * 更新当前标签页的全选按钮状态
     * @param tabType 标签页类型
     */
    public void updateSelectAllButtonForCurrentTab(String tabType) {
        selectionManager.setCurrentTabType(tabType);
        boolean isAllSelected = Boolean.TRUE.equals(selectionManager.getAllSelectedLiveData(tabType).getValue());
        updateSelectAllButtonState(isAllSelected);
    }
} 