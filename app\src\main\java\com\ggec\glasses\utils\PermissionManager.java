package com.ggec.glasses.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import java.util.ArrayList;
import java.util.List;

/**
 * 权限管理类
 * 用于统一处理应用需要的各种权限
 */
public class PermissionManager {
    
    private static final int PERMISSION_REQUEST_CODE = 100;
    private static final int ESSENTIAL_PERMISSION_REQUEST_CODE = 101;
    private static final int RECORD_AUDIO_PERMISSION_REQUEST_CODE = 102;
    
    // 必要权限 - 用户必须授予这些权限应用才能正常工作
    public static final String[] ESSENTIAL_PERMISSIONS = {
        // 存储权限
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE,
        
        // 蓝牙权限（根据Android版本区分）
        Manifest.permission.BLUETOOTH,
        Manifest.permission.BLUETOOTH_ADMIN,
        
        // 位置权限（蓝牙扫描需要）
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    };
    
    // 非必要权限 - 用户可以拒绝这些权限，应用仍然可以运行
    public static final String[] NON_ESSENTIAL_PERMISSIONS = {
        // 录音权限
        Manifest.permission.RECORD_AUDIO
    };
    
    // Android 12+需要的额外必要蓝牙权限
    public static final String[] ANDROID_S_ESSENTIAL_PERMISSIONS = {
        Manifest.permission.BLUETOOTH_CONNECT,
        Manifest.permission.BLUETOOTH_ADVERTISE,
        Manifest.permission.BLUETOOTH_SCAN
    };
    
    // 权限结果回调接口
    public interface PermissionCallback {
        void onPermissionGranted();
        void onPermissionDenied(List<String> deniedPermissions);
        void onPermissionPermanentlyDenied(List<String> permanentlyDeniedPermissions);
    }
    
    private Activity activity;
    private PermissionCallback callback;
    
    public PermissionManager(Activity activity) {
        this.activity = activity;
    }
    
    /**
     * 检查是否已有所有必要权限
     */
    public boolean hasAllEssentialPermissions() {
        return getMissingEssentialPermissions().isEmpty();
    }
    
    /**
     * 检查是否有录音权限
     */
    public boolean hasRecordAudioPermission() {
        return ContextCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO) 
               == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 请求录音权限
     */
    public void requestRecordAudioPermission(PermissionCallback callback) {
        this.callback = callback;
        
        // 如果已经有录音权限，直接回调
        if (hasRecordAudioPermission()) {
            if (callback != null) {
                callback.onPermissionGranted();
            }
            return;
        }
        
        // 检查录音权限是否被永久拒绝
        boolean isPermanentlyDenied = !ActivityCompat.shouldShowRequestPermissionRationale(
                activity, Manifest.permission.RECORD_AUDIO);
        
        if (isPermanentlyDenied) {
            // 如果永久拒绝，告知用户需要去设置中开启
            List<String> permanentlyDeniedPermissions = new ArrayList<>();
            permanentlyDeniedPermissions.add(Manifest.permission.RECORD_AUDIO);
            
            if (callback != null) {
                callback.onPermissionPermanentlyDenied(permanentlyDeniedPermissions);
            }
            return;
        }
        
        // 请求录音权限
        ActivityCompat.requestPermissions(
                activity,
                new String[]{Manifest.permission.RECORD_AUDIO},
                RECORD_AUDIO_PERMISSION_REQUEST_CODE
        );
    }
    
    /**
     * 请求所有权限（包括必要和非必要）
     */
    public void requestAllPermissions(PermissionCallback callback) {
        this.callback = callback;
        List<String> missingPermissions = new ArrayList<>();
        
        // 添加所有缺少的必要权限
        missingPermissions.addAll(getMissingEssentialPermissions());
        
        // 添加所有缺少的非必要权限
        for (String permission : NON_ESSENTIAL_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                missingPermissions.add(permission);
            }
        }
        
        if (missingPermissions.isEmpty()) {
            // 已有所有权限
            if (callback != null) {
                callback.onPermissionGranted();
            }
            return;
        }
        
        // 请求缺少的所有权限
        ActivityCompat.requestPermissions(
            activity, 
            missingPermissions.toArray(new String[0]),
            PERMISSION_REQUEST_CODE
        );
    }
    
    /**
     * 获取缺少的必要权限列表
     */
    public List<String> getMissingEssentialPermissions() {
        List<String> missingPermissions = new ArrayList<>();
        
        // 检查所有必要权限
        for (String permission : ESSENTIAL_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                missingPermissions.add(permission);
            }
        }
        
        // 如果是Android 12+，还需检查额外的蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            for (String permission : ANDROID_S_ESSENTIAL_PERMISSIONS) {
                if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                    missingPermissions.add(permission);
                }
            }
        }
        
        return missingPermissions;
    }
    
    /**
     * 请求必要权限（重试时调用）
     */
    public void requestEssentialPermissions(PermissionCallback callback) {
        this.callback = callback;
        List<String> missingEssentialPermissions = getMissingEssentialPermissions();
        
        // 检查是否有永久被拒绝的权限
        List<String> permanentlyDeniedPermissions = getPermanentlyDeniedEssentialPermissions();
        
        if (missingEssentialPermissions.isEmpty()) {
            // 已有所有必要权限
            if (callback != null) {
                callback.onPermissionGranted();
            }
            return;
        }
        
        // 如果有永久被拒绝的权限，则需要引导用户去设置页面开启
        if (!permanentlyDeniedPermissions.isEmpty()) {
            if (callback != null) {
                callback.onPermissionPermanentlyDenied(permanentlyDeniedPermissions);
            }
            return;
        }
        
        // 请求缺少的必要权限
        ActivityCompat.requestPermissions(
            activity, 
            missingEssentialPermissions.toArray(new String[0]),
            ESSENTIAL_PERMISSION_REQUEST_CODE
        );
    }
    
    /**
     * 获取永久被拒绝的必要权限列表（用户选择了"不再询问"）
     */
    public List<String> getPermanentlyDeniedEssentialPermissions() {
        List<String> permanentlyDeniedPermissions = new ArrayList<>();
        
        // 检查所有缺少的必要权限
        for (String permission : getMissingEssentialPermissions()) {
            // 用户在之前已拒绝过权限且选择了"不再询问"
            if (!ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)) {
                permanentlyDeniedPermissions.add(permission);
            }
        }
        
        return permanentlyDeniedPermissions;
    }
    
    /**
     * 打开应用设置页面
     */
    public void openAppSettings() {
        Intent intent = new Intent();
        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
        intent.setData(uri);
        activity.startActivity(intent);
    }
    
    /**
     * 在Activity的onRequestPermissionsResult中调用
     */
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if ((requestCode != PERMISSION_REQUEST_CODE && 
             requestCode != ESSENTIAL_PERMISSION_REQUEST_CODE && 
             requestCode != RECORD_AUDIO_PERMISSION_REQUEST_CODE) || callback == null) {
            return;
        }
        
        // 对于完整权限请求，只检查必要权限的授予情况
        if (requestCode == PERMISSION_REQUEST_CODE) {
            List<String> deniedEssentialPermissions = getMissingEssentialPermissions();
            
            if (deniedEssentialPermissions.isEmpty()) {
                callback.onPermissionGranted();
            } else {
                // 检查是否有永久被拒绝的权限
                List<String> permanentlyDeniedPermissions = getPermanentlyDeniedEssentialPermissions();
                if (!permanentlyDeniedPermissions.isEmpty()) {
                    callback.onPermissionPermanentlyDenied(permanentlyDeniedPermissions);
                } else {
                    callback.onPermissionDenied(deniedEssentialPermissions);
                }
            }
        } 
        // 对于必要权限请求，直接检查结果
        else if (requestCode == ESSENTIAL_PERMISSION_REQUEST_CODE) {
            List<String> deniedPermissions = new ArrayList<>();
            List<String> permanentlyDeniedPermissions = new ArrayList<>();
            
            // 检查结果
            for (int i = 0; i < permissions.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    deniedPermissions.add(permissions[i]);
                    
                    // 检查是否永久拒绝
                    if (!ActivityCompat.shouldShowRequestPermissionRationale(activity, permissions[i])) {
                        permanentlyDeniedPermissions.add(permissions[i]);
                    }
                }
            }
            
            // 回调结果
            if (deniedPermissions.isEmpty()) {
                callback.onPermissionGranted();
            } else if (!permanentlyDeniedPermissions.isEmpty()) {
                callback.onPermissionPermanentlyDenied(permanentlyDeniedPermissions);
            } else {
                callback.onPermissionDenied(deniedPermissions);
            }
        }
        // 对于录音权限请求，直接检查结果
        else if (requestCode == RECORD_AUDIO_PERMISSION_REQUEST_CODE) {
            if (permissions.length > 0 && 
                Manifest.permission.RECORD_AUDIO.equals(permissions[0])) {
                
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    // 录音权限已授予
                    callback.onPermissionGranted();
                } else {
                    // 录音权限被拒绝
                    List<String> deniedPermissions = new ArrayList<>();
                    deniedPermissions.add(Manifest.permission.RECORD_AUDIO);
                    
                    // 检查是否永久拒绝
                    if (!ActivityCompat.shouldShowRequestPermissionRationale(
                            activity, Manifest.permission.RECORD_AUDIO)) {
                        List<String> permanentlyDeniedPermissions = new ArrayList<>();
                        permanentlyDeniedPermissions.add(Manifest.permission.RECORD_AUDIO);
                        callback.onPermissionPermanentlyDenied(permanentlyDeniedPermissions);
                    } else {
                        callback.onPermissionDenied(deniedPermissions);
                    }
                }
            }
        }
    }
} 