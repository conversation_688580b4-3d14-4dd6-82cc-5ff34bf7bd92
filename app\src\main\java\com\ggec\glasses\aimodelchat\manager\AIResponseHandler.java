package com.ggec.glasses.aimodelchat.manager;

import android.util.Log;
import com.ggec.glasses.ai.data.db.entity.ChatMessage;
import com.ggec.glasses.ai.data.db.repository.RepositoryCallback;
import com.ggec.glasses.ai.manager.ChatMessageManager;

/**
 * AI 响应处理器
 * 负责接收 AI 模型的响应并将其存储到数据库
 */
public class AIResponseHandler {

    private static final String TAG = "AIResponseHandler";
    private final ChatMessageManager chatMessageManager;

    public AIResponseHandler(ChatMessageManager chatMessageManager) {
        this.chatMessageManager = chatMessageManager;
    }

    /**
     * 处理从 AI 模型获取的响应文本
     * @param aiText AI 返回的文本
     * @param conversationId 当前会话 ID
     */
    public void handleAIResponse(String aiText, long conversationId) {
        if (aiText == null || aiText.trim().isEmpty()) {
            Log.w(TAG, "收到空的 AI 响应，已忽略。");
            return;
        }

        Log.d(TAG, "正在处理会话 " + conversationId + " 的 AI 响应: " + aiText);

        // 创建 AI 消息实体
        ChatMessage aiMessage = chatMessageManager.createAIMessageEntity(aiText);
        
        // 确保使用正确的会话ID
        // 注意：虽然ChatMessageManager已设置默认值，但为安全起见，再次确认
        if (aiMessage.getConversationId() != conversationId) {
            Log.d(TAG, "会话ID不一致，使用传入的会话ID: " + conversationId + 
                  " 而不是默认ID: " + aiMessage.getConversationId());
            aiMessage.setConversationId(conversationId);
        }

        // 将 AI 消息插入数据库 (这也会更新缓存)
        chatMessageManager.insertMessage(aiMessage, new RepositoryCallback<Long>() {
            @Override
            public void onSuccess(Long messageId) {
                Log.d(TAG, "AI 消息成功插入，ID: " + messageId + 
                      ", 会话ID: " + aiMessage.getConversationId());
                // 数据库更新后，LiveData 会自动通知 ChatViewModel -> (最终) UI 更新
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "插入 AI 消息失败: " + error);
                // TODO: 考虑如何处理插入错误 (例如，通知用户?)
            }
        });
    }
} 