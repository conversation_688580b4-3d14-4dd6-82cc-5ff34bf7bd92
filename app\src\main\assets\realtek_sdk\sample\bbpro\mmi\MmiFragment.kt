/*
 * Copyright (c) 2017-2023. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.bbpro.mmi

import android.bluetooth.BluetoothDevice
import android.os.Bundle
import android.view.View
import com.realsil.bbpro.R
import com.realsil.bbpro.databinding.FragmentMmiBinding
import com.realsil.sdk.audioconnect.support.AudioConnectFragment
import com.realsil.sdk.audioconnect.support.AudioConnectHelper
import com.realsil.sdk.bbpro.MultiPeripheralConnectionManager
import com.realsil.sdk.bbpro.PeripheralConnectionManager
import com.realsil.sdk.bbpro.core.BeeError
import com.realsil.sdk.bbpro.core.transportlayer.AckPacket
import com.realsil.sdk.bbpro.params.Mmi
import com.realsil.sdk.bbpro.profile.MmiReq
import com.realsil.sdk.bbpro.vendor.VendorConstants
import com.realsil.sdk.bbpro.vendor.VendorModelCallback
import com.realsil.sdk.core.logger.ZLogger

/**
 * MMI
 * <AUTHOR>
 * @date 2020/01/07
 */
class MmiFragment : AudioConnectFragment<FragmentMmiBinding>(FragmentMmiBinding::inflate) {

    private var mBeeProManager: PeripheralConnectionManager? = null

    private val beeProManager: PeripheralConnectionManager
        get() {
            if (mBeeProManager == null) {
                mBeeProManager = MultiPeripheralConnectionManager.getInstance(context).getPeripheralConnectionManager(mDeviceAddress)
//                mBeeProManager!!.addManagerCallback(mBeeProManagerCallback)
            }
            return mBeeProManager as PeripheralConnectionManager
        }


    override fun initDataOnCreateView() {
        super.initDataOnCreateView()
        beeProManager
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.mToolbar.setTitle(R.string.title_mmi)
        binding.mToolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material)
        binding.mToolbar.setNavigationOnClickListener { activity?.onBackPressed() }
        binding.mToolbar.inflateMenu(R.menu.menu_test_api)
        binding.mToolbar.setOnMenuItemClickListener { item ->
            if (item.itemId == R.id.action_sync) {
                true
            } else if (item.itemId == R.id.action_clear_logcat) {
                binding.logView.clear()
                true
            } else
                false
        }
        binding.mToolbar.setOnCreateContextMenuListener { menu, v, menuInfo ->
            val syncMenuItem = menu.findItem(R.id.action_sync_view)
            syncMenuItem?.let {
                if (mBeeProManager!!.state != PeripheralConnectionManager.STATE_DATA_SYNC_PROCESSING) {
                    syncMenuItem.actionView = null
                } else {
                    syncMenuItem.setActionView(
                        R.layout.actionbar_indeterminate_progress
                    )
                }
            }
        }

        binding.itemAvPlay.setOnClickListener {
            sendMmi(Mmi.AU_MMI_AV_PLAY_PAUSE)
        }
        binding.itemAvStop.setOnClickListener {
            sendMmi(Mmi.AU_MMI_AV_STOP)
        }
        binding.itemAvForward.setOnClickListener {
            sendMmi(Mmi.AU_MMAU_MMI_AV_FWD)
        }
        binding.itemAvBackwared.setOnClickListener {
            sendMmi(Mmi.AU_MMI_AV_BWD)
        }

        mBeeProManager!!.registerVendorModelCallback(mVendorModelCallback)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (mBeeProManager != null) {
            mBeeProManager!!.unregisterVendorModelCallback(mVendorModelCallback)
        }
    }

    private fun sendMmi(mmi:Byte) {
        showProgressBar(R.string.toast_processing)
        binding.logView.d("sendMmi ...")
        val ret = beeProManager.vendorClient.sendAppReq(MmiReq.Builder(mmi).build())
        if (ret.code != BeeError.SUCCESS) {
            binding.logView.w("sendMmi failed")
            cancelProgressBar()
            showShortToast(ret.message)
        }
    }

    private val mVendorModelCallback = object : VendorModelCallback() {

        override fun onStateChanged(state: Int) {
            super.onStateChanged(state)
            if (state == PeripheralConnectionManager.STATE_DEVICE_DISCONNECTED) {
                activity?.runOnUiThread {
                    cancelProgressBar()
                }
            }
        }

        override fun onOperationComplete(operation: Int, status: Byte) {
            super.onOperationComplete(operation, status)
            when (operation) {
                VendorConstants.Operation.SET_MMI,
                VendorConstants.Operation.GET_STATUS,
                VendorConstants.Operation.GET_CFG_SETTINGS,
                VendorConstants.Operation.SET_APT_VOLUME -> {
                    activity?.runOnUiThread {
                        cancelProgressBar()
                        if (status != AckPacket.ACK_STATUS_COMPLETE) {
                            binding.logView.w(AudioConnectHelper.parseAckStatus(operation, status))
                        } else {
                            binding.logView.d(AudioConnectHelper.parseAckStatus(operation, status))
                        }
                    }
                }
            }
        }
    }

    companion object {
        const val TAG = "MmiFragment"
        private const val D = true

        fun newInstance(args: Bundle?): MmiFragment {

            val fragment = MmiFragment()
            if (args != null) {
                fragment.arguments = args
            }
            return fragment
        }
    }


}
