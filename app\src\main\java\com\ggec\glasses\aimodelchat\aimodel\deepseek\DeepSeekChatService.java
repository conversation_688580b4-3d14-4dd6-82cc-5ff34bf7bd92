package com.ggec.glasses.aimodelchat.aimodel.deepseek;

import android.content.Context;
import android.util.Log;
import androidx.annotation.NonNull;

// 移动文件后更新的导入路径
import com.ggec.glasses.aimodelchat.aimodel.config.DeepSeekConfig;
import com.ggec.glasses.aimodelchat.aimodel.deepseek.DeepSeekAPIService;
import com.ggec.glasses.aimodelchat.aimodel.deepseek.DeepSeekRequest;
import com.ggec.glasses.aimodelchat.aimodel.deepseek.DeepSeekResponse;
import com.ggec.glasses.aimodelchat.network.RetrofitClient; // RetrofitClient 保持在通用的 network 包中

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * DeepSeek 聊天服务类
 * 封装与 DeepSeek API 的交互逻辑
 */
public class DeepSeekChatService {

    private static final String TAG = "DeepSeekChatService";

    private final DeepSeekAPIService apiService;
    private final boolean isServiceAvailable;

    /**
     * 回调接口，用于处理异步的 API 响应结果
     */
    public interface CompletionCallback {
        void onSuccess(String response); // 成功时回调，返回 AI 响应文本
        void onError(String error);     // 失败时回调，返回错误信息
    }

    /**
     * 构造函数，使用应用上下文初始化服务
     * @param context 应用上下文，用于获取API密钥
     */
    public DeepSeekChatService(Context context) {
        String apiKey = DeepSeekConfig.getApiKey(context);
        
        // 检查 API Key 是否可用
        if (apiKey != null && !apiKey.isEmpty()) {
            // 使用 RetrofitClient 创建 DeepSeekAPIService 实例
            this.apiService = RetrofitClient.createService(
                DeepSeekAPIService.class, 
                DeepSeekConfig.BASE_URL, 
                apiKey
            );
            this.isServiceAvailable = true;
        } else {
            this.apiService = null;
            this.isServiceAvailable = false;
            Log.e(TAG, "DeepSeek 服务初始化失败：未找到有效的 API Key");
        }
    }

    /**
     * 获取聊天补全结果 (异步)
     * @param userMessage 用户输入的消息
     * @param callback 回调函数，用于接收结果或错误
     */
    public void getChatCompletion(String userMessage, @NonNull CompletionCallback callback) {
        // 首先检查服务是否可用
        if (!isServiceAvailable) {
            callback.onError("DeepSeek 服务不可用：未初始化或 API Key 无效。");
            return;
        }
        
        if (userMessage == null || userMessage.trim().isEmpty()) {
            callback.onError("用户消息不能为空。");
            return;
        }

        // 构建请求体 - 加入 System Prompt
        List<DeepSeekRequest.Message> messages = new ArrayList<>();
        messages.add(new DeepSeekRequest.Message("system", DeepSeekConfig.SYSTEM_PROMPT));
        messages.add(new DeepSeekRequest.Message("user", userMessage));    // 添加用户消息

        // 修改构造函数调用，使用配置中的 temperature 参数
        DeepSeekRequest request = new DeepSeekRequest(
                DeepSeekConfig.MODEL_NAME, 
                messages, 
                DeepSeekConfig.DEFAULT_TEMPERATURE // 使用配置值
        );

        Log.d(TAG, "发送请求到 DeepSeek API (包含System Prompt 和 Temperature): " + request.toString());

        // 异步调用 API
        // 为 apiKey header 传递空字符串，因为它由拦截器处理
        apiService.createChatCompletion("", request).enqueue(new Callback<DeepSeekResponse>() {
            @Override
            public void onResponse(@NonNull Call<DeepSeekResponse> call, @NonNull Response<DeepSeekResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    DeepSeekResponse deepSeekResponse = response.body();
                    // 提取第一个 choice 的 message content
                    if (deepSeekResponse.getChoices() != null && !deepSeekResponse.getChoices().isEmpty()) {
                        DeepSeekResponse.Message aiMessage = deepSeekResponse.getChoices().get(0).getMessage();
                        if (aiMessage != null && aiMessage.getContent() != null) {
                            Log.d(TAG, "成功收到来自 DeepSeek API 的响应: " + aiMessage.getContent());
                            callback.onSuccess(aiMessage.getContent());
                        } else {
                            Log.e(TAG, "DeepSeek API 响应格式错误: AI 消息内容为空。");
                            callback.onError("AI 响应格式错误。");
                        }
                    } else {
                        Log.e(TAG, "DeepSeek API 响应格式错误: Choices 列表为空或不存在。");
                        callback.onError("AI 响应格式错误。");
                    }
                } else {
                    String errorBody = "未知错误";
                    try {
                        if (response.errorBody() != null) {
                            errorBody = response.errorBody().string(); // 读取错误体内容
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "读取错误体时出错", e);
                    }
                    Log.e(TAG, "DeepSeek API 请求失败: " + response.code() + " - " + errorBody);
                    callback.onError("API 请求失败: " + response.code() + " - " + errorBody);
                }
            }

            @Override
            public void onFailure(@NonNull Call<DeepSeekResponse> call, @NonNull Throwable t) {
                Log.e(TAG, "DeepSeek API 请求失败 (网络层面): " + t.getMessage(), t);
                callback.onError("网络错误: " + t.getMessage());
            }
        });
    }
    
    /**
     * 检查服务是否可用
     * @return 如果服务初始化成功且可用则返回 true
     */
    public boolean isAvailable() {
        return isServiceAvailable;
    }
} 