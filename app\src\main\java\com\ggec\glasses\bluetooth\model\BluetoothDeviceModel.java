package com.ggec.glasses.bluetooth.model;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;

/**
 * 蓝牙设备数据模型
 * 用于展示蓝牙设备信息
 */
public class BluetoothDeviceModel {
    
    private String name;
    private String address;
    @DrawableRes
    private int iconResId;
    private boolean isPaired;
    private BluetoothDeviceType deviceType;

    /**
     * 构造方法
     * @param name 设备名称
     * @param address 设备MAC地址
     * @param iconResId 设备图标资源ID
     * @param isPaired 是否已配对
     */
    public BluetoothDeviceModel(String name, String address, int iconResId, boolean isPaired) {
        this.name = name;
        this.address = address;
        this.iconResId = iconResId;
        this.isPaired = isPaired;
        this.deviceType = BluetoothDeviceType.UNKNOWN;
    }
    
    /**
     * 构造方法（带设备类型）
     * @param name 设备名称
     * @param address 设备MAC地址
     * @param deviceType 设备类型
     * @param isPaired 是否已配对
     */
    public BluetoothDeviceModel(String name, String address, BluetoothDeviceType deviceType, boolean isPaired) {
        this.name = name;
        this.address = address;
        this.deviceType = deviceType;
        this.iconResId = deviceType.getIconResId();
        this.isPaired = isPaired;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getIconResId() {
        return iconResId;
    }

    public void setIconResId(int iconResId) {
        this.iconResId = iconResId;
    }

    public boolean isPaired() {
        return isPaired;
    }

    public void setPaired(boolean paired) {
        isPaired = paired;
    }
    
    /**
     * 获取设备类型
     * @return 设备类型
     */
    @NonNull
    public BluetoothDeviceType getDeviceType() {
        return deviceType;
    }
    
    /**
     * 设置设备类型
     * @param deviceType 设备类型
     */
    public void setDeviceType(@NonNull BluetoothDeviceType deviceType) {
        this.deviceType = deviceType;
        this.iconResId = deviceType.getIconResId();
    }
    
    /**
     * 根据设备名称自动设置设备类型
     */
    public void autoDetectDeviceType() {
        if (name != null && !name.isEmpty()) {
            this.deviceType = BluetoothDeviceType.getTypeByName(name);
            this.iconResId = deviceType.getIconResId();
        }
    }
} 