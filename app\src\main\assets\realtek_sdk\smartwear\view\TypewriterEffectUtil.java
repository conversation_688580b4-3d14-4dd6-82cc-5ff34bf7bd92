/*
 * Copyright (c) 2025 Realsil.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Authors: <AUTHORS>
 */

package com.realsil.sample.audioconnect.smartwear.view;

import android.os.Handler;
import android.os.Looper;

import com.realsil.sample.audioconnect.smartwear.adapter.MsgListAdapter;
import com.realsil.sample.audioconnect.smartwear.entity.MessageInfo;


/**
 * TextView with typewriter-like effect.
 * <p>
 *
 * <AUTHOR>
 */
public class TypewriterEffectUtil {

    private static final String TAG = "Typewriter";

    /**
     * Default Typing cursor.
     */
    public static final String DEFAULT_PRINT_INDICATOR = "●";

    /**
     * Default typing interval.
     */
    private static final int DEFAULT_PRINT_INTERVAL = 20;

    private final Handler mHandler;

    public TypewriterEffectUtil() {
        mHandler = new Handler(Looper.getMainLooper());
    }

    public void startTyping(MessageInfo msg, MsgListAdapter adapter) {
        if (msg.isTyping()) return;
        msg.setTyping(true);

        Runnable typingRunnable = new Runnable() {
            @Override
            public void run() {
                if (msg.getPendingText().length() > 0) {
                    char nextChar = msg.getPendingText().charAt(0);
                    msg.getPendingText().deleteCharAt(0);
                    msg.setTypedText(msg.getTypedText() + nextChar);
                    adapter.notifyItemChanged(msg.getPositionInDisplayList());
                    mHandler.postDelayed(this, DEFAULT_PRINT_INTERVAL);
                } else {
                    adapter.notifyItemChanged(msg.getPositionInDisplayList());
                    msg.setTyping(false);
                }
            }
        };

        mHandler.postDelayed(typingRunnable, DEFAULT_PRINT_INTERVAL);
    }

    public void stopTyping() {
        mHandler.removeCallbacksAndMessages(null);
    }

}