# 相册媒体数据库输入输出解决方案

## 目录

1. [架构概述](#架构概述)
2. [数据流向](#数据流向)
3. [数据输入处理](#数据输入处理)
4. [数据输出处理](#数据输出处理)
5. [媒体文件处理](#媒体文件处理)
6. [缓存策略](#缓存策略)
7. [性能优化](#性能优化)
8. [实现细节](#实现细节)
9. [特殊情况处理](#特殊情况处理)

## 架构概述

本项目采用 MVVM（Model-View-ViewModel）架构模式与 Repository 模式相结合的数据库设计，实现了高内聚、低耦合的媒体数据管理解决方案。

### 架构图

```
┌─────────────┐      ┌───────────────┐      ┌───────────────┐      ┌─────────────┐
│    视图层    │      │   ViewModel   │      │   Repository  │      │   数据源    │
│   (Views)   │ <──> │    (MVVM)     │ <──> │   (Pattern)   │ <──> │ (DataSource)│
└─────────────┘      └───────────────┘      └───────────────┘      └─────────────┘
     Fragment             MediaViewModel         MediaRepository       Room + Files
```

### 技术栈选择

- **Room**: 用于数据持久化，提供了一个抽象层，使得直接 SQLite 操作更加简单
- **LiveData**: 提供数据观察机制，实现UI与数据的自动同步
- **ViewModel**: 管理 UI 相关的数据，并处理 UI 生命周期
- **Glide**: 高效图片加载和缓存库
- **MediaMetadataRetriever**: 用于提取视频首帧作为缩略图

## 数据流向

### 输入数据流

```
外部来源 → MediaRepository → Room数据库 → 文件存储
 (URI)      (处理、转换)      (元数据存储)   (媒体文件)
```

### 输出数据流

```
Room数据库 → MediaRepository → ViewModel → UI组件
(查询结果)   (数据处理)       (转换、过滤)  (显示)
```

## 数据输入处理

### 媒体导入流程

1. **接收数据源**：
   - 用户通过相机拍摄
   - 从系统相册选择
   - 从外部应用共享

2. **数据预处理**：
   - 格式验证
   - 文件大小检查
   - 元数据提取（宽高、时长等）

3. **存储和缓存**：
   - 将文件保存到应用内部存储
   - 为图片和视频生成缩略图
   - 将元数据存入数据库

```java
// MediaRepository.java 中的媒体保存方法示例
public void saveMedia(Uri sourceUri, String mediaType, int width, int height, 
                      long duration, String mimeType, OnMediaSavedCallback callback) {
    executor.execute(() -> {
        try {
            // 保存媒体文件
            File savedFile = fileManager.saveMediaFile(sourceUri, mediaType);
            
            // 创建数据库实体
            Media media = new Media();
            media.setFileName(savedFile.getName());
            media.setFilePath(savedFile.getAbsolutePath());
            media.setType(mediaType);
            // ... 设置其他属性
            
            // 生成缩略图
            File thumbnail = fileManager.generateThumbnail(savedFile, mediaType);
            if (thumbnail != null) {
                media.setThumbnailPath(thumbnail.getAbsolutePath());
            }
            
            // 保存到数据库
            long mediaId = database.mediaDao().insertMedia(media);
            
            // 回调通知
            if (callback != null) {
                media.setId(mediaId);
                callback.onSuccess(media);
            }
        } catch (Exception e) {
            // 错误处理
            callback.onError("Error saving media: " + e.getMessage());
        }
    });
}
```

## 数据输出处理

### 媒体读取流程

1. **数据查询**：
   - 基于类型（图片/视频/全部）
   - 基于相册/标签分组
   - 支持排序（时间/名称等）

2. **数据转换**：
   - 查询结果包装为 LiveData
   - 数据转换为UI所需格式

3. **UI呈现**：
   - 通过观察者模式自动更新UI
   - 支持分页加载和懒加载

```java
// MediaViewModel.java 中的数据获取方法示例
public LiveData<List<Media>> getAllMedia() {
    return allMedia; // 从 Repository 获取的 LiveData
}

public LiveData<List<Media>> getPhotoMedia() {
    return photoMedia; // 只包含图片的 LiveData
}

public LiveData<List<Media>> getVideoMedia() {
    return videoMedia; // 只包含视频的 LiveData
}
```

```java
// Fragment 中的数据订阅示例
@Override
public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
    super.onViewCreated(view, savedInstanceState);
    
    mediaViewModel = new ViewModelProvider(requireActivity()).get(MediaViewModel.class);
    
    // 观察媒体数据变化
    mediaViewModel.getAllMedia().observe(getViewLifecycleOwner(), this::updateMediaList);
}
```

## 媒体文件处理

### 文件存储结构

```
/data/data/com.example.glasses/files/
    ├── media/                 # 媒体文件主目录
        ├── images/            # 图片存储目录
        │   ├── thumbnails/    # 图片缩略图目录
        │   └── originals/     # 原始图片目录
        ├── videos/            # 视频存储目录
            ├── thumbnails/    # 视频缩略图目录
            └── originals/     # 原始视频目录
```

### 缩略图生成

1. **图片缩略图**：
   - 使用 BitmapFactory 进行尺寸压缩
   - 使用高质量缩放算法保持图片质量

2. **视频缩略图**：
   - 使用 MediaMetadataRetriever 提取视频首帧
   - 保存为JPEG格式的静态图像

```java
// VideoFrameExtractor.java 中的视频帧提取方法
public static Bitmap extractVideoFrameFromVideo(Context context, String videoPath) {
    MediaMetadataRetriever retriever = new MediaMetadataRetriever();
    Bitmap bitmap = null;
    
    try {
        retriever.setDataSource(videoPath);
        // 获取视频的首帧
        bitmap = retriever.getFrameAtTime(0, MediaMetadataRetriever.OPTION_CLOSEST_SYNC);
    } catch (Exception e) {
        Log.e(TAG, "Failed to extract video frame: " + e.getMessage());
    } finally {
        retriever.release();
    }
    
    return bitmap;
}
```

## 缓存策略

### 内存缓存

- 使用 Glide 自动管理图片内存缓存
- 避免直接在内存中缓存大量媒体数据
- 采用 LruCache 策略保持内存使用高效

### 磁盘缓存

- 图片和视频原文件保存在内部存储中
- 缩略图单独保存，快速访问
- 使用 Glide 的磁盘缓存优化加载性能

```java
// 在适配器中使用 Glide 加载图片示例
private void loadImageWithGlide(ImageView imageView, String path) {
    glide.load(new File(path))
            .apply(new RequestOptions()
                    .centerCrop()
                    .diskCacheStrategy(DiskCacheStrategy.ALL))
            .into(imageView);
}
```

## 性能优化

### 异步处理

- 所有数据库操作在后台线程执行
- 使用线程池管理并发任务
- 文件 I/O 操作离开主线程

### 懒加载和分页

- 使用 RecyclerView 高效显示大量数据
- 支持视图回收和重用
- 配置 GridLayoutManager 实现网格布局

### 数据加载优化

- 使用 DiffUtil 高效更新 RecyclerView
- 仅加载可见区域的媒体缩略图
- 预加载即将可见的内容

## 实现细节

### 数据库实体

```java
// 媒体实体类
@Entity(tableName = "media")
public class Media {
    @PrimaryKey(autoGenerate = true)
    private long id;
    
    @ColumnInfo(name = "file_name")
    private String fileName;
    
    @ColumnInfo(name = "file_path")
    private String filePath;
    
    @ColumnInfo(name = "thumbnail_path")
    private String thumbnailPath;
    
    @ColumnInfo(name = "type")
    private String type; // IMAGE 或 VIDEO
    
    // ... 其他属性和方法
}
```

### DAO接口

```java
// 媒体数据访问接口
@Dao
public interface MediaDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertMedia(Media media);
    
    @Query("SELECT * FROM media WHERE type = :type AND is_deleted = 0 ORDER BY creation_date DESC")
    LiveData<List<Media>> getMediaByTypeLive(String type);
    
    // ... 其他查询方法
}
```

### UI适配器

```java
// 媒体适配器
public class MediaAdapter extends ListAdapter<Media, MediaAdapter.MediaViewHolder> {
    
    // 使用 DiffUtil 优化列表更新
    private static final DiffUtil.ItemCallback<Media> DIFF_CALLBACK =
            new DiffUtil.ItemCallback<Media>() {
                @Override
                public boolean areItemsTheSame(@NonNull Media oldItem, @NonNull Media newItem) {
                    return oldItem.getId() == newItem.getId();
                }
                
                @Override
                public boolean areContentsTheSame(@NonNull Media oldItem, @NonNull Media newItem) {
                    return oldItem.getFilePath().equals(newItem.getFilePath());
                }
            };
    
    // ... 其他方法
}
```

### 依赖注入

- 使用单例模式管理 Repository
- ViewModel 负责协调数据和UI操作
- 清晰的组件间责任划分

## 特殊情况处理

### 重复文件处理
当尝试导入相同媒体文件时，系统采用以下策略处理：

1. **URI检测**：首先通过源URI检查媒体是否已存在于数据库
   ```java
   // 通过原始URI查找媒体
   private Media findExistingMediaByPath(String uriPath) {
       List<Media> allMedia = database.mediaDao().getAllMedia();
       for (Media media : allMedia) {
           // 检查自定义元数据中是否存储了原始URI
           JSONObject json = new JSONObject(media.getCustomMetadata());
           if (json.has("sourceUri") && json.getString("sourceUri").equals(uriPath)) {
               return media;
           }
       }
       return null;
   }
   ```

2. **文件路径检测**：如果文件已保存，检查数据库中是否存在相同路径的记录
   ```java
   // 根据文件路径查找媒体
   private Media findMediaByFilePath(String filePath) {
       return database.mediaDao().getMediaByFilePath(filePath);
   }
   ```

3. **已删除媒体恢复**：如果媒体被标记为已删除，则恢复而不是重新创建
   ```java
   if (mediaByPath != null && mediaByPath.isDeleted()) {
       // 恢复被标记为已删除的媒体
       mediaByPath.setDeleted(false);
       mediaByPath.setModificationDate(new Date());
       database.mediaDao().updateMedia(mediaByPath);
   }
   ```

### 文件命名冲突
媒体文件使用唯一命名策略，避免命名冲突：

1. **唯一文件名生成**：结合时间戳、UUID和媒体类型生成唯一文件名
   ```java
   private String generateUniqueFileName(String mediaType, Uri sourceUri) {
       // 提取原始文件扩展名
       // 生成包含时间戳和UUID的唯一文件名
       String prefix = TYPE_IMAGE.equals(mediaType) ? "img" : "vid";
       String timestamp = String.valueOf(System.currentTimeMillis());
       String uuid = UUID.randomUUID().toString().substring(0, 8);
       
       return String.format("%s_%s_%s.%s", prefix, timestamp, uuid, extension);
   }
   ```

2. **文件存在检测**：保存前检查文件是否已存在，避免覆盖
   ```java
   if (outputFile.exists()) {
       // 文件已存在，检查缩略图是否存在
       String thumbnailName = getThumbnailName(fileName);
       File thumbnailFile = new File(thumbnailDir, thumbnailName);
       
       if (thumbnailFile.exists()) {
           // 文件和缩略图都已存在，直接返回已存在的文件
           return outputFile;
       }
   }
   ```

### 元数据存储
使用JSON格式存储额外元数据，方便扩展：

1. **元数据创建**：初始保存媒体时创建元数据
   ```java
   private void setInitialCustomMetadata(Media media, String sourceUri) {
       JSONObject metadata = new JSONObject();
       metadata.put("sourceUri", sourceUri);
       metadata.put("importDate", new Date().getTime());
       media.setCustomMetadata(metadata.toString());
   }
   ```

2. **元数据更新**：媒体恢复或更新时更新元数据
   ```java
   private void updateCustomMetadata(Media media, String sourceUri) {
       JSONObject metadata = new JSONObject(media.getCustomMetadata());
       metadata.put("sourceUri", sourceUri);
       metadata.put("lastModified", new Date().getTime());
       media.setCustomMetadata(metadata.toString());
   }
   ```

### 无效参数处理
对可能的无效输入进行健壮处理：

1. **参数检查**：验证URI和媒体类型等关键参数
   ```java
   if (sourceUri == null) {
       callback.onError("Source URI cannot be null");
       return;
   }
   
   if (mediaType == null || (!mediaType.equals(TYPE_IMAGE) && !mediaType.equals(TYPE_VIDEO))) {
       callback.onError("Invalid media type");
       return;
   }
   ```

2. **默认值处理**：为缺失或无效的参数提供默认值
   ```java
   media.setMimeType(mimeType != null ? mimeType : getDefaultMimeType(mediaType));
   media.setWidth(width > 0 ? width : 0);
   media.setHeight(height > 0 ? height : 0);
   ```

### 错误恢复
实现了完善的错误处理和恢复机制：

1. **异常捕获**：全面捕获可能的异常，避免崩溃
   ```java
   try {
       // 保存文件和处理操作
   } catch (Exception e) {
       Log.e(TAG, "Error saving media", e);
       if (callback != null) {
           callback.onError("Error saving media: " + e.getMessage());
       }
   }
   ```

2. **资源清理**：确保在错误情况下释放资源、删除半成品文件
   ```java
   } catch (IOException e) {
       if (outputFile != null && outputFile.exists()) {
           outputFile.delete();
       }
       return null;
   } finally {
       // 关闭流
   }
   ```

通过以上设计，媒体数据能够高效地在数据库和UI之间流动，保证了应用的响应性和用户体验。 