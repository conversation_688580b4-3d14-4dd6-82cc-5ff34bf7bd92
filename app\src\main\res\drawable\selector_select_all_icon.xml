<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态 -->
    <item android:state_selected="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/transparent" />
                </shape>
            </item>
            <item>
                <vector
                    android:width="24dp"
                    android:height="24dp"
                    android:viewportWidth="24"
                    android:viewportHeight="24">
                    <path
                        android:pathData="M15.013,4.5C16.573,4.5 17.139,4.662 17.709,4.967C18.28,5.273 18.727,5.72 19.033,6.291L19.134,6.493C19.374,7.008 19.5,7.61 19.5,8.987L19.5,17.513C19.5,19.073 19.338,19.639 19.033,20.209C18.727,20.78 18.28,21.227 17.709,21.533L17.507,21.634C16.992,21.874 16.39,22 15.013,22L6.487,22C4.927,22 4.361,21.838 3.791,21.533C3.22,21.227 2.773,20.78 2.467,20.209L2.366,20.007C2.126,19.492 2,18.89 2,17.513L2,8.987C2,7.427 2.162,6.861 2.467,6.291C2.773,5.72 3.22,5.273 3.791,4.967L3.993,4.866C4.473,4.642 5.03,4.517 6.222,4.502L15.013,4.5ZM6.487,6C5.291,6 4.899,6.076 4.498,6.29C4.189,6.455 3.955,6.689 3.79,6.998L3.712,7.16C3.569,7.49 3.509,7.884 3.501,8.758L3.5,17.513C3.5,18.709 3.576,19.101 3.79,19.502C3.955,19.811 4.189,20.045 4.498,20.21L4.66,20.288C4.99,20.431 5.384,20.491 6.258,20.499L6.487,20.5L15.013,20.5L15.451,20.496C16.314,20.477 16.654,20.396 17.002,20.21C17.311,20.045 17.545,19.811 17.71,19.502L17.788,19.34C17.931,19.01 17.991,18.616 17.999,17.742L18,17.513L18,8.987L17.996,8.549C17.977,7.686 17.896,7.346 17.71,6.998C17.545,6.689 17.311,6.455 17.002,6.29L16.84,6.212C16.51,6.069 16.116,6.009 15.242,6.001L6.487,6ZM15.59,2C17.819,2 18.627,2.232 19.442,2.668C20.257,3.104 20.896,3.743 21.332,4.558C21.768,5.373 22,6.181 22,8.41L22,14.372C22,16.155 21.814,16.802 21.466,17.454C21.22,17.913 20.894,18.302 20.495,18.614C20.498,18.47 20.5,18.317 20.5,18.154L20.5,8.346C20.5,6.976 20.366,6.283 19.981,5.563C19.626,4.9 19.1,4.374 18.437,4.019C17.765,3.659 17.117,3.519 15.919,3.502L5.846,3.5C5.683,3.5 5.53,3.502 5.385,3.506C5.698,3.106 6.087,2.78 6.546,2.534C7.198,2.186 7.845,2 9.628,2L15.59,2ZM15.808,10.255C16.091,10.537 16.101,10.989 15.839,11.284L15.808,11.316L10.609,16.515C9.939,17.185 8.863,17.199 8.177,16.557L8.134,16.515L5.684,14.066C5.391,13.773 5.391,13.298 5.684,13.005C5.966,12.723 6.418,12.713 6.713,12.975L6.745,13.005L8.841,15.101C9.123,15.384 9.575,15.394 9.869,15.131L9.901,15.101L14.748,10.255C15.041,9.962 15.515,9.962 15.808,10.255Z"
                        android:fillColor="@color/brand"
                        android:fillType="evenOdd" />
                </vector>
            </item>
        </layer-list>
    </item>
    <!-- 默认状态 -->
    <item android:drawable="@drawable/ic_public_select_all" />
</selector> 