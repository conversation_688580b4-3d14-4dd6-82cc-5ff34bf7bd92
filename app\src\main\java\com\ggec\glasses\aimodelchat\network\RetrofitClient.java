package com.ggec.glasses.aimodelchat.network;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.util.concurrent.TimeUnit;

public class RetrofitClient {

    private static final long TIMEOUT_SECONDS = 30; // 设置超时时间

    // 私有构造函数，防止实例化
    private RetrofitClient() {}

    /**
     * 创建包含认证和日志拦截器的 Retrofit 服务实例
     *
     * @param serviceClass 服务接口的 Class 对象
     * @param baseUrl      API 的基础 URL
     * @param apiKey       用于认证的 API Key (可选，如果不需要认证则传 null)
     * @param <T>          服务接口类型
     * @return 服务实例
     */
    public static <T> T createService(Class<T> serviceClass, String baseUrl, final String apiKey) {
        // 配置 OkHttpClient
        OkHttpClient.Builder httpClientBuilder = new OkHttpClient.Builder();

        // 添加认证拦截器 (如果提供了 API Key)
        if (apiKey != null && !apiKey.isEmpty()) {
            httpClientBuilder.addInterceptor(chain -> {
                Request original = chain.request();
                Request.Builder requestBuilder = original.newBuilder()
                        .header("Authorization", "Bearer " + apiKey)
                        .method(original.method(), original.body());
                Request request = requestBuilder.build();
                return chain.proceed(request);
            });
        }

        // 添加日志拦截器 (仅在 Debug 模式下)
        // if (BuildConfig.DEBUG) { // 假设有 BuildConfig.DEBUG
            HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
            loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
            httpClientBuilder.addInterceptor(loggingInterceptor);
        // }

        // 设置超时时间
        httpClientBuilder.connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        httpClientBuilder.readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        httpClientBuilder.writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS);

        OkHttpClient client = httpClientBuilder.build();

        // 创建 Retrofit 实例
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(baseUrl)
                .addConverterFactory(GsonConverterFactory.create())
                .client(client)
                .build();

        return retrofit.create(serviceClass);
    }
} 