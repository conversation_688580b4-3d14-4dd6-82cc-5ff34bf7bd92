<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.Glasses" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
        <item name="bottomNavigationStyle">@style/Widget.App.BottomNavigationView</item>
        <!-- 设置状态栏颜色为雪域灰 -->
        <item name="android:statusBarColor">@color/comp_background_gray</item>
        <!-- 设置启动窗口背景为雪域灰 -->
        <item name="android:windowBackground">@color/comp_background_gray</item>
    </style>

    <style name="Theme.Glasses" parent="Base.Theme.Glasses" />
    
    <!-- 无ActionBar主题，用于启动页面 -->
    <style name="Theme.Glasses.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/comp_background_gray</item>
        <item name="android:statusBarColor">@color/comp_background_gray</item>
    </style>
    
    <!-- 全屏主题，用于媒体展示Activity -->
    <style name="Theme.Glasses.Fullscreen" parent="Theme.Glasses">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowBackground">@color/comp_background_gray</item>
    </style>
</resources>