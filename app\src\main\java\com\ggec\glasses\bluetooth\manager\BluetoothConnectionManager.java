package com.ggec.glasses.bluetooth.manager;

import android.Manifest;
import android.bluetooth.BluetoothA2dp;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothHeadset;
import android.bluetooth.BluetoothHidDevice;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import java.util.Map;

/**
 * 蓝牙连接管理器
 * 负责处理设备连接、配对和扫描操作
 */
public class BluetoothConnectionManager {
    
    private static final String TAG = "BluetoothConnectionMgr";
    
    private final Context context;
    private final android.bluetooth.BluetoothAdapter nativeAdapter;
    private final BluetoothProfileManager profileManager;
    
    /**
     * 构造函数
     * @param context 上下文
     * @param nativeAdapter 原生蓝牙适配器
     * @param profileManager 蓝牙配置文件管理器
     */
    public BluetoothConnectionManager(Context context, android.bluetooth.BluetoothAdapter nativeAdapter, BluetoothProfileManager profileManager) {
        this.context = context.getApplicationContext();
        this.nativeAdapter = nativeAdapter;
        this.profileManager = profileManager;
    }
    
    /**
     * 开始扫描蓝牙设备
     * @return 是否成功开始扫描
     */
    public boolean startDiscovery() {
        if (nativeAdapter == null) {
            Log.e(TAG, "开始扫描失败：蓝牙适配器不可用");
            return false;
        }
        
        // 检查蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "开始扫描失败：缺少BLUETOOTH_SCAN权限");
                return false;
            }
        }
        
        try {
            // 如果已经在扫描，先取消当前扫描
            if (nativeAdapter.isDiscovering()) {
                nativeAdapter.cancelDiscovery();
            }
            
            // 开始新的扫描
            boolean result = nativeAdapter.startDiscovery();
            if (result) {
                Log.d(TAG, "蓝牙设备扫描已开始");
            } else {
                Log.e(TAG, "开始蓝牙设备扫描失败");
            }
            return result;
        } catch (SecurityException e) {
            Log.e(TAG, "开始蓝牙设备扫描时出现安全异常", e);
            return false;
        }
    }
    
    /**
     * 取消蓝牙设备扫描
     * @return 是否成功取消扫描
     */
    public boolean cancelDiscovery() {
        if (nativeAdapter == null) {
            Log.e(TAG, "取消扫描失败：蓝牙适配器不可用");
            return false;
        }
        
        // 检查蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "取消扫描失败：缺少BLUETOOTH_SCAN权限");
                return false;
            }
        }
        
        try {
            // 只有在扫描时才需要取消
            if (nativeAdapter.isDiscovering()) {
                boolean result = nativeAdapter.cancelDiscovery();
                if (result) {
                    Log.d(TAG, "蓝牙设备扫描已取消");
                } else {
                    Log.e(TAG, "取消蓝牙设备扫描失败");
                }
                return result;
            }
            return true;
        } catch (SecurityException e) {
            Log.e(TAG, "取消蓝牙设备扫描时出现安全异常", e);
            return false;
        }
    }
    
    /**
     * 连接到蓝牙设备
     * 尝试连接所有支持的配置文件
     * @param device 蓝牙设备
     * @return 是否成功开始连接
     */
    public boolean connectToDevice(BluetoothDevice device) {
        if (device == null) {
            Log.e(TAG, "连接设备失败：设备对象为null");
            return false;
        }
        
        // 检查蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "连接设备失败：缺少BLUETOOTH_CONNECT权限");
                return false;
            }
        }
        
        // 取消扫描以提高连接成功率
        cancelDiscovery();
        
        // 尝试连接各种支持的配置文件
        boolean anyProfileConnecting = false;
        
        Map<Integer, BluetoothProfile> profiles = profileManager.getProfiles();
        
        // 连接A2DP配置文件（如耳机、音箱等）
        BluetoothProfile a2dpProfile = profiles.get(BluetoothProfile.A2DP);
        if (a2dpProfile != null && a2dpProfile instanceof BluetoothA2dp) {
            BluetoothA2dp a2dp = (BluetoothA2dp) a2dpProfile;
            try {
                // 使用反射调用connect方法，因为这是一个隐藏方法
                a2dp.getClass().getMethod("connect", BluetoothDevice.class).invoke(a2dp, device);
                anyProfileConnecting = true;
                Log.d(TAG, "开始连接A2DP配置文件: " + device.getName());
            } catch (Exception e) {
                Log.e(TAG, "连接A2DP配置文件失败", e);
            }
        }
        
        // 连接HEADSET配置文件（如耳机等）
        BluetoothProfile headsetProfile = profiles.get(BluetoothProfile.HEADSET);
        if (headsetProfile != null && headsetProfile instanceof BluetoothHeadset) {
            BluetoothHeadset headset = (BluetoothHeadset) headsetProfile;
            try {
                // 使用反射调用connect方法，因为这是一个隐藏方法
                headset.getClass().getMethod("connect", BluetoothDevice.class).invoke(headset, device);
                anyProfileConnecting = true;
                Log.d(TAG, "开始连接HEADSET配置文件: " + device.getName());
            } catch (Exception e) {
                Log.e(TAG, "连接HEADSET配置文件失败", e);
            }
        }
        
        // 连接HID_DEVICE配置文件（如键盘、鼠标等）
        BluetoothProfile hidProfile = profiles.get(BluetoothProfile.HID_DEVICE);
        if (hidProfile != null && hidProfile instanceof BluetoothHidDevice) {
            BluetoothHidDevice hid = (BluetoothHidDevice) hidProfile;
            try {
                // 使用反射调用connect方法，因为这是一个隐藏方法
                hid.getClass().getMethod("connect", BluetoothDevice.class).invoke(hid, device);
                anyProfileConnecting = true;
                Log.d(TAG, "开始连接HID_DEVICE配置文件: " + device.getName());
            } catch (Exception e) {
                Log.e(TAG, "连接HID_DEVICE配置文件失败", e);
            }
        }
        
        return anyProfileConnecting;
    }
    
    /**
     * 与设备配对
     * @param device 蓝牙设备
     * @return 是否成功开始配对
     */
    public boolean pairDevice(BluetoothDevice device) {
        if (device == null) {
            Log.e(TAG, "配对设备失败：设备对象为null");
            return false;
        }
        
        // 检查蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "配对设备失败：缺少BLUETOOTH_CONNECT权限");
                return false;
            }
        }
        
        try {
            // 检查设备当前配对状态
            if (device.getBondState() == BluetoothDevice.BOND_BONDED) {
                // 设备已配对
                Log.d(TAG, "设备已配对，无需再次配对: " + device.getName());
                return true;
            } else if (device.getBondState() == BluetoothDevice.BOND_BONDING) {
                // 设备正在配对中
                Log.d(TAG, "设备正在配对中: " + device.getName());
                return true;
            }
            
            // 开始配对
            boolean result = device.createBond();
            if (result) {
                Log.d(TAG, "开始与设备配对: " + device.getName());
            } else {
                Log.e(TAG, "开始配对失败: " + device.getName());
            }
            return result;
        } catch (SecurityException e) {
            Log.e(TAG, "配对设备时出现安全异常", e);
            return false;
        }
    }
} 