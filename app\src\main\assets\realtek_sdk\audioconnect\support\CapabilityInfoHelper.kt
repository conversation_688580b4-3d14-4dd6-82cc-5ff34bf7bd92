/*
 * Copyright (c) 2017-2023. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.realsil.sdk.audioconnect.support

import android.annotation.SuppressLint
import android.content.Context
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.support.preference.BaseSharedPrefes


class CapabilityInfoHelper private constructor(context: Context) : BaseSharedPrefes(context) {
    val isSwitchOtaEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_OTA)) {
                set(KEY_SWITCH_OTA, false)

            }
            return getBoolean(KEY_SWITCH_OTA, false)
        }

    val isSwitchDeviceNameEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_DEVICE_NAME)) {
                set(KEY_SWITCH_DEVICE_NAME, false)
            }
            return getBoolean(KEY_SWITCH_DEVICE_NAME, false)
        }

    val isSwitchLanguageEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_LANGUAGE)) {
                set(KEY_SWITCH_LANGUAGE, false)
            }
            return getBoolean(KEY_SWITCH_LANGUAGE, false)
        }

    val isSwitchRwsChannelEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_RWS_CHANNEL)) {
                set(KEY_SWITCH_RWS_CHANNEL, false)
            }
            return getBoolean(KEY_SWITCH_RWS_CHANNEL, false)
        }

    val isSwitchGamingModeEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_GAMING_MODE)) {
                set(KEY_SWITCH_GAMING_MODE, false)
            }
            return getBoolean(KEY_SWITCH_GAMING_MODE, false)
        }

    val isSwitchEqEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_EQ)) {
                set(KEY_SWITCH_EQ, false)
            }
            return getBoolean(KEY_SWITCH_EQ, false)
        }

    val isSwitchVoiceEqEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_VOICE_EQ)) {
                set(KEY_SWITCH_VOICE_EQ, false)
            }
            return getBoolean(KEY_SWITCH_VOICE_EQ, false)
        }

    //公版
    val isSwitchKeyMapEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_KEY_MAP)) {
                set(KEY_SWITCH_KEY_MAP, false)
            }
            return getBoolean(KEY_SWITCH_KEY_MAP, false)
        }

    val isSwitchVpRingtoneEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_VP_RINGTONE)) {
                set(KEY_SWITCH_VP_RINGTONE, false)
            }
            return getBoolean(KEY_SWITCH_VP_RINGTONE, false)
        }

    val isSwitchRealHearingEnhancementEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_REAL_HEARING_ENHANCEMENT)) {
                set(KEY_SWITCH_REAL_HEARING_ENHANCEMENT, false)
            }
            return getBoolean(KEY_SWITCH_REAL_HEARING_ENHANCEMENT, false)
        }

    val isSwitchEarDetectionEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_EAR_DETECTION)) {
                set(KEY_SWITCH_EAR_DETECTION, false)
            }
            return getBoolean(KEY_SWITCH_EAR_DETECTION, false)
        }

    val isSwitchSppDataCaptureEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_SPP_DATA_CAPTURE)) {
                set(KEY_SWITCH_SPP_DATA_CAPTURE, false)
            }
            return getBoolean(KEY_SWITCH_SPP_DATA_CAPTURE, false)
        }

    val isSwitchAncApplyBurnEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_ANC_APPLY_BURN)) {
                set(KEY_SWITCH_ANC_APPLY_BURN, false)
            }
            return getBoolean(KEY_SWITCH_ANC_APPLY_BURN, false)
        }


    //Durian
    val isSwitchDurianKeyMapEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_DURIAN_KEY_MAP)) {
                set(KEY_SWITCH_DURIAN_KEY_MAP, false)
            }
            return getBoolean(KEY_SWITCH_DURIAN_KEY_MAP, false)
        }

    val isSwitchNoiseControlEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_NOISE_CONTROL)) {
                set(KEY_SWITCH_NOISE_CONTROL, false)
            }
            return getBoolean(KEY_SWITCH_NOISE_CONTROL, false)
        }

    val isSwitchAutoInearDetectionEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_AUTO_INEAR_DETECTION)) {
                set(KEY_SWITCH_AUTO_INEAR_DETECTION, false)
            }
            return getBoolean(KEY_SWITCH_AUTO_INEAR_DETECTION, false)
        }

    val isSwitchDurianMultiLinkEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_DURIAN_MULTI_LINK)) {
                set(KEY_SWITCH_DURIAN_MULTI_LINK, false)
            }
            return getBoolean(KEY_SWITCH_DURIAN_MULTI_LINK, false)
        }

    val isSwitchDeviceIdEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_DEVICE_ID)) {
                set(KEY_SWITCH_DEVICE_ID, false)
            }
            return getBoolean(KEY_SWITCH_DEVICE_ID, false)
        }

    val isChargingCaseEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_CHARGING_CASE)) {
                set(KEY_SWITCH_CHARGING_CASE, false)
            }
            return getBoolean(KEY_SWITCH_CHARGING_CASE, false)
        }

    /*val isSwitchLeftDeviceIdEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_LEFT_DEVICE_ID)) {
                set(KEY_SWITCH_LEFT_DEVICE_ID, true)
                return true
            }
            return getBoolean(KEY_SWITCH_LEFT_DEVICE_ID, true)
        }

    val isSwitchRightDeviceIdEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_RIGHT_DEVICE_ID)) {
                set(KEY_SWITCH_RIGHT_DEVICE_ID, true)
                return true
            }
            return getBoolean(KEY_SWITCH_RIGHT_DEVICE_ID, true)
        }*/


    val isSwitchTtsEnabled: Boolean
        get() {
            if (!contains(KEY_SWITCH_TTS)) {
                set(KEY_SWITCH_TTS, false)
            }
            return getBoolean(KEY_SWITCH_TTS, false)
        }


    val localPlayBackSupported: Boolean
        get() {
            if (!contains(KEY_FEATURE_LOCAL_PLAY_BACK)) {
                set(KEY_FEATURE_LOCAL_PLAY_BACK, false)
            }
            return getBoolean(KEY_FEATURE_LOCAL_PLAY_BACK, false)
        }

    val logFlashDumpSupported: Boolean
        get() {
            if (!contains(KEY_LOG_FLASH_DUMP)) {
                set(KEY_LOG_FLASH_DUMP, false)
            }
            return getBoolean(KEY_LOG_FLASH_DUMP, false)
        }
    val rssiMonitorAndDumpSupported: Boolean
        get() {
            if (!contains(KEY_RSSI_MONITOR_AND_DUMP)) {
                set(KEY_RSSI_MONITOR_AND_DUMP, false)
            }
            return getBoolean(KEY_RSSI_MONITOR_AND_DUMP, false)
        }
    val ftlFlashDumpSupported: Boolean
        get() {
            if (!contains(KEY_FTL_FLASH_DUMP)) {
                set(KEY_FTL_FLASH_DUMP, false)
            }
            return getBoolean(KEY_FTL_FLASH_DUMP, false)
        }

    val aiForSmartWearSupported: Boolean
        get() {
            if (!contains(KEY_AI_SMART_WEARABLE_DEVICE)) {
                set(KEY_AI_SMART_WEARABLE_DEVICE, false)
            }
            return getBoolean(KEY_AI_SMART_WEARABLE_DEVICE, false)
        }

    val icBindingSupported: Boolean
        get() {
            if (!contains(KEY_IC_BINDING)) {
                set(KEY_IC_BINDING, false)
            }
            return getBoolean(KEY_IC_BINDING, false)
        }
    init {
        ZLogger.v("OtaEnabled:$isSwitchOtaEnabled,DeviceNameEnabled:$isSwitchDeviceNameEnabled")
        ZLogger.v("isSwitchLanguageEnabled:$isSwitchLanguageEnabled, RwsChannelEnabled:$isSwitchRwsChannelEnabled")
        ZLogger.v("isSwitchGamingModeEnabled:$isSwitchGamingModeEnabled")
        ZLogger.v("isSwitchEqEnabled:$isSwitchEqEnabled, VoiceEqEnabled:$isSwitchVoiceEqEnabled")
        ZLogger.v("isSwitchKeyMapEnabled:$isSwitchKeyMapEnabled, VpRingtoneEnabled:$isSwitchVpRingtoneEnabled")
        ZLogger.v("isSwitchRealHearingEnhancementEnabled:$isSwitchRealHearingEnhancementEnabled, EarDetectionEnabled:$isSwitchEarDetectionEnabled")
        ZLogger.v("isSwitchSppDataCaptureEnabled:$isSwitchSppDataCaptureEnabled, AncApplyBurnEnabled:$isSwitchAncApplyBurnEnabled")
        ZLogger.v("isSwitchDurianKeyMapEnabled:$isSwitchDurianKeyMapEnabled, NoiseControlEnabled:$isSwitchNoiseControlEnabled")
        ZLogger.v("isSwitchAutoInearDetectionEnabled:$isSwitchAutoInearDetectionEnabled, DurianMultiLinkEnabled:$isSwitchDurianMultiLinkEnabled")
        ZLogger.v("isSwitchDeviceIdEnabled:$isSwitchDeviceIdEnabled")
        /*ZLogger.v("isSwitchLeftDeviceIdEnabled:$isSwitchLeftDeviceIdEnabled")
        ZLogger.v("isSwitchRightDeviceIdEnabled:$isSwitchRightDeviceIdEnabled")*/
        ZLogger.v("isSwitchTtsEnabled:$isSwitchTtsEnabled")
        ZLogger.v("localPlayBackSupported:$localPlayBackSupported")
        ZLogger.v("logFlashDumpSupported:$logFlashDumpSupported")
        ZLogger.v("rssiMonitorAndDumpSupported:$rssiMonitorAndDumpSupported")
        ZLogger.v("ftlFlashDumpSupported:$ftlFlashDumpSupported")
        ZLogger.v("aiForSmartWearSupported:$aiForSmartWearSupported")
    }

    companion object {
        const val KEY_SWITCH_OTA = "switch_ota"
        const val KEY_SWITCH_DEVICE_NAME = "switch_device_name"
        const val KEY_SWITCH_LANGUAGE = "switch_language"
        const val KEY_SWITCH_RWS_CHANNEL = "switch_rws_channel"
        const val KEY_SWITCH_GAMING_MODE = "switch_gaming_mode"
        const val KEY_SWITCH_EQ = "switch_equalizer"
        const val KEY_SWITCH_VOICE_EQ = "switch_voice_equalizer"

        const val KEY_SWITCH_KEY_MAP = "switch_key_remap"
        const val KEY_SWITCH_VP_RINGTONE = "switch_vp_ringtone"
        const val KEY_SWITCH_REAL_HEARING_ENHANCEMENT = "switch_real_hearing_enhancement"
        const val KEY_SWITCH_EAR_DETECTION = "switch_ear_detection"
        const val KEY_SWITCH_SPP_DATA_CAPTURE = "switch_spp_data_capture"
        const val KEY_SWITCH_ANC_APPLY_BURN = "switch_burn_apply"

        const val KEY_SWITCH_DURIAN_KEY_MAP = "switch_durian_key_remap"
        const val KEY_SWITCH_NOISE_CONTROL = "switch_noise_control"
        const val KEY_SWITCH_AUTO_INEAR_DETECTION = "switch_auto_inear_detection"
        const val KEY_SWITCH_DURIAN_MULTI_LINK = "switch_durian_multi_link"
        const val KEY_SWITCH_DEVICE_ID = "switch_device_id"
        const val KEY_SWITCH_LEFT_DEVICE_ID = "switch_left_device_id"
        const val KEY_SWITCH_RIGHT_DEVICE_ID = "switch_right_device_id"
        const val KEY_SWITCH_CHARGING_CASE = "KEY_SWITCH_CHARGING_CASE"

        const val KEY_SWITCH_TTS = "switch_tts"
        const val KEY_FEATURE_LOCAL_PLAY_BACK = "KEY_FEATURE_LOCAL_PLAY_BACK"
        const val KEY_LOG_FLASH_DUMP = "KEY_LOG_FLASH_DUMP"
        const val KEY_FTL_FLASH_DUMP = "KEY_FTL_FLASH_DUMP"
        const val KEY_RSSI_MONITOR_AND_DUMP = "KEY_RSSI_MONITOR_AND_DUMP"
        const val KEY_AI_SMART_WEARABLE_DEVICE = "KEY_AI_SMART_WEARABLE_DEVICE"
        const val KEY_IC_BINDING = "KEY_IC_BINDING"

        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: CapabilityInfoHelper? = null

        fun initialize(context: Context) {
            if (instance == null) {
                synchronized(CapabilityInfoHelper::class.java) {
                    if (instance == null) {
                        instance =
                            CapabilityInfoHelper(context.applicationContext)
                    }
                }
            }
        }

        @JvmStatic
        fun getInstance(): CapabilityInfoHelper? {
            if (instance == null) {
                ZLogger.w("not initialized, please call initialize(Context context) first")
            }
            return instance
        }
    }

}
