package com.ggec.glasses.voice.manager;

/**
 * 语音识别回调接口
 * 用于通知识别状态和传递识别结果
 */
public interface VoiceRecognitionCallback {
    
    /**
     * 当识别开始时调用
     */
    void onRecognitionStarted();
    
    /**
     * 当收到中间识别结果时调用
     * @param text 中间识别结果文本
     */
    void onPartialResult(String text);
    
    /**
     * 当收到最终识别结果时调用
     * @param text 最终识别结果文本
     */
    void onFinalResult(String text);
    
    /**
     * 当识别过程中发生错误时调用
     * @param error 错误信息
     */
    void onRecognitionError(String error);
    
    /**
     * 当识别完成时调用
     */
    void onRecognitionComplete();
} 