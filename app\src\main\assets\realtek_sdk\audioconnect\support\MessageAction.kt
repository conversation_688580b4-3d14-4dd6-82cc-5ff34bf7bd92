/*
 * Copyright (c) 2024. Realtek Semiconductor Corporation.
 *  
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *  
 *       http://www.apache.org/licenses/LICENSE-2.0
 *  
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */
package com.realsil.sdk.audioconnect.support

/**
 * Define global broadcasts in the project.
 *
 * <AUTHOR>
 */
interface MessageAction {
    companion object {
        /**
         * This broadcast is sent when the device is connected
         */
        const val ACTION_BLUETOOTH_DEVICE_CONNECTED: String =
            "com.realsil.bbpro.action.BLUETOOTH_DEVICE_CONNECTED"

        /**
         * This broadcast is sent when the device disconnects
         */
        const val ACTION_BLUETOOTH_DEVICE_DISCONNECTED: String =
            "com.realsil.bbpro.action.BLUETOOTH_DEVICE_DISCONNECTED"

        /**
         * This broadcast is sent when the device disconnects
         */
        const val EXTRA_DEVICE_ADDRESS: String = "device_address"
    }
}
