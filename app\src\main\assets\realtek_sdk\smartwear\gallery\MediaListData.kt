/*
 * Copyright (c) 2025. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.realsil.sample.audioconnect.smartwear.gallery

import android.graphics.Bitmap
import java.io.File
import java.io.Serializable

/**
 * <AUTHOR>
 * @date 2025/03/26
 */
data class MediaListData(
    val type: String = "folder",
    val name: String = "aiglass:/",
    val time: String = "",
    val contents: ArrayList<MediaListItem> = ArrayList()
) : Serializable {}

data class MediaListItem(
    val type: String = "file",
    val name: String = "",
    val time: String = ""
) : Serializable {}

data class LocaleMediaFile(
    var index:Int = 0,
    var type: Int = FileType.IMAGE,
    var filePath: String = "",
    var name: String = "",
    var time: String = "",
    var originFile: File,
    var uriString : String = "",
    var thumbnail:Bitmap? = null
) : Serializable {}

object FileType {
    const val IMAGE = 0
    const val VIDEO = 1
}