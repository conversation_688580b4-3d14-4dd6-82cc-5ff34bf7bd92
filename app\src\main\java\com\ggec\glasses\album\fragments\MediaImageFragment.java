package com.ggec.glasses.album.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.ggec.glasses.R;
import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.viewmodel.MediaDisplayViewModel;
import com.github.chrisbanes.photoview.PhotoView;

import java.io.File;

/**
 * 图片展示Fragment，专门用于处理图片的加载和展示
 */
public class MediaImageFragment extends Fragment {

    private static final String ARG_MEDIA = "media";
    
    private Media media;
    private PhotoView imgDisplay;
    private MediaDisplayViewModel viewModel;
    
    /**
     * 创建MediaImageFragment实例的工厂方法
     * @param media 媒体对象
     * @return 新的MediaImageFragment实例
     */
    public static MediaImageFragment newInstance(Media media) {
        MediaImageFragment fragment = new MediaImageFragment();
        Bundle args = new Bundle();
        args.putParcelable(ARG_MEDIA, media);
        fragment.setArguments(args);
        return fragment;
    }
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            media = getArguments().getParcelable(ARG_MEDIA);
        }
        
        // 获取ViewModel，从Activity共享
        viewModel = new ViewModelProvider(requireActivity()).get(MediaDisplayViewModel.class);
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_media_image, container, false);
        
        // 初始化视图
        imgDisplay = view.findViewById(R.id.img_display);
        
        // 设置点击事件，用于切换操作层显示/隐藏状态
        imgDisplay.setOnClickListener(v -> {
            viewModel.toggleOperationLayerVisibility();
        });
        
        return view;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 如果Media通过参数传入，使用它
        if (media != null) {
            loadImage(media);
        } else {
            // 否则观察ViewModel中的媒体数据变化
            viewModel.getMedia().observe(getViewLifecycleOwner(), this::loadImage);
        }
    }
    
    /**
     * 加载图片
     * @param media 媒体数据
     */
    private void loadImage(Media media) {
        if (media == null) return;
        
        if (!"IMAGE".equals(media.getType())) {
            // 如果不是图片类型，显示警告并返回
            Toast.makeText(requireContext(), "不是有效的图片文件", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 加载图片
        if (media.getFilePath() != null && new File(media.getFilePath()).exists()) {
            // 使用Glide加载图片，确保图片适合屏幕
            Glide.with(this)
                .load(new File(media.getFilePath()))
                .apply(new RequestOptions()
                        .fitCenter()
                        .diskCacheStrategy(DiskCacheStrategy.ALL))
                .into(imgDisplay);
        } else if (media.getThumbnailPath() != null && new File(media.getThumbnailPath()).exists()) {
            // 如果原图不存在，尝试加载缩略图
            Glide.with(this)
                .load(new File(media.getThumbnailPath()))
                .apply(new RequestOptions()
                        .fitCenter()
                        .diskCacheStrategy(DiskCacheStrategy.ALL))
                .into(imgDisplay);
        } else {
            // 如果都不存在，显示默认图标
            imgDisplay.setImageResource(R.drawable.ic_empty_album);
            Toast.makeText(requireContext(), "图片文件不存在", Toast.LENGTH_SHORT).show();
        }
    }
} 