package com.ggec.glasses.fragments;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager2.widget.ViewPager2;

import com.ggec.glasses.MainActivity;
import com.ggec.glasses.R;
import com.ggec.glasses.album.adapter.MediaPagerAdapter;
import com.ggec.glasses.album.manager.MediaSelectionManager;
import com.ggec.glasses.album.manager.MultiSelectModeManager;
import com.ggec.glasses.album.viewmodel.MediaViewModel;
import com.ggec.glasses.utils.DialogUtils;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

public class AlbumFragment extends Fragment implements MultiSelectModeManager.MultiSelectModeCallback {

    private ImageView btnMore;
    private TabLayout tabLayout;
    private ViewPager2 viewPager;
    private final String[] tabTitles = new String[]{"全部", "图片", "视频"};
    private PopupWindow popupWindow;
    private View headerLayout; // 顶部导航栏视图
    private MediaViewModel mediaViewModel; // 媒体ViewModel
    
    // 多选模式相关
    private View multiSelectToolbar; // 多选模式底部工具栏
    private View multiSelectHeaderLayout; // 多选模式顶部导航栏
    private MultiSelectModeManager multiSelectModeManager; // 多选模式管理器
    private final MutableLiveData<Boolean> multiSelectModeLiveData = new MutableLiveData<>(false);
    
    // 返回键处理
    private OnBackPressedCallback backPressedCallback;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_album, container, false);
        
        // 初始化视图
        initViews(view);
        
        // 创建多选模式管理器
        multiSelectModeManager = new MultiSelectModeManager(
                requireContext(),
                this,
                headerLayout,
                multiSelectHeaderLayout,
                multiSelectToolbar
        );
        
        // 恢复多选模式状态
        multiSelectModeManager.restoreInstanceState(savedInstanceState);
        
        // 设置返回键处理
        setupBackPressedCallback();
        
        return view;
    }
    
    /**
     * 设置返回键处理回调
     */
    private void setupBackPressedCallback() {
        backPressedCallback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                // 如果处于多选模式，则退出多选模式
                if (multiSelectModeManager != null && multiSelectModeManager.isInMultiSelectMode()) {
                    multiSelectModeManager.exitMultiSelectMode();
                    // 返回键事件已消费
                } else {
                    // 不处于多选模式，禁用当前回调，让系统处理返回事件
                    this.setEnabled(false);
                    requireActivity().getOnBackPressedDispatcher().onBackPressed();
                    // 重新启用回调，以便下次返回键事件能被拦截
                    this.setEnabled(true);
                }
            }
        };
        
        // 只有当处于多选模式时，才启用返回键拦截
        updateBackPressedCallbackState();
        
        // 向Activity注册返回键回调
        requireActivity().getOnBackPressedDispatcher().addCallback(getViewLifecycleOwner(), backPressedCallback);
    }
    
    /**
     * 更新返回键回调的启用状态
     */
    private void updateBackPressedCallbackState() {
        if (backPressedCallback != null) {
            backPressedCallback.setEnabled(multiSelectModeManager != null && multiSelectModeManager.isInMultiSelectMode());
        }
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 初始化ViewModel
        mediaViewModel = new ViewModelProvider(requireActivity()).get(MediaViewModel.class);
        
        // 如果处于多选模式，恢复UI状态
        if (multiSelectModeManager.isInMultiSelectMode()) {
            multiSelectModeManager.restoreUIState();
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        
        // 确保在多选模式下从系统桌面返回时底部导航栏保持隐藏
        if (multiSelectModeManager != null && multiSelectModeManager.isInMultiSelectMode() && getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).setBottomNavigationVisibility(false);
        }
        
        // 更新返回键回调状态
        updateBackPressedCallbackState();
    }
    
    private void initViews(View view) {
        btnMore = view.findViewById(R.id.btn_more);
        tabLayout = view.findViewById(R.id.tab_layout);
        viewPager = view.findViewById(R.id.view_pager);
        headerLayout = view.findViewById(R.id.header_layout); // 获取导航栏视图
        multiSelectToolbar = view.findViewById(R.id.multi_select_toolbar); // 获取多选工具栏视图
        multiSelectHeaderLayout = view.findViewById(R.id.multi_select_header_layout); // 获取多选模式顶部导航栏
        
        // 设置更多按钮点击事件
        btnMore.setOnClickListener(v -> {
            showPopupMenu();
        });
        
        // 设置ViewPager适配器
        MediaPagerAdapter pagerAdapter = new MediaPagerAdapter(this);
        viewPager.setAdapter(pagerAdapter);
        
        // 将TabLayout与ViewPager2关联
        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            tab.setText(tabTitles[position]);
        }).attach();
        
        // 设置标签页之间的间距
        for (int i = 0; i < tabLayout.getTabCount(); i++) {
            View tabView = ((ViewGroup) tabLayout.getChildAt(0)).getChildAt(i);
            ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) tabView.getLayoutParams();
            params.setMarginEnd(12);
            tabView.setLayoutParams(params);
        }
        
        // 设置标签页切换监听器
        viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                // 更新MediaSelectionManager中的当前标签页类型
                String tabType;
                switch (position) {
                    case 1:
                        tabType = com.ggec.glasses.album.fragments.MediaListFragment.TYPE_PHOTO;
                        break;
                    case 2:
                        tabType = com.ggec.glasses.album.fragments.MediaListFragment.TYPE_VIDEO;
                        break;
                    case 0:
                    default:
                        tabType = com.ggec.glasses.album.fragments.MediaListFragment.TYPE_ALL;
                        break;
                }
                // 更新当前标签页类型
                MediaSelectionManager.getInstance().setCurrentTabType(tabType);
                
                // 更新全选按钮状态，确保其反映当前标签页的选择情况
                if (multiSelectModeManager != null && multiSelectModeManager.isInMultiSelectMode()) {
                    multiSelectModeManager.updateSelectAllButtonForCurrentTab(tabType);
                }
            }
        });
    }
    
    /**
     * 显示弹出菜单
     */
    private void showPopupMenu() {
        // 创建弹出窗口
        View popupView = LayoutInflater.from(requireContext()).inflate(R.layout.album_popup_menu, null);
        popupWindow = new PopupWindow(
                popupView,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                true
        );
        
        // 设置弹出窗口的背景和阴影效果
        popupWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT)); // 使用透明背景以便显示自定义阴影
        popupWindow.setElevation(5); // 降低阴影度
        popupWindow.setAnimationStyle(android.R.style.Animation_Dialog);
        
        // 设置菜单项点击事件
        setupMenuItemClickListeners(popupView);
        
        // 获取屏幕尺寸
        WindowManager windowManager = (WindowManager) requireContext().getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(displayMetrics);
        int screenWidth = displayMetrics.widthPixels;
        
        // 获取导航栏位置
        int[] headerLocation = new int[2];
        headerLayout.getLocationOnScreen(headerLocation);
        int headerHeight = headerLayout.getHeight();
        int headerBottom = headerLocation[1] + headerHeight;
        
        // 获取弹出菜单的尺寸
        popupView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
        int popupWidth = popupView.getMeasuredWidth();
        int popupHeight = popupView.getMeasuredHeight();
        
        // 计算弹出菜单的位置
        // 水平位置：按钮右侧对齐，距离屏幕右边界16dp
        int rightMargin = dpToPx(16);
        int xPos = screenWidth - popupWidth - rightMargin;
        
        // 垂直位置：紧贴导航栏底部
        int yPos = headerBottom;
        
        // 显示弹出菜单
        popupWindow.showAtLocation(btnMore, Gravity.NO_GRAVITY, xPos, yPos);
    }
    
    /**
     * 设置菜单项点击事件
     */
    private void setupMenuItemClickListeners(View popupView) {
        // 下载图片按钮
        TextView btnDownloadImage = popupView.findViewById(R.id.btn_download_image);
        btnDownloadImage.setOnClickListener(v -> {
            Toast.makeText(requireContext(), "下载图片功能待开发", Toast.LENGTH_SHORT).show();
            popupWindow.dismiss();
        });
        
        // 下载视频按钮
        TextView btnDownloadVideo = popupView.findViewById(R.id.btn_download_video);
        btnDownloadVideo.setOnClickListener(v -> {
            Toast.makeText(requireContext(), "下载视频功能待开发", Toast.LENGTH_SHORT).show();
            popupWindow.dismiss();
        });
        
        // 多选照片按钮已删除，无需引用
        
        // 加载测试数据按钮
        TextView btnLoadTestData = popupView.findViewById(R.id.btn_load_test_data);
        btnLoadTestData.setOnClickListener(v -> {
            mediaViewModel.loadTestData();
            popupWindow.dismiss();
        });
        
        // 清空相册按钮
        TextView btnClearAlbum = popupView.findViewById(R.id.btn_clear_album);
        btnClearAlbum.setOnClickListener(v -> {
            // 显示确认对话框
            DialogUtils.showConfirmDialog(
                    requireContext(),
                    getString(R.string.album_confirm_clear_title),
                    getString(R.string.album_confirm_clear_message),
                    "确定",
                    "取消",
                    (dialog, which) -> {
                        mediaViewModel.clearAllMedia();
                        Toast.makeText(requireContext(), "相册已清空", Toast.LENGTH_SHORT).show();
                    },
                    null
            );
            
            popupWindow.dismiss();
        });
    }
    
    @Override
    public void onMultiSelectModeChanged(boolean isInMultiSelectMode) {
        // 更新多选模式状态
        multiSelectModeLiveData.setValue(isInMultiSelectMode);
        
        // 更新返回键回调状态
        updateBackPressedCallbackState();
        
        // 更新底部导航栏可见性
        if (getActivity() instanceof MainActivity) {
            ((MainActivity) getActivity()).setBottomNavigationVisibility(!isInMultiSelectMode);
        }
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        
        // 移除返回键回调
        if (backPressedCallback != null) {
            backPressedCallback.remove();
            backPressedCallback = null;
        }
        
        // 销毁弹出窗口
        if (popupWindow != null) {
            popupWindow.dismiss();
            popupWindow = null;
        }
    }
    
    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        
        // 保存多选模式状态
        if (multiSelectModeManager != null) {
            multiSelectModeManager.saveInstanceState(outState);
        }
    }
    
    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }
    
    public boolean isInMultiSelectMode() {
        return multiSelectModeManager != null && multiSelectModeManager.isInMultiSelectMode();
    }
    
    /**
     * 获取多选模式状态的LiveData
     * @return 多选模式状态的LiveData
     */
    public LiveData<Boolean> getMultiSelectModeLiveData() {
        return multiSelectModeLiveData;
    }

    /**
     * 更新已选择项计数
     * @param count 已选择项数量
     */
    public void updateSelectedCount(int count) {
        if (multiSelectModeManager != null) {
            multiSelectModeManager.updateSelectedCount(count);
        }
    }
    
    /**
     * 进入多选模式
     * 该方法用于支持长按触发多选模式
     */
    public void enterMultiSelectMode() {
        if (multiSelectModeManager != null) {
            multiSelectModeManager.enterMultiSelectMode();
        }
    }
} 