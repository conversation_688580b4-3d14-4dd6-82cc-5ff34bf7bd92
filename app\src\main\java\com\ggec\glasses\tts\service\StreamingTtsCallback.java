package com.ggec.glasses.tts.service;

import java.nio.ByteBuffer;

/**
 * 流式文本转语音合成的回调接口。
 * 实现者可以在音频块生成时接收它们。
 */
public interface StreamingTtsCallback {

    /**
     * 当新的音频数据块可用时调用。
     * @param chunk 包含音频数据块 (PCM 格式) 的 ByteBuffer。
     */
    void onAudioChunk(ByteBuffer chunk);

    /**
     * 当整个 TTS 合成流完成时调用。
     * 此调用后将不再传递音频块。
     */
    void onStreamingComplete();

    /**
     * 当 TTS 合成过程中发生错误时调用。
     * @param error 错误的描述。
     * @param e 发生的异常，如果可用 (可以为 null)。
     */
    void onError(String error, Exception e);
} 