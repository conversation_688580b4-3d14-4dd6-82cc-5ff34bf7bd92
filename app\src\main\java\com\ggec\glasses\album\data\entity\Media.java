package com.ggec.glasses.album.data.entity;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.TypeConverters;

import java.util.Date;

/**
 * 媒体文件实体类，对应数据库中的Media表
 */
@Entity(tableName = "media")
public class Media implements Parcelable {
    
    @PrimaryKey(autoGenerate = true)
    private long id;
    
    @ColumnInfo(name = "file_name")
    private String fileName;
    
    @ColumnInfo(name = "file_path")
    private String filePath;
    
    @ColumnInfo(name = "thumbnail_path")
    private String thumbnailPath;
    
    @ColumnInfo(name = "type")
    private String type; // IMAGE 或 VIDEO
    
    @ColumnInfo(name = "mime_type")
    private String mimeType;
    
    @ColumnInfo(name = "size")
    private long size; // 文件大小，单位：字节
    
    @ColumnInfo(name = "width")
    private int width; // 宽度，单位：像素
    
    @ColumnInfo(name = "height")
    private int height; // 高度，单位：像素
    
    @ColumnInfo(name = "duration")
    private long duration; // 视频时长，单位：毫秒，图片为0
    
    @ColumnInfo(name = "creation_date")
    private Date creationDate; // 创建日期
    
    @ColumnInfo(name = "modification_date")
    private Date modificationDate; // 修改日期
    
    @ColumnInfo(name = "is_deleted")
    private boolean isDeleted; // 是否已删除，用于回收站功能
    
    @ColumnInfo(name = "custom_metadata")
    private String customMetadata; // 自定义元数据，JSON格式
    
    // 构造函数
    public Media() {
    }
    
    // Getters and Setters
    
    public long getId() {
        return id;
    }
    
    public void setId(long id) {
        this.id = id;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public String getThumbnailPath() {
        return thumbnailPath;
    }
    
    public void setThumbnailPath(String thumbnailPath) {
        this.thumbnailPath = thumbnailPath;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getMimeType() {
        return mimeType;
    }
    
    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }
    
    public long getSize() {
        return size;
    }
    
    public void setSize(long size) {
        this.size = size;
    }
    
    public int getWidth() {
        return width;
    }
    
    public void setWidth(int width) {
        this.width = width;
    }
    
    public int getHeight() {
        return height;
    }
    
    public void setHeight(int height) {
        this.height = height;
    }
    
    public long getDuration() {
        return duration;
    }
    
    public void setDuration(long duration) {
        this.duration = duration;
    }
    
    public Date getCreationDate() {
        return creationDate;
    }
    
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
    
    public Date getModificationDate() {
        return modificationDate;
    }
    
    public void setModificationDate(Date modificationDate) {
        this.modificationDate = modificationDate;
    }
    
    public boolean isDeleted() {
        return isDeleted;
    }
    
    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }
    
    public String getCustomMetadata() {
        return customMetadata;
    }
    
    public void setCustomMetadata(String customMetadata) {
        this.customMetadata = customMetadata;
    }
    
    // Parcelable实现
    protected Media(Parcel in) {
        id = in.readLong();
        fileName = in.readString();
        filePath = in.readString();
        thumbnailPath = in.readString();
        type = in.readString();
        mimeType = in.readString();
        size = in.readLong();
        width = in.readInt();
        height = in.readInt();
        duration = in.readLong();
        long tmpCreationDate = in.readLong();
        creationDate = tmpCreationDate != -1 ? new Date(tmpCreationDate) : null;
        long tmpModificationDate = in.readLong();
        modificationDate = tmpModificationDate != -1 ? new Date(tmpModificationDate) : null;
        isDeleted = in.readByte() != 0;
        customMetadata = in.readString();
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(id);
        dest.writeString(fileName);
        dest.writeString(filePath);
        dest.writeString(thumbnailPath);
        dest.writeString(type);
        dest.writeString(mimeType);
        dest.writeLong(size);
        dest.writeInt(width);
        dest.writeInt(height);
        dest.writeLong(duration);
        dest.writeLong(creationDate != null ? creationDate.getTime() : -1);
        dest.writeLong(modificationDate != null ? modificationDate.getTime() : -1);
        dest.writeByte((byte) (isDeleted ? 1 : 0));
        dest.writeString(customMetadata);
    }
    
    @Override
    public int describeContents() {
        return 0;
    }
    
    public static final Creator<Media> CREATOR = new Creator<Media>() {
        @Override
        public Media createFromParcel(Parcel in) {
            return new Media(in);
        }
        
        @Override
        public Media[] newArray(int size) {
            return new Media[size];
        }
    };
} 