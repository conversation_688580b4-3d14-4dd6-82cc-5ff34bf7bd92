#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 130656 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=15952, tid=33860
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.6+7 (21.0.6+7) (build 21.0.6+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.6+7 (21.0.6+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\508957f1c5a37a88085b1c5d26b946a3\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.41.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\508957f1c5a37a88085b1c5d26b946a3\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-f9f01a72ac5ee10374949fd0b080bdbe-sock

Host: 13th Gen Intel(R) Core(TM) i7-13700H, 20 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3958)
Time: Fri Apr 18 17:05:08 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.3958) elapsed time: 301.969379 seconds (0d 0h 5m 1s)

---------------  T H R E A D  ---------------

Current thread (0x000002e61fcd9390):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=33860, stack(0x000000fa45000000,0x000000fa45100000) (1024K)]


Current CompileTask:
C2:301969 10947       4       org.eclipse.core.internal.dtree.AbstractDataTreeNode::storeStrings (42 bytes)

Stack: [0x000000fa45000000,0x000000fa45100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cdee9]
V  [jvm.dll+0x8a83d1]
V  [jvm.dll+0x8aa8fe]
V  [jvm.dll+0x8aafe3]
V  [jvm.dll+0x27f706]
V  [jvm.dll+0xc500d]
V  [jvm.dll+0xc5543]
V  [jvm.dll+0x2f2acd]
V  [jvm.dll+0x5f6a2a]
V  [jvm.dll+0x250bb2]
V  [jvm.dll+0x24908e]
V  [jvm.dll+0x246ec4]
V  [jvm.dll+0x1c75ee]
V  [jvm.dll+0x25685a]
V  [jvm.dll+0x254dfa]
V  [jvm.dll+0x3f0256]
V  [jvm.dll+0x851f8b]
V  [jvm.dll+0x6cc5ed]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5af08]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002e6205daf50, length=52, elements={
0x000002e5bc6ea840, 0x000002e5bead31e0, 0x000002e5bead3ff0, 0x000002e5bead6830,
0x000002e5bead9680, 0x000002e5beadb1d0, 0x000002e5beadeb20, 0x000002e6153f8ec0,
0x000002e615401da0, 0x000002e6154e7600, 0x000002e617373030, 0x000002e61c57ef30,
0x000002e61c2105f0, 0x000002e61c4177c0, 0x000002e61c418a10, 0x000002e61ca31e50,
0x000002e61cb8b110, 0x000002e6179a43f0, 0x000002e6179a4a80, 0x000002e61cd9e600,
0x000002e61cd9ec90, 0x000002e61cd9f9b0, 0x000002e61cda13f0, 0x000002e61cd9cbc0,
0x000002e61cda0040, 0x000002e61cd9d250, 0x000002e61cda06d0, 0x000002e61cd9df70,
0x000002e61cd9b810, 0x000002e61cd9d8e0, 0x000002e61cda0d60, 0x000002e61cda1a80,
0x000002e61cda2110, 0x000002e61cd9b180, 0x000002e61cd9bea0, 0x000002e61cd9c530,
0x000002e61d577ae0, 0x000002e61d572590, 0x000002e61d572c20, 0x000002e61d5732b0,
0x000002e61d573940, 0x000002e61d574660, 0x000002e61d573fd0, 0x000002e61d574cf0,
0x000002e61d575380, 0x000002e61d578170, 0x000002e61d576730, 0x000002e6179a5110,
0x000002e620819b60, 0x000002e62081b5a0, 0x000002e62081d670, 0x000002e61fcd9390
}

Java Threads: ( => current thread )
  0x000002e5bc6ea840 JavaThread "main"                              [_thread_blocked, id=34640, stack(0x000000fa45200000,0x000000fa45300000) (1024K)]
  0x000002e5bead31e0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=39480, stack(0x000000fa45600000,0x000000fa45700000) (1024K)]
  0x000002e5bead3ff0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=26452, stack(0x000000fa45700000,0x000000fa45800000) (1024K)]
  0x000002e5bead6830 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=27072, stack(0x000000fa45800000,0x000000fa45900000) (1024K)]
  0x000002e5bead9680 JavaThread "Attach Listener"            daemon [_thread_blocked, id=10756, stack(0x000000fa45900000,0x000000fa45a00000) (1024K)]
  0x000002e5beadb1d0 JavaThread "Service Thread"             daemon [_thread_blocked, id=24856, stack(0x000000fa45a00000,0x000000fa45b00000) (1024K)]
  0x000002e5beadeb20 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=28500, stack(0x000000fa45b00000,0x000000fa45c00000) (1024K)]
  0x000002e6153f8ec0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=16004, stack(0x000000fa45c00000,0x000000fa45d00000) (1024K)]
  0x000002e615401da0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=16320, stack(0x000000fa45d00000,0x000000fa45e00000) (1024K)]
  0x000002e6154e7600 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=33620, stack(0x000000fa45e00000,0x000000fa45f00000) (1024K)]
  0x000002e617373030 JavaThread "Notification Thread"        daemon [_thread_blocked, id=28592, stack(0x000000fa46300000,0x000000fa46400000) (1024K)]
  0x000002e61c57ef30 JavaThread "Active Thread: Equinox Container: e55ca40a-4240-4a83-8972-a334b6d50271"        [_thread_blocked, id=11996, stack(0x000000fa46d00000,0x000000fa46e00000) (1024K)]
  0x000002e61c2105f0 JavaThread "Refresh Thread: Equinox Container: e55ca40a-4240-4a83-8972-a334b6d50271" daemon [_thread_blocked, id=5996, stack(0x000000fa46f00000,0x000000fa47000000) (1024K)]
  0x000002e61c4177c0 JavaThread "Framework Event Dispatcher: Equinox Container: e55ca40a-4240-4a83-8972-a334b6d50271" daemon [_thread_blocked, id=27088, stack(0x000000fa47000000,0x000000fa47100000) (1024K)]
  0x000002e61c418a10 JavaThread "Start Level: Equinox Container: e55ca40a-4240-4a83-8972-a334b6d50271" daemon [_thread_blocked, id=14164, stack(0x000000fa47100000,0x000000fa47200000) (1024K)]
  0x000002e61ca31e50 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=14036, stack(0x000000fa46c00000,0x000000fa46d00000) (1024K)]
  0x000002e61cb8b110 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=7580, stack(0x000000fa47500000,0x000000fa47600000) (1024K)]
  0x000002e6179a43f0 JavaThread "Worker-JM"                         [_thread_blocked, id=35924, stack(0x000000fa47900000,0x000000fa47a00000) (1024K)]
  0x000002e6179a4a80 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=30148, stack(0x000000fa47e00000,0x000000fa47f00000) (1024K)]
  0x000002e61cd9e600 JavaThread "Worker-0"                          [_thread_blocked, id=30348, stack(0x000000fa47f00000,0x000000fa48000000) (1024K)]
  0x000002e61cd9ec90 JavaThread "Worker-1"                          [_thread_blocked, id=27212, stack(0x000000fa48000000,0x000000fa48100000) (1024K)]
  0x000002e61cd9f9b0 JavaThread "Java indexing"              daemon [_thread_blocked, id=35404, stack(0x000000fa47a00000,0x000000fa47b00000) (1024K)]
  0x000002e61cda13f0 JavaThread "Thread-2"                   daemon [_thread_in_native, id=35492, stack(0x000000fa48500000,0x000000fa48600000) (1024K)]
  0x000002e61cd9cbc0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=16240, stack(0x000000fa48600000,0x000000fa48700000) (1024K)]
  0x000002e61cda0040 JavaThread "Thread-4"                   daemon [_thread_in_native, id=21508, stack(0x000000fa48700000,0x000000fa48800000) (1024K)]
  0x000002e61cd9d250 JavaThread "Thread-5"                   daemon [_thread_in_native, id=31400, stack(0x000000fa48800000,0x000000fa48900000) (1024K)]
  0x000002e61cda06d0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=39932, stack(0x000000fa48900000,0x000000fa48a00000) (1024K)]
  0x000002e61cd9df70 JavaThread "Thread-7"                   daemon [_thread_in_native, id=14888, stack(0x000000fa48a00000,0x000000fa48b00000) (1024K)]
  0x000002e61cd9b810 JavaThread "Thread-8"                   daemon [_thread_in_native, id=24128, stack(0x000000fa48b00000,0x000000fa48c00000) (1024K)]
  0x000002e61cd9d8e0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=35192, stack(0x000000fa48c00000,0x000000fa48d00000) (1024K)]
  0x000002e61cda0d60 JavaThread "Thread-10"                  daemon [_thread_in_native, id=32216, stack(0x000000fa48d00000,0x000000fa48e00000) (1024K)]
  0x000002e61cda1a80 JavaThread "Thread-11"                  daemon [_thread_in_native, id=3540, stack(0x000000fa48e00000,0x000000fa48f00000) (1024K)]
  0x000002e61cda2110 JavaThread "Thread-12"                  daemon [_thread_in_native, id=18136, stack(0x000000fa48f00000,0x000000fa49000000) (1024K)]
  0x000002e61cd9b180 JavaThread "Thread-13"                  daemon [_thread_in_native, id=21840, stack(0x000000fa49000000,0x000000fa49100000) (1024K)]
  0x000002e61cd9bea0 JavaThread "Thread-14"                  daemon [_thread_in_native, id=22580, stack(0x000000fa49100000,0x000000fa49200000) (1024K)]
  0x000002e61cd9c530 JavaThread "Thread-15"                  daemon [_thread_in_native, id=36536, stack(0x000000fa49200000,0x000000fa49300000) (1024K)]
  0x000002e61d577ae0 JavaThread "Thread-16"                  daemon [_thread_in_native, id=37852, stack(0x000000fa49300000,0x000000fa49400000) (1024K)]
  0x000002e61d572590 JavaThread "Thread-17"                  daemon [_thread_in_native, id=30604, stack(0x000000fa49400000,0x000000fa49500000) (1024K)]
  0x000002e61d572c20 JavaThread "Thread-18"                  daemon [_thread_in_native, id=4412, stack(0x000000fa49500000,0x000000fa49600000) (1024K)]
  0x000002e61d5732b0 JavaThread "Thread-19"                  daemon [_thread_in_native, id=27260, stack(0x000000fa49600000,0x000000fa49700000) (1024K)]
  0x000002e61d573940 JavaThread "Thread-20"                  daemon [_thread_in_native, id=30404, stack(0x000000fa49700000,0x000000fa49800000) (1024K)]
  0x000002e61d574660 JavaThread "Thread-21"                  daemon [_thread_in_native, id=31224, stack(0x000000fa49800000,0x000000fa49900000) (1024K)]
  0x000002e61d573fd0 JavaThread "Thread-22"                  daemon [_thread_in_native, id=38464, stack(0x000000fa49900000,0x000000fa49a00000) (1024K)]
  0x000002e61d574cf0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=3624, stack(0x000000fa49b00000,0x000000fa49c00000) (1024K)]
  0x000002e61d575380 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=19120, stack(0x000000fa49c00000,0x000000fa49d00000) (1024K)]
  0x000002e61d578170 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=16560, stack(0x000000fa49d00000,0x000000fa49e00000) (1024K)]
  0x000002e61d576730 JavaThread "Timer-0"                           [_thread_blocked, id=24456, stack(0x000000fa49e00000,0x000000fa49f00000) (1024K)]
  0x000002e6179a5110 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=35284, stack(0x000000fa47600000,0x000000fa47700000) (1024K)]
  0x000002e620819b60 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=36740, stack(0x000000fa49f00000,0x000000fa4a000000) (1024K)]
  0x000002e62081b5a0 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=2764, stack(0x000000fa4a100000,0x000000fa4a200000) (1024K)]
  0x000002e62081d670 JavaThread "ForkJoinPool.commonPool-worker-7" daemon [_thread_blocked, id=24388, stack(0x000000fa4a400000,0x000000fa4a500000) (1024K)]
=>0x000002e61fcd9390 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=33860, stack(0x000000fa45000000,0x000000fa45100000) (1024K)]
Total: 52

Other Threads:
  0x000002e6153f62a0 VMThread "VM Thread"                           [id=34924, stack(0x000000fa45500000,0x000000fa45600000) (1024K)]
  0x000002e5d2cfcb20 WatcherThread "VM Periodic Task Thread"        [id=28264, stack(0x000000fa45400000,0x000000fa45500000) (1024K)]
  0x000002e5bea4efb0 WorkerThread "GC Thread#0"                     [id=29568, stack(0x000000fa45300000,0x000000fa45400000) (1024K)]
  0x000002e617a75650 WorkerThread "GC Thread#1"                     [id=22052, stack(0x000000fa46500000,0x000000fa46600000) (1024K)]
  0x000002e617a759f0 WorkerThread "GC Thread#2"                     [id=10044, stack(0x000000fa46600000,0x000000fa46700000) (1024K)]
  0x000002e61c2d9b70 WorkerThread "GC Thread#3"                     [id=3536, stack(0x000000fa46700000,0x000000fa46800000) (1024K)]
  0x000002e6179ad4c0 WorkerThread "GC Thread#4"                     [id=25116, stack(0x000000fa46800000,0x000000fa46900000) (1024K)]
  0x000002e6179ad860 WorkerThread "GC Thread#5"                     [id=16328, stack(0x000000fa46900000,0x000000fa46a00000) (1024K)]
  0x000002e6179adc00 WorkerThread "GC Thread#6"                     [id=17444, stack(0x000000fa46a00000,0x000000fa46b00000) (1024K)]
  0x000002e61c2868e0 WorkerThread "GC Thread#7"                     [id=29852, stack(0x000000fa46e00000,0x000000fa46f00000) (1024K)]
  0x000002e61c0d6b50 WorkerThread "GC Thread#8"                     [id=37444, stack(0x000000fa47200000,0x000000fa47300000) (1024K)]
  0x000002e61c0d7d70 WorkerThread "GC Thread#9"                     [id=22712, stack(0x000000fa47300000,0x000000fa47400000) (1024K)]
  0x000002e61c0d84b0 WorkerThread "GC Thread#10"                    [id=37864, stack(0x000000fa47400000,0x000000fa47500000) (1024K)]
  0x000002e61c0d79d0 WorkerThread "GC Thread#11"                    [id=16856, stack(0x000000fa47700000,0x000000fa47800000) (1024K)]
  0x000002e61c0d8110 WorkerThread "GC Thread#12"                    [id=37200, stack(0x000000fa47800000,0x000000fa47900000) (1024K)]
  0x000002e61c82fdd0 WorkerThread "GC Thread#13"                    [id=5432, stack(0x000000fa47c00000,0x000000fa47d00000) (1024K)]
  0x000002e61c831390 WorkerThread "GC Thread#14"                    [id=35536, stack(0x000000fa47d00000,0x000000fa47e00000) (1024K)]
Total: 17

Threads with active compile tasks:
C2 CompilerThread1  301995 10947       4       org.eclipse.core.internal.dtree.AbstractDataTreeNode::storeStrings (42 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002e5d3000000-0x000002e5d3ba0000-0x000002e5d3ba0000), size 12189696, SharedBaseAddress: 0x000002e5d3000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002e5d4000000-0x000002e614000000, reserved size: 1073741824
Narrow klass base: 0x000002e5d3000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 20 total, 20 available
 Memory: 16108M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 15

Heap:
 PSYoungGen      total 6656K, used 2634K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 5632K, 32% used [0x00000000eab00000,0x00000000eacc2b88,0x00000000eb080000)
  from space 1024K, 81% used [0x00000000eb580000,0x00000000eb650020,0x00000000eb680000)
  to   space 1024K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb580000)
 ParOldGen       total 521728K, used 521438K [0x00000000c0000000, 0x00000000dfd80000, 0x00000000eab00000)
  object space 521728K, 99% used [0x00000000c0000000,0x00000000dfd37b98,0x00000000dfd80000)
 Metaspace       used 71661K, committed 73216K, reserved 1114112K
  class space    used 7885K, committed 8576K, reserved 1048576K

Card table byte_map: [0x000002e5be3e0000,0x000002e5be5f0000] _byte_map_base: 0x000002e5bdde0000

Marking Bits: (ParMarkBitMap*) 0x00007ff99caf3260
 Begin Bits: [0x000002e5d0c20000, 0x000002e5d1c20000)
 End Bits:   [0x000002e5d1c20000, 0x000002e5d2c20000)

Polling page: 0x000002e5bc950000

Metaspace:

Usage:
  Non-class:     62.28 MB used.
      Class:      7.70 MB used.
       Both:     69.98 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      63.12 MB ( 99%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       8.38 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      71.50 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  176.00 KB
       Class:  7.52 MB
        Both:  7.70 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1284.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1143.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 23.
num_chunks_taken_from_freelist: 4391.
num_chunk_merges: 13.
num_chunk_splits: 2869.
num_chunks_enlarged: 1855.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=8932Kb max_used=8932Kb free=110235Kb
 bounds [0x000002e5c97c0000, 0x000002e5ca080000, 0x000002e5d0c20000]
CodeHeap 'profiled nmethods': size=119104Kb used=22879Kb max_used=22879Kb free=96224Kb
 bounds [0x000002e5c1c20000, 0x000002e5c3280000, 0x000002e5c9070000]
CodeHeap 'non-nmethods': size=7488Kb used=1454Kb max_used=3199Kb free=6033Kb
 bounds [0x000002e5c9070000, 0x000002e5c93a0000, 0x000002e5c97c0000]
 total_blobs=10868 nmethods=10095 adapters=678
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 253.381 Thread 0x000002e615401da0 10937       3       java.util.concurrent.ThreadPoolExecutor::runStateAtLeast (11 bytes)
Event: 253.381 Thread 0x000002e615401da0 nmethod 10937 0x000002e5c3275110 code [0x000002e5c32752a0, 0x000002e5c32753e0]
Event: 270.751 Thread 0x000002e615401da0 10938   !   3       java.util.concurrent.locks.LockSupport::parkNanos (56 bytes)
Event: 270.751 Thread 0x000002e615401da0 nmethod 10938 0x000002e5c3275490 code [0x000002e5c32756a0, 0x000002e5c3275ae0]
Event: 301.959 Thread 0x000002e6153f8ec0 10939       4       java.util.HashMap::putIfAbsent (13 bytes)
Event: 301.959 Thread 0x000002e615401da0 10940       3       org.eclipse.core.internal.utils.StringPool::add (50 bytes)
Event: 301.959 Thread 0x000002e615401da0 nmethod 10940 0x000002e5c3275c90 code [0x000002e5c3275e80, 0x000002e5c3276350]
Event: 301.959 Thread 0x000002e615401da0 10942       3       java.lang.String::hashCode (60 bytes)
Event: 301.959 Thread 0x000002e615401da0 nmethod 10942 0x000002e5c3276510 code [0x000002e5c32766e0, 0x000002e5c3276a20]
Event: 301.959 Thread 0x000002e615401da0 10941       3       org.eclipse.core.internal.dtree.AbstractDataTreeNode::storeStrings (42 bytes)
Event: 301.959 Thread 0x000002e615401da0 nmethod 10941 0x000002e5c3276b10 code [0x000002e5c3276ce0, 0x000002e5c3277058]
Event: 301.959 Thread 0x000002e615401da0 10943       3       org.eclipse.core.internal.dtree.DataTreeNode::storeStrings (28 bytes)
Event: 301.959 Thread 0x000002e615401da0 nmethod 10943 0x000002e5c3277210 code [0x000002e5c32773e0, 0x000002e5c3277860]
Event: 301.959 Thread 0x000002e615401da0 10944       3       org.eclipse.core.internal.resources.ResourceInfo::shareStrings (43 bytes)
Event: 301.959 Thread 0x000002e615401da0 nmethod 10944 0x000002e5c3277990 code [0x000002e5c3277b60, 0x000002e5c3277e28]
Event: 301.964 Thread 0x000002e6153f8ec0 nmethod 10939 0x000002e5ca075c10 code [0x000002e5ca075e20, 0x000002e5ca076608]
Event: 301.964 Thread 0x000002e6153f8ec0 10945       4       java.lang.String::hashCode (60 bytes)
Event: 301.964 Thread 0x000002e61fcd9390 10947       4       org.eclipse.core.internal.dtree.AbstractDataTreeNode::storeStrings (42 bytes)
Event: 301.964 Thread 0x000002e6153f8ec0 nmethod 10945 0x000002e5ca076c10 code [0x000002e5ca076de0, 0x000002e5ca077118]
Event: 301.964 Thread 0x000002e6153f8ec0 10946       4       org.eclipse.core.internal.utils.StringPool::add (50 bytes)

GC Heap History (20 events):
Event: 9.925 GC heap before
{Heap before GC invocations=464 (full 3):
 PSYoungGen      total 5632K, used 5344K [0x00000000eab00000, 0x00000000eb300000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000eab00000,0x00000000eae00000,0x00000000eae00000)
  from space 2560K, 88% used [0x00000000eb080000,0x00000000eb2b8000,0x00000000eb300000)
  to   space 2560K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eb080000)
 ParOldGen       total 494080K, used 493779K [0x00000000c0000000, 0x00000000de280000, 0x00000000eab00000)
  object space 494080K, 99% used [0x00000000c0000000,0x00000000de234d48,0x00000000de280000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.926 GC heap after
{Heap after GC invocations=464 (full 3):
 PSYoungGen      total 5632K, used 2528K [0x00000000eab00000, 0x00000000eb500000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eae00000)
  from space 2560K, 98% used [0x00000000eae00000,0x00000000eb078000,0x00000000eb080000)
  to   space 3072K, 0% used [0x00000000eb200000,0x00000000eb200000,0x00000000eb500000)
 ParOldGen       total 496128K, used 495851K [0x00000000c0000000, 0x00000000de480000, 0x00000000eab00000)
  object space 496128K, 99% used [0x00000000c0000000,0x00000000de43ad48,0x00000000de480000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.930 GC heap before
{Heap before GC invocations=465 (full 3):
 PSYoungGen      total 5632K, used 5600K [0x00000000eab00000, 0x00000000eb500000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000eab00000,0x00000000eae00000,0x00000000eae00000)
  from space 2560K, 98% used [0x00000000eae00000,0x00000000eb078000,0x00000000eb080000)
  to   space 3072K, 0% used [0x00000000eb200000,0x00000000eb200000,0x00000000eb500000)
 ParOldGen       total 496128K, used 495851K [0x00000000c0000000, 0x00000000de480000, 0x00000000eab00000)
  object space 496128K, 99% used [0x00000000c0000000,0x00000000de43ad48,0x00000000de480000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.932 GC heap after
{Heap after GC invocations=465 (full 3):
 PSYoungGen      total 7168K, used 2592K [0x00000000eab00000, 0x00000000eb500000, 0x0000000100000000)
  eden space 4096K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eaf00000)
  from space 3072K, 84% used [0x00000000eb200000,0x00000000eb488000,0x00000000eb500000)
  to   space 3072K, 0% used [0x00000000eaf00000,0x00000000eaf00000,0x00000000eb200000)
 ParOldGen       total 498688K, used 498243K [0x00000000c0000000, 0x00000000de700000, 0x00000000eab00000)
  object space 498688K, 99% used [0x00000000c0000000,0x00000000de690d48,0x00000000de700000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.939 GC heap before
{Heap before GC invocations=466 (full 3):
 PSYoungGen      total 7168K, used 6688K [0x00000000eab00000, 0x00000000eb500000, 0x0000000100000000)
  eden space 4096K, 100% used [0x00000000eab00000,0x00000000eaf00000,0x00000000eaf00000)
  from space 3072K, 84% used [0x00000000eb200000,0x00000000eb488000,0x00000000eb500000)
  to   space 3072K, 0% used [0x00000000eaf00000,0x00000000eaf00000,0x00000000eb200000)
 ParOldGen       total 498688K, used 498243K [0x00000000c0000000, 0x00000000de700000, 0x00000000eab00000)
  object space 498688K, 99% used [0x00000000c0000000,0x00000000de690d48,0x00000000de700000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.941 GC heap after
{Heap after GC invocations=466 (full 3):
 PSYoungGen      total 7168K, used 3072K [0x00000000eab00000, 0x00000000ebc00000, 0x0000000100000000)
  eden space 4096K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eaf00000)
  from space 3072K, 100% used [0x00000000eaf00000,0x00000000eb200000,0x00000000eb200000)
  to   space 6144K, 0% used [0x00000000eb600000,0x00000000eb600000,0x00000000ebc00000)
 ParOldGen       total 501248K, used 500787K [0x00000000c0000000, 0x00000000de980000, 0x00000000eab00000)
  object space 501248K, 99% used [0x00000000c0000000,0x00000000de90cd48,0x00000000de980000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.947 GC heap before
{Heap before GC invocations=467 (full 3):
 PSYoungGen      total 7168K, used 7168K [0x00000000eab00000, 0x00000000ebc00000, 0x0000000100000000)
  eden space 4096K, 100% used [0x00000000eab00000,0x00000000eaf00000,0x00000000eaf00000)
  from space 3072K, 100% used [0x00000000eaf00000,0x00000000eb200000,0x00000000eb200000)
  to   space 6144K, 0% used [0x00000000eb600000,0x00000000eb600000,0x00000000ebc00000)
 ParOldGen       total 501248K, used 500787K [0x00000000c0000000, 0x00000000de980000, 0x00000000eab00000)
  object space 501248K, 99% used [0x00000000c0000000,0x00000000de90cd48,0x00000000de980000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.949 GC heap after
{Heap after GC invocations=467 (full 3):
 PSYoungGen      total 8704K, used 3264K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 5120K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb000000)
  from space 3584K, 91% used [0x00000000eb600000,0x00000000eb930000,0x00000000eb980000)
  to   space 4608K, 0% used [0x00000000eb080000,0x00000000eb080000,0x00000000eb500000)
 ParOldGen       total 504320K, used 503859K [0x00000000c0000000, 0x00000000dec80000, 0x00000000eab00000)
  object space 504320K, 99% used [0x00000000c0000000,0x00000000dec0cd48,0x00000000dec80000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.956 GC heap before
{Heap before GC invocations=468 (full 3):
 PSYoungGen      total 8704K, used 8384K [0x00000000eab00000, 0x00000000eb980000, 0x0000000100000000)
  eden space 5120K, 100% used [0x00000000eab00000,0x00000000eb000000,0x00000000eb000000)
  from space 3584K, 91% used [0x00000000eb600000,0x00000000eb930000,0x00000000eb980000)
  to   space 4608K, 0% used [0x00000000eb080000,0x00000000eb080000,0x00000000eb500000)
 ParOldGen       total 504320K, used 503859K [0x00000000c0000000, 0x00000000dec80000, 0x00000000eab00000)
  object space 504320K, 99% used [0x00000000c0000000,0x00000000dec0cd48,0x00000000dec80000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.959 GC heap after
{Heap after GC invocations=468 (full 3):
 PSYoungGen      total 10240K, used 4128K [0x00000000eab00000, 0x00000000eba00000, 0x0000000100000000)
  eden space 5632K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb080000)
  from space 4608K, 89% used [0x00000000eb080000,0x00000000eb488000,0x00000000eb500000)
  to   space 4608K, 0% used [0x00000000eb580000,0x00000000eb580000,0x00000000eba00000)
 ParOldGen       total 507392K, used 507027K [0x00000000c0000000, 0x00000000def80000, 0x00000000eab00000)
  object space 507392K, 99% used [0x00000000c0000000,0x00000000def24d48,0x00000000def80000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.966 GC heap before
{Heap before GC invocations=469 (full 3):
 PSYoungGen      total 10240K, used 9760K [0x00000000eab00000, 0x00000000eba00000, 0x0000000100000000)
  eden space 5632K, 100% used [0x00000000eab00000,0x00000000eb080000,0x00000000eb080000)
  from space 4608K, 89% used [0x00000000eb080000,0x00000000eb488000,0x00000000eb500000)
  to   space 4608K, 0% used [0x00000000eb580000,0x00000000eb580000,0x00000000eba00000)
 ParOldGen       total 507392K, used 507027K [0x00000000c0000000, 0x00000000def80000, 0x00000000eab00000)
  object space 507392K, 99% used [0x00000000c0000000,0x00000000def24d48,0x00000000def80000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.968 GC heap after
{Heap after GC invocations=469 (full 3):
 PSYoungGen      total 10240K, used 4544K [0x00000000eab00000, 0x00000000ebc00000, 0x0000000100000000)
  eden space 5632K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb080000)
  from space 4608K, 98% used [0x00000000eb580000,0x00000000eb9f0000,0x00000000eba00000)
  to   space 5120K, 0% used [0x00000000eb080000,0x00000000eb080000,0x00000000eb580000)
 ParOldGen       total 511488K, used 510987K [0x00000000c0000000, 0x00000000df380000, 0x00000000eab00000)
  object space 511488K, 99% used [0x00000000c0000000,0x00000000df302d48,0x00000000df380000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.978 GC heap before
{Heap before GC invocations=470 (full 3):
 PSYoungGen      total 10240K, used 10176K [0x00000000eab00000, 0x00000000ebc00000, 0x0000000100000000)
  eden space 5632K, 100% used [0x00000000eab00000,0x00000000eb080000,0x00000000eb080000)
  from space 4608K, 98% used [0x00000000eb580000,0x00000000eb9f0000,0x00000000eba00000)
  to   space 5120K, 0% used [0x00000000eb080000,0x00000000eb080000,0x00000000eb580000)
 ParOldGen       total 511488K, used 510987K [0x00000000c0000000, 0x00000000df380000, 0x00000000eab00000)
  object space 511488K, 99% used [0x00000000c0000000,0x00000000df302d48,0x00000000df380000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 9.980 GC heap after
{Heap after GC invocations=470 (full 3):
 PSYoungGen      total 10752K, used 4544K [0x00000000eab00000, 0x00000000ebb00000, 0x0000000100000000)
  eden space 5632K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb080000)
  from space 5120K, 88% used [0x00000000eb080000,0x00000000eb4f0000,0x00000000eb580000)
  to   space 4608K, 0% used [0x00000000eb680000,0x00000000eb680000,0x00000000ebb00000)
 ParOldGen       total 515584K, used 515387K [0x00000000c0000000, 0x00000000df780000, 0x00000000eab00000)
  object space 515584K, 99% used [0x00000000c0000000,0x00000000df74ed48,0x00000000df780000)
 Metaspace       used 70748K, committed 72192K, reserved 1114112K
  class space    used 7830K, committed 8448K, reserved 1048576K
}
Event: 30.340 GC heap before
{Heap before GC invocations=471 (full 3):
 PSYoungGen      total 10752K, used 10152K [0x00000000eab00000, 0x00000000ebb00000, 0x0000000100000000)
  eden space 5632K, 99% used [0x00000000eab00000,0x00000000eb07a1a0,0x00000000eb080000)
  from space 5120K, 88% used [0x00000000eb080000,0x00000000eb4f0000,0x00000000eb580000)
  to   space 4608K, 0% used [0x00000000eb680000,0x00000000eb680000,0x00000000ebb00000)
 ParOldGen       total 515584K, used 515387K [0x00000000c0000000, 0x00000000df780000, 0x00000000eab00000)
  object space 515584K, 99% used [0x00000000c0000000,0x00000000df74ed48,0x00000000df780000)
 Metaspace       used 71100K, committed 72576K, reserved 1114112K
  class space    used 7864K, committed 8512K, reserved 1048576K
}
Event: 30.342 GC heap after
{Heap after GC invocations=471 (full 3):
 PSYoungGen      total 7680K, used 1795K [0x00000000eab00000, 0x00000000eb880000, 0x0000000100000000)
  eden space 5632K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb080000)
  from space 2048K, 87% used [0x00000000eb680000,0x00000000eb840e50,0x00000000eb880000)
  to   space 3072K, 0% used [0x00000000eb280000,0x00000000eb280000,0x00000000eb580000)
 ParOldGen       total 520192K, used 519771K [0x00000000c0000000, 0x00000000dfc00000, 0x00000000eab00000)
  object space 520192K, 99% used [0x00000000c0000000,0x00000000dfb96d48,0x00000000dfc00000)
 Metaspace       used 71100K, committed 72576K, reserved 1114112K
  class space    used 7864K, committed 8512K, reserved 1048576K
}
Event: 30.549 GC heap before
{Heap before GC invocations=472 (full 3):
 PSYoungGen      total 7680K, used 7427K [0x00000000eab00000, 0x00000000eb880000, 0x0000000100000000)
  eden space 5632K, 100% used [0x00000000eab00000,0x00000000eb080000,0x00000000eb080000)
  from space 2048K, 87% used [0x00000000eb680000,0x00000000eb840e50,0x00000000eb880000)
  to   space 3072K, 0% used [0x00000000eb280000,0x00000000eb280000,0x00000000eb580000)
 ParOldGen       total 520192K, used 519771K [0x00000000c0000000, 0x00000000dfc00000, 0x00000000eab00000)
  object space 520192K, 99% used [0x00000000c0000000,0x00000000dfb96d48,0x00000000dfc00000)
 Metaspace       used 71324K, committed 72832K, reserved 1114112K
  class space    used 7873K, committed 8512K, reserved 1048576K
}
Event: 30.551 GC heap after
{Heap after GC invocations=472 (full 3):
 PSYoungGen      total 8704K, used 739K [0x00000000eab00000, 0x00000000eb700000, 0x0000000100000000)
  eden space 5632K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb080000)
  from space 3072K, 24% used [0x00000000eb280000,0x00000000eb338e30,0x00000000eb580000)
  to   space 1536K, 0% used [0x00000000eb580000,0x00000000eb580000,0x00000000eb700000)
 ParOldGen       total 521728K, used 521326K [0x00000000c0000000, 0x00000000dfd80000, 0x00000000eab00000)
  object space 521728K, 99% used [0x00000000c0000000,0x00000000dfd1bb98,0x00000000dfd80000)
 Metaspace       used 71324K, committed 72832K, reserved 1114112K
  class space    used 7873K, committed 8512K, reserved 1048576K
}
Event: 30.881 GC heap before
{Heap before GC invocations=473 (full 3):
 PSYoungGen      total 8704K, used 6353K [0x00000000eab00000, 0x00000000eb700000, 0x0000000100000000)
  eden space 5632K, 99% used [0x00000000eab00000,0x00000000eb07b858,0x00000000eb080000)
  from space 3072K, 24% used [0x00000000eb280000,0x00000000eb338e30,0x00000000eb580000)
  to   space 1536K, 0% used [0x00000000eb580000,0x00000000eb580000,0x00000000eb700000)
 ParOldGen       total 521728K, used 521326K [0x00000000c0000000, 0x00000000dfd80000, 0x00000000eab00000)
  object space 521728K, 99% used [0x00000000c0000000,0x00000000dfd1bb98,0x00000000dfd80000)
 Metaspace       used 71622K, committed 73152K, reserved 1114112K
  class space    used 7884K, committed 8576K, reserved 1048576K
}
Event: 30.883 GC heap after
{Heap after GC invocations=473 (full 3):
 PSYoungGen      total 6656K, used 832K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 5632K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb080000)
  from space 1024K, 81% used [0x00000000eb580000,0x00000000eb650020,0x00000000eb680000)
  to   space 1024K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb580000)
 ParOldGen       total 521728K, used 521438K [0x00000000c0000000, 0x00000000dfd80000, 0x00000000eab00000)
  object space 521728K, 99% used [0x00000000c0000000,0x00000000dfd37b98,0x00000000dfd80000)
 Metaspace       used 71622K, committed 73152K, reserved 1114112K
  class space    used 7884K, committed 8576K, reserved 1048576K
}

Dll operation events (13 events):
Event: 0.011 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
Event: 0.031 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.081 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
Event: 0.084 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
Event: 0.086 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
Event: 0.088 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
Event: 0.098 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
Event: 0.149 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
Event: 0.819 Loaded shared library C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 1.912 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-1543076575\jna12210080934692186412.dll
Event: 5.762 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 6.431 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management.dll
Event: 6.434 Loaded shared library C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management_ext.dll

Deoptimization events (20 events):
Event: 30.677 Thread 0x000002e61c57ef30 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002e5c9c094fc relative=0x00000000000001bc
Event: 30.677 Thread 0x000002e61c57ef30 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002e5c9c094fc method=java.util.ArrayList.hashCodeRange(II)I @ 53 c2
Event: 30.677 Thread 0x000002e61c57ef30 DEOPT PACKING pc=0x000002e5c9c094fc sp=0x000000fa46dfed70
Event: 30.677 Thread 0x000002e61c57ef30 DEOPT UNPACKING pc=0x000002e5c90c3aa2 sp=0x000000fa46dfec80 mode 2
Event: 30.677 Thread 0x000002e61c57ef30 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002e5c9c094fc relative=0x00000000000001bc
Event: 30.677 Thread 0x000002e61c57ef30 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002e5c9c094fc method=java.util.ArrayList.hashCodeRange(II)I @ 53 c2
Event: 30.677 Thread 0x000002e61c57ef30 DEOPT PACKING pc=0x000002e5c9c094fc sp=0x000000fa46dfedb0
Event: 30.677 Thread 0x000002e61c57ef30 DEOPT UNPACKING pc=0x000002e5c90c3aa2 sp=0x000000fa46dfecc0 mode 2
Event: 30.677 Thread 0x000002e61c57ef30 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002e5c9c094fc relative=0x00000000000001bc
Event: 30.677 Thread 0x000002e61c57ef30 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002e5c9c094fc method=java.util.ArrayList.hashCodeRange(II)I @ 53 c2
Event: 30.677 Thread 0x000002e61c57ef30 DEOPT PACKING pc=0x000002e5c9c094fc sp=0x000000fa46dfedb0
Event: 30.677 Thread 0x000002e61c57ef30 DEOPT UNPACKING pc=0x000002e5c90c3aa2 sp=0x000000fa46dfecc0 mode 2
Event: 30.677 Thread 0x000002e61c57ef30 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002e5c9b6ba3c relative=0x000000000000017c
Event: 30.677 Thread 0x000002e61c57ef30 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002e5c9b6ba3c method=java.util.ArrayList.hashCodeRange(II)I @ 53 c2
Event: 30.677 Thread 0x000002e61c57ef30 DEOPT PACKING pc=0x000002e5c9b6ba3c sp=0x000000fa46dfed30
Event: 30.677 Thread 0x000002e61c57ef30 DEOPT UNPACKING pc=0x000002e5c90c3aa2 sp=0x000000fa46dfecb8 mode 2
Event: 301.959 Thread 0x000002e61cd9e600 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002e5c97ca6d4 relative=0x00000000000000b4
Event: 301.959 Thread 0x000002e61cd9e600 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002e5c97ca6d4 method=java.lang.String.hashCode()I @ 20 c2
Event: 301.959 Thread 0x000002e61cd9e600 DEOPT PACKING pc=0x000002e5c97ca6d4 sp=0x000000fa47ffed10
Event: 301.959 Thread 0x000002e61cd9e600 DEOPT UNPACKING pc=0x000002e5c90c3aa2 sp=0x000000fa47ffeca0 mode 2

Classes loaded (20 events):
Event: 30.702 Loading class sun/util/locale/provider/TimeZoneNameProviderImpl done
Event: 30.702 Loading class sun/util/cldr/CLDRTimeZoneNameProviderImpl done
Event: 30.703 Loading class sun/util/locale/provider/BaseLocaleDataMetaInfo
Event: 30.703 Loading class sun/util/locale/provider/BaseLocaleDataMetaInfo done
Event: 30.704 Loading class sun/util/resources/cldr/TimeZoneNames
Event: 30.704 Loading class sun/util/resources/TimeZoneNamesBundle
Event: 30.704 Loading class sun/util/resources/OpenListResourceBundle
Event: 30.704 Loading class sun/util/resources/OpenListResourceBundle done
Event: 30.704 Loading class sun/util/resources/TimeZoneNamesBundle done
Event: 30.704 Loading class sun/util/resources/cldr/TimeZoneNames done
Event: 30.704 Loading class sun/util/resources/cldr/TimeZoneNames_en
Event: 30.705 Loading class sun/util/resources/cldr/TimeZoneNames_en done
Event: 30.705 Loading class sun/util/resources/cldr/TimeZoneNames_en_US
Event: 30.705 Loading class sun/util/resources/cldr/TimeZoneNames_en_US done
Event: 30.706 Loading class sun/util/cldr/CLDRBaseLocaleDataMetaInfo$TZCanonicalIDMapHolder
Event: 30.706 Loading class sun/util/cldr/CLDRBaseLocaleDataMetaInfo$TZCanonicalIDMapHolder done
Event: 30.707 Loading class sun/util/resources/TimeZoneNames
Event: 30.708 Loading class sun/util/resources/TimeZoneNames done
Event: 30.708 Loading class sun/util/resources/TimeZoneNames_en
Event: 30.708 Loading class sun/util/resources/TimeZoneNames_en done

Classes unloaded (7 events):
Event: 2.279 Thread 0x000002e6153f62a0 Unloading class 0x000002e5d4195000 'java/lang/invoke/LambdaForm$MH+0x000002e5d4195000'
Event: 2.279 Thread 0x000002e6153f62a0 Unloading class 0x000002e5d4194c00 'java/lang/invoke/LambdaForm$MH+0x000002e5d4194c00'
Event: 2.279 Thread 0x000002e6153f62a0 Unloading class 0x000002e5d4194800 'java/lang/invoke/LambdaForm$MH+0x000002e5d4194800'
Event: 2.279 Thread 0x000002e6153f62a0 Unloading class 0x000002e5d4194400 'java/lang/invoke/LambdaForm$MH+0x000002e5d4194400'
Event: 2.279 Thread 0x000002e6153f62a0 Unloading class 0x000002e5d4194000 'java/lang/invoke/LambdaForm$BMH+0x000002e5d4194000'
Event: 2.279 Thread 0x000002e6153f62a0 Unloading class 0x000002e5d4193c00 'java/lang/invoke/LambdaForm$DMH+0x000002e5d4193c00'
Event: 2.279 Thread 0x000002e6153f62a0 Unloading class 0x000002e5d4192c00 'java/lang/invoke/LambdaForm$DMH+0x000002e5d4192c00'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 8.032 Thread 0x000002e61cd9aaf0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac22c58}> (0x00000000eac22c58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 8.300 Thread 0x000002e61cd9f320 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf29d20}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, long)'> (0x00000000eaf29d20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.316 Thread 0x000002e61d578170 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eac6b008}: Found class java.lang.Object, but interface was expected> (0x00000000eac6b008) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 8.350 Thread 0x000002e620819b60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eabe7c98}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x00000000eabe7c98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.378 Thread 0x000002e620819b60 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eaf38130}: Found class java.lang.Object, but interface was expected> (0x00000000eaf38130) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 8.441 Thread 0x000002e620819b60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ead194b8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ead194b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 9.626 Thread 0x000002e61cd9f9b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab03050}> (0x00000000eab03050) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 9.745 Thread 0x000002e620817a90 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ead61358}> (0x00000000ead61358) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 9.745 Thread 0x000002e620817a90 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ead61c58}> (0x00000000ead61c58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 9.748 Thread 0x000002e620817a90 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab0b4e8}> (0x00000000eab0b4e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 9.748 Thread 0x000002e620817a90 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab0bdc8}> (0x00000000eab0bdc8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 9.748 Thread 0x000002e620817a90 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab0d4b8}> (0x00000000eab0d4b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 9.748 Thread 0x000002e620817a90 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab0dd98}> (0x00000000eab0dd98) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 9.765 Thread 0x000002e62081a1f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac73198}> (0x00000000eac73198) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 10.155 Thread 0x000002e61d578170 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eacc7220}: Found class java.lang.Object, but interface was expected> (0x00000000eacc7220) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 11.865 Thread 0x000002e61d578170 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eae168f0}: Found class java.lang.Object, but interface was expected> (0x00000000eae168f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 30.333 Thread 0x000002e61d578170 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eafd2b08}: Found class java.lang.Object, but interface was expected> (0x00000000eafd2b08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 30.337 Thread 0x000002e6208194d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb016fb0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000eb016fb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 30.337 Thread 0x000002e6208194d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb01b160}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000eb01b160) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 30.337 Thread 0x000002e6208194d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb01eb90}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eb01eb90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 11.985 Executing VM operation: Cleanup
Event: 11.985 Executing VM operation: Cleanup done
Event: 13.993 Executing VM operation: Cleanup
Event: 13.993 Executing VM operation: Cleanup done
Event: 30.064 Executing VM operation: Cleanup
Event: 30.064 Executing VM operation: Cleanup done
Event: 30.340 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 30.342 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 30.549 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 30.551 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 30.881 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 30.883 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 31.884 Executing VM operation: Cleanup
Event: 31.884 Executing VM operation: Cleanup done
Event: 32.885 Executing VM operation: Cleanup
Event: 32.885 Executing VM operation: Cleanup done
Event: 60.070 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 60.070 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 60.070 Executing VM operation: RendezvousGCThreads
Event: 60.070 Executing VM operation: RendezvousGCThreads done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c261ff90
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2620490
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2620890
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2621b90
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2622890
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2623d90
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2625890
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2626690
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2628910
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c262e510
Event: 6.573 Thread 0x000002e6153f62a0 flushing osr nmethod 0x000002e5c2633890
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2639f90
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c263ba90
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2640d10
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2641710
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2641b90
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2644310
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c2644690
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c264e510
Event: 6.573 Thread 0x000002e6153f62a0 flushing  nmethod 0x000002e5c268d810

Events (20 events):
Event: 30.524 Thread 0x000002e6153f8ec0 Thread added: 0x000002e61fcd5d10
Event: 30.540 Thread 0x000002e61fcd9390 Thread added: 0x000002e61fcd85f0
Event: 30.674 Thread 0x000002e61fcd5d10 Thread exited: 0x000002e61fcd5d10
Event: 30.678 Thread 0x000002e61fcd85f0 Thread added: 0x000002e61fcd5d10
Event: 30.883 Thread 0x000002e61fcd5d10 Thread exited: 0x000002e61fcd5d10
Event: 30.886 Thread 0x000002e61fcd9390 Thread added: 0x000002e61fcd5d10
Event: 31.248 Thread 0x000002e61fcd5d10 Thread exited: 0x000002e61fcd5d10
Event: 33.224 Thread 0x000002e61fcd85f0 Thread exited: 0x000002e61fcd85f0
Event: 42.861 Thread 0x000002e61fcd9390 Thread exited: 0x000002e61fcd9390
Event: 66.908 Thread 0x000002e61d571870 Thread exited: 0x000002e61d571870
Event: 66.908 Thread 0x000002e61d571f00 Thread exited: 0x000002e61d571f00
Event: 66.914 Thread 0x000002e61d578800 Thread exited: 0x000002e61d578800
Event: 69.920 Thread 0x000002e61cd9f320 Thread exited: 0x000002e61cd9f320
Event: 69.920 Thread 0x000002e61d575a10 Thread exited: 0x000002e61d575a10
Event: 69.920 Thread 0x000002e61cd9aaf0 Thread exited: 0x000002e61cd9aaf0
Event: 92.425 Thread 0x000002e6208194d0 Thread exited: 0x000002e6208194d0
Event: 152.559 Thread 0x000002e620818e40 Thread exited: 0x000002e620818e40
Event: 212.695 Thread 0x000002e620817a90 Thread exited: 0x000002e620817a90
Event: 272.828 Thread 0x000002e62081af10 Thread exited: 0x000002e62081af10
Event: 301.964 Thread 0x000002e6153f8ec0 Thread added: 0x000002e61fcd9390


Dynamic libraries:
0x00007ff753ed0000 - 0x00007ff753ede000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.exe
0x00007ffa533b0000 - 0x00007ffa535c7000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa513c0000 - 0x00007ffa51484000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa50cc0000 - 0x00007ffa51077000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa52410000 - 0x00007ffa524c2000 	C:\Windows\System32\ADVAPI32.DLL
0x00007ffa530c0000 - 0x00007ffa53167000 	C:\Windows\System32\msvcrt.dll
0x00007ffa51a00000 - 0x00007ffa51aa8000 	C:\Windows\System32\sechost.dll
0x00007ffa510b0000 - 0x00007ffa510d8000 	C:\Windows\System32\bcrypt.dll
0x00007ffa52fa0000 - 0x00007ffa530b4000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa506c0000 - 0x00007ffa507d1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa376b0000 - 0x00007ffa376c8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jli.dll
0x00007ffa376e0000 - 0x00007ffa376fe000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffa53170000 - 0x00007ffa5331f000 	C:\Windows\System32\USER32.dll
0x00007ffa51080000 - 0x00007ffa510a6000 	C:\Windows\System32\win32u.dll
0x00007ffa52280000 - 0x00007ffa522a9000 	C:\Windows\System32\GDI32.dll
0x00007ffa507e0000 - 0x00007ffa508f8000 	C:\Windows\System32\gdi32full.dll
0x00007ffa2aee0000 - 0x00007ffa2b173000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3672_none_2713b9d173822955\COMCTL32.dll
0x00007ffa50a30000 - 0x00007ffa50aca000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa52150000 - 0x00007ffa52181000 	C:\Windows\System32\IMM32.DLL
0x00007ffa48ab0000 - 0x00007ffa48abc000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffa0c5e0000 - 0x00007ffa0c66d000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\msvcp140.dll
0x00007ff99be40000 - 0x00007ff99cbd0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server\jvm.dll
0x00007ffa51c90000 - 0x00007ffa51d01000 	C:\Windows\System32\WS2_32.dll
0x00007ffa4f570000 - 0x00007ffa4f5bd000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa45fc0000 - 0x00007ffa45fca000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa489b0000 - 0x00007ffa489e4000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa4f550000 - 0x00007ffa4f563000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa4f800000 - 0x00007ffa4f818000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa46660000 - 0x00007ffa4666a000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\jimage.dll
0x00007ffa4de20000 - 0x00007ffa4e052000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa51d90000 - 0x00007ffa5211e000 	C:\Windows\System32\combase.dll
0x00007ffa51490000 - 0x00007ffa51567000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa44060000 - 0x00007ffa44092000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa50c40000 - 0x00007ffa50cbb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa37c20000 - 0x00007ffa37c2f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\instrument.dll
0x00007ffa370b0000 - 0x00007ffa370cf000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\java.dll
0x00007ffa525d0000 - 0x00007ffa52e39000 	C:\Windows\System32\SHELL32.dll
0x00007ffa4e590000 - 0x00007ffa4ee8f000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa4e420000 - 0x00007ffa4e55f000 	C:\Windows\SYSTEM32\wintypes.dll
0x00007ffa524d0000 - 0x00007ffa525c9000 	C:\Windows\System32\SHCORE.dll
0x00007ffa52190000 - 0x00007ffa521ee000 	C:\Windows\System32\shlwapi.dll
0x00007ffa505f0000 - 0x00007ffa50617000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa36e10000 - 0x00007ffa36e28000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\zip.dll
0x00007ffa464f0000 - 0x00007ffa46500000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\net.dll
0x00007ffa4a000000 - 0x00007ffa4a136000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffa4fc70000 - 0x00007ffa4fcd9000 	C:\Windows\system32\mswsock.dll
0x00007ffa2a6f0000 - 0x00007ffa2a706000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\nio.dll
0x00007ffa37ce0000 - 0x00007ffa37cf0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\verify.dll
0x00007ffa15930000 - 0x00007ffa15975000 	C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffa51ac0000 - 0x00007ffa51c65000 	C:\Windows\System32\ole32.dll
0x00007ffa4fec0000 - 0x00007ffa4fedb000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffa4f760000 - 0x00007ffa4f795000 	C:\Windows\system32\rsaenh.dll
0x00007ffa4fd60000 - 0x00007ffa4fd88000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffa4fee0000 - 0x00007ffa4feec000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffa4efb0000 - 0x00007ffa4efdd000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffa52120000 - 0x00007ffa52129000 	C:\Windows\System32\NSI.dll
0x00007ff9ee750000 - 0x00007ff9ee799000 	C:\Users\<USER>\AppData\Local\Temp\jna-1543076575\jna12210080934692186412.dll
0x00007ffa519f0000 - 0x00007ffa519f8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffa49e90000 - 0x00007ffa49ea9000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa49eb0000 - 0x00007ffa49ecf000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ff9e8cd0000 - 0x00007ff9e8cf7000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffa3be10000 - 0x00007ffa3be1a000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management.dll
0x00007ffa3b080000 - 0x00007ffa3b08b000 	C:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\management_ext.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3672_none_2713b9d173822955;c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\jre\21.0.6-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.41.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-1543076575;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\508957f1c5a37a88085b1c5d26b946a3\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.41.1\config_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\508957f1c5a37a88085b1c5d26b946a3\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-f9f01a72ac5ee10374949fd0b080bdbe-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.41.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250331-1702.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\508957f1c5a37a88085b1c5d26b946a3\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
CLASSPATH=%JAVA HOME%\lib
PATH=E:\Program Files (x86)\VMware\VMware Workstation\bin\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Java\jdk-1.8\bin;E:\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-1.8\bin\server;C:\Program Files\Pandoc\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;E:\Program Files\JetBrains\IntelliJ IDEA 2024.3.4\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\nodejs\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;E:\00Tools\apktool;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;E:\Program Files\JetBrains\IntelliJ IDEA 2024.3.4\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=498475
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 186 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3958)
OS uptime: 1 days 7:02 hours

CPU: total 20 (initial active 20) (10 cores per cpu, 2 threads per core) family 6 model 186 stepping 2 microcode 0x4114, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, serialize, rdtscp, rdpid, fsrm, f16c, pku, cet_ibt, cet_ss
Processor Information for the first 20 processors :
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400

Memory: 4k page, system-wide physical 16108M (583M free)
TotalPageFile size 41708M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 859M, peak: 927M
current process commit charge ("private bytes"): 799M, peak: 870M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+7-LTS) for windows-amd64 JRE (21.0.6+7-LTS), built on 2025-01-21T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
