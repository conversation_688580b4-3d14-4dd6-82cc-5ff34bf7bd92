package com.ggec.glasses.ai.data.db.entity;

/**
 * 消息状态枚举类，用于表示AI聊天消息的不同状态
 */
public enum MessageStatus {
    /**
     * 消息正在发送中
     */
    SENDING(0),
    
    /**
     * 消息已成功发送
     */
    SENT(1),
    
    /**
     * 消息发送失败
     */
    FAILED(2),
    
    /**
     * 消息已被接收
     */
    RECEIVED(3),
    
    /**
     * 消息已被阅读
     */
    READ(4);
    
    private final int value;
    
    MessageStatus(int value) {
        this.value = value;
    }
    
    /**
     * 获取状态的整型值
     * @return 整型值
     */
    public int getValue() {
        return value;
    }
    
    /**
     * 从整型值获取枚举
     * @param value 整型值
     * @return 对应的枚举值，如果没有匹配则返回FAILED
     */
    public static MessageStatus fromInt(int value) {
        for (MessageStatus status : MessageStatus.values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        return FAILED; // 默认返回失败状态
    }
} 