package com.ggec.glasses.album.manager;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.ggec.glasses.R;
import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.data.repository.MediaRepository;
import com.ggec.glasses.album.viewmodel.MediaViewModel;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.channels.FileChannel;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 媒体导出管理器
 * 负责处理媒体文件的导出保存、删除等操作
 */
public class AlbumMediaExportManager {
    private static final String TAG = "AlbumMediaExportManager";

    /**
     * 保存媒体文件到系统相册的回调接口
     */
    public interface SaveMediaCallback {
        /**
         * 保存成功回调
         */
        void onSuccess();

        /**
         * 保存失败回调
         * @param errorMessage 错误信息
         */
        void onFailed(String errorMessage);
    }

    /**
     * 删除媒体文件的回调接口
     */
    public interface DeleteMediaCallback {
        /**
         * 删除成功回调
         */
        void onSuccess();

        /**
         * 删除失败回调
         * @param errorMessage 错误信息
         */
        void onFailed(String errorMessage);
    }

    /**
     * 保存媒体文件到系统相册
     * @param context 上下文
     * @param media 媒体文件实体
     * @param callback 回调
     */
    public static void saveMediaToGallery(Context context, Media media, SaveMediaCallback callback) {
        if (media == null || media.getFilePath() == null) {
            if (callback != null) {
                callback.onFailed("媒体文件不存在");
            }
            return;
        }

        File sourceFile = new File(media.getFilePath());
        if (!sourceFile.exists()) {
            if (callback != null) {
                callback.onFailed("源文件不存在");
            }
            return;
        }

        // 根据Android版本使用不同的方法保存文件
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            saveMediaAndroidQ(context, media, callback);
        } else {
            saveMediaLegacy(context, media, callback);
        }
    }

    /**
     * Android Q及以上版本保存媒体文件的方法
     */
    @RequiresApi(api = Build.VERSION_CODES.Q)
    private static void saveMediaAndroidQ(Context context, Media media, SaveMediaCallback callback) {
        ContentResolver resolver = context.getContentResolver();
        ContentValues contentValues = new ContentValues();

        // 设置公共目录类型
        String collection;
        if ("VIDEO".equals(media.getType())) {
            collection = MediaStore.Video.Media.RELATIVE_PATH;
            contentValues.put(MediaStore.Video.Media.RELATIVE_PATH, Environment.DIRECTORY_MOVIES + "/Glasses");
            contentValues.put(MediaStore.Video.Media.MIME_TYPE, media.getMimeType() != null ? media.getMimeType() : "video/*");
            contentValues.put(MediaStore.Video.Media.IS_PENDING, 1);
        } else {
            collection = MediaStore.Images.Media.RELATIVE_PATH;
            contentValues.put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES + "/Glasses");
            contentValues.put(MediaStore.Images.Media.MIME_TYPE, media.getMimeType() != null ? media.getMimeType() : "image/*");
            contentValues.put(MediaStore.Images.Media.IS_PENDING, 1);
        }

        // 设置文件名，如果没有则生成一个默认的文件名
        String fileName = media.getFileName();
        if (fileName == null || fileName.isEmpty()) {
            String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            if ("VIDEO".equals(media.getType())) {
                fileName = "VIDEO_" + timeStamp + ".mp4";
            } else {
                fileName = "IMG_" + timeStamp + ".jpg";
            }
        }
        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, fileName);

        // 插入到媒体库
        Uri uri;
        if ("VIDEO".equals(media.getType())) {
            uri = resolver.insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, contentValues);
        } else {
            uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);
        }

        if (uri != null) {
            try {
                // 复制文件内容
                try (OutputStream os = resolver.openOutputStream(uri);
                     InputStream is = new FileInputStream(media.getFilePath())) {
                    
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = is.read(buffer)) != -1) {
                        os.write(buffer, 0, bytesRead);
                    }
                }

                // 更新IS_PENDING标志
                contentValues.clear();
                if ("VIDEO".equals(media.getType())) {
                    contentValues.put(MediaStore.Video.Media.IS_PENDING, 0);
                } else {
                    contentValues.put(MediaStore.Images.Media.IS_PENDING, 0);
                }
                resolver.update(uri, contentValues, null, null);

                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (IOException e) {
                Log.e(TAG, "Failed to save media file", e);
                resolver.delete(uri, null, null); // 删除失败的记录
                if (callback != null) {
                    callback.onFailed("保存文件时出错: " + e.getMessage());
                }
            }
        } else {
            if (callback != null) {
                callback.onFailed("无法创建媒体文件");
            }
        }
    }

    /**
     * 旧版Android系统保存媒体文件的方法
     */
    private static void saveMediaLegacy(Context context, Media media, SaveMediaCallback callback) {
        File destDir;
        if ("VIDEO".equals(media.getType())) {
            destDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MOVIES), "Glasses");
        } else {
            destDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), "Glasses");
        }

        if (!destDir.exists()) {
            if (!destDir.mkdirs()) {
                if (callback != null) {
                    callback.onFailed("无法创建目标目录");
                }
                return;
            }
        }

        // 设置文件名，如果没有则生成一个默认的文件名
        String fileName = media.getFileName();
        if (fileName == null || fileName.isEmpty()) {
            String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            if ("VIDEO".equals(media.getType())) {
                fileName = "VIDEO_" + timeStamp + ".mp4";
            } else {
                fileName = "IMG_" + timeStamp + ".jpg";
            }
        }

        File destFile = new File(destDir, fileName);

        try {
            copy(new File(media.getFilePath()), destFile);

            // 通知媒体扫描器扫描新文件
            MediaScannerConnection.scanFile(context,
                    new String[]{destFile.getAbsolutePath()},
                    null,
                    (path, uri) -> {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    });
        } catch (IOException e) {
            Log.e(TAG, "Failed to save media file", e);
            if (callback != null) {
                callback.onFailed("保存文件时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 复制文件
     * @param src 源文件
     * @param dst 目标文件
     * @throws IOException IO异常
     */
    private static void copy(File src, File dst) throws IOException {
        try (FileInputStream inStream = new FileInputStream(src);
             FileOutputStream outStream = new FileOutputStream(dst);
             FileChannel inChannel = inStream.getChannel();
             FileChannel outChannel = outStream.getChannel()) {
            
            inChannel.transferTo(0, inChannel.size(), outChannel);
        }
    }

    /**
     * 删除媒体文件
     * 注意：此方法将永久删除媒体文件及其数据库记录
     * @param context 上下文
     * @param media 媒体文件实体
     * @param callback 回调
     */
    public static void deleteMedia(Context context, Media media, DeleteMediaCallback callback) {
        if (media == null) {
            if (callback != null) {
                callback.onFailed("媒体文件不存在");
            }
            return;
        }
        
        // 获取MediaRepository实例进行数据库操作
        MediaRepository repository = MediaRepository.getInstance(context);
        
        // 调用永久删除方法
        repository.permanentlyDeleteMedia(media.getId(), new MediaRepository.OnMediaDeletedCallback() {
            @Override
            public void onSuccess() {
                if (callback != null) {
                    callback.onSuccess();
                }
            }
            
            @Override
            public void onError(String errorMessage) {
                if (callback != null) {
                    callback.onFailed(errorMessage);
                }
            }
            
            @Override
            public void onPartialSuccess(String message) {
                if (callback != null) {
                    callback.onSuccess(); // 部分成功也视为成功
                }
            }
        });
    }
} 