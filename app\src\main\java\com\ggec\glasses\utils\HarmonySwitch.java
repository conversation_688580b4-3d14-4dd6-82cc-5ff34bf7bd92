package com.ggec.glasses.utils;

import android.content.Context;
import android.util.AttributeSet;
import androidx.appcompat.widget.SwitchCompat;
import androidx.core.content.ContextCompat;

import com.ggec.glasses.R;

/**
 * 鸿蒙风格的自定义开关组件
 * 该组件继承自SwitchCompat，强制应用鸿蒙样式
 */
public class HarmonySwitch extends SwitchCompat {

    public HarmonySwitch(Context context) {
        super(context);
        init();
    }

    public HarmonySwitch(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public HarmonySwitch(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    /**
     * 初始化开关样式和尺寸
     */
    private void init() {
        // 设置轨道和滑块样式
        setTrackDrawable(ContextCompat.getDrawable(getContext(), <PERSON>.drawable.hm_switch_track));
        setThumbDrawable(ContextCompat.getDrawable(getContext(), R.drawable.hm_switch_thumb));

        // 调整尺寸
        setSwitchMinWidth(dpToPx(50));   // 轨道最小宽度
        setThumbSize(dpToPx(24));        // 滑块尺寸
    }

    /**
     * 设置滑块尺寸
     * @param size 滑块尺寸（像素）
     */
    private void setThumbSize(int size) {
        if (getThumbDrawable() != null) {
            getThumbDrawable().setBounds(0, 0, size, size);
        }
    }

    /**
     * dp 转 px 工具方法
     * @param dp dp值
     * @return 转换后的像素值
     */
    private int dpToPx(float dp) {
        float density = getContext().getResources().getDisplayMetrics().density;
        return (int) (dp * density + 0.5f);
    }
} 