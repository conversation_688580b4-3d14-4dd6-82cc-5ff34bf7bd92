package com.ggec.glasses.bluetooth.model;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;

import com.ggec.glasses.R;

/**
 * 蓝牙设备类型枚举
 * 定义不同类型的蓝牙设备及其对应的图标资源
 */
public enum BluetoothDeviceType {
    
    UNKNOWN(R.drawable.ic_public_bluetooth),
    GLASSES(R.drawable.ic_device_glass),
    EARPHONE(R.drawable.ic_device_earphone),
    WATCH(R.drawable.ic_device_watch),
    PHONE(R.drawable.ic_devices_phone),
    TABLET(R.drawable.ic_device_pad),
    COMPUTER(R.drawable.ic_device_computer),
    TV(R.drawable.ic_device_smartscreen);
    
    @DrawableRes
    private final int iconResId;
    
    /**
     * 构造方法
     * @param iconResId 设备图标资源ID
     */
    BluetoothDeviceType(@DrawableRes int iconResId) {
        this.iconResId = iconResId;
    }
    
    /**
     * 获取设备图标资源ID
     * @return 图标资源ID
     */
    @DrawableRes
    public int getIconResId() {
        return iconResId;
    }
    
    /**
     * 根据设备名称判断设备类型
     * @param deviceName 设备名称
     * @return 设备类型
     */
    @NonNull
    public static BluetoothDeviceType getTypeByName(@NonNull String deviceName) {
        String name = deviceName.toLowerCase();
        
        if (name.contains("glass") || name.contains("眼镜")) {
            return GLASSES;
        } else if (name.contains("earphone") || name.contains("headphone") || 
                  name.contains("earbud") || name.contains("耳机")) {
            return EARPHONE;
        } else if (name.contains("watch") || name.contains("手表")) {
            return WATCH;
        } else if (name.contains("phone") || name.contains("手机")) {
            return PHONE;
        } else if (name.contains("pad") || name.contains("tablet") || name.contains("平板")) {
            return TABLET;
        } else if (name.contains("pc") || name.contains("computer") || 
                  name.contains("laptop") || name.contains("电脑") || 
                  name.contains("笔记本")) {
            return COMPUTER;
        } else if (name.contains("tv") || name.contains("television") || 
                  name.contains("screen") || name.contains("电视")) {
            return TV;
        }
        
        return UNKNOWN;
    }
} 