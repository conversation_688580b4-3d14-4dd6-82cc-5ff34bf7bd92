/*
 * Copyright (c) 2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.sample.audioconnect.smartwear.gallery

import android.content.Context
import android.net.ConnectivityManager
import android.net.ConnectivityManager.NetworkCallback
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.net.wifi.WifiNetworkSpecifier
import android.net.wifi.WifiNetworkSuggestion
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.lifecycle.MutableLiveData
import com.realsil.sdk.core.logger.ZLogger
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.IOException
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * <AUTHOR>
 * @date 2025/03/26
 */
class WiFiHelper(private val mContext: Context) {

    var wifiConnected = false
    var wifiConnectedLiveData: MutableLiveData<Boolean> = MutableLiveData()

    fun updateWifiConnectionState(connected: Boolean) {
        ZLogger.v("updateWifiConnectionState:$connected")
        wifiConnected = connected
        wifiConnectedLiveData.postValue(wifiConnected)
    }

    fun updateSsid(ssid: String) {
        val connected = isSpecificWifiConnected(ssid)
        updateWifiConnectionState(connected)
    }

    fun isSpecificWifiConnected(ssid: String): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            return isSpecificWifiConnectedR(ssid)
        } else {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                isSpecificWifiConnectedM(ssid)
            } else {
                ZLogger.d("VERSION.SDK_INT < M")
                false
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.M)
    fun isSpecificWifiConnectedM(targetSsid: String): Boolean {
        val connectivityManager =
            mContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network)

        if (networkCapabilities != null &&
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        ) {
            val wifiManager = mContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val connectionInfo = wifiManager.connectionInfo
            if (connectionInfo != null) {
                val currentSsid = connectionInfo.ssid.replace("\"", "")
                return currentSsid == targetSsid
            }
        }
        return false
    }

    @RequiresApi(Build.VERSION_CODES.R)
    fun isSpecificWifiConnectedR(targetSsid: String): Boolean {
        val connectivityManager =
            mContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val wifiManager = mContext.getSystemService(Context.WIFI_SERVICE) as WifiManager

        val wifiInfo = wifiManager.getConnectionInfo()
        if (wifiInfo != null) {
            val currentSsid = wifiInfo.ssid.replace("\"", "")
            ZLogger.v("currentSsid=$currentSsid")
            return currentSsid == targetSsid
        }
        return false
    }

    suspend fun connectToWifi(ssid: String, password: String): Boolean {
        ZLogger.v(String.format("connectToWifi:%s", ssid))
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            return connectToWifiQ(ssid, password)
        } else {
            return connectToWifiBelowQ(ssid, password)
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    private suspend fun connectToWifiQ(ssid: String, password: String): Boolean {
        return suspendCoroutine { cont ->
            val wifiNetworkSuggestion = WifiNetworkSuggestion.Builder()
                .setSsid(ssid)
                .setWpa2Passphrase((password))
                .setIsAppInteractionRequired(true)
                .build();

            val suggestionsList = ArrayList<WifiNetworkSuggestion>();
            suggestionsList.add(wifiNetworkSuggestion);

            val wifiManager = mContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val status = wifiManager.addNetworkSuggestions(suggestionsList);
            ZLogger.v("addNetworkSuggestions.status=$status")
            if (status == WifiManager.STATUS_NETWORK_SUGGESTIONS_SUCCESS ||
                status == WifiManager.STATUS_NETWORK_SUGGESTIONS_ERROR_ADD_DUPLICATE
            ) {
                val wifiNetworkSpecifier = WifiNetworkSpecifier.Builder()
                    .setSsid(ssid)
                    .setWpa2Passphrase(password)
                    .build()

                //Request with wifi network specifier cannot contain NET_CAPABILITY_INTERNET. Rejecting
                val networkRequest = NetworkRequest.Builder()
                    .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
                    .addCapability(NetworkCapabilities.NET_CAPABILITY_TRUSTED)
                    .removeCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                    .setNetworkSpecifier(wifiNetworkSpecifier)
                    .build()

                val connectivityManager =
                    mContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

                ZLogger.v("requestNetwork")
                connectivityManager.requestNetwork(networkRequest, object : NetworkCallback() {
                    override fun onAvailable(network: Network) {
                        super.onAvailable(network)
                        ZLogger.i("onAvailable:$network")
                        connectivityManager.bindProcessToNetwork(network)
                        checkNetwork()
                    }

                    override fun onUnavailable() {
                        super.onUnavailable()
                        ZLogger.w("onUnavailable:")
                        updateWifiConnectionState(false)
                    }
                })
                cont.resume(true)
            } else {
                cont.resume(false)
            }
        }
    }

    private suspend fun connectToWifiBelowQ(ssid: String, password: String): Boolean {
        return suspendCoroutine { cont ->
            val wifiManager = mContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            if (!wifiManager.isWifiEnabled) {
                wifiManager.setWifiEnabled(true)
            }

            val wifiConfig = WifiConfiguration()
            wifiConfig.SSID = String.format("\"%s\"", ssid)
            wifiConfig.preSharedKey = String.format("\"%s\"", password)

            var netId = wifiManager.connectionInfo.networkId
            val configuredNetworks = wifiManager.configuredNetworks
            for (config in configuredNetworks) {
                if (config.SSID != null && config.SSID == wifiConfig.SSID) {
                    netId = config.networkId
                    ZLogger.v("found netId:$netId")
                    break
                }
            }

            if (netId == -1) {
                netId = wifiManager.addNetwork(wifiConfig)
                ZLogger.v("add netId:$netId")
            }

            wifiManager.disconnect()
            wifiManager.enableNetwork(netId, true)
            val ret = wifiManager.reconnect()
            cont.resume(ret)
            updateWifiConnectionState(ret)
        }

    }
    //"http://192.168.43.1:8080/pingpong"
    private fun checkNetwork(url: String = "http://192.168.43.1:8080/pingpong" ) {
        ZLogger.v("checkNetwork:$url")
        val client = OkHttpClient()
        val request: Request =
            Request.Builder().url("$url")
                .get()
                .build()

        try {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    throw IOException("Unexpected code $response")
                } else {
                    ZLogger.i("network is ok")
                    updateWifiConnectionState(true)
                }
            }
        } catch (e: IOException) {
            e.printStackTrace()
            ZLogger.w("checkNetwork fail")
        }
    }
}
