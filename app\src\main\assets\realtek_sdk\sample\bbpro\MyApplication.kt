/*
 * Copyright (c) 2017-2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.bbpro

import com.realsil.bbpro.ui.nearby.NearbyActivity
import com.realsil.sample.audioconnect.eq.spk.v0.EqPref
import com.realsil.sample.audioconnect.fastpair.FastPairConfigure
import com.realsil.sample.audioconnect.fastpair.FastPairSettingsHelper
import com.realsil.sample.audioconnect.ota.configure.OtaSettingsHelper
import com.realsil.sdk.audioconnect.durian.DurianModelProxy
import com.realsil.sdk.audioconnect.hearingaid.HearingAidModelProxy
import com.realsil.sdk.audioconnect.repository.RepositoryViewModel
import com.realsil.sdk.audioconnect.smartwear.SmartWearModelProxy
import com.realsil.sdk.audioconnect.support.CapabilityInfoHelper
import com.realsil.sdk.audioconnect.support.SettingsHelper
import com.realsil.sdk.audioconnect.tts.TtsModelProxy
import com.realsil.sdk.audioconnect.tts.params.TtsConfig
import com.realsil.sdk.bbpro.BeeProParams
import com.realsil.sdk.bbpro.MultiPeripheralConnectionManager
import com.realsil.sdk.bbpro.core.spp.SppTransportConnParams
import com.realsil.sdk.core.RtkConfigure
import com.realsil.sdk.core.RtkCore
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.dfu.RtkDfu
import com.realsil.sdk.support.RealtekApplication
import com.realsil.sdk.support.RtkSupport
import com.realsil.sdk.tts.baidu.BaiduTtsEngine
import com.realtek.apps.audioconnect.customized.CustomizedLogger
import com.realtek.sdk.audioconnect.cloud.OtaCloudManager
import com.realtek.sdk.support.debugger.DebuggerConfigure
import com.realtek.sdk.support.debugger.RtkDebugger
import java.util.Locale
import java.util.UUID

/**
 * <AUTHOR>
 * @date 2019/6/13
 */
class MyApplication : RealtekApplication() {
    override fun onCreate() {
        super.onCreate()
        ZLogger.v(D, "initial")
        // Mandatory, initialize rtk-core library
        // this: context
        // isDebug: true, switch on debug log; false, switch off debug log
        val configure = RtkConfigure.Builder()
            .debugEnabled(true)
            .printLog(true)
            .globalLogLevel(ZLogger.INFO)
            .logTag("AudioConnectSDK")
            .devModeEnabled(true)
            .build()
        ZLogger.setLogger(CustomizedLogger())
        RtkCore.initialize(this, configure)
        RtkCore.VDBG = true

        // Mandatory for Demo App
        RtkSupport.initialize(this, false)
        // optional for debugger
        RtkDebugger.initialize(
            this,
            DebuggerConfigure.Builder()
                .logTag("AudioConnect")
                .bugly("4a0748951a")
                .build()
        )
        // Option for OTA function
        RtkDfu.initialize(this, true)
        OtaSettingsHelper.initialize(this)
        OtaCloudManager.initialize(this)

        SettingsHelper.initialize(this)
        // Option for EQ function
        EqPref.initialize(this)

        CapabilityInfoHelper.initialize(this)
        RepositoryViewModel.initialize(this)

        val pid = android.os.Process.myPid()
        val processAppName = getProcessName(this, pid)
        if (processAppName == packageName) {
            AppPref.initialize(this)
            val builder = BeeProParams.Builder()
                .syncDataWhenConnected(true)
                .connectA2dp(true)
                .listenHfp(true)
                .uuid(UUID.fromString("6A24EEAB-4B65-4693-986B-3C26C352264F"))
                .transport(SppTransportConnParams.TRANSPORT_VENDOR)

            MultiPeripheralConnectionManager.getInstance(this).initialize(builder.build())
            TtsModelProxy.initialize(
                this,
                TtsConfig.Builder()
                    .ttsEngine(
                        BaiduTtsEngine(
                            "22577928",
                            "tpDMw7ht8fjVKm57agDtpCnY",
                            "5dBWKlIS1rLvPNaK6aowTbcwlSIIeTXD"
                        )
                    )
                    .build()
            )
            DurianModelProxy.initialize(this)

            // Add hearing aid feature support
            HearingAidModelProxy.initialize(this)

            // Add SmartWear feature support
            SmartWearModelProxy.initialize(this)

            FastPairSettingsHelper.initialize(this)
            FastPairConfigure.updateFilter(this, NearbyActivity::class.java, true)
        }

        ZLogger.d("Application created")
    }

    companion object {
        private const val TAG = "MyApplication"
        private const val D = true
    }
}