package com.ggec.glasses.ai.data.db.migration;

import androidx.room.migration.Migration;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据库迁移工厂类，用于集中管理所有数据库迁移
 */
public class MigrationFactory {
    
    /**
     * 获取所有数据库迁移对象
     * @return 数据库迁移对象数组
     */
    public static Migration[] getAllMigrations() {
        List<Migration> migrations = new ArrayList<>();
        
        // 添加所有迁移策略
        migrations.add(new Migration_1_2());
        // 添加后续迁移
        // migrations.add(new Migration_2_3());
        // migrations.add(new Migration_3_4());
        
        return migrations.toArray(new Migration[0]);
    }
    
    /**
     * 获取指定版本范围内的迁移对象
     * @param startVersion 起始版本
     * @param endVersion 结束版本
     * @return 数据库迁移对象数组
     */
    public static Migration[] getMigrations(int startVersion, int endVersion) {
        List<Migration> allMigrations = new ArrayList<>();
        allMigrations.add(new Migration_1_2());
        // 添加后续迁移
        // allMigrations.add(new Migration_2_3());
        // allMigrations.add(new Migration_3_4());
        
        List<Migration> filteredMigrations = new ArrayList<>();
        for (Migration migration : allMigrations) {
            if (migration instanceof androidx.room.migration.Migration) {
                androidx.room.migration.Migration roomMigration = (androidx.room.migration.Migration) migration;
                if (roomMigration.startVersion >= startVersion && roomMigration.endVersion <= endVersion) {
                    filteredMigrations.add(migration);
                }
            }
        }
        
        return filteredMigrations.toArray(new Migration[0]);
    }
} 