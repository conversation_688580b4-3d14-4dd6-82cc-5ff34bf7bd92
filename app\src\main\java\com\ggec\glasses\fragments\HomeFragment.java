package com.ggec.glasses.fragments;

import android.Manifest;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.ggec.glasses.GlassesApplication;
import com.ggec.glasses.R;
import com.ggec.glasses.bluetooth.manager.BluetoothManager;
import com.ggec.glasses.bluetooth.BluetoothConnectionActivity;
import com.ggec.glasses.device.model.Device;
import com.ggec.glasses.device.viewmodel.DeviceViewModel;
import com.ggec.glasses.utils.StatusBarUtils;
import com.ggec.glasses.ai.AiModelSettingsActivity;
import com.ggec.glasses.device.KeySettingsActivity;
import com.ggec.glasses.device.DebugActivity;
import com.ggec.glasses.device.GlassesSettingsActivity;

public class HomeFragment extends Fragment implements BluetoothManager.BluetoothStateListener {

    private View statusBarBackground;
    private TextView tvDeviceName;
    private TextView tvBluetoothStatus;
    private TextView tvBatteryLevel;
    private ImageView btnDeviceSpinner;
    private ImageView btnSettings;
    private DeviceViewModel deviceViewModel;
    private BluetoothManager bluetoothManager;
    
    // 新增的卡片视图
    private CardView cardAiModelSettings;
    private CardView cardKeySettings;
    private CardView cardDebug;
    private CardView cardEmpty;
    
    // 蓝牙启用请求启动器
    private ActivityResultLauncher<Intent> enableBluetoothLauncher;
    // 蓝牙权限请求启动器
    private ActivityResultLauncher<String[]> requestPermissionLauncher;
    // 蓝牙设置跳转启动器
    private ActivityResultLauncher<Intent> bluetoothSettingsLauncher;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化蓝牙管理器
        bluetoothManager = GlassesApplication.getBluetoothManager();
        bluetoothManager.setBluetoothStateListener(this);
        
        // 注册蓝牙启用请求启动器
        enableBluetoothLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    // 用户操作结束后，请求刷新蓝牙状态 (更新将在回调中发生)
                    bluetoothManager.checkBluetoothState();
                });
        
        // 注册权限请求启动器
        requestPermissionLauncher = registerForActivityResult(
                new ActivityResultContracts.RequestMultiplePermissions(),
                permissions -> {
                    boolean allGranted = true;
                    for (Boolean granted : permissions.values()) {
                        if (!granted) {
                            allGranted = false;
                            break;
                        }
                    }
                    
                    if (allGranted) {
                        // 所有权限都已授予，直接请求开启蓝牙
                        enableBluetooth();
                    } else {
                        Toast.makeText(requireContext(), R.string.bluetooth_permission_required, Toast.LENGTH_SHORT).show();
                    }
                });
        
        // 注册蓝牙设置跳转启动器
        bluetoothSettingsLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    // 从蓝牙设置页面返回后，请求刷新蓝牙状态 (更新将在回调中发生)
                    bluetoothManager.checkBluetoothState(); 
                });
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_home, container, false);
        initViews(view);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setupStatusBar();
        setupClickListeners();
        setupViewModel();
        
        // View创建后，请求检查一次蓝牙状态 (更新将在回调中发生)
        bluetoothManager.checkBluetoothState(); 
    }
    
    @Override
    public void onResume() {
        super.onResume();
        
        // 确保蓝牙管理器已初始化
        if (bluetoothManager == null) {
            bluetoothManager = GlassesApplication.getBluetoothManager();
            bluetoothManager.setBluetoothStateListener(this);
        }
        
        // 在页面恢复时请求检查蓝牙状态
        // 先检查一次蓝牙适配器状态
        BluetoothManager.BluetoothState currentState = bluetoothManager.getCurrentBluetoothState();
        updateBluetoothStatusUI(currentState);
        
        // 然后请求异步刷新
        bluetoothManager.checkBluetoothState();
    }
    
    // 添加onPause方法
    @Override
    public void onPause() {
        super.onPause();
        // 不需要在这里做任何事情，保持蓝牙状态监听
    }
    
    // 添加onDestroy方法
    @Override
    public void onDestroy() {
        super.onDestroy();
        // 移除蓝牙状态监听器
        if (bluetoothManager != null) {
            bluetoothManager.setBluetoothStateListener(null);
        }
    }

    private void initViews(View view) {
        statusBarBackground = view.findViewById(R.id.status_bar_background);
        tvDeviceName = view.findViewById(R.id.tv_device_name);
        tvBluetoothStatus = view.findViewById(R.id.tv_bluetooth_status);
        tvBatteryLevel = view.findViewById(R.id.tv_battery_level);
        btnDeviceSpinner = view.findViewById(R.id.btn_device_spinner);
        btnSettings = view.findViewById(R.id.btn_settings);
        
        // 初始化卡片视图
        cardAiModelSettings = view.findViewById(R.id.card_ai_model_settings);
        cardKeySettings = view.findViewById(R.id.card_key_settings);
        cardDebug = view.findViewById(R.id.card_debug);
        cardEmpty = view.findViewById(R.id.card_empty);
    }

    private void setupStatusBar() {
        // 设置状态栏背景高度
        if (statusBarBackground != null && getActivity() != null) {
            ViewGroup.LayoutParams layoutParams = statusBarBackground.getLayoutParams();
            layoutParams.height = StatusBarUtils.getStatusBarHeight(getActivity());
            statusBarBackground.setLayoutParams(layoutParams);
        }
    }

    private void setupClickListeners() {
        // 设置设备选择下拉箭头点击事件
        btnDeviceSpinner.setOnClickListener(v -> {
            Toast.makeText(requireContext(), "设备选择功能开发中", Toast.LENGTH_SHORT).show();
        });

        // 设置设置按钮点击事件
        btnSettings.setOnClickListener(v -> {
            // 跳转到眼镜设置页面
            Intent intent = new Intent(requireContext(), GlassesSettingsActivity.class);
            startActivity(intent);
        });
        
        // 设置蓝牙状态点击事件
        tvBluetoothStatus.setOnClickListener(v -> {
            // 点击时，根据当前获取的状态（可能不是最新的）决定行为
            // 注意：直接获取状态可能不是完全实时的，但对于决定点击行为通常足够
            BluetoothManager.BluetoothState currentState = bluetoothManager.getCurrentBluetoothState(); 
            switch (currentState) {
                case NOT_ENABLED:
                    // 申请蓝牙权限并开启蓝牙
                    requestBluetoothPermissions();
                    break;
                case ENABLED_NOT_CONNECTED:
                    // 跳转到蓝牙连接页面
                    Intent bluetoothConnectionIntent = new Intent(requireContext(), BluetoothConnectionActivity.class);
                    startActivity(bluetoothConnectionIntent);
                    break;
                case CONNECTING: // 连接中依然允许跳转设置
                    // 跳转到系统蓝牙设置
                    openBluetoothSettings();
                    break;
                case ENABLED_CONNECTED:
                    // 蓝牙已连接状态下点击不做任何操作
                    // openBluetoothSettings(); // 已注释：蓝牙已连接状态下不跳转系统设置
                    break;
            }
            // 触发一次状态检查，确保UI最终能反映最新状态
            bluetoothManager.checkBluetoothState();
        });
        
        // 添加长按刷新功能
        tvBluetoothStatus.setOnLongClickListener(v -> {
            // 强制刷新蓝牙状态
            Toast.makeText(requireContext(), "正在刷新蓝牙状态...", Toast.LENGTH_SHORT).show();
            bluetoothManager.checkBluetoothState();
            return true;
        });
        
        // 设置新增卡片的点击事件
        cardAiModelSettings.setOnClickListener(v -> {
            // 跳转到AI大模型设置页面
            Intent intent = new Intent(requireContext(), AiModelSettingsActivity.class);
            startActivity(intent);
        });
        
        cardKeySettings.setOnClickListener(v -> {
            // 跳转到按键设置页面
            Intent intent = new Intent(requireContext(), KeySettingsActivity.class);
            startActivity(intent);
        });
        
        cardDebug.setOnClickListener(v -> {
            // 跳转到Debug页面
            Intent intent = new Intent(requireContext(), DebugActivity.class);
            startActivity(intent);
        });
        
        cardEmpty.setOnClickListener(v -> {
            Toast.makeText(requireContext(), "功能开发中", Toast.LENGTH_SHORT).show();
        });
    }

    private void setupViewModel() {
        // 初始化ViewModel
        deviceViewModel = new ViewModelProvider(requireActivity()).get(DeviceViewModel.class);
        
        // 观察当前设备变化
        deviceViewModel.getCurrentDevice().observe(getViewLifecycleOwner(), this::updateDeviceInfo);
    }
    
    private void updateDeviceInfo(Device device) {
        if (device != null) {
            tvDeviceName.setText(device.getName());
            
            // 更新电量显示
            if (bluetoothManager.getCurrentBluetoothState() == BluetoothManager.BluetoothState.ENABLED_CONNECTED) {
                // 有连接时，显示设备电量
                int batteryLevel = device.getBatteryLevel();
                tvBatteryLevel.setText(batteryLevel + "%");
                tvBatteryLevel.setTextColor(ContextCompat.getColor(requireContext(), R.color.font_primary));
                
                // 更新电池图标颜色
                Drawable batteryIcon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_battery);
                if (batteryIcon != null) {
                    batteryIcon.setTint(ContextCompat.getColor(requireContext(), R.color.font_primary));
                    tvBatteryLevel.setCompoundDrawablesWithIntrinsicBounds(batteryIcon, null, null, null);
                }
            } else {
                // 无连接时，显示--％
                tvBatteryLevel.setText("--%");
                tvBatteryLevel.setTextColor(ContextCompat.getColor(requireContext(), R.color.font_tertiary));
                
                // 更新电池图标颜色
                Drawable batteryIcon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_battery);
                if (batteryIcon != null) {
                    batteryIcon.setTint(ContextCompat.getColor(requireContext(), R.color.font_tertiary));
                    tvBatteryLevel.setCompoundDrawablesWithIntrinsicBounds(batteryIcon, null, null, null);
                }
            }
        } else {
            tvDeviceName.setText(R.string.no_device_connected);
            
            // 无设备时，显示--％
            tvBatteryLevel.setText("--%");
            tvBatteryLevel.setTextColor(ContextCompat.getColor(requireContext(), R.color.font_tertiary));
            
            // 更新电池图标颜色
            Drawable batteryIcon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_battery);
            if (batteryIcon != null) {
                batteryIcon.setTint(ContextCompat.getColor(requireContext(), R.color.font_tertiary));
                tvBatteryLevel.setCompoundDrawablesWithIntrinsicBounds(batteryIcon, null, null, null);
            }
        }
    }
    
    /**
     * 更新蓝牙状态UI (此方法现在只由 onBluetoothStateChanged 调用)
     * @param state 蓝牙状态
     */
    private void updateBluetoothStatusUI(BluetoothManager.BluetoothState state) {
        // 避免在Fragment未附加到Activity时更新UI
        if (!isAdded() || getContext() == null) {
            return;
        }
        
        switch (state) {
            case NOT_ENABLED:
                tvBluetoothStatus.setText(R.string.bluetooth_not_enabled);
                tvBluetoothStatus.setTextColor(ContextCompat.getColor(requireContext(), R.color.warning));
                
                // 更新电量显示
                tvBatteryLevel.setText("--%");
                tvBatteryLevel.setTextColor(ContextCompat.getColor(requireContext(), R.color.font_tertiary));
                updateBatteryIcon(false);
                break;
            case ENABLED_NOT_CONNECTED:
                tvBluetoothStatus.setText(R.string.bluetooth_enabled_not_connected);
                tvBluetoothStatus.setTextColor(ContextCompat.getColor(requireContext(), R.color.brand));
                
                // 更新电量显示
                tvBatteryLevel.setText("--%");
                tvBatteryLevel.setTextColor(ContextCompat.getColor(requireContext(), R.color.font_tertiary));
                updateBatteryIcon(false);
                break;
            case CONNECTING:
                tvBluetoothStatus.setText(R.string.bluetooth_connecting);
                tvBluetoothStatus.setTextColor(ContextCompat.getColor(requireContext(), R.color.alert));
                
                // 更新电量显示
                tvBatteryLevel.setText("--%");
                tvBatteryLevel.setTextColor(ContextCompat.getColor(requireContext(), R.color.font_tertiary));
                updateBatteryIcon(false);
                break;
            case ENABLED_CONNECTED:
                tvBluetoothStatus.setText(R.string.bluetooth_enabled_connected);
                tvBluetoothStatus.setTextColor(ContextCompat.getColor(requireContext(), R.color.confirm));
                
                // 更新电量显示，从当前设备中获取
                Device device = deviceViewModel.getCurrentDevice().getValue();
                if (device != null) {
                    int batteryLevel = device.getBatteryLevel();
                    tvBatteryLevel.setText(batteryLevel + "%");
                    tvBatteryLevel.setTextColor(ContextCompat.getColor(requireContext(), R.color.font_primary));
                    updateBatteryIcon(true);
                }
                break;
        }
    }
    
    /**
     * 更新电池图标颜色
     * @param isConnected 是否已连接
     */
    private void updateBatteryIcon(boolean isConnected) {
        Drawable batteryIcon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_battery);
        if (batteryIcon != null) {
            int color = isConnected ? 
                    ContextCompat.getColor(requireContext(), R.color.font_primary) : 
                    ContextCompat.getColor(requireContext(), R.color.font_tertiary);
            batteryIcon.setTint(color);
            tvBatteryLevel.setCompoundDrawablesWithIntrinsicBounds(batteryIcon, null, null, null);
        }
    }
    
    /**
     * 请求蓝牙权限
     */
    private void requestBluetoothPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Android 12+需要请求BLUETOOTH_CONNECT和BLUETOOTH_SCAN权限
            String[] permissions = {
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
            };
            
            boolean allGranted = true;
            for (String permission : permissions) {
                if (ContextCompat.checkSelfPermission(requireContext(), permission) != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (allGranted) {
                // 权限已授予，直接请求开启蓝牙
                enableBluetooth();
            } else {
                // 请求权限
                requestPermissionLauncher.launch(permissions);
            }
        } else {
            // Android 12以下，只需要检查普通蓝牙权限
            if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.BLUETOOTH_ADMIN) != PackageManager.PERMISSION_GRANTED ||
                    ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.BLUETOOTH) != PackageManager.PERMISSION_GRANTED) {
                
                String[] permissions = {
                        Manifest.permission.BLUETOOTH,
                        Manifest.permission.BLUETOOTH_ADMIN
                };
                requestPermissionLauncher.launch(permissions);
            } else {
                // 权限已授予，直接请求开启蓝牙
                enableBluetooth();
            }
        }
    }
    
    /**
     * 开启蓝牙
     */
    private void enableBluetooth() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Android 12+需要通过Intent请求用户开启蓝牙
            // 检查权限是否已授予
            if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                // 如果没有权限，可以提示用户或再次请求权限，这里简单返回
                Toast.makeText(requireContext(), R.string.bluetooth_permission_required, Toast.LENGTH_SHORT).show();
                requestBluetoothPermissions(); // 重新请求权限
                return;
            }
            Intent enableBtIntent = bluetoothManager.getEnableBluetoothIntent();
            try {
                enableBluetoothLauncher.launch(enableBtIntent);
                // 启动Intent后，状态更新依赖于Activity Result和广播
            } catch (SecurityException e) {
                Log.e("HomeFragment", "启动蓝牙开启Intent时缺少权限", e);
                Toast.makeText(requireContext(), "缺少启动蓝牙的权限", Toast.LENGTH_SHORT).show();
            }
        } else {
            // Android 12以下可以直接开启蓝牙
            // 检查权限是否已授予
             if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.BLUETOOTH_ADMIN) != PackageManager.PERMISSION_GRANTED) {
                 Toast.makeText(requireContext(), R.string.bluetooth_permission_required, Toast.LENGTH_SHORT).show();
                 requestBluetoothPermissions(); // 重新请求权限
                 return;
             }
            // 直接调用开启方法
            bluetoothManager.enableBluetooth(); 
            // 调用后立即触发一次状态检查，UI将通过回调更新
            bluetoothManager.checkBluetoothState();
        }
    }
    
    /**
     * 打开系统蓝牙设置
     */
    private void openBluetoothSettings() {
        Intent intent = bluetoothManager.getBluetoothSettingsIntent();
        bluetoothSettingsLauncher.launch(intent);
    }
    
    @Override
    public void onBluetoothStateChanged(BluetoothManager.BluetoothState state) {
        // 在UI线程中更新UI
        if (isAdded() && getActivity() != null) {
            getActivity().runOnUiThread(() -> updateBluetoothStatusUI(state));
        }
    }
} 