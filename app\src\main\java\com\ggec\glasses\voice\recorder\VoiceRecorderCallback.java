package com.ggec.glasses.voice.recorder;

/**
 * 语音录制回调接口
 * 用于通知录音状态和传递录音数据
 */
public interface VoiceRecorderCallback {
    
    /**
     * 当录音开始时调用
     */
    void onRecordingStarted();
    
    /**
     * 当收到音频数据时调用
     * @param audioData 音频数据字节数组
     * @param sizeInBytes 有效数据大小（字节）
     */
    void onAudioDataReceived(byte[] audioData, int sizeInBytes);
    
    /**
     * 当录音过程中发生错误时调用
     * @param error 错误信息
     */
    void onRecordingError(String error);
    
    /**
     * 当录音停止时调用
     */
    void onRecordingStopped();
} 