package com.realsil.sdk.audioconnect.support

import android.view.View
import com.realsil.sdk.audioconnect.repository.database.general.CapabilityEntity
import com.realsil.sdk.core.logger.ZLogger

class CapabilityCacheInfo {
    var earDetectionFeatureSupported: Boolean = false
    var realHearingEnhancementFeatureSupported: Boolean = false
    var sppCaptureFeatureSupported: Boolean = false
    var ancApplyBurnFeatureSupported: Boolean = false
    var autoInearDetectionFeatureSupported: Boolean = false
    var durianFeatureSupported: Boolean = false
    var durianMultiLinkFeatureSupported: Boolean = false
    var deviceIdFeatureSupported: Boolean = false
    var ttsFeatureSupported: Boolean = false
    var vpRingtoneFeatureSupported: Boolean = false
    var keyMapFeatureSupported: Boolean = false
    var durianKeyMapFeatureSupported: Boolean = false
    var otaFeatureSupported: Boolean = false
    var deviceNameFeatureSupported: Boolean = false
    var gamingModeFeatureSupported: Boolean = false
    var eqModeFeatureSupported: Boolean = false
    var spkVoiceEqFeatureSupported: Boolean = false
    var spkEqCompensationFeatureSupported: Boolean = false
    var languageFeatureSupported: Boolean = false
    var rwsChannelFeatureSupported: Boolean = false
    var inEarDetectionFeatureSupported: Boolean = false
    var chargingCaseFeatureSupported: Boolean = false
    var localPlayBackSupported: Boolean = false
    var logFlashDumpSupported: Boolean = false
    var ftlFlashDumpSupported: Boolean = false
    var rssiMonitorAndDumpSupported: Boolean = false
    var aiForSmartWearSupported: Boolean = false
    var icBindingSupported: Boolean = false

    fun update(capabilityEntity: CapabilityEntity) {
        ZLogger.v("reloadCapabilityInfo:$capabilityEntity")
        durianFeatureSupported = capabilityEntity.durianFeatureSupported
        durianMultiLinkFeatureSupported =
            capabilityEntity.durianMultiLinkFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchDurianMultiLinkEnabled

        deviceIdFeatureSupported =
            capabilityEntity.deviceIdFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchDeviceIdEnabled

        earDetectionFeatureSupported =
            capabilityEntity.earDetectionFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchEarDetectionEnabled
        keyMapFeatureSupported =
            capabilityEntity.keyMapFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchKeyMapEnabled
        vpRingtoneFeatureSupported =
            capabilityEntity.vpRingtoneFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchVpRingtoneEnabled
        realHearingEnhancementFeatureSupported =
            capabilityEntity.realHearingEnhancementFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchRealHearingEnhancementEnabled
        sppCaptureFeatureSupported =
            capabilityEntity.sppCaptureFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchSppDataCaptureEnabled
        ancApplyBurnFeatureSupported =
            capabilityEntity.ancApplyBurnFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchAncApplyBurnEnabled

        otaFeatureSupported =
            capabilityEntity.otaFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchOtaEnabled
        deviceNameFeatureSupported =
            capabilityEntity.deviceNameFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchDeviceNameEnabled
        gamingModeFeatureSupported =
            capabilityEntity.gamingModeFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchGamingModeEnabled
        ttsFeatureSupported =
            capabilityEntity.ttsFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchTtsEnabled
        eqModeFeatureSupported =
            capabilityEntity.eqModeFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchEqEnabled
        spkVoiceEqFeatureSupported =
            capabilityEntity.spkVoiceEqFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchVoiceEqEnabled
        spkEqCompensationFeatureSupported =
            capabilityEntity.spkEqCompensationFeatureSupported

        languageFeatureSupported =
            capabilityEntity.languageFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchLanguageEnabled
        rwsChannelFeatureSupported =
            capabilityEntity.rwsChannelFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchRwsChannelEnabled

        inEarDetectionFeatureSupported =
            capabilityEntity.inEarDetectionFeatureSupported || CapabilityInfoHelper.getInstance()!!.isSwitchAutoInearDetectionEnabled
        chargingCaseFeatureSupported =
            capabilityEntity.chargingCaseFeatureSupported || CapabilityInfoHelper.getInstance()!!.isChargingCaseEnabled
        localPlayBackSupported =
            capabilityEntity.localPlayBackSupported || CapabilityInfoHelper.getInstance()!!.localPlayBackSupported
        logFlashDumpSupported =
            capabilityEntity.logFlashDumpSupported || CapabilityInfoHelper.getInstance()!!.logFlashDumpSupported
        rssiMonitorAndDumpSupported =
            capabilityEntity.rssiMonitorAndDumpSupported || CapabilityInfoHelper.getInstance()!!.rssiMonitorAndDumpSupported
        ftlFlashDumpSupported =
            capabilityEntity.ftlFlashDumpSupported || CapabilityInfoHelper.getInstance()!!.ftlFlashDumpSupported
        aiForSmartWearSupported =
            capabilityEntity.aiForSmartWearSupported || CapabilityInfoHelper.getInstance()!!.aiForSmartWearSupported
        icBindingSupported =
            capabilityEntity.aiForSmartWearSupported || CapabilityInfoHelper.getInstance()!!.icBindingSupported
    }


    fun getSupportedInternalFeatureCount():Int{
        var supportedFeatureNumber = 0
        if (rssiMonitorAndDumpSupported) {
            supportedFeatureNumber += 1
        }
        if (logFlashDumpSupported) {
            supportedFeatureNumber += 1
        }
        if (ftlFlashDumpSupported) {
            supportedFeatureNumber += 1
        }
        return supportedFeatureNumber
    }

    override fun toString(): String {
        return "CapabilityInfo:tts:$ttsFeatureSupported,keyMap=$keyMapFeatureSupported"
    }
}
