package com.ggec.glasses.fragments;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.glasses.R;
import com.ggec.glasses.ai.adapter.MessageAdapter;
import com.ggec.glasses.ai.ui.AIPopupMenuHandler;
import com.ggec.glasses.ai.ui.AIVoiceUIHandler;
import com.ggec.glasses.ai.viewmodel.ChatViewModel;
import com.ggec.glasses.utils.DialogUtils;

import java.util.ArrayList;

public class AIFragment extends Fragment {
    private static final String TAG = "AIFragment";
    
    private ConstraintLayout headerLayout;
    private ConstraintLayout contentLayout;
    private TextView tvTitle;
    private RecyclerView recyclerViewMessages;
    private MessageAdapter messageAdapter;
    private ImageView btnVoice;
    private ImageView btnMore;
    
    // 语音识别状态显示
    private TextView tvRecognitionStatus;
    
    // ViewModel
    private ChatViewModel chatViewModel;
    
    // 消息加载标志
    private boolean isInitialLoad = true;

    // UI Handlers
    private AIPopupMenuHandler popupMenuHandler;
    private AIVoiceUIHandler voiceUIHandler;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView: 开始创建视图");
        View view = inflater.inflate(R.layout.fragment_ai, container, false);
        
        try {
            // 初始化视图
            initViews(view);
            Log.d(TAG, "onCreateView: 视图初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "onCreateView: 初始化视图异常", e);
        }
        
        return view;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.d(TAG, "onViewCreated: 视图创建完成");
        
        try {
            // 初始化RecyclerView - 先初始化UI组件
            initRecyclerView();
            Log.d(TAG, "onViewCreated: RecyclerView初始化完成");
            
            // 观察数据变化 - 先设置观察者
            setupViewModel();
            Log.d(TAG, "onViewCreated: ViewModel设置完成");

            // 在 ViewModel 设置完成后初始化 Handler
            initHandlers();
            Log.d(TAG, "onViewCreated: Handlers 初始化完成");
            
            // 设置UI监听器
            setupListeners();
            Log.d(TAG, "onViewCreated: 监听器设置完成");
        } catch (Exception e) {
            Log.e(TAG, "onViewCreated: 初始化AIFragment异常", e);
            Toast.makeText(requireContext(), "初始化AI助手失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume: Fragment恢复");
        
        // 在Fragment恢复时确保消息在底部
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (isAdded() && !isDetached() && recyclerViewMessages != null) {
                scrollToBottom(false);
            }
        }, 100);
    }
    
    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: Fragment暂停");
        
        // 如果正在录音，停止录音
        if (chatViewModel != null && chatViewModel.getIsRecording().getValue() != null 
                && chatViewModel.getIsRecording().getValue()) {
            chatViewModel.stopVoiceRecognition();
        }
    }
    
    @Override
    public void onStop() {
        super.onStop();
        Log.d(TAG, "onStop: Fragment停止");
        
        // 确保停止录音
        if (chatViewModel != null) {
            chatViewModel.stopVoiceRecognition();
        }
    }
    
    private void initViews(View view) {
        // 顶部导航栏
        headerLayout = view.findViewById(R.id.header_layout);
        contentLayout = view.findViewById(R.id.content_layout);
        tvTitle = view.findViewById(R.id.tv_title);
        recyclerViewMessages = view.findViewById(R.id.recyclerview_messages);
        btnVoice = view.findViewById(R.id.btn_voice);
        btnMore = view.findViewById(R.id.btn_more);
        
        // 添加语音识别状态文本视图
        tvRecognitionStatus = new TextView(requireContext());
        tvRecognitionStatus.setTextColor(Color.WHITE);
        tvRecognitionStatus.setPadding(16, 8, 16, 8);
        tvRecognitionStatus.setBackgroundResource(R.drawable.bg_voice_button);
        tvRecognitionStatus.setVisibility(View.GONE);
        tvRecognitionStatus.setGravity(Gravity.CENTER);
        
        // 不再将语音识别状态文本视图添加到UI
        /*
        ConstraintLayout bottomBar = view.findViewById(R.id.bottom_bar);
        if (bottomBar != null) {
            ConstraintLayout.LayoutParams params = new ConstraintLayout.LayoutParams(
                    ConstraintLayout.LayoutParams.WRAP_CONTENT,
                    ConstraintLayout.LayoutParams.WRAP_CONTENT
            );
            params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
            params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
            params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
            params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
            params.topMargin = -60; // 位于按钮上方
            
            bottomBar.addView(tvRecognitionStatus, params);
        }
        */
    }
    
    /**
     * 初始化ViewModel和设置观察者
     */
    private void setupViewModel() {
        try {
            // 使用ViewModelProvider获取ViewModel实例
            chatViewModel = new ViewModelProvider(this).get(ChatViewModel.class);
            
            // 观察显示中的消息列表变化
            chatViewModel.getDisplayMessages().observe(getViewLifecycleOwner(), messages -> {
                if (messages != null) {
                    Log.d(TAG, "收到消息列表更新，消息数量: " + messages.size());
                    boolean wasEmpty = messageAdapter.getItemCount() == 0;
                    messageAdapter.submitList(new ArrayList<>(messages));
                    
                    if (messages.size() > 0) {
                        Log.d(TAG, "最后一条消息: " + 
                              messages.get(messages.size() - 1).getContent().substring(0, 
                              Math.min(30, messages.get(messages.size() - 1).getContent().length())) + 
                              "..., 类型: " + (messages.get(messages.size() - 1).getType() == 0 ? "用户" : "AI"));
                    }
                    
                    // 如果之前列表为空或是初始加载，则立即滚动到底部
                    // 否则使用平滑滚动
                    scrollToBottom(!(wasEmpty || isInitialLoad));
                } else {
                    Log.w(TAG, "收到空消息列表");
                }
            });
            
            // 观察加载状态变化
            chatViewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
                // 这里可以添加加载指示器的显示逻辑
                Log.d(TAG, "加载状态: " + isLoading);
            });
            
            // 观察错误消息
            chatViewModel.getErrorMessage().observe(getViewLifecycleOwner(), error -> {
                if (error != null && !error.isEmpty()) {
                    Toast.makeText(requireContext(), error, Toast.LENGTH_SHORT).show();
                }
            });
            
            // 观察录音状态 -> 委托给 Handler 更新 UI
            chatViewModel.getIsRecording().observe(getViewLifecycleOwner(), isRecording -> {
                if (voiceUIHandler != null) {
                    voiceUIHandler.updateVoiceButtonState(isRecording);
                }
            });
            
            // 观察识别状态
            chatViewModel.getIsRecognizing().observe(getViewLifecycleOwner(), isRecognizing -> {
                // 可以根据识别状态更新UI (如果需要)
            });
            
            // 观察部分识别结果 (Fragment 不再直接处理 UI 更新)
            chatViewModel.getPartialRecognitionResult().observe(getViewLifecycleOwner(), result -> {
                 Log.d(TAG, "Partial result: " + result); // 仅日志记录
            });
            
            // 延迟初始化数据 - 使用更长的延迟确保ViewModel内部组件初始化完成
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (isAdded() && !isDetached() && chatViewModel != null) {
                    try {
                        // 设置初始加载标志
                        isInitialLoad = true;
                        
                        // 预加载最近的20条消息
                        chatViewModel.preloadRecentMessages(20);
                        
                        // 再次延迟加载UI数据，确保预加载完成
                        new Handler(Looper.getMainLooper()).postDelayed(() -> {
                            if (isAdded() && !isDetached() && chatViewModel != null) {
                                try {
                                    chatViewModel.loadInitialMessages();
                                    
                                    // 确保加载完成后滚动到底部
                                    new Handler(Looper.getMainLooper()).postDelayed(() -> {
                                        if (isAdded() && !isDetached()) {
                                            scrollToBottom(false);
                                        }
                                    }, 100);
                                } catch (Exception e) {
                                    Log.e(TAG, "Error loading messages to UI: " + e.getMessage(), e);
                                }
                            }
                        }, 500); // 加载到UI的延迟时间
                    } catch (Exception e) {
                        Log.e(TAG, "Error preloading messages: " + e.getMessage(), e);
                    }
                }
            }, 300); // 预加载的延迟时间
        } catch (Exception e) {
            Log.e(TAG, "Error setting up ViewModel: " + e.getMessage(), e);
        }
    }
    
    /**
     * 初始化自定义的 UI Handlers
     */
    private void initHandlers() {
        // 确保 ViewModel 已初始化
        if (chatViewModel == null) {
            Log.e(TAG, "ViewModel is null, cannot initialize handlers");
            return;
        }
        popupMenuHandler = new AIPopupMenuHandler(requireContext(), btnMore, chatViewModel, headerLayout);
        voiceUIHandler = new AIVoiceUIHandler(this, btnVoice, chatViewModel); // 初始化 Voice Handler
    }
    
    /**
     * 初始化RecyclerView
     */
    private void initRecyclerView() {
        // 创建布局管理器
        LinearLayoutManager layoutManager = new LinearLayoutManager(requireContext());
        recyclerViewMessages.setLayoutManager(layoutManager);
        
        // 创建适配器
        messageAdapter = new MessageAdapter();
        recyclerViewMessages.setAdapter(messageAdapter);
        
        // 添加全局布局监听器，确保在布局完成后滚动到底部
        recyclerViewMessages.getViewTreeObserver().addOnGlobalLayoutListener(
                new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        if (messageAdapter != null && messageAdapter.getItemCount() > 0) {
                            // 布局完成后确保滚动到底部
                            scrollToBottom(false);
                        }
                    }
                });
    }
    
    /**
     * 设置监听器
     */
    private void setupListeners() {
        // 语音按钮触摸事件 (委托给 Handler)
        if (voiceUIHandler != null) {
            voiceUIHandler.setupVoiceButtonListener();
        }
        
        // More按钮点击事件
        if (btnMore != null && popupMenuHandler != null) {
            btnMore.setOnClickListener(v -> {
                popupMenuHandler.showPopupMenu(); // 调用 Handler 显示菜单
            });
        }
    }
    
    /**
     * 滚动到底部显示最新消息
     * @param smooth 是否使用平滑滚动
     */
    private void scrollToBottom(boolean smooth) {
        if (recyclerViewMessages != null && messageAdapter != null && messageAdapter.getItemCount() > 0) {
            int lastPosition = messageAdapter.getItemCount() - 1;
            
            // 检查是否真的需要滚动 - 只在当前可见的最后一项不是最后一项时滚动
            LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerViewMessages.getLayoutManager();
            if (layoutManager != null) {
                int lastVisiblePosition = layoutManager.findLastVisibleItemPosition();
                
                // 如果最后可见项不是最后一项或者是初始加载，则滚动
                if (lastVisiblePosition != lastPosition || isInitialLoad) {
                    if (smooth) {
                        // 平滑滚动，用于用户交互
                        recyclerViewMessages.smoothScrollToPosition(lastPosition);
                    } else {
                        // 立即滚动，用于初始加载或从其他Fragment返回
                        recyclerViewMessages.scrollToPosition(lastPosition);
                    }
                    Log.d(TAG, "滚动到底部显示最新消息 - " + (smooth ? "平滑" : "立即"));
                    
                    // 初始加载完成后重置标志
                    isInitialLoad = false;
                }
            }
        }
    }
    
    /**
     * 滚动到底部显示最新消息（默认使用平滑滚动）
     */
    private void scrollToBottom() {
        // 调用重载方法，默认使用平滑滚动
        scrollToBottom(true);
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        
        // 清理动画资源 (委托给 Handler)
        // if (pulseAnimator != null) {
        //     pulseAnimator.cancel();
        //     pulseAnimator = null;
        // }
        
        // 调用 Handlers 的清理方法
        if (popupMenuHandler != null) {
            popupMenuHandler.dismiss();
            popupMenuHandler = null;
        }
        if (voiceUIHandler != null) {
            voiceUIHandler.release();
            voiceUIHandler = null;
        }
    }
} 