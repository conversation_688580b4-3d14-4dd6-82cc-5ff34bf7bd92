package com.ggec.glasses.ai.ui;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.glasses.R;
import com.ggec.glasses.ai.viewmodel.ChatViewModel;
import com.ggec.glasses.utils.DialogUtils;

/**
 * 处理 AIFragment 弹出菜单的帮助类
 */
public class AIPopupMenuHandler {

    private final Context context;
    private final View anchorView; // 例如 btnMore
    private final ChatViewModel chatViewModel;
    private final ConstraintLayout headerLayout; // 用于定位
    private PopupWindow popupWindow;

    /**
     * 构造函数
     * @param context 上下文
     * @param anchorView 弹出菜单的锚点视图
     * @param chatViewModel ChatViewModel 实例
     * @param headerLayout 顶部布局，用于计算菜单位置
     */
    public AIPopupMenuHandler(Context context, View anchorView, ChatViewModel chatViewModel, ConstraintLayout headerLayout) {
        this.context = context;
        this.anchorView = anchorView;
        this.chatViewModel = chatViewModel;
        this.headerLayout = headerLayout;
    }

    /**
     * 显示弹出菜单
     */
    public void showPopupMenu() {
        // 创建弹出窗口视图
        View popupView = LayoutInflater.from(context).inflate(R.layout.ai_popup_menu, null);
        popupWindow = new PopupWindow(
                popupView,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                true // 可聚焦
        );

        // 配置 PopupWindow
        popupWindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        popupWindow.setElevation(5);
        popupWindow.setAnimationStyle(android.R.style.Animation_Dialog);

        // 设置菜单项点击事件
        setupPopupMenuListeners(popupView);

        // 计算并设置显示位置
        calculateAndSetPosition(popupView);
    }

    /**
     * 设置弹出菜单项的监听器
     * @param popupView 弹出菜单的根视图
     */
    private void setupPopupMenuListeners(View popupView) {
        // 加载示例消息
        TextView btnLoadExampleMessages = popupView.findViewById(R.id.btn_load_example_messages);
        btnLoadExampleMessages.setOnClickListener(v -> {
            chatViewModel.appendSampleMessages();
            Toast.makeText(context, R.string.loading_sample_messages, Toast.LENGTH_SHORT).show();
            dismiss();
        });

        // User 测试消息
        TextView btnUserTestMessage = popupView.findViewById(R.id.btn_user_test_message);
        btnUserTestMessage.setOnClickListener(v -> {
            String content = "这是一条用户测试消息，当前时间: " + System.currentTimeMillis();
            chatViewModel.addUserTestMessage(content);
            Toast.makeText(context, R.string.test_message_sent, Toast.LENGTH_SHORT).show();
            dismiss();
        });

        // AI 测试消息
        TextView btnAITestMessage = popupView.findViewById(R.id.btn_ai_test_message);
        btnAITestMessage.setOnClickListener(v -> {
            String content = "这是一条AI测试消息，当前时间: " + System.currentTimeMillis();
            chatViewModel.addAITestMessage(content);
            Toast.makeText(context, R.string.test_message_sent, Toast.LENGTH_SHORT).show();
            dismiss();
        });

        // 清空消息
        TextView btnClearMessages = popupView.findViewById(R.id.btn_clear_messages);
        btnClearMessages.setOnClickListener(v -> {
            DialogUtils.showConfirmDialog(
                    context,
                    context.getString(R.string.ai_confirm_clear_title),
                    context.getString(R.string.ai_confirm_clear_message),
                    "确定",
                    "取消",
                    (dialog, which) -> {
                        chatViewModel.clearAllMessages();
                        Toast.makeText(context, R.string.ai_messages_cleared, Toast.LENGTH_SHORT).show();
                    },
                    null
            );
            dismiss();
        });
    }

    /**
     * 计算弹出菜单的位置并显示
     * @param popupView 弹出菜单的根视图
     */
    private void calculateAndSetPosition(View popupView) {
        // 获取屏幕尺寸
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(displayMetrics);
        int screenWidth = displayMetrics.widthPixels;

        // 获取导航栏位置
        int[] headerLocation = new int[2];
        headerLayout.getLocationOnScreen(headerLocation);
        int headerHeight = headerLayout.getHeight();
        int headerBottom = headerLocation[1] + headerHeight;

        // 获取弹出菜单的尺寸
        popupView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
        int popupWidth = popupView.getMeasuredWidth();
        int popupHeight = popupView.getMeasuredHeight(); // 获取实际高度

        // 计算弹出菜单的位置
        int rightMargin = dpToPx(16);
        int xPos = screenWidth - popupWidth - rightMargin;
        int yPos = headerBottom; // 紧贴导航栏底部

        // 显示弹出菜单
        popupWindow.showAtLocation(anchorView, Gravity.NO_GRAVITY, xPos, yPos);
    }

    /**
     * 关闭弹出菜单
     */
    public void dismiss() {
        if (popupWindow != null && popupWindow.isShowing()) {
            popupWindow.dismiss();
        }
    }

    /**
     * dp 转 px 工具方法
     * @param dp dp 值
     * @return px 值
     */
    private int dpToPx(int dp) {
        float density = context.getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }
} 