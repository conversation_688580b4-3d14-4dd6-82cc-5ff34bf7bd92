/*
 * Copyright (c) 2024. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.sdk.audioconnect.support.device

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.realsil.sdk.audioconnect.repository.database.general.DeviceInfoEntity
import com.realsil.sdk.audioconnect.support.AudioConnectViewModel
import com.realsil.sdk.audioconnect.support.sync.DeviceInfoSyncManager
import com.realsil.sdk.core.bluetooth.scanner.LeScannerPresenter

/**
 * The ViewModel for [NearbyActivity].
 * <AUTHOR>
 * @date 10/05/2024
 */

class NearbyViewModel(application: Application) : AudioConnectViewModel(application) {

    protected var mLeScannerPresenter: LeScannerPresenter
    var deviceInfoSyncManager: DeviceInfoSyncManager
    val deviceListLiveData: MutableLiveData<ArrayList<DeviceInfoEntity>> by lazy {
        MutableLiveData<ArrayList<DeviceInfoEntity>>()
    }

    fun loadDeviceList() {
//        val headsetDevices: List<ExtendedBluetoothDevice> =
//            mLeScannerPresenter.getPairedDevicesByProfile(BluetoothProfile.HEADSET) as List<ExtendedBluetoothDevice>
//        if (headsetDevices.isNotEmpty()) {
//            for (device in headsetDevices) {
//                // TODO: filter device at here
//                ZLogger.v(BluetoothHelper.dumpBluetoothDevice(device.device))
//
//                val pairDevice = deviceInfoSyncManager.getAudioConnectDevice(
//                    device.device.address,
//                    device.name
//                )
//                device.name = pairDevice.deviceName
////                ZLogger.v("update paired device name:" + pairDevice.deviceName)
//            }
//        }

        val deviceList = deviceInfoSyncManager.getPairDeviceInfos()
        deviceListLiveData.postValue(deviceList as ArrayList<DeviceInfoEntity>?)
    }

    init {
        mLeScannerPresenter = LeScannerPresenter(application, null, null)
        deviceInfoSyncManager = DeviceInfoSyncManager.getInstance(application)
    }
}
