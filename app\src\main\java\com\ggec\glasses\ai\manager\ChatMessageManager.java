package com.ggec.glasses.ai.manager;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.ggec.glasses.ai.data.ChatUIMessage;
import com.ggec.glasses.ai.data.db.entity.ChatMessage;
import com.ggec.glasses.ai.data.db.entity.MessageStatus;
import com.ggec.glasses.ai.data.db.repository.RepositoryCallback;
import com.ggec.glasses.ai.data.repository.CachedChatMessageRepository;
import com.ggec.glasses.ai.data.repository.MessageRepositoryFactory;
import com.ggec.glasses.ai.util.SampleMessageProvider;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 聊天消息管理器
 * 负责与数据仓库交互，管理消息的加载、保存、删除和转换
 */
public class ChatMessageManager {
    private static final String TAG = "ChatMessageManager";

    private final CachedChatMessageRepository repository;
    private final long defaultConversationId;

    // LiveData for messages from the repository
    private final LiveData<List<ChatMessage>> dbMessagesLiveData;

    /**
     * 构造函数
     * @param application 应用程序上下文
     * @param defaultConversationId 默认会话ID
     */
    public ChatMessageManager(Application application, long defaultConversationId) {
        this.defaultConversationId = defaultConversationId;
        this.repository = MessageRepositoryFactory.getDefaultRepository(application);
        this.dbMessagesLiveData = repository.getMessagesByConversationLive(defaultConversationId);
    }

    /**
     * 获取数据库消息列表的 LiveData
     * @return LiveData<List<ChatMessage>>
     */
    public LiveData<List<ChatMessage>> getDbMessagesLiveData() {
        return dbMessagesLiveData;
    }

    /**
     * 获取UI层消息列表的 LiveData
     * 通过转换 dbMessagesLiveData 得到
     * @return LiveData<List<ChatUIMessage>>
     */
    public LiveData<List<ChatUIMessage>> getUiMessagesLiveData() {
        Log.d(TAG, "获取UI消息LiveData, 会话ID: " + defaultConversationId);
        return Transformations.map(dbMessagesLiveData, this::convertToMessages);
    }

    /**
     * 将 ChatMessage 列表转换为 ChatUIMessage 列表 (UI层)
     * @param chatMessages 数据库消息列表
     * @return UI 消息列表
     */
    public List<ChatUIMessage> convertToMessages(List<ChatMessage> chatMessages) {
        List<ChatUIMessage> messages = new ArrayList<>();
        if (chatMessages != null) {
            for (ChatMessage chatMessage : chatMessages) {
                messages.add(convertToMessage(chatMessage));
            }
        }
        return messages;
    }

    /**
     * 将单个 ChatMessage 转换为 ChatUIMessage (UI层)
     * @param chatMessage 数据库消息
     * @return UI 消息
     */
    public ChatUIMessage convertToMessage(ChatMessage chatMessage) {
        if (chatMessage == null) {
            return null;
        }
        return new ChatUIMessage(
                chatMessage.getId(),
                chatMessage.getType(),
                chatMessage.getContent(),
                chatMessage.getTimestamp() != null ? chatMessage.getTimestamp().getTime() : System.currentTimeMillis() // 处理可能的null时间戳
        );
    }

    /**
     * 预加载最近消息
     * @param limit 消息数量限制
     */
    public void preloadRecentMessages(int limit) {
        Log.d(TAG, "预加载最近 " + limit + " 条消息");
        repository.preloadRecentMessages(limit);
    }

    /**
     * 导入示例消息到数据库
     * @param callback 回调，在导入完成后通知结果
     */
    public void importSampleMessages(RepositoryCallback<List<ChatMessage>> callback) {
        Log.d(TAG, "开始导入示例消息到数据库");
        // 使用 SampleMessageProvider 获取DB层消息，然后插入
        SampleMessageProvider.importWithCallback(defaultConversationId, new RepositoryCallback<List<ChatMessage>>() {
            @Override
            public void onSuccess(List<ChatMessage> sampleMessages) {
                if (sampleMessages != null && !sampleMessages.isEmpty()) {
                    repository.insertMessages(sampleMessages, new RepositoryCallback<List<Long>>() {
                        @Override
                        public void onSuccess(List<Long> messageIds) {
                            Log.d(TAG, "示例消息成功导入数据库, IDs: " + messageIds);
                            // 导入成功后，返回这些消息给调用者处理（例如用于UI追加）
                            callback.onSuccess(sampleMessages);
                        }

                        @Override
                        public void onError(String error) {
                            Log.e(TAG, "插入示例消息到数据库失败: " + error);
                            callback.onError("插入示例消息失败: " + error);
                        }
                    });
                } else {
                    Log.w(TAG, "获取到的示例消息为空");
                    callback.onSuccess(new ArrayList<>()); // 返回空列表
                }
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "获取示例消息失败: " + error);
                callback.onError("获取示例消息失败: " + error);
            }
        });
    }

    /**
     * 插入单条消息到数据库
     * @param message 要插入的消息
     * @param callback 回调
     */
    public void insertMessage(ChatMessage message, RepositoryCallback<Long> callback) {
        Log.d(TAG, "插入消息: " + message.toString() + 
              ", 会话ID: " + message.getConversationId() + 
              ", 默认会话ID: " + defaultConversationId);
        repository.insertMessage(message, callback);
    }

    /**
     * 删除指定会话的所有消息
     * @param callback 回调
     */
    public void deleteAllMessages(RepositoryCallback<Void> callback) {
        Log.d(TAG, "删除会话 " + defaultConversationId + " 的所有消息");
        repository.deleteMessagesByConversation(defaultConversationId, callback);
    }

    /**
     * 异步获取指定会话的所有消息
     * @param callback 回调，返回消息列表或错误
     */
    public void getMessagesByConversation(RepositoryCallback<List<ChatMessage>> callback) {
        Log.d(TAG, "获取会话 " + defaultConversationId + " 的所有消息");
        repository.getMessagesByConversation(defaultConversationId, callback);
    }

    /**
     * 创建用户消息实体
     * @param content 消息内容
     * @return ChatMessage 实例
     */
    public ChatMessage createUserMessageEntity(String content) {
        return ChatMessage.createUserMessage(content, defaultConversationId);
    }

    /**
     * 创建AI消息实体
     * @param content 消息内容
     * @return ChatMessage 实例
     */
    public ChatMessage createAIMessageEntity(String content) {
        ChatMessage message = ChatMessage.createAIMessage(content, defaultConversationId);
        Log.d(TAG, "创建AI消息实体: " + message.toString());
        return message;
    }
} 