package com.ggec.glasses.ai.manager;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.ggec.glasses.ai.data.ChatUIMessage;
// import com.example.glasses.ai.util.SampleMessageProvider; // 不再需要

import java.util.ArrayList;
import java.util.List;

/**
 * 消息显示管理器
 * 负责管理消息的逐条显示，提供平滑的用户体验
 */
public class MessageDisplayManager {
    private static final String TAG = "MessageDisplayManager";
    private static final int DEFAULT_DELAY = 800; // 默认延迟800毫秒
    
    private final Handler handler = new Handler(Looper.getMainLooper());
    private final List<ChatUIMessage> pendingMessages = new ArrayList<>();
    private final List<ChatUIMessage> displayedMessages = new ArrayList<>();
    private boolean isDisplaying = false;
    private OnMessageDisplayListener listener;

    /**
     * 消息显示监听器接口
     */
    public interface OnMessageDisplayListener {
        /**
         * 当消息显示时回调
         * @param currentMessages 当前已显示的所有消息
         */
        void onMessageDisplayed(List<ChatUIMessage> currentMessages);
        
        /**
         * 当所有消息都显示完毕时回调
         */
        void onAllMessagesDisplayed();
    }

    /**
     * 设置消息显示监听器
     * @param listener 监听器
     */
    public void setOnMessageDisplayListener(OnMessageDisplayListener listener) {
        this.listener = listener;
    }

    /**
     * 逐条显示消息列表
     * @param messages 要显示的消息列表
     */
    public void displayMessagesSequentially(List<ChatUIMessage> messages) {
        if (isDisplaying || messages == null || messages.isEmpty()) {
            Log.d(TAG, "无法显示消息：正在显示中或消息列表为空");
            return;
        }
        
        Log.d(TAG, "开始逐条显示 " + messages.size() + " 条消息");
        isDisplaying = true;
        pendingMessages.clear();
        pendingMessages.addAll(messages);
        displayedMessages.clear();
        
        // 显示第一条消息
        displayNextMessage();
    }

    /**
     * 显示下一条消息
     */
    private void displayNextMessage() {
        if (pendingMessages.isEmpty()) {
            Log.d(TAG, "所有消息已显示完毕");
            isDisplaying = false;
            if (listener != null) {
                listener.onAllMessagesDisplayed();
            }
            return;
        }
        
        // 添加下一条消息到已显示列表
        ChatUIMessage nextMessage = pendingMessages.remove(0);
        displayedMessages.add(nextMessage);
        
        Log.d(TAG, "显示消息: " + (nextMessage.isUserMessage() ? "用户" : "AI") + 
                " - " + (nextMessage.getContent().length() > 20 ? 
                nextMessage.getContent().substring(0, 20) + "..." : 
                nextMessage.getContent()));
        
        // 通知UI更新
        if (listener != null) {
            listener.onMessageDisplayed(new ArrayList<>(displayedMessages));
        }
        
        // 延迟显示下一条消息
        handler.postDelayed(this::displayNextMessage, DEFAULT_DELAY);
    }
    
    /**
     * 重置状态
     */
    public void reset() {
        Log.d(TAG, "重置消息显示管理器状态");
        handler.removeCallbacksAndMessages(null);
        pendingMessages.clear();
        displayedMessages.clear();
        isDisplaying = false;
    }
    
    /**
     * 是否正在显示消息
     * @return 是否正在显示
     */
    public boolean isDisplaying() {
        return isDisplaying;
    }
} 