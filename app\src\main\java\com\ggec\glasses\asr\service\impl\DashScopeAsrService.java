package com.ggec.glasses.asr.service.impl;

import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import com.alibaba.dashscope.audio.asr.recognition.Recognition;
import com.alibaba.dashscope.audio.asr.recognition.RecognitionParam;
import com.alibaba.dashscope.audio.asr.recognition.RecognitionResult;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.ggec.glasses.asr.exception.AsrException;
import com.ggec.glasses.asr.model.AsrRequest;
import com.ggec.glasses.asr.model.AsrResult;
import com.ggec.glasses.asr.service.AsrService;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicBoolean;

import io.reactivex.BackpressureStrategy;
import io.reactivex.Flowable;
import io.reactivex.processors.PublishProcessor;

/**
 * 阿里云DashScope语音识别服务实现
 */
public class DashScopeAsrService implements AsrService {
    private static final String TAG = "DashScopeAsrService";
    
    // 配置常量
    private static final String SECRETS_FILE = "secrets.properties";
    private static final String DASHSCOPE_API_KEY = "DASHSCOPE_API_KEY";
    private static final String DEFAULT_MODEL = "paraformer-realtime-v2";
    private static final String DEFAULT_FORMAT = "pcm";
    private static final int DEFAULT_SAMPLE_RATE = 16000;
    
    private final Context context;
    private String apiKey;
    private String model = DEFAULT_MODEL;
    private String format = DEFAULT_FORMAT;
    private int sampleRate = DEFAULT_SAMPLE_RATE;
    
    private Recognition recognition;
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final AtomicBoolean recognizing = new AtomicBoolean(false);
    
    private PublishProcessor<AsrRequest> audioDataProcessor;
    private PublishProcessor<AsrResult> resultProcessor;
    
    /**
     * 创建DashScope语音识别服务
     * @param context 应用上下文
     */
    public DashScopeAsrService(Context context) {
        this.context = context.getApplicationContext();
        loadConfigFromAssets();
    }
    
    /**
     * 从assets目录的secrets.properties文件加载配置
     */
    private void loadConfigFromAssets() {
        Properties properties = new Properties();
        AssetManager assetManager = context.getAssets();
        
        try (InputStream inputStream = assetManager.open(SECRETS_FILE)) {
            properties.load(inputStream);
            this.apiKey = properties.getProperty(DASHSCOPE_API_KEY);
            
            if (this.apiKey == null || this.apiKey.equals("your_api_key_here") || this.apiKey.isEmpty()) {
                Log.w(TAG, "API密钥未设置或使用了默认值，请在secrets.properties中设置有效的API密钥");
            } else {
                Log.d(TAG, "成功加载API密钥");
            }
        } catch (IOException e) {
            Log.e(TAG, "加载secrets.properties文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查配置是否有效
     */
    private boolean isConfigValid() {
        return apiKey != null && !apiKey.isEmpty() && 
               !apiKey.equals("your_api_key_here") &&
               model != null && !model.isEmpty() &&
               format != null && !format.isEmpty() &&
               sampleRate > 0;
    }
    
    @Override
    public boolean initialize() {
        if (initialized.get()) {
            Log.d(TAG, "已经初始化，跳过");
            return true;
        }
        
        if (!isConfigValid()) {
            Log.e(TAG, "配置无效，无法初始化ASR服务");
            return false;
        }
        
        try {
            recognition = new Recognition();
            // 注意：新版本的API不使用setApiKey方法，而是在构建RecognitionParam时设置apiKey
            initialized.set(true);
            Log.d(TAG, "DashScope语音识别服务初始化成功");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "初始化DashScope语音识别服务失败", e);
            return false;
        }
    }
    
    @Override
    public boolean isInitialized() {
        return initialized.get();
    }
    
    @Override
    public Flowable<AsrResult> startRecognition() {
        if (!initialized.get()) {
            return Flowable.error(new AsrException("服务未初始化，请先调用initialize()方法"));
        }
        
        if (recognizing.getAndSet(true)) {
            return Flowable.error(new AsrException("识别已在进行中，请先停止当前识别"));
        }
        
        try {
            // 创建用于接收音频数据的处理器
            audioDataProcessor = PublishProcessor.create();
            
            // 创建用于发送结果的处理器
            resultProcessor = PublishProcessor.create();
            
            // 创建音频数据Flowable
            Flowable<java.nio.ByteBuffer> audioSourceFlowable = createAudioSourceFlowable();
            
            // 创建识别参数
            RecognitionParam param = RecognitionParam.builder()
                    .model(model)
                    .format(format)
                    .sampleRate(sampleRate)
                    .apiKey(apiKey)  // API密钥在这里设置
                    .build();
            
            // 启动异步流式调用
            new Thread(() -> {
                try {
                    recognition.streamCall(param, audioSourceFlowable)
                            .blockingForEach(result -> {
                                if (recognizing.get()) {
                                    processRecognitionResult(result);
                                }
                            });
                } catch (NoApiKeyException e) {
                    Log.e(TAG, "API Key错误", e);
                    resultProcessor.onError(new AsrException("API Key错误: " + e.getMessage()));
                } catch (Exception e) {
                    Log.e(TAG, "语音识别过程出错", e);
                    if (recognizing.get()) {
                        resultProcessor.onError(new AsrException("语音识别过程出错: " + e.getMessage()));
                    }
                } finally {
                    recognizing.set(false);
                }
            }).start();
            
            return resultProcessor;
        } catch (Exception e) {
            recognizing.set(false);
            Log.e(TAG, "启动语音识别失败", e);
            return Flowable.error(new AsrException("启动语音识别失败: " + e.getMessage()));
        }
    }
    
    /**
     * 创建音频源Flowable
     * @return 音频数据流
     */
    private Flowable<java.nio.ByteBuffer> createAudioSourceFlowable() {
        return Flowable.create(emitter -> {
            // 订阅音频数据处理器
            audioDataProcessor.subscribe(
                request -> {
                    try {
                        if (request.isEnd()) {
                            if (!emitter.isCancelled()) {
                                emitter.onComplete();
                            }
                        } else {
                            if (!emitter.isCancelled()) {
                                emitter.onNext(request.getAudioData());
                            }
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "处理音频数据时出错", e);
                        if (!emitter.isCancelled()) {
                            emitter.onError(e);
                        }
                    }
                },
                error -> {
                    Log.e(TAG, "音频数据流处理出错", error);
                    if (!emitter.isCancelled()) {
                        emitter.onError(error);
                    }
                },
                () -> {
                    if (!emitter.isCancelled()) {
                        emitter.onComplete();
                    }
                }
            );
        }, BackpressureStrategy.BUFFER);
    }
    
    /**
     * 处理识别结果
     * @param result DashScope识别结果
     */
    private void processRecognitionResult(RecognitionResult result) {
        if (result == null) {
            return;
        }
        
        try {
            String text = result.getSentence().getText();
            boolean isFinal = result.isSentenceEnd();
            Log.d(TAG, "识别结果: " + text + ", 是否最终结果: " + isFinal);
            
            AsrResult asrResult = AsrResult.success(text, isFinal);
            resultProcessor.onNext(asrResult);
        } catch (Exception e) {
            Log.e(TAG, "处理识别结果时出错", e);
        }
    }
    
    @Override
    public boolean processAudioData(AsrRequest request) {
        if (!initialized.get()) {
            Log.e(TAG, "服务未初始化，无法处理音频数据");
            return false;
        }
        
        if (!recognizing.get()) {
            Log.e(TAG, "识别未在进行中，无法处理音频数据");
            return false;
        }
        
        try {
            audioDataProcessor.onNext(request);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "处理音频数据出错", e);
            return false;
        }
    }
    
    @Override
    public boolean stopRecognition() {
        if (!recognizing.getAndSet(false)) {
            Log.d(TAG, "识别未在进行中，无需停止");
            return true;
        }
        
        try {
            // 发送结束信号
            if (audioDataProcessor != null && !audioDataProcessor.hasComplete()) {
                audioDataProcessor.onNext(AsrRequest.end());
                audioDataProcessor.onComplete();
            }
            
            // 关闭结果处理器
            if (resultProcessor != null && !resultProcessor.hasComplete()) {
                resultProcessor.onComplete();
            }
            
            Log.d(TAG, "语音识别已停止");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "停止识别时出错", e);
            return false;
        }
    }
    
    @Override
    public void release() {
        stopRecognition();
        initialized.set(false);
        recognition = null;
        Log.d(TAG, "DashScope语音识别服务已释放");
    }
    
    /**
     * 设置识别模型
     * @param model 模型名称
     */
    public void setModel(String model) {
        this.model = model;
    }
    
    /**
     * 设置音频格式
     * @param format 音频格式
     */
    public void setFormat(String format) {
        this.format = format;
    }
    
    /**
     * 设置采样率
     * @param sampleRate 采样率
     */
    public void setSampleRate(int sampleRate) {
        this.sampleRate = sampleRate;
    }
} 