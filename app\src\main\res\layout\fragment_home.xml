<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray">

    <!-- 状态栏和导航栏的容器 -->
    <LinearLayout
        android:id="@+id/top_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 状态栏背景 -->
        <View
            android:id="@+id/status_bar_background"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/comp_background_gray" />

        <!-- 顶部导航栏 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/header_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/comp_background_gray"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="12dp">

            <!-- 设备名称 -->
            <TextView
                android:id="@+id/tv_device_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="GGEC_AIGlass"
                android:textColor="@color/font_primary"
                android:textSize="26sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginTop="8dp" />

            <!-- 蓝牙状态 -->
            <TextView
                android:id="@+id/tv_bluetooth_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="系统蓝牙未开启，点我开启"
                android:textColor="@color/brand"
                android:textSize="14sp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_device_name"
                android:layout_marginTop="2dp" />

            <!-- 下拉菜单按钮 -->
            <ImageView
                android:id="@+id/btn_device_spinner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:contentDescription="下拉菜单"
                android:padding="8dp"
                android:src="@drawable/ic_public_spinner_small"
                app:layout_constraintBottom_toBottomOf="@id/tv_device_name"
                app:layout_constraintStart_toEndOf="@id/tv_device_name"
                app:layout_constraintTop_toTopOf="@id/tv_device_name"
                app:tint="@color/icon_primary" />

            <!-- 电量显示 -->
            <TextView
                android:id="@+id/tv_battery_level"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--%"
                android:textColor="@color/font_tertiary"
                android:textSize="14sp"
                android:drawableStart="@drawable/ic_battery"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_bluetooth_status"
                app:layout_constraintBottom_toBottomOf="@id/tv_bluetooth_status" />

            <!-- 设置按钮 -->
            <ImageView
                android:id="@+id/btn_settings"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="设置"
                android:padding="12dp"
                android:src="@drawable/ic_public_settings"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/icon_primary" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <!-- 眼镜图片 -->
    <ImageView
        android:id="@+id/iv_glasses"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="0dp"
        android:contentDescription="眼镜图片"
        android:src="@drawable/glasses"
        android:scaleType="fitCenter"
        app:layout_constraintWidth_percent="0.95"
        app:layout_constraintDimensionRatio="1:0.6"
        app:layout_constraintTop_toBottomOf="@id/top_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btn_pair_glasses"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintVertical_bias="0" />

    <!-- 配对眼镜按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_pair_glasses"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="12dp"
        android:paddingStart="32dp"
        android:paddingEnd="32dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:text="配对眼镜"
        android:textColor="@color/font_on_primary"
        android:textSize="16sp"
        app:backgroundTint="@color/brand"
        app:cornerRadius="24dp"
        app:rippleColor="@color/interactive_pressed"
        app:layout_constraintWidth_percent="0.65"
        app:layout_constraintTop_toBottomOf="@+id/iv_glasses"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/volume_settings_card"
        app:layout_constraintVertical_bias="0" />
        
    <!-- 音量设置卡片 -->
    <include
        android:id="@+id/volume_settings_card"
        layout="@layout/item_volume_settings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@+id/btn_pair_glasses"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_ai_glasses_settings_title"
        app:layout_constraintVertical_bias="0" />

    <!-- AI眼镜设置标题 -->
    <TextView
        android:id="@+id/tv_ai_glasses_settings_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:text="眼镜功能设置"
        android:textColor="@color/font_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@+id/volume_settings_card"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintVertical_bias="0" />

    <!-- 功能卡片网格 - 第一行 -->
    <!-- AI大模型设置卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_ai_model_settings"
        android:layout_width="0dp"
        android:layout_height="90dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="4dp"
        app:cardBackgroundColor="@color/background_primary"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toStartOf="@+id/card_key_settings"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_ai_glasses_settings_title">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="AI大模型设置"
                android:textColor="@color/font_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="待开发"
                android:textColor="@color/font_tertiary"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 按键设置卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_key_settings"
        android:layout_width="0dp"
        android:layout_height="90dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        app:cardBackgroundColor="@color/background_primary"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/card_ai_model_settings"
        app:layout_constraintTop_toBottomOf="@+id/tv_ai_glasses_settings_title">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="按键设置"
                android:textColor="@color/font_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="待开发"
                android:textColor="@color/font_tertiary"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 功能卡片网格 - 第二行 -->
    <!-- Debug卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_debug"
        android:layout_width="0dp"
        android:layout_height="90dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="4dp"
        app:cardBackgroundColor="@color/background_primary"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toStartOf="@+id/card_empty"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_ai_model_settings">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Debug"
                android:textColor="@color/font_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="待开发"
                android:textColor="@color/font_tertiary"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 空白卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_empty"
        android:layout_width="0dp"
        android:layout_height="90dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        app:cardBackgroundColor="@color/background_primary"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/card_debug"
        app:layout_constraintTop_toBottomOf="@+id/card_key_settings">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textColor="@color/font_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="invisible" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="待开发"
                android:textColor="@color/font_tertiary"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 