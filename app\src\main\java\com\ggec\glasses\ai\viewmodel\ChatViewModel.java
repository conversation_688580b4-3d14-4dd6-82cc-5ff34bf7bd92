package com.ggec.glasses.ai.viewmodel;

import android.app.Application;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import android.util.Log;

import com.ggec.glasses.ai.constants.ChatConstants;
import com.ggec.glasses.ai.data.ChatUIMessage;
import com.ggec.glasses.ai.data.db.entity.ChatMessage;
import com.ggec.glasses.ai.data.db.repository.RepositoryCallback;
import com.ggec.glasses.ai.manager.ChatVoiceHandler;
import com.ggec.glasses.ai.manager.ChatMessageManager;
import com.ggec.glasses.ai.manager.ChatUIManager;
import com.ggec.glasses.ai.manager.AIChatCoordinator;

import java.util.ArrayList;
import java.util.List;

/**
 * 聊天视图模型 (Refactored)
 * 协调 <PERSON>t<PERSON>essageManager, ChatVoiceHand<PERSON>, AIChatCoordinator 和 ChatUIManager (主要负责UI状态)
 */
public class ChatViewModel extends AndroidViewModel {
    
    // 使用统一的会话ID常量
    private final long defaultConversationId = ChatConstants.DEFAULT_CONVERSATION_ID;
    
    // 管理器引用
    private final ChatVoiceHandler chatVoiceHandler;
    private final ChatMessageManager chatMessageManager;
    private final ChatUIManager chatUIManager; // 主要用于 UI 状态管理
    private final AIChatCoordinator aiChatCoordinator;
    
    // 直接暴露来自 ChatMessageManager 的 LiveData 给 UI 层
    private final LiveData<List<ChatUIMessage>> messagesLiveData;
    
    /**
     * 构造函数
     * @param application 应用程序上下文
     */
    public ChatViewModel(Application application) {
        super(application);
        
        // 初始化管理器
        chatMessageManager = new ChatMessageManager(application, defaultConversationId);
        chatVoiceHandler = new ChatVoiceHandler(application);
        chatUIManager = new ChatUIManager(); 
        aiChatCoordinator = new AIChatCoordinator(application, chatMessageManager);
        
        // 获取由数据库驱动的消息 LiveData
        messagesLiveData = chatMessageManager.getUiMessagesLiveData();
        
        Log.d("ChatViewModel", "初始化完成，使用会话ID: " + defaultConversationId);

        // 设置监听器
        setupManagerListeners();
    }

    /**
     * 设置监听器
     */
    private void setupManagerListeners() {
        // 监听语音识别结果 -> 触发消息添加
        chatVoiceHandler.getFinalRecognitionResult().observeForever(finalResult -> {
            if (finalResult != null && !finalResult.trim().isEmpty()) {
                addUserMessage(finalResult); 
            }
        });

        // 监听语音识别错误 -> 更新UI状态
        chatVoiceHandler.getRecognitionError().observeForever(error -> {
            if (error != null) {
                chatUIManager.postError("语音识别错误: " + error);
            }
        });
        
        // 可以在这里添加对 messagesLiveData 的观察，用于触发加载状态或动画
        // messagesLiveData.observeForever(messages -> {
        //     // 例如，根据列表是否为空设置初始加载状态
        //     if (chatUIManager.getIsLoading().getValue() == null || !chatUIManager.getIsLoading().getValue()) {
        //         // 只有在非加载状态下才根据列表变化调整，避免冲突
        //     }
        // });
    }
    
    /**
     * 获取消息列表的LiveData (UI层)
     * 直接返回由 ChatMessageManager 提供的 LiveData
     * @return 消息列表的LiveData
     */
    public LiveData<List<ChatUIMessage>> getMessages() {
        return messagesLiveData;
    }
    
    /**
     * 获取显示中的消息列表LiveData (兼容旧接口，直接返回 messagesLiveData)
     * @return 显示中的消息列表LiveData
     */
    public LiveData<List<ChatUIMessage>> getDisplayMessages() {
        return messagesLiveData;
    }
    
    /**
     * 获取加载状态的LiveData
     * 从 ChatUIManager 获取
     * @return 加载状态的LiveData
     */
    public LiveData<Boolean> getIsLoading() {
        return chatUIManager.getIsLoading();
    }
    
    /**
     * 获取错误消息的LiveData
     * 从 ChatUIManager 获取
     * @return 错误消息的LiveData
     */
    public LiveData<String> getErrorMessage() {
        return chatUIManager.getErrorMessage();
    }
    
    /**
     * 预加载最近消息
     * 委托给 ChatMessageManager
     */
    public void preloadRecentMessages(int limit) {
        // 可以考虑在这里设置加载状态
        // chatUIManager.setLoading(true);
        try {
            chatMessageManager.preloadRecentMessages(limit);
        } catch (Exception e) {
             chatUIManager.postError("预加载消息时出错: " + e.getMessage());
        }
        // 加载状态应由 preloadRecentMessages 的回调或 LiveData 驱动
    }
    
    /**
     * 追加示例消息 (主要用于测试/演示)
     * 1. ChatMessageManager 导入数据
     * 2. 数据导入成功后，LiveData 会自动更新 UI
     * 3. 可以选择性触发追加动画
     */
    public void appendSampleMessages() {
        chatUIManager.setLoading(true);
        chatMessageManager.importSampleMessages(new RepositoryCallback<List<ChatMessage>>() {
            @Override
            public void onSuccess(List<ChatMessage> result) {
                 // 数据已插入，LiveData 会更新 UI。这里只需处理加载状态。
                 // 如果需要动画，可以在这里获取转换后的UI列表触发 appendMessagesSequentially
                 // List<ChatUIMessage> messages = chatMessageManager.convertToMessages(result);
                 // chatUIManager.appendMessagesSequentially(messages);
                 // 目前不触发动画，仅结束加载
                 chatUIManager.setLoading(false); 
            }
            
            @Override
            public void onError(String error) {
                chatUIManager.postError("追加示例消息失败: " + error);
                chatUIManager.setLoading(false);
            }
        });
    }
    
    /**
     * 触发消息列表的逐条显示动画
     * 委托给 ChatUIManager
     * @param messages 要显示动画的消息列表 (UI层)
     */
    public void displayMessagesSequentially(List<ChatUIMessage> messages) {
        chatUIManager.displayMessagesSequentially(messages);
    }
    
    /**
     * 触发消息列表的追加动画
     * 委托给 ChatUIManager
     * @param messages 要追加动画的消息列表 (UI层)
     */
    public void appendMessagesSequentially(List<ChatUIMessage> messages) {
        chatUIManager.appendMessagesSequentially(messages);
    }
    
    /**
     * 添加单条用户测试消息
     * 1. ChatMessageManager 创建并插入数据库
     * 2. LiveData 自动更新 UI
     * @param content 消息内容
     */
    public void addUserTestMessage(String content) {
        // 可以考虑设置加载状态
        // chatUIManager.setLoading(true);
        ChatMessage chatMessage = chatMessageManager.createUserMessageEntity(content);
        chatMessageManager.insertMessage(chatMessage, new RepositoryCallback<Long>() {
            @Override
            public void onSuccess(Long messageId) {
                 // LiveData 会更新 UI
                 // chatUIManager.setLoading(false);
            }
            
            @Override
            public void onError(String error) {
                chatUIManager.postError("添加用户测试消息失败: " + error);
                // chatUIManager.setLoading(false);
            }
        });
    }
    
    /**
     * 添加单条AI测试消息
     * 1. ChatMessageManager 创建并插入数据库
     * 2. LiveData 自动更新 UI
     * @param content 消息内容
     */
    public void addAITestMessage(String content) {
        // chatUIManager.setLoading(true);
        ChatMessage chatMessage = chatMessageManager.createAIMessageEntity(content);
        chatMessageManager.insertMessage(chatMessage, new RepositoryCallback<Long>() {
            @Override
            public void onSuccess(Long messageId) {
                 // LiveData 会更新 UI
                 // chatUIManager.setLoading(false);
            }
            
            @Override
            public void onError(String error) {
                chatUIManager.postError("添加AI测试消息失败: " + error);
                // chatUIManager.setLoading(false);
            }
        });
    }
    
    /**
     * 取消正在进行的消息显示/追加动画
     * 委托给 ChatUIManager
     */
    public void cancelMessageDisplay() {
        chatUIManager.cancelMessageDisplay();
    }
    
    /**
     * 清空所有消息
     * 1. ChatMessageManager 删除数据库数据
     * 2. LiveData 自动更新 UI
     */
    public void clearAllMessages() {
        chatUIManager.setLoading(true); // 开始清空操作，设置加载状态
        chatMessageManager.deleteAllMessages(new RepositoryCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                // LiveData 会更新 UI 为空列表
                // chatUIManager.clearMessagesUI(); // 不再需要手动清空 UI 列表
                chatUIManager.setLoading(false); // 结束加载状态
            }
            
            @Override
            public void onError(String error) {
                chatUIManager.postError("清空消息失败: " + error);
                chatUIManager.setLoading(false);
            }
        });
    }
    
    /**
     * 加载初始消息 (此方法逻辑已改变)
     * 由于 LiveData 会自动加载和更新，此方法主要用于触发初始加载状态（如果需要）
     * 或执行首次加载时的特定逻辑
     */
    public void loadInitialMessages() { 
        Log.d("ChatViewModel", "loadInitialMessages called. Data loading is handled by LiveData.");
        // 可以设置初始加载状态，直到 LiveData 首次返回数据
        // chatUIManager.setLoading(true);
        // messagesLiveData.observeForever(new Observer<List<ChatUIMessage>>() {
        //     @Override
        //     public void onChanged(List<ChatUIMessage> messages) {
        //         if (messages != null) {
        //             chatUIManager.setLoading(false);
        //             messagesLiveData.removeObserver(this); // 移除观察者，避免重复触发
        //         }
        //     }
        // });
        // 注意：上面的示例需要在 ViewModel clear 时移除观察者
    }

    /**
     * 开始语音识别
     * 委托给 ChatVoiceHandler
     */
    public void startVoiceRecognition() {
        chatVoiceHandler.startVoiceRecognition();
    }

    /**
     * 停止语音识别
     * 委托给 ChatVoiceHandler
     */
    public void stopVoiceRecognition() {
        chatVoiceHandler.stopVoiceRecognition();
    }
    
    /**
     * 添加用户消息 (由文本输入或语音识别触发)
     * 1. ChatMessageManager 创建并保存到数据库
     * 2. LiveData 自动更新 UI
     * 3. 触发AI回复逻辑
     * @param content 消息内容
     */
    public void addUserMessage(String content) {
        if (content == null || content.trim().isEmpty()) {
            return;
        }
        // 可以考虑在这里设置加载状态，直到用户消息显示
        // chatUIManager.setLoading(true); 
        ChatMessage userMessage = chatMessageManager.createUserMessageEntity(content);
        chatMessageManager.insertMessage(userMessage, new RepositoryCallback<Long>() {
            @Override
            public void onSuccess(Long messageId) {
                // 用户消息已插入，LiveData 会更新 UI
                // ChatUIMessage uiMessage = chatMessageManager.convertToMessage(userMessage);
                // chatUIManager.addMessageToUI(uiMessage); // 移除
                
                // 触发AI回复逻辑 (可以考虑在这里设置 AI 回复的加载状态)
                // chatUIManager.setLoading(true); // 开始等待 AI 回复
                aiChatCoordinator.requestAIReply(content, defaultConversationId);
                
                // chatUIManager.setLoading(false); // 用户消息加载完成，但可能在等待 AI
            }
            
            @Override
            public void onError(String error) {
                chatUIManager.postError("添加用户消息失败: " + error);
                // chatUIManager.setLoading(false);
            }
        });
    }
    
    // --- LiveData Getters for Voice (从 ChatVoiceHandler 获取) ---

    /**
     * 获取是否正在录音的LiveData
     * @return 录音状态LiveData
     */
    public LiveData<Boolean> getIsRecording() {
        return chatVoiceHandler.getIsRecording();
    }

    /**
     * 获取是否正在识别的LiveData
     * @return 识别状态LiveData
     */
    public LiveData<Boolean> getIsRecognizing() {
        return chatVoiceHandler.getIsRecognizing();
    }

    /**
     * 获取部分识别结果的LiveData
     * @return 部分识别结果LiveData
     */
    public LiveData<String> getPartialRecognitionResult() {
        return chatVoiceHandler.getPartialRecognitionResult();
    }
    
    @Override
    protected void onCleared() {
        super.onCleared();
        // 可以在这里清理监听器，虽然 observeForever 可能需要手动移除
        // 但对于 ViewModel 的 LiveData，通常由 LifecycleOwner 自动管理
        Log.d("ChatViewModel", "ViewModel cleared.");
    }
} 