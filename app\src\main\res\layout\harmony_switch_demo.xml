<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 使用样式方式1: 通过自定义的HarmonySwitch组件 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="使用自定义HarmonySwitch组件"
            android:textColor="@color/font_primary"
            android:textSize="16sp" />

        <com.ggec.glasses.utils.HarmonySwitch
            android:id="@+id/harmony_switch_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:gravity="center_vertical" />
    </LinearLayout>

    <!-- 使用样式方式2: 通过HarmonySwitchStyle样式 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="使用HarmonySwitchStyle样式"
            android:textColor="@color/font_primary"
            android:textSize="16sp" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/harmony_switch_2"
            style="@style/HarmonySwitchStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:gravity="center_vertical" />
    </LinearLayout>

    <!-- 默认开关样式(对比) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="默认开关样式(对比)"
            android:textColor="@color/font_primary"
            android:textSize="16sp" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/default_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:gravity="center_vertical" />
    </LinearLayout>
</LinearLayout> 