package com.ggec.glasses.ai.data.cache.api;

/**
 * 缓存条目包装类
 * 用于在缓存中存储值及其元数据
 *
 * @param <V> 缓存值类型
 */
public class CacheEntry<V> {
    
    /** 缓存的值 */
    private V value;
    
    /** 创建时间戳 */
    private final long createTime;
    
    /** 最后访问时间戳 */
    private long accessTime;
    
    /** 过期时间(毫秒)，0表示永不过期 */
    private long expireTime;
    
    /**
     * 创建缓存条目
     *
     * @param value 缓存值
     */
    public CacheEntry(V value) {
        this(value, 0);
    }
    
    /**
     * 创建缓存条目
     *
     * @param value 缓存值
     * @param expireTime 过期时间(毫秒)，0表示永不过期
     */
    public CacheEntry(V value, long expireTime) {
        this.value = value;
        this.createTime = System.currentTimeMillis();
        this.accessTime = this.createTime;
        this.expireTime = expireTime;
    }
    
    /**
     * 获取缓存值
     *
     * @return 缓存值
     */
    public V getValue() {
        this.accessTime = System.currentTimeMillis();
        return value;
    }
    
    /**
     * 设置缓存值
     *
     * @param value 缓存值
     * @return 旧的缓存值
     */
    public V setValue(V value) {
        V oldValue = this.value;
        this.value = value;
        this.accessTime = System.currentTimeMillis();
        return oldValue;
    }
    
    /**
     * 获取创建时间
     *
     * @return 创建时间戳
     */
    public long getCreateTime() {
        return createTime;
    }
    
    /**
     * 获取最后访问时间
     *
     * @return 最后访问时间戳
     */
    public long getAccessTime() {
        return accessTime;
    }
    
    /**
     * 更新访问时间
     */
    public void updateAccessTime() {
        this.accessTime = System.currentTimeMillis();
    }
    
    /**
     * 获取过期时间
     *
     * @return 过期时间(毫秒)
     */
    public long getExpireTime() {
        return expireTime;
    }
    
    /**
     * 设置过期时间
     *
     * @param expireTime 过期时间(毫秒)
     */
    public void setExpireTime(long expireTime) {
        this.expireTime = expireTime;
    }
    
    /**
     * 检查是否已过期
     *
     * @return 是否已过期
     */
    public boolean isExpired() {
        // 如果过期时间为0，表示永不过期
        if (expireTime <= 0) {
            return false;
        }
        
        // 计算生存时间与过期时间的比较
        long lifetime = System.currentTimeMillis() - createTime;
        return lifetime >= expireTime;
    }
    
    /**
     * 获取剩余生存时间
     *
     * @return 剩余生存时间(毫秒)，如果已过期返回0，如果永不过期返回Long.MAX_VALUE
     */
    public long getRemainingLifetime() {
        if (expireTime <= 0) {
            return Long.MAX_VALUE; // 永不过期
        }
        
        long lifetime = System.currentTimeMillis() - createTime;
        if (lifetime >= expireTime) {
            return 0; // 已过期
        }
        
        return expireTime - lifetime;
    }
    
    /**
     * 获取存活时间
     *
     * @return 从创建到现在的时间(毫秒)
     */
    public long getLifetime() {
        return System.currentTimeMillis() - createTime;
    }
    
    /**
     * 获取空闲时间
     *
     * @return 从最后访问到现在的时间(毫秒)
     */
    public long getIdleTime() {
        return System.currentTimeMillis() - accessTime;
    }
} 