package com.ggec.glasses.album.data.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.Index;

/**
 * 标签实体类，对应数据库中的tag表
 */
@Entity(
    tableName = "tag",
    indices = {
        @Index(value = "name", unique = true)
    }
)
public class Tag {
    
    @PrimaryKey(autoGenerate = true)
    private long id;
    
    @ColumnInfo(name = "name")
    private String name;
    
    @ColumnInfo(name = "color_code")
    private String colorCode;
    
    // 构造函数
    public Tag() {
    }
    
    public Tag(String name, String colorCode) {
        this.name = name;
        this.colorCode = colorCode;
    }
    
    // Getters and Setters
    
    public long getId() {
        return id;
    }
    
    public void setId(long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public void setColorCode(String colorCode) {
        this.colorCode = colorCode;
    }
} 