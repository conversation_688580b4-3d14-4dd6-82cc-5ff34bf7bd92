<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_dialog"
    android:paddingTop="16dp"
    android:paddingLeft="20dp"
    android:paddingRight="20dp"
    android:paddingBottom="8dp">

    <!-- 标题 -->
    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:textColor="@color/font_primary"
        android:textSize="20sp"
        android:textStyle="bold" />

    <!-- 消息内容 -->
    <TextView
        android:id="@+id/dialog_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="start"
        android:textColor="@color/font_secondary"
        android:textSize="15sp"
        android:lineSpacingExtra="2dp" />

    <!-- 按钮栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal">

        <!-- 取消按钮 -->
        <Button
            android:id="@+id/btn_negative"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:textColor="@color/brand"
            android:textSize="16sp"
            android:text="取消"
            android:padding="0dp"
            android:textAllCaps="false"
            style="?android:attr/borderlessButtonStyle" />

        <!-- 竖向分隔线 -->
        <View
            android:layout_width="0.8dp"
            android:layout_height="18dp"
            android:layout_gravity="center_vertical"
            android:background="@color/comp_divider" />

        <!-- 确认按钮 -->
        <Button
            android:id="@+id/btn_positive"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:textColor="@color/warning"
            android:textSize="16sp"
            android:textStyle="bold"
            android:text="确认"
            android:padding="0dp"
            android:textAllCaps="false"
            style="?android:attr/borderlessButtonStyle" />
    </LinearLayout>
</LinearLayout> 