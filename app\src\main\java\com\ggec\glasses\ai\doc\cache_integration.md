# 缓存与数据库集成指南

本文档介绍了AI模块中缓存系统与数据库系统的集成方案，以及如何使用这个集成系统来提高应用性能。

## 1. 集成架构

### 1.1 总体架构

缓存与数据库的集成采用了"缓存穿透"设计模式，其工作流程如下：

1. 应用层通过 `MessageRepositoryFactory` 获取仓库实例
2. 仓库层首先检查缓存是否有所需数据
3. 如果缓存命中，直接返回缓存数据
4. 如果缓存未命中，从数据库获取数据并更新缓存
5. 所有写操作（插入、更新、删除）都会同时更新缓存和数据库

这种设计确保了数据的一致性，同时最大化了读取性能。

### 1.2 关键组件

集成系统包含以下关键组件：

- **CachedChatMessageRepository**: 带缓存功能的消息仓库，是缓存与数据库的桥梁
- **MessageRepositoryFactory**: 仓库工厂，用于获取不同类型的仓库实例
- **CacheInitializer**: 缓存系统初始化器，负责在应用启动时初始化缓存并预加载数据
- **MessageCache**: 专用于缓存聊天消息的缓存实现
- **CacheManager**: 缓存管理器，负责管理所有缓存实例

## 2. 使用指南

### 2.1 初始化

在应用启动时（例如在 `Application` 类的 `onCreate` 方法中）初始化缓存系统：

```java
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        // 初始化缓存系统
        CacheInitializer.init(getApplicationContext());
    }
    
    @Override
    public void onTerminate() {
        // 关闭缓存系统
        CacheInitializer.shutdown();
        super.onTerminate();
    }
}
```

### 2.2 获取仓库实例

在需要访问消息数据的地方，通过工厂获取仓库实例：

```java
// 获取带缓存的仓库（推荐）
CachedChatMessageRepository repository = 
    MessageRepositoryFactory.getCachedRepository(context);

// 或者获取标准仓库（直接访问数据库，不使用缓存）
ChatMessageRepository standardRepo = 
    MessageRepositoryFactory.getStandardRepository(context);

// 或者获取默认仓库（默认是带缓存的版本）
CachedChatMessageRepository defaultRepo = 
    MessageRepositoryFactory.getDefaultRepository(context);
```

### 2.3 使用仓库

使用仓库的方法与直接使用 `ChatMessageRepository` 相同，只是内部会自动处理缓存：

```java
// 获取消息
repository.getMessageById(messageId, new RepositoryCallback<ChatMessage>() {
    @Override
    public void onSuccess(ChatMessage message) {
        // 使用消息
    }

    @Override
    public void onError(String error) {
        // 处理错误
    }
});

// 获取会话消息
repository.getMessagesByConversation(conversationId, new RepositoryCallback<List<ChatMessage>>() {
    @Override
    public void onSuccess(List<ChatMessage> messages) {
        // 使用消息列表
    }

    @Override
    public void onError(String error) {
        // 处理错误
    }
});

// 使用LiveData版本
LiveData<List<ChatMessage>> messagesLiveData = 
    repository.getMessagesByConversationLive(conversationId);
messagesLiveData.observe(lifecycleOwner, messages -> {
    // 使用消息列表
});
```

### 2.4 特殊操作

#### 预加载数据

在适当的时机预加载数据到缓存：

```java
// 预加载最近50条消息
repository.preloadRecentMessages(50);
```

#### 清空缓存

当需要释放内存或确保数据最新时，可以清空缓存：

```java
// 清空所有缓存
CacheInitializer.clearAll();

// 或者只清空特定会话的缓存
repository.deleteMessagesByConversation(conversationId, callback);
```

## 3. 性能优化

### 3.1 缓存策略

当前实现使用了LRU（最近最少使用）缓存策略，默认配置为：

- 消息缓存容量：100条消息
- 会话缓存容量：每个会话50条消息
- 缓存过期时间：10分钟
- 清理间隔：30秒

这些参数可以根据实际使用情况进行调整。

### 3.2 预加载策略

系统在启动时会自动预加载最近的50条消息。在进入某个会话时，建议预加载该会话的消息：

```java
// 当用户进入一个会话时
void onEnterConversation(long conversationId) {
    // 预加载该会话的消息
    repository.getMessagesByConversation(conversationId, new RepositoryCallback<List<ChatMessage>>() {
        @Override
        public void onSuccess(List<ChatMessage> messages) {
            // 消息已加载到缓存，可以直接使用
        }

        @Override
        public void onError(String error) {
            // 处理错误
        }
    });
}
```

## 4. 注意事项

1. **数据一致性**：虽然系统设计确保了缓存和数据库的一致性，但在多线程或多进程环境下仍可能出现短暂的不一致。如果应用对数据一致性有极高要求，请使用不带缓存的仓库。

2. **内存占用**：缓存会增加应用的内存占用。如果应用在低内存设备上运行，可以考虑减小缓存容量或完全禁用缓存。

3. **缓存失效**：缓存中的数据会在10分钟后过期。如果需要确保数据始终最新，可以减小过期时间或在关键操作后手动清空相关缓存。

4. **搜索操作**：搜索操作不使用缓存，而是直接查询数据库，以确保搜索结果的准确性。

## 5. 未来优化方向

1. **二级缓存**：增加磁盘缓存作为二级缓存，提高应用重启后的加载速度
2. **智能预加载**：基于用户行为分析，智能预测并预加载用户可能需要的数据
3. **缓存同步**：在多设备场景下，实现缓存的跨设备同步
4. **自适应缓存策略**：根据设备性能和内存状况自动调整缓存参数 