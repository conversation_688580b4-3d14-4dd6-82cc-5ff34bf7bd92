package com.ggec.glasses.album.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.ggec.glasses.album.data.entity.AlbumMedia;
import com.ggec.glasses.album.data.entity.Media;

import java.util.List;

/**
 * 相册媒体关联数据访问对象接口
 */
@Dao
public interface AlbumMediaDao {
    
    /**
     * 添加媒体到相册
     * @param albumMedia 相册媒体关联对象
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAlbumMedia(AlbumMedia albumMedia);
    
    /**
     * 批量添加媒体到相册
     * @param albumMediaList 相册媒体关联对象列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAlbumMediaList(List<AlbumMedia> albumMediaList);
    
    /**
     * 从相册中移除媒体
     * @param albumMedia 相册媒体关联对象
     */
    @Delete
    void deleteAlbumMedia(AlbumMedia albumMedia);
    
    /**
     * 从相册中移除指定的媒体
     * @param albumId 相册ID
     * @param mediaId 媒体ID
     */
    @Query("DELETE FROM album_media WHERE album_id = :albumId AND media_id = :mediaId")
    void removeMediaFromAlbum(long albumId, long mediaId);
    
    /**
     * 清空相册中的所有媒体
     * @param albumId 相册ID
     */
    @Query("DELETE FROM album_media WHERE album_id = :albumId")
    void clearAlbum(long albumId);
    
    /**
     * 获取相册中的所有媒体
     * @param albumId 相册ID
     * @return 相册中的媒体列表
     */
    @Query("SELECT m.* FROM media m INNER JOIN album_media am ON m.id = am.media_id WHERE am.album_id = :albumId AND m.is_deleted = 0 ORDER BY am.added_date DESC")
    List<Media> getMediaInAlbum(long albumId);
    
    /**
     * 获取相册中的所有媒体（LiveData版本）
     * @param albumId 相册ID
     * @return 包含相册中媒体列表的LiveData
     */
    @Query("SELECT m.* FROM media m INNER JOIN album_media am ON m.id = am.media_id WHERE am.album_id = :albumId AND m.is_deleted = 0 ORDER BY am.added_date DESC")
    LiveData<List<Media>> getMediaInAlbumLive(long albumId);
    
    /**
     * 获取相册中的特定类型媒体
     * @param albumId 相册ID
     * @param type 媒体类型
     * @return 相册中指定类型的媒体列表
     */
    @Query("SELECT m.* FROM media m INNER JOIN album_media am ON m.id = am.media_id WHERE am.album_id = :albumId AND m.type = :type AND m.is_deleted = 0 ORDER BY am.added_date DESC")
    List<Media> getMediaInAlbumByType(long albumId, String type);
    
    /**
     * 获取相册中的特定类型媒体（LiveData版本）
     * @param albumId 相册ID
     * @param type 媒体类型
     * @return 包含相册中指定类型媒体列表的LiveData
     */
    @Query("SELECT m.* FROM media m INNER JOIN album_media am ON m.id = am.media_id WHERE am.album_id = :albumId AND m.type = :type AND m.is_deleted = 0 ORDER BY am.added_date DESC")
    LiveData<List<Media>> getMediaInAlbumByTypeLive(long albumId, String type);
    
    /**
     * 检查媒体是否在相册中
     * @param albumId 相册ID
     * @param mediaId 媒体ID
     * @return 如果媒体在相册中，则返回true；否则返回false
     */
    @Query("SELECT COUNT(*) > 0 FROM album_media WHERE album_id = :albumId AND media_id = :mediaId")
    boolean isMediaInAlbum(long albumId, long mediaId);
    
    /**
     * 获取包含指定媒体的所有相册
     * @param mediaId 媒体ID
     * @return 包含指定媒体的相册ID列表
     */
    @Query("SELECT album_id FROM album_media WHERE media_id = :mediaId")
    List<Long> getAlbumsByMediaId(long mediaId);
    
    /**
     * 获取相册中的媒体数量
     * @param albumId 相册ID
     * @return 相册中的媒体数量
     */
    @Query("SELECT COUNT(*) FROM album_media am INNER JOIN media m ON am.media_id = m.id WHERE am.album_id = :albumId AND m.is_deleted = 0")
    int getMediaCountInAlbum(long albumId);
    
    /**
     * 获取相册中指定类型的媒体数量
     * @param albumId 相册ID
     * @param type 媒体类型
     * @return 相册中指定类型的媒体数量
     */
    @Query("SELECT COUNT(*) FROM album_media am INNER JOIN media m ON am.media_id = m.id WHERE am.album_id = :albumId AND m.type = :type AND m.is_deleted = 0")
    int getMediaCountInAlbumByType(long albumId, String type);
} 