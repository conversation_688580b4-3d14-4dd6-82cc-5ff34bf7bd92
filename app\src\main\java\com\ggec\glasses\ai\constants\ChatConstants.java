package com.ggec.glasses.ai.constants;

/**
 * 聊天相关常量
 * 集中定义所有聊天功能用到的常量，以确保一致性
 */
public class ChatConstants {
    
    /**
     * 默认会话ID
     * 所有组件应使用此常量以确保会话数据一致性
     */
    public static final long DEFAULT_CONVERSATION_ID = 0L;
    
    /**
     * 最大消息显示数量
     */
    public static final int MAX_MESSAGES_DISPLAY = 100;
    
    /**
     * 默认预加载消息数量
     */
    public static final int DEFAULT_PRELOAD_COUNT = 20;
    
    private ChatConstants() {
        // 私有构造函数防止实例化
    }
} 