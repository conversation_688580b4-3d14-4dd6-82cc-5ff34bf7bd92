<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginTop="36dp"
        android:background="@color/comp_background_gray"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="19dp"
            android:text="我的"
            android:textColor="@color/font_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 用户信息区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/user_info_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="19dp"
        android:layout_marginTop="0dp"
        android:paddingVertical="16dp"
        app:layout_constraintTop_toBottomOf="@id/header_layout">

        <!-- 用户头像 -->
        <ImageView
            android:id="@+id/iv_avatar"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="16dp"
            android:src="@drawable/ggec"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:contentDescription="用户头像" />

        <!-- 用户昵称 -->
        <TextView
            android:id="@+id/tv_username"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="GGEC_001"
            android:textColor="@color/font_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/iv_avatar"
            app:layout_constraintTop_toTopOf="@id/iv_avatar"
            app:layout_constraintBottom_toTopOf="@id/btn_edit_profile"
            app:layout_constraintVertical_chainStyle="packed" />

        <!-- 编辑个人资料按钮 -->
        <Button
            android:id="@+id/btn_edit_profile"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/btn_profile_edit_bg"
            android:includeFontPadding="false"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:stateListAnimator="@null"
            android:text="编辑个人资料"
            android:textColor="@color/brand"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@id/iv_avatar"
            app:layout_constraintTop_toBottomOf="@id/tv_username"
            app:layout_constraintBottom_toBottomOf="@id/iv_avatar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 设置区域 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/setting_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:clipToPadding="false"
        android:paddingHorizontal="19dp"
        android:paddingBottom="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/user_info_layout">

        <!-- 设置卡片容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 账号设置卡片 -->
            <include layout="@layout/layout_profile_account_security" />

            <!-- 系统设置卡片 -->
            <include
                layout="@layout/layout_profile_system_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp" />

            <!-- APP设置卡片 -->
            <include
                layout="@layout/layout_profile_app_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 