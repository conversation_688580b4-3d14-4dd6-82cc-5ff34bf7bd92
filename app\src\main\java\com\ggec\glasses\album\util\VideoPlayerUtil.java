package com.ggec.glasses.album.util;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ggec.glasses.R;
import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.google.android.exoplayer2.util.Util;

import java.io.File;
import java.util.HashMap;

/**
 * 视频播放工具类
 * 负责管理ExoPlayer实例和视频播放逻辑
 */
public class VideoPlayerUtil {
    private static final String TAG = "VideoPlayerUtil";
    private static final int ANIMATION_DURATION = 300; // 动画持续时间（毫秒）
    private static final float DEFAULT_ASPECT_RATIO = 16f / 9f; // 默认宽高比
    
    private Context context;
    private SimpleExoPlayer player;
    private PlayerView playerView;
    private boolean isPlayerInitialized = false;
    private boolean isInitialSizeSet = false; // 标记是否已设置初始尺寸
    private Handler mainHandler = new Handler(Looper.getMainLooper());
    private int[] preloadedDimensions = null; // 预加载的视频尺寸
    private boolean isSizeAdjusting = false; // 标记尺寸是否正在调整中，避免重复调整
    private ValueAnimator currentAnimator = null; // 当前运行的动画
    
    // 添加播放器准备完成的回调接口
    public interface OnPlayerPreparedListener {
        void onPlayerPrepared();
    }
    
    private OnPlayerPreparedListener onPlayerPreparedListener;
    
    /**
     * 设置播放器准备完成的回调
     * @param listener 回调接口
     */
    public void setOnPlayerPreparedListener(OnPlayerPreparedListener listener) {
        this.onPlayerPreparedListener = listener;
    }
    
    /**
     * 构造函数
     * @param context 上下文
     */
    public VideoPlayerUtil(Context context) {
        this.context = context;
    }
    
    /**
     * 初始化播放器
     * @param playerView 播放器视图
     */
    public void initializePlayer(@NonNull PlayerView playerView) {
        this.playerView = playerView;
        
        // 完全隐藏播放器视图，等准备好后再显示
        playerView.setAlpha(0f);
        
        // 创建播放器实例
        player = new SimpleExoPlayer.Builder(context)
                .setLoadControl(new com.google.android.exoplayer2.DefaultLoadControl.Builder()
                        .setBufferDurationsMs(1000, 3000, 500, 1000)
                        .build())
                .build();
        
        // 预加载Surface配置
        if (playerView.getVideoSurfaceView() != null) {
            playerView.getVideoSurfaceView().setAlpha(0f);
        }
        
        playerView.setPlayer(player);
        
        // 确保初始显示播放按钮
        View playButton = playerView.findViewById(R.id.exo_play);
        View pauseButton = playerView.findViewById(R.id.exo_pause);
        if (playButton != null) {
            playButton.setVisibility(View.VISIBLE);
        }
        if (pauseButton != null) {
            pauseButton.setVisibility(View.GONE);
        }
        
        // 设置播放器事件监听
        player.addListener(new Player.Listener() {
            public void onPlaybackStateChanged(int state) {
                // 根据播放状态更新UI
                if (state == Player.STATE_READY) {
                    // 视频准备好后显示播放器，隐藏缓冲器
                    
                    // 确保缓冲指示器被隐藏
                    View bufferingView = playerView.findViewById(R.id.exo_buffering);
                    if (bufferingView != null) {
                        bufferingView.setVisibility(View.GONE);
                    }
                    
                    // 先调整尺寸，再显示
                    if (!isInitialSizeSet) {
                        adjustPlayerAspectRatio(true);
                        // 调整完尺寸后淡入显示
                        mainHandler.postDelayed(() -> fadeInPlayerView(), 50);
                    } else {
                        // 已经设置过初始尺寸，微调到正确尺寸后淡入
                        adjustPlayerAspectRatio(false);
                        // 确保播放器显示
                        fadeInPlayerView();
                    }
                    
                    // 确保Surface也淡入
                    if (playerView.getVideoSurfaceView() != null && playerView.getVideoSurfaceView().getAlpha() < 1f) {
                        playerView.getVideoSurfaceView().animate()
                                .alpha(1f)
                                .setDuration(ANIMATION_DURATION)
                                .setInterpolator(new AccelerateDecelerateInterpolator())
                                .start();
                    }
                    
                    // 触发播放器准备完成的回调
                    if (onPlayerPreparedListener != null) {
                        onPlayerPreparedListener.onPlayerPrepared();
                    }
                } else if (state == Player.STATE_BUFFERING) {
                    // 缓冲中，显示缓冲指示器
                    View bufferingView = playerView.findViewById(R.id.exo_buffering);
                    if (bufferingView != null) {
                        bufferingView.setVisibility(View.VISIBLE);
                    }
                } else if (state == Player.STATE_ENDED) {
                    // 播放结束，隐藏缓冲指示器
                    View bufferingView = playerView.findViewById(R.id.exo_buffering);
                    if (bufferingView != null) {
                        bufferingView.setVisibility(View.GONE);
                    }
                }
            }
            
            public void onPlayerError(@NonNull ExoPlaybackException error) {
                // 播放错误处理
                Toast.makeText(context, R.string.video_player_error, Toast.LENGTH_SHORT).show();
                // 出错时也需要隐藏缓冲指示器
                View bufferingView = playerView.findViewById(R.id.exo_buffering);
                if (bufferingView != null) {
                    bufferingView.setVisibility(View.GONE);
                }
                // 错误时也要显示播放器（可能显示错误信息）
                fadeInPlayerView();
            }
        });
        
        isPlayerInitialized = true;
    }
    
    /**
     * 平滑淡入播放器视图
     */
    private void fadeInPlayerView() {
        if (playerView != null && playerView.getAlpha() < 1f) {
            playerView.animate()
                    .alpha(1f)
                    .setDuration(ANIMATION_DURATION)
                    .setInterpolator(new AccelerateDecelerateInterpolator())
                    .start();
        }
    }
    
    /**
     * 尝试预加载视频尺寸信息
     * @param videoPath 视频文件路径
     * @return 是否成功预加载
     */
    private boolean preloadVideoDimensions(String videoPath) {
        try {
            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
            retriever.setDataSource(videoPath);
            
            String width = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
            String height = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
            String rotation = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION);
            
            if (width != null && height != null) {
                int videoWidth = Integer.parseInt(width);
                int videoHeight = Integer.parseInt(height);
                
                // 考虑视频旋转
                if (rotation != null && (rotation.equals("90") || rotation.equals("270"))) {
                    // 如果视频旋转了90/270度，宽高需要对调
                    int temp = videoWidth;
                    videoWidth = videoHeight;
                    videoHeight = temp;
                }
                
                if (videoWidth > 0 && videoHeight > 0) {
                    preloadedDimensions = new int[] {videoWidth, videoHeight};
                    retriever.release();
                    return true;
                }
            }
            
            retriever.release();
        } catch (Exception e) {
            // 忽略异常，后面会使用默认比例
        }
        
        return false;
    }
    
    /**
     * 设置视频初始占位尺寸（在加载前）
     * @param videoPath 视频文件路径
     */
    private void setInitialPlaceholderSize(String videoPath) {
        // 获取播放器容器
        ViewGroup playerContainer = (ViewGroup) playerView.getParent();
        if (playerContainer == null) return;
        
        // 屏幕尺寸信息
        int screenWidth = context.getResources().getDisplayMetrics().widthPixels;
        int screenHeight = context.getResources().getDisplayMetrics().heightPixels;
        int maxHeight = (int) (screenHeight * 0.7);
        float aspectRatio = DEFAULT_ASPECT_RATIO; // 默认16:9
        
        // 先尝试预加载视频尺寸
        if (preloadVideoDimensions(videoPath) && preloadedDimensions != null) {
            aspectRatio = (float) preloadedDimensions[0] / preloadedDimensions[1];
        }
        
        // 计算初始高度
        int initialHeight = (int) (screenWidth / aspectRatio);
        if (initialHeight > maxHeight) {
            initialHeight = maxHeight;
        }
        
        // 设置播放器高度
        ViewGroup.LayoutParams layoutParams = playerView.getLayoutParams();
        layoutParams.height = initialHeight;
        playerView.setLayoutParams(layoutParams);
        
        isInitialSizeSet = true;
    }
    
    /**
     * 设置视频路径并开始播放
     * @param videoPath 视频文件路径
     * @param autoPlay 是否自动播放
     */
    public void setVideoPath(String videoPath, boolean autoPlay) {
        if (!isPlayerInitialized || player == null) {
            throw new IllegalStateException("Player not initialized. Call initializePlayer() first");
        }
        
        // 验证文件是否存在
        File videoFile = new File(videoPath);
        if (!videoFile.exists()) {
            Toast.makeText(context, "视频文件不存在", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 预先加载视频尺寸，避免后续抖动
        boolean preloaded = preloadVideoDimensions(videoPath);
        
        // 预先设置播放器的占位尺寸，避免后续大幅度变化
        setInitialPlaceholderSize(videoPath);
        
        // 确保播放器视图和表面完全透明
        playerView.setAlpha(0f);
        if (playerView.getVideoSurfaceView() != null) {
            playerView.getVideoSurfaceView().setAlpha(0f);
        }
        
        // 创建媒体源
        Uri videoUri = Uri.fromFile(videoFile);
        DataSource.Factory dataSourceFactory = new DefaultDataSourceFactory(
                context, Util.getUserAgent(context, context.getPackageName()));
        MediaSource mediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory)
                .createMediaSource(MediaItem.fromUri(videoUri));
        
        // 设置媒体源并播放
        player.setMediaSource(mediaSource);
        player.prepare();
        
        // 如果预加载成功并设置了初始尺寸，可以立即显示控制UI
        if (preloaded && isInitialSizeSet) {
            mainHandler.postDelayed(() -> {
                // 控制UI可以先显示，视频内容会在播放准备好后再显示
                // 直接淡入整个播放器UI，不单独处理控制器
                if (playerView != null && playerView.getAlpha() < 1f) {
                    playerView.animate()
                            .alpha(0.8f)
                            .setDuration(ANIMATION_DURATION)
                            .start();
                }
            }, 100);
        }
        
        player.setPlayWhenReady(autoPlay);
        
        // 添加一次性监听器以确保调整视频比例
        player.addListener(new Player.Listener() {
            public void onPlaybackStateChanged(int playbackState) {
                if (playbackState == Player.STATE_READY) {
                    // 视频准备好后调整大小（无论是否自动播放）
                    adjustPlayerAspectRatio(false);
                    // 移除此监听器，避免重复调用
                    player.removeListener(this);
                }
            }
        });
        
        // 根据播放状态更新按钮可见性
        updatePlayPauseButtonState(playerView, autoPlay);
    }
    
    /**
     * 根据播放状态更新播放/暂停按钮的可见性
     * @param playerView 播放器视图
     * @param isPlaying 是否正在播放
     */
    private void updatePlayPauseButtonState(PlayerView playerView, boolean isPlaying) {
        if (playerView == null) return;
        
        View playButton = playerView.findViewById(R.id.exo_play);
        View pauseButton = playerView.findViewById(R.id.exo_pause);
        
        if (playButton != null && pauseButton != null) {
            if (isPlaying) {
                playButton.setVisibility(View.GONE);
                pauseButton.setVisibility(View.VISIBLE);
            } else {
                playButton.setVisibility(View.VISIBLE);
                pauseButton.setVisibility(View.GONE);
            }
        }
    }
    
    /**
     * 播放暂停状态切换
     */
    public void togglePlayPause() {
        if (player != null) {
            boolean newPlayState = !player.getPlayWhenReady();
            player.setPlayWhenReady(newPlayState);
            updatePlayPauseButtonState(playerView, newPlayState);
        }
    }
    
    /**
     * 暂停播放
     */
    public void pause() {
        if (player != null) {
            player.setPlayWhenReady(false);
            updatePlayPauseButtonState(playerView, false);
        }
    }
    
    /**
     * 继续播放
     */
    public void resume() {
        if (player != null) {
            player.setPlayWhenReady(true);
            updatePlayPauseButtonState(playerView, true);
        }
    }
    
    /**
     * 释放播放器资源
     */
    public void release() {
        if (player != null) {
            player.release();
            player = null;
        }
        isPlayerInitialized = false;
        isInitialSizeSet = false;
        preloadedDimensions = null;
    }
    
    /**
     * 释放资源并设置播放器为null
     */
    public void cleanup() {
        release();
        playerView = null;
    }
    
    /**
     * 是否正在播放
     * @return 是否正在播放
     */
    public boolean isPlaying() {
        return player != null && player.getPlayWhenReady();
    }
    
    /**
     * 获取播放器实例
     * @return ExoPlayer实例
     */
    @Nullable
    public SimpleExoPlayer getPlayer() {
        return player;
    }
    
    /**
     * 根据视频的宽高比调整播放器尺寸
     * @param immediate 是否立即调整（不使用动画）
     */
    private void adjustPlayerAspectRatio(boolean immediate) {
        if (player == null || playerView == null || isSizeAdjusting) return;

        // 标记为正在调整中
        isSizeAdjusting = true;
        
        // 获取视频宽高
        int videoWidth = 0;
        int videoHeight = 0;
        
        // 如果有预加载的尺寸，优先使用
        if (preloadedDimensions != null) {
            videoWidth = preloadedDimensions[0];
            videoHeight = preloadedDimensions[1];
        }
        
        // 如果没有预加载尺寸，尝试从播放器获取
        if (videoWidth <= 0 || videoHeight <= 0) {
            try {
                // 尝试不同方式获取视频尺寸
                if (player.getVideoSize().width > 0 && player.getVideoSize().height > 0) {
                    videoWidth = player.getVideoSize().width;
                    videoHeight = player.getVideoSize().height;
                } else if (player.getVideoFormat() != null && player.getVideoFormat().width > 0) {
                    videoWidth = player.getVideoFormat().width;
                    videoHeight = player.getVideoFormat().height;
                }
            } catch (Exception e) {
                // 忽略错误，使用默认比例
            }
        }
        
        // 如果无法获取视频宽高，使用默认16:9比例
        if (videoWidth <= 0 || videoHeight <= 0) {
            videoWidth = 16;
            videoHeight = 9;
        }
        
        // 获取播放器容器
        ViewGroup playerContainer = (ViewGroup) playerView.getParent();
        if (playerContainer == null) {
            isSizeAdjusting = false;
            return;
        }
        
        // 获取屏幕宽度
        int screenWidth = context.getResources().getDisplayMetrics().widthPixels;
        
        // 计算适合屏幕的播放器高度（保持视频比例）
        int playerHeight = (int) (screenWidth * videoHeight / (float) videoWidth);
        
        // 限制最大高度（不超过屏幕高度的70%）
        int screenHeight = context.getResources().getDisplayMetrics().heightPixels;
        int maxHeight = (int) (screenHeight * 0.7);
        int playerWidth = screenWidth;
        
        if (playerHeight > maxHeight) {
            playerHeight = maxHeight;
            // 重新计算宽度，保持比例
            playerWidth = (int) (maxHeight * videoWidth / (float) videoHeight);
        }
        
        // 获取当前播放器高度
        final int currentHeight = playerView.getLayoutParams().height;
        final int targetHeight = playerHeight;
        final int targetWidth = playerWidth;
        
        // 如果高度相近或要求立即调整，直接设置
        if (immediate || Math.abs(currentHeight - targetHeight) < 10) {
            ViewGroup.LayoutParams containerParams = playerContainer.getLayoutParams();
            if (targetWidth < screenWidth) {
                containerParams.width = targetWidth;
            } else {
                containerParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
            }
            playerContainer.setLayoutParams(containerParams);
            
            ViewGroup.LayoutParams layoutParams = playerView.getLayoutParams();
            layoutParams.height = targetHeight;
            playerView.setLayoutParams(layoutParams);
            
            // 标记调整完成
            isSizeAdjusting = false;
            return;
        }
        
        // 取消当前可能正在运行的动画
        if (currentAnimator != null && currentAnimator.isRunning()) {
            currentAnimator.cancel();
        }
        
        // 使用动画平滑过渡到新的尺寸
        final ValueAnimator heightAnimator = ValueAnimator.ofInt(currentHeight, targetHeight);
        currentAnimator = heightAnimator;
        heightAnimator.setDuration(ANIMATION_DURATION);
        heightAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        
        heightAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (playerView == null) return;
                
                int animatedValue = (int) animation.getAnimatedValue();
                ViewGroup.LayoutParams layoutParams = playerView.getLayoutParams();
                layoutParams.height = animatedValue;
                playerView.setLayoutParams(layoutParams);
                
                // 如果需要调整宽度
                if (targetWidth < screenWidth && playerContainer != null) {
                    float progress = animation.getAnimatedFraction();
                    int currentWidth = playerContainer.getLayoutParams().width;
                    if (currentWidth != targetWidth) {
                        int animatedWidth;
                        if (currentWidth == ViewGroup.LayoutParams.MATCH_PARENT) {
                            animatedWidth = (int)(screenWidth - (screenWidth - targetWidth) * progress);
                        } else {
                            animatedWidth = (int)(currentWidth + (targetWidth - currentWidth) * progress);
                        }
                        ViewGroup.LayoutParams containerParams = playerContainer.getLayoutParams();
                        containerParams.width = animatedWidth;
                        playerContainer.setLayoutParams(containerParams);
                    }
                }
            }
        });
        
        heightAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                // 动画结束时标记为完成
                isSizeAdjusting = false;
            }
            
            @Override
            public void onAnimationCancel(Animator animation) {
                // 动画取消时也要标记为完成
                isSizeAdjusting = false;
            }
        });
        
        heightAnimator.start();
    }
} 