package com.ggec.glasses.album.data.entity;

import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.ColumnInfo;

import java.util.Date;

/**
 * 相册和媒体的关联实体类，对应数据库中的album_media表
 * 使用复合主键（albumId和mediaId）
 */
@Entity(
    tableName = "album_media",
    primaryKeys = {"album_id", "media_id"},
    foreignKeys = {
        @ForeignKey(
            entity = Album.class,
            parentColumns = "id",
            childColumns = "album_id",
            onDelete = ForeignKey.CASCADE
        ),
        @ForeignKey(
            entity = Media.class,
            parentColumns = "id",
            childColumns = "media_id",
            onDelete = ForeignKey.CASCADE
        )
    },
    indices = {
        @Index("album_id"),
        @Index("media_id")
    }
)
public class AlbumMedia {
    
    @ColumnInfo(name = "album_id")
    private long albumId;
    
    @ColumnInfo(name = "media_id")
    private long mediaId;
    
    @ColumnInfo(name = "added_date")
    private Date addedDate;
    
    // 构造函数
    public AlbumMedia() {
    }
    
    public AlbumMedia(long albumId, long mediaId) {
        this.albumId = albumId;
        this.mediaId = mediaId;
        this.addedDate = new Date();
    }
    
    // Getters and Setters
    
    public long getAlbumId() {
        return albumId;
    }
    
    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }
    
    public long getMediaId() {
        return mediaId;
    }
    
    public void setMediaId(long mediaId) {
        this.mediaId = mediaId;
    }
    
    public Date getAddedDate() {
        return addedDate;
    }
    
    public void setAddedDate(Date addedDate) {
        this.addedDate = addedDate;
    }
} 