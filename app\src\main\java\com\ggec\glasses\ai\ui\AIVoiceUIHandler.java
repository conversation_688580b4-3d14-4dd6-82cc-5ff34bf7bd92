package com.ggec.glasses.ai.ui;

import android.Manifest;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.view.MotionEvent;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.ggec.glasses.R;
import com.ggec.glasses.ai.viewmodel.ChatViewModel;
import com.ggec.glasses.utils.DialogUtils;
import com.ggec.glasses.utils.PermissionManager;

import java.util.List;

/**
 * 处理 AIFragment 语音按钮 UI 和权限的帮助类
 */
public class AIVoiceUIHandler {

    private static final int REQUEST_RECORD_AUDIO_PERMISSION = 200;

    private final Fragment fragment;
    private final Context context;
    private final ImageView btnVoice;
    private final ChatViewModel chatViewModel;
    private ValueAnimator pulseAnimator;
    private PermissionManager permissionManager;

    /**
     * 构造函数
     * @param fragment AIFragment 实例
     * @param btnVoice 语音按钮 ImageView
     * @param chatViewModel ChatViewModel 实例
     */
    public AIVoiceUIHandler(Fragment fragment, ImageView btnVoice, ChatViewModel chatViewModel) {
        this.fragment = fragment;
        this.context = fragment.requireContext();
        this.btnVoice = btnVoice;
        this.chatViewModel = chatViewModel;
        this.permissionManager = new PermissionManager(fragment.requireActivity());
        createPulseAnimation();
    }

    /**
     * 创建按钮脉冲动画
     */
    private void createPulseAnimation() {
        pulseAnimator = ValueAnimator.ofFloat(1.0f, 1.2f, 1.0f);
        pulseAnimator.setDuration(800);
        pulseAnimator.setRepeatCount(ValueAnimator.INFINITE);
        pulseAnimator.addUpdateListener(animation -> {
            float value = (float) animation.getAnimatedValue();
            if (btnVoice != null) {
                btnVoice.setScaleX(value);
                btnVoice.setScaleY(value);
            }
        });
    }

    /**
     * 更新语音按钮状态和动画
     * @param isRecording 是否正在录音
     */
    public void updateVoiceButtonState(boolean isRecording) {
        if (isRecording) {
            // 开始脉冲动画
            if (pulseAnimator != null && !pulseAnimator.isRunning()) {
                pulseAnimator.start();
            }
        } else {
            // 停止脉冲动画
            if (pulseAnimator != null && pulseAnimator.isRunning()) {
                pulseAnimator.cancel();
            }
            // 重置按钮大小
            if (btnVoice != null) {
                btnVoice.setScaleX(1.0f);
                btnVoice.setScaleY(1.0f);
            }
        }
    }

    /**
     * 检查录音权限并开始录音，使用PermissionManager
     */
    public void checkRecordPermissionAndStart() {
        // 使用PermissionManager请求录音权限
        permissionManager.requestRecordAudioPermission(new PermissionManager.PermissionCallback() {
            @Override
            public void onPermissionGranted() {
                // 已有权限，开始录音
                startVoiceRecognition();
            }

            @Override
            public void onPermissionDenied(List<String> deniedPermissions) {
                // 显示自定义对话框，提示用户需要麦克风权限
                DialogUtils.showConfirmDialog(
                    context,
                    "需要录音权限",
                    "要使用语音功能，需要获取录音权限。请点击允许以继续使用语音功能。",
                    "重试",
                    "取消",
                    (dialog, which) -> checkRecordPermissionAndStart(), // 重试
                    null // 取消不做任何操作
                );
            }

            @Override
            public void onPermissionPermanentlyDenied(List<String> permanentlyDeniedPermissions) {
                // 引导用户去设置页面开启权限
                DialogUtils.showConfirmDialog(
                    context,
                    "需要在设置中开启权限",
                    "录音权限已被禁用，要使用语音功能，请前往设置页面手动开启录音权限。",
                    "去设置",
                    "取消",
                    (dialog, which) -> permissionManager.openAppSettings(), // 打开设置页面
                    null // 取消不做任何操作
                );
            }
        });
    }

    /**
     * 开始语音识别（调用ViewModel）
     */
    private void startVoiceRecognition() {
        if (chatViewModel != null) {
            chatViewModel.startVoiceRecognition();
        }
    }

    /**
     * 处理权限请求结果，传递给PermissionManager
     */
    public void handlePermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        permissionManager.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    /**
     * 设置语音按钮的触摸监听器
     */
    public void setupVoiceButtonListener() {
        if (btnVoice != null) {
            btnVoice.setOnTouchListener((v, event) -> {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        // 按下时开始录音
                        checkRecordPermissionAndStart();
                        return true;
                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        // 松开时停止录音
                        if (chatViewModel != null) {
                            chatViewModel.stopVoiceRecognition();
                        }
                        return true;
                }
                return false;
            });
        }
    }

    /**
     * 释放资源（例如取消动画）
     */
    public void release() {
        if (pulseAnimator != null) {
            pulseAnimator.cancel();
            pulseAnimator = null;
        }
    }
} 