package com.ggec.glasses.ai.data.cache.impl;

import com.ggec.glasses.ai.data.cache.api.CacheCallback;
import com.ggec.glasses.ai.data.cache.api.CacheEntry;
import com.ggec.glasses.ai.data.cache.api.CachePolicy;
import com.ggec.glasses.ai.data.cache.api.ICache;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 抽象缓存实现
 * 提供缓存接口的通用实现
 *
 * @param <K> 缓存键类型
 * @param <V> 缓存值类型
 */
public abstract class AbstractCache<K, V> implements ICache<K, V> {
    
    /** 缓存存储Map */
    protected final Map<K, CacheEntry<V>> cacheMap;
    
    /** 缓存策略 */
    protected CachePolicy cachePolicy;
    
    /** 缓存回调列表 */
    protected final List<CacheCallback<K, V>> callbacks;
    
    /** 读写锁，保证线程安全 */
    protected final ReadWriteLock lock;
    
    /** 最后一次清理时间 */
    protected long lastCleanTime;
    
    /**
     * 创建抽象缓存
     */
    public AbstractCache() {
        this(new CachePolicy.DefaultPolicy());
    }
    
    /**
     * 创建抽象缓存
     *
     * @param cachePolicy 缓存策略
     */
    public AbstractCache(CachePolicy cachePolicy) {
        this.cacheMap = new ConcurrentHashMap<>();
        this.cachePolicy = cachePolicy;
        this.callbacks = new ArrayList<>();
        this.lock = new ReentrantReadWriteLock();
        this.lastCleanTime = System.currentTimeMillis();
    }
    
    @Override
    public boolean put(K key, V value) {
        return put(key, value, cachePolicy.getExpireTime());
    }
    
    @Override
    public boolean put(K key, V value, long expireTime) {
        if (key == null || value == null) {
            return false;
        }
        
        // 检查并清理过期项
        checkAndCleanExpired();
        
        lock.writeLock().lock();
        try {
            CacheEntry<V> oldEntry = cacheMap.get(key);
            CacheEntry<V> newEntry = new CacheEntry<>(value, expireTime);
            
            if (oldEntry != null) {
                V oldValue = oldEntry.getValue();
                cacheMap.put(key, newEntry);
                notifyEntryUpdated(key, oldValue, value);
            } else {
                // 检查缓存是否已满
                if (cachePolicy.isFull(cacheMap.size()) && !removeEldestEntry()) {
                    return false;
                }
                
                cacheMap.put(key, newEntry);
                notifyEntryAdded(key, value);
            }
            
            return true;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    @Override
    public int putAll(Map<K, V> map) {
        if (map == null || map.isEmpty()) {
            return 0;
        }
        
        int count = 0;
        for (Map.Entry<K, V> entry : map.entrySet()) {
            if (put(entry.getKey(), entry.getValue())) {
                count++;
            }
        }
        
        return count;
    }
    
    @Override
    public V get(K key) {
        if (key == null) {
            return null;
        }
        
        lock.readLock().lock();
        try {
            CacheEntry<V> entry = cacheMap.get(key);
            if (entry == null) {
                return null;
            }
            
            // 检查是否过期
            if (entry.isExpired()) {
                lock.readLock().unlock();
                
                // 需要写锁来移除
                lock.writeLock().lock();
                try {
                    // 再次检查，防止锁释放后其他线程已经移除
                    entry = cacheMap.get(key);
                    if (entry != null && entry.isExpired()) {
                        // 已过期，移除并返回null
                        V value = entry.getValue(); // 获取过期值用于回调
                        cacheMap.remove(key);
                        notifyEntryRemoved(key, value, CacheCallback.RemovalReason.EXPIRED);
                        return null;
                    }
                    
                    // 未过期（可能被其他线程更新），重新获取
                    entry = cacheMap.get(key);
                    if (entry == null) {
                        return null;
                    }
                    
                    lock.readLock().lock(); // 降级为读锁
                } finally {
                    lock.writeLock().unlock();
                }
            }
            
            // 获取值并更新访问时间
            V value = entry.getValue();
            notifyEntryAccessed(key, value);
            return value;
        } finally {
            // 使用try-catch方式替代isHeldByCurrentThread检查
            try {
                lock.readLock().unlock();
            } catch (IllegalMonitorStateException e) {
                // 忽略异常，如果当前线程不持有锁
            }
        }
    }
    
    @Override
    public Map<K, V> getAll(List<K> keys) {
        if (keys == null || keys.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<K, V> result = new HashMap<>();
        for (K key : keys) {
            V value = get(key);
            if (value != null) {
                result.put(key, value);
            }
        }
        
        return result;
    }
    
    @Override
    public boolean contains(K key) {
        if (key == null) {
            return false;
        }
        
        lock.readLock().lock();
        try {
            CacheEntry<V> entry = cacheMap.get(key);
            return entry != null && !entry.isExpired();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    @Override
    public V remove(K key) {
        if (key == null) {
            return null;
        }
        
        lock.writeLock().lock();
        try {
            CacheEntry<V> entry = cacheMap.remove(key);
            if (entry == null) {
                return null;
            }
            
            V value = entry.getValue();
            notifyEntryRemoved(key, value, CacheCallback.RemovalReason.REMOVED);
            return value;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    @Override
    public int removeAll(List<K> keys) {
        if (keys == null || keys.isEmpty()) {
            return 0;
        }
        
        int count = 0;
        for (K key : keys) {
            if (remove(key) != null) {
                count++;
            }
        }
        
        return count;
    }
    
    @Override
    public void clear() {
        lock.writeLock().lock();
        try {
            cacheMap.clear();
            notifyCacheCleared();
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    @Override
    public int size() {
        lock.readLock().lock();
        try {
            // 返回非过期项的数量
            int count = 0;
            for (CacheEntry<V> entry : cacheMap.values()) {
                if (!entry.isExpired()) {
                    count++;
                }
            }
            return count;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    @Override
    public void addCallback(CacheCallback<K, V> callback) {
        if (callback != null) {
            lock.writeLock().lock();
            try {
                callbacks.add(callback);
            } finally {
                lock.writeLock().unlock();
            }
        }
    }
    
    @Override
    public void removeCallback(CacheCallback<K, V> callback) {
        if (callback != null) {
            lock.writeLock().lock();
            try {
                callbacks.remove(callback);
            } finally {
                lock.writeLock().unlock();
            }
        }
    }
    
    @Override
    public void setCachePolicy(CachePolicy policy) {
        if (policy != null) {
            lock.writeLock().lock();
            try {
                this.cachePolicy = policy;
                // 策略更改后，可能需要清理过期项
                checkAndCleanExpired();
            } finally {
                lock.writeLock().unlock();
            }
        }
    }
    
    @Override
    public CachePolicy getCachePolicy() {
        lock.readLock().lock();
        try {
            return cachePolicy;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 检查并清理过期项
     */
    protected void checkAndCleanExpired() {
        if (!cachePolicy.shouldCleanExpired()) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        long cleanInterval = cachePolicy.getCleanInterval();
        
        // 检查是否需要清理
        if (cleanInterval <= 0 || (currentTime - lastCleanTime) < cleanInterval) {
            return;
        }
        
        lock.writeLock().lock();
        try {
            // 再次检查时间间隔，避免多线程竞争
            if (cleanInterval > 0 && (currentTime - lastCleanTime) >= cleanInterval) {
                for (Map.Entry<K, CacheEntry<V>> entry : cacheMap.entrySet()) {
                    K key = entry.getKey();
                    CacheEntry<V> cacheEntry = entry.getValue();
                    
                    if (cacheEntry.isExpired()) {
                        V value = cacheEntry.getValue();
                        cacheMap.remove(key);
                        notifyEntryRemoved(key, value, CacheCallback.RemovalReason.EXPIRED);
                    }
                }
                
                lastCleanTime = currentTime;
            }
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 移除最旧的条目
     * 当缓存已满时调用
     *
     * @return 是否成功移除
     */
    protected abstract boolean removeEldestEntry();
    
    /**
     * 通知条目已添加
     *
     * @param key 缓存键
     * @param value 缓存值
     */
    protected void notifyEntryAdded(K key, V value) {
        for (CacheCallback<K, V> callback : callbacks) {
            try {
                callback.onEntryAdded(key, value);
            } catch (Exception e) {
                // 忽略回调异常
            }
        }
    }
    
    /**
     * 通知条目已访问
     *
     * @param key 缓存键
     * @param value 缓存值
     */
    protected void notifyEntryAccessed(K key, V value) {
        for (CacheCallback<K, V> callback : callbacks) {
            try {
                callback.onEntryAccessed(key, value);
            } catch (Exception e) {
                // 忽略回调异常
            }
        }
    }
    
    /**
     * 通知条目已更新
     *
     * @param key 缓存键
     * @param oldValue 旧缓存值
     * @param newValue 新缓存值
     */
    protected void notifyEntryUpdated(K key, V oldValue, V newValue) {
        for (CacheCallback<K, V> callback : callbacks) {
            try {
                callback.onEntryUpdated(key, oldValue, newValue);
            } catch (Exception e) {
                // 忽略回调异常
            }
        }
    }
    
    /**
     * 通知条目已移除
     *
     * @param key 缓存键
     * @param value 缓存值
     * @param reason 移除原因
     */
    protected void notifyEntryRemoved(K key, V value, CacheCallback.RemovalReason reason) {
        for (CacheCallback<K, V> callback : callbacks) {
            try {
                callback.onEntryRemoved(key, value, reason);
            } catch (Exception e) {
                // 忽略回调异常
            }
        }
    }
    
    /**
     * 通知缓存已清空
     */
    protected void notifyCacheCleared() {
        for (CacheCallback<K, V> callback : callbacks) {
            try {
                callback.onCacheCleared();
            } catch (Exception e) {
                // 忽略回调异常
            }
        }
    }
} 