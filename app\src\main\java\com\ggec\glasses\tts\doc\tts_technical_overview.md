# 文本转语音 (TTS) 技术实现概述

## 目录

1. [概述](#概述)
2. [技术栈](#技术栈)
3. [系统架构](#系统架构)
4. [关键参数](#关键参数)
5. [实现流程](#实现流程)
6. [性能考量](#性能考量)
7. [扩展方向](#扩展方向)

## 概述

本文档提供了智能眼镜项目中文本转语音(TTS)功能的技术概述。TTS模块负责将文本转换为自然语音，为用户提供听觉反馈，增强智能眼镜的交互体验。

## 技术栈

### 核心组件

| 组件 | 技术选型 | 说明 |
|------|----------|------|
| TTS引擎 | 阿里云CosyVoice SDK | 高质量语音合成服务 |
| 音频播放 | Android MediaPlayer | 本地音频播放组件 |
| 音频格式 | WAV (22050Hz, Mono, 16bit) | 语音合成输出格式 |
| 存储 | 应用缓存目录 | 临时音频文件存储 |
| 配置管理 | SecretsLoader | API密钥安全管理 |

### 依赖项

```groovy
// 阿里云CosyVoice SDK
implementation 'com.alibaba:dashscope-sdk-java:2.11.0'  // 使用DashScope SDK中的TTS模块
```

## 系统架构

TTS模块采用分层架构设计，确保各组件职责清晰，便于维护和扩展。

### 架构图

```
┌─────────────────┐
│    应用层       │
│ (Activity/      │
│  Fragment)      │
└────────┬────────┘
         │
         │ 请求合成并播放
         ▼
┌─────────────────┐
│    协调层       │
│ (TtsCoordinator)│
└────────┬────────┘
         │
         ├───────────────┐
         │               │
         ▼               ▼
┌─────────────────┐    ┌────────────────┐
│    服务层       │    │   播放管理层   │
│ (CosyVoice-     │    │ (AudioPlayback-│
│  TtsService)    │    │  Manager)      │
└────────┬────────┘    └────────▲───────┘
         │                      │
         │ 音频文件路径         │
         └──────────────────────┘
```

### 核心组件

1. **协调层**
   - `TtsCoordinator`: 中央协调器，管理TTS服务与音频播放的交互
   - 负责初始化各组件并处理交互流程

2. **服务层**
   - `CosyVoiceTtsService`: 封装阿里云CosyVoice SDK的交互逻辑
   - `TtsServiceCallback`: 定义TTS服务回调接口

3. **播放管理层**
   - `AudioPlaybackManager`: 负责音频文件的播放和资源管理
   - 处理MediaPlayer的生命周期和临时文件清理

## 关键参数

### TTS引擎参数

| 参数 | 值 | 说明 |
|------|-----|------|
| 模型 | cosyvoice-v1 | CosyVoice语音合成模型 |
| 音色 | longxiaochun | 默认使用的中文男声音色 |
| 音频格式 | WAV_22050HZ_MONO_16BIT | 高质量WAV格式 |

### 音频播放参数

| 参数 | 值 | 说明 |
|------|-----|------|
| 内容类型 | CONTENT_TYPE_SPEECH | 指示内容类型为语音 |
| 用途 | USAGE_MEDIA | 音频播放用途 |
| 临时文件 | UUID生成 | 使用UUID生成唯一文件名 |

### 安全参数

- API密钥存储在`assets/secrets.properties`文件中
- 使用`SecretsLoader`类安全加载配置
- 键名: `COSYVOICE_API_KEY`

## 实现流程

### 初始化流程

1. 应用启动时创建`TtsCoordinator`实例
2. 从配置中加载API密钥
3. 初始化`CosyVoiceTtsService`和`AudioPlaybackManager`
4. 验证API密钥有效性

### 语音合成与播放流程

1. 应用调用`TtsCoordinator.synthesizeAndPlay(text)`方法
2. 验证文本内容和服务可用性
3. `CosyVoiceTtsService`异步合成语音并生成临时WAV文件
4. 合成完成后通过回调传递音频文件路径
5. `AudioPlaybackManager`播放生成的音频文件
6. 播放完成后自动清理临时文件

### 数据流程图

```
┌──────────┐    ┌───────────────┐    ┌────────────┐    ┌──────────┐
│ 应用层   │───>│ TtsCoordinator│───>│ CosyVoice  │───>│ 阿里云   │
│ 请求TTS  │    │ 协调请求      │    │ TtsService │    │ API      │
└──────────┘    └───────────────┘    └─────┬──────┘    └────┬─────┘
                                           │                 │
                                           │                 │
                                           │                 │
┌──────────┐    ┌───────────────┐    ┌─────▼──────┐    ┌────▼─────┐
│ 用户听到 │<───│ MediaPlayer   │<───│ 音频文件   │<───│ 语音合成 │
│ 语音     │    │ 播放音频      │    │ (WAV格式)  │    │ 数据返回 │
└──────────┘    └───────────────┘    └────────────┘    └──────────┘
```

## 性能考量

### 内存优化

1. **临时文件管理**
   - 音频合成后保存为临时文件，而非保持在内存中
   - 播放完成后自动删除临时文件，避免存储空间泄漏

2. **资源释放**
   - 严格管理MediaPlayer资源释放
   - 使用try-with-resources和finally块确保流资源释放

### 用户体验优化

1. **异步处理**
   - 使用异步模式进行语音合成，避免阻塞UI线程
   - 使用`prepareAsync()`方法异步准备MediaPlayer

2. **错误处理**
   - 详细日志记录便于问题排查
   - 完善的错误处理确保健壮性

## 扩展方向

1. **多音色支持**
   - 提供音色选择界面
   - 根据不同场景自动切换音色

2. **语音参数调整**
   - 支持调整语速、音调等参数
   - 提供个性化语音风格

3. **缓存机制**
   - 为常用短语建立音频缓存
   - 减少重复合成的网络请求

4. **离线支持**
   - 集成轻量级离线TTS引擎
   - 在无网络环境下提供基础语音合成能力

5. **情感语音**
   - 利用CosyVoice的情感合成能力
   - 根据文本内容自动调整情感表达

6. **多语言支持**
   - 扩展支持英语、日语等多语言合成
   - 自动识别文本语言并选择合适的语音模型 