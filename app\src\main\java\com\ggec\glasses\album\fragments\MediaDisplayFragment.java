package com.ggec.glasses.album.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.ggec.glasses.R;
import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.manager.AlbumMediaExportManager;
import com.ggec.glasses.album.util.UIAnimationHelper;
import com.ggec.glasses.album.viewmodel.MediaDisplayViewModel;
import com.ggec.glasses.utils.DialogUtils;
import com.ggec.glasses.utils.StringUtils;
import com.github.chrisbanes.photoview.PhotoView;

import java.io.File;

/**
 * 媒体展示Fragment，负责管理UI和用户交互，将业务逻辑委托给ViewModel
 */
public class MediaDisplayFragment extends Fragment {

    private ImageView btnBack;
    private TextView tvTitle;
    private LinearLayout btnDownloadGroup;
    private LinearLayout btnShareGroup;
    private LinearLayout btnDeleteGroup;
    private LinearLayout btnMoreGroup;
    private String fullFileName; // 保存完整文件名
    private String middleEllipsisFileName; // 保存省略后的文件名
    
    // 用于控制操作层显示隐藏
    private ConstraintLayout operationLayer;
    private LinearLayout topContainer; // 状态栏和导航栏的组合容器
    private ConstraintLayout headerLayout;
    private ConstraintLayout footerLayout;
    private View statusBarBackground; // 状态栏背景
    private ConstraintLayout backgroundLayer;
    private int originalBackgroundColor; // 保存原始背景颜色

    // 媒体容器
    private FrameLayout mediaContainer;
    
    // ViewModel
    private MediaDisplayViewModel viewModel;
    
    // 动画助手
    private UIAnimationHelper animationHelper;
    private boolean isInitialLoad = true; // 添加初始加载标志

    /**
     * 创建MediaDisplayFragment实例的工厂方法
     * @return 新的MediaDisplayFragment实例
     */
    public static MediaDisplayFragment newInstance() {
        return new MediaDisplayFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化ViewModel，从Activity共享
        viewModel = new ViewModelProvider(requireActivity()).get(MediaDisplayViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_media_display, container, false);
        
        // 初始化视图
        btnBack = view.findViewById(R.id.btn_back);
        tvTitle = view.findViewById(R.id.tv_title);
        btnDownloadGroup = view.findViewById(R.id.btn_download_group);
        btnShareGroup = view.findViewById(R.id.btn_share_group);
        btnDeleteGroup = view.findViewById(R.id.btn_delete_group);
        btnMoreGroup = view.findViewById(R.id.btn_more_group);
        
        // 获取操作层视图
        operationLayer = view.findViewById(R.id.operation_layer);
        topContainer = view.findViewById(R.id.top_container);
        headerLayout = view.findViewById(R.id.header_layout);
        footerLayout = view.findViewById(R.id.footer_layout);
        statusBarBackground = view.findViewById(R.id.status_bar_background);
        
        // 获取背景层视图
        backgroundLayer = view.findViewById(R.id.background_layer);
        originalBackgroundColor = ContextCompat.getColor(requireContext(), R.color.comp_background_gray);
        
        // 获取媒体容器
        mediaContainer = view.findViewById(R.id.media_container);
        
        // 设置返回按钮点击事件
        btnBack.setOnClickListener(v -> {
            viewModel.navigateBack();
        });
        
        // 设置底部工具栏按钮事件
        setupToolbarButtons();
        
        // 初始化动画助手
        animationHelper = new UIAnimationHelper(requireContext(), view);
        animationHelper.setOriginalBackgroundColor(originalBackgroundColor);
        
        return view;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 确保状态栏背景与顶部导航栏背景颜色一致
        statusBarBackground.setBackgroundColor(originalBackgroundColor);
        
        // 确保整个页面背景为雪域灰色
        backgroundLayer.setBackgroundColor(originalBackgroundColor);
        
        // 观察媒体数据变化
        viewModel.getMedia().observe(getViewLifecycleOwner(), this::updateMediaContent);
        
        // 观察操作层可见性变化
        viewModel.getOperationLayerVisible().observe(getViewLifecycleOwner(), visible -> {
            if (visible && isInitialLoad) {
                // 首次加载且需要显示操作层：执行动画，但不执行背景动画
                animationHelper.animateOperationLayer(true, topContainer, footerLayout, false);
                isInitialLoad = false; // 更新标志
            } else {
                // 非首次加载 或 需要隐藏操作层：执行动画，包括背景动画
                animationHelper.animateOperationLayer(visible, topContainer, footerLayout, true);
            }
        });
    }
    
    /**
     * 更新媒体内容
     * @param media 媒体数据
     */
    private void updateMediaContent(Media media) {
        if (media == null) return;
        
        // 更新标题
        updateTitle(media);
        
        // 加载合适的媒体Fragment
        loadMediaFragment(media);
    }
    
    /**
     * 更新标题
     * @param media 媒体数据
     */
    private void updateTitle(Media media) {
            String fileName = media.getFileName();
            if (fileName != null && !fileName.isEmpty()) {
                fullFileName = fileName; // 保存完整文件名
                
                // 获取屏幕宽度，动态计算适合的文件名长度
                int screenWidth = requireContext().getResources().getDisplayMetrics().widthPixels;
                // 恢复原始的字符宽度估算
                int maxChars = screenWidth / 20;
                // 显著减小前缀和后缀的比例，仅保留少量字符
                int prefixLength = (int) (maxChars * 0.25); // 前缀占25%
                int suffixLength = (int) (maxChars * 0.25); // 后缀占25%
                
                // 设置一个较小的固定字符上限
                maxChars = Math.min(maxChars, 20); // 限制最大显示字符数为20
                
                // 处理文件名显示
                middleEllipsisFileName = StringUtils.getMiddleEllipsisFilename(
                        fileName, maxChars, prefixLength, suffixLength);
                tvTitle.setText(middleEllipsisFileName);
                
                // 设置点击和长按事件
                tvTitle.setOnClickListener(v -> {
                    // 点击时切换显示完整文件名和省略文件名
                    if (tvTitle.getText().toString().equals(fullFileName)) {
                        tvTitle.setText(middleEllipsisFileName);
                    } else {
                        tvTitle.setText(fullFileName);
                        // 显示提示
                        Toast.makeText(requireContext(), R.string.click_again_to_collapse, Toast.LENGTH_SHORT).show();
                    }
                });
                
                tvTitle.setOnLongClickListener(v -> {
                    // 长按时显示完整文件名的Toast提示
                    Toast.makeText(requireContext(), fullFileName, Toast.LENGTH_LONG).show();
                    return true;
                });
            } else {
                // 根据媒体类型设置默认标题
                if ("VIDEO".equals(media.getType())) {
                    tvTitle.setText(R.string.video_detail);
                } else {
                    tvTitle.setText(R.string.image_detail);
            }
        }
    }
    
    /**
     * 加载媒体Fragment
     * @param media 媒体数据
     */
    private void loadMediaFragment(Media media) {
        Fragment fragment;
            if ("VIDEO".equals(media.getType())) {
            fragment = MediaVideoFragment.newInstance(media);
                } else {
            fragment = MediaImageFragment.newInstance(media);
        }
        
        // 使用ChildFragmentManager加载媒体Fragment
        getChildFragmentManager().beginTransaction()
                .replace(R.id.media_container, fragment)
                .commit();
    }
    
    /**
     * 设置底部工具栏按钮事件
     */
    private void setupToolbarButtons() {
        // 下载按钮组
        btnDownloadGroup.setOnClickListener(v -> {
            Toast.makeText(requireContext(), "正在保存到系统相册...", Toast.LENGTH_SHORT).show();
            
            viewModel.exportMedia(requireContext(), new AlbumMediaExportManager.SaveMediaCallback() {
                @Override
                public void onSuccess() {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            Toast.makeText(requireContext(), "已成功保存到系统相册", Toast.LENGTH_SHORT).show();
                        });
                    }
                }
                
                @Override
                public void onFailed(String errorMessage) {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            Toast.makeText(requireContext(), "保存失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                        });
                    }
                }
            });
        });
        
        // 分享按钮组
        btnShareGroup.setOnClickListener(v -> {
            String message = getString(R.string.btn_share) + getString(R.string.function_under_development);
            Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show();
        });
        
        // 删除按钮组
        btnDeleteGroup.setOnClickListener(v -> {
            if (viewModel.getCurrentMedia() == null) {
                Toast.makeText(requireContext(), "媒体文件不存在", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 使用自定义确认对话框
            DialogUtils.showConfirmDialog(
                    requireContext(),
                    "确认删除",
                    "确定要删除此媒体文件吗？此操作将永久删除文件且无法恢复。",
                    "删除",
                    "取消",
                    (dialog, which) -> {
                        // 显示删除中的提示
                        Toast.makeText(requireContext(), "正在删除...", Toast.LENGTH_SHORT).show();
                        
                        // 使用ViewModel删除媒体文件
                        viewModel.deleteMedia(new com.ggec.glasses.album.data.repository.MediaRepository.OnMediaDeletedCallback() {
    @Override
                            public void onSuccess() {
                                if (getActivity() != null) {
                                    getActivity().runOnUiThread(() -> {
                                        Toast.makeText(requireContext(), "已成功删除", Toast.LENGTH_SHORT).show();
                                        viewModel.navigateBack();
                                    });
        }
    }
    
    @Override
                            public void onError(String errorMessage) {
                                if (getActivity() != null) {
                                    getActivity().runOnUiThread(() -> {
                                        Toast.makeText(requireContext(), "删除失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                                    });
        }
    }
    
    @Override
                            public void onPartialSuccess(String message) {
                                if (getActivity() != null) {
                                    getActivity().runOnUiThread(() -> {
                                        Toast.makeText(requireContext(), "文件已从数据库中删除", Toast.LENGTH_SHORT).show();
                                        viewModel.navigateBack();
                                    });
                                }
                            }
                        });
                    },
                    null
            );
        });
        
        // 更多按钮组
        btnMoreGroup.setOnClickListener(v -> {
            String message = getString(R.string.btn_more) + getString(R.string.function_under_development);
            Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show();
        });
    }
} 