package com.ggec.glasses.album.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestManager;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.ggec.glasses.R;
import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.manager.MediaSelectionManager;
import com.ggec.glasses.album.util.VideoFrameExtractor;
import com.ggec.glasses.album.util.TimeFormatUtil;

import java.io.File;
import java.util.List;

/**
 * 媒体文件适配器，处理图片和视频的展示
 */
public class MediaAdapter extends ListAdapter<Media, MediaAdapter.MediaViewHolder> {
    
    private final Context context;
    private final RequestManager glide;
    private OnItemClickListener itemClickListener;
    private boolean isMultiSelectMode = false;
    private final MediaSelectionManager selectionManager;
    
    private static final DiffUtil.ItemCallback<Media> DIFF_CALLBACK =
            new DiffUtil.ItemCallback<Media>() {
                @Override
                public boolean areItemsTheSame(@NonNull Media oldItem, @NonNull Media newItem) {
                    return oldItem.getId() == newItem.getId();
                }
                
                @Override
                public boolean areContentsTheSame(@NonNull Media oldItem, @NonNull Media newItem) {
                    return oldItem.getFilePath().equals(newItem.getFilePath()) &&
                           oldItem.getModificationDate().equals(newItem.getModificationDate());
                }
            };
    
    public MediaAdapter(Context context) {
        super(DIFF_CALLBACK);
        this.context = context;
        this.glide = Glide.with(context);
        this.selectionManager = MediaSelectionManager.getInstance();
        
        // 观察选中状态变化
        selectionManager.getSelectedCountLiveData().observeForever(count -> {
            if (isMultiSelectMode) {
                notifyDataSetChanged();
            }
        });
    }
    
    @NonNull
    @Override
    public MediaViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_media, parent, false);
        return new MediaViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull MediaViewHolder holder, int position) {
        Media media = getItem(position);
        if (media == null) return;
        
        // 根据媒体类型加载缩略图并设置播放图标
        if ("VIDEO".equals(media.getType())) {
            // 视频：显示播放图标，加载视频首帧或缩略图
            holder.ivPlayIcon.setVisibility(View.VISIBLE);
            
            // 处理视频时长显示
            if (media.getDuration() > 0) {
                String formattedDuration = TimeFormatUtil.formatDuration(media.getDuration());
                holder.tvVideoDuration.setText(formattedDuration);
                holder.tvVideoDuration.setVisibility(View.VISIBLE);
                // 添加文本阴影效果以确保在不同背景上可见
                holder.tvVideoDuration.setShadowLayer(3.0f, 1.0f, 1.0f, Color.BLACK);
            } else {
                holder.tvVideoDuration.setVisibility(View.GONE);
            }
            
            if (media.getThumbnailPath() != null && new File(media.getThumbnailPath()).exists()) {
                // 如果缩略图存在，直接加载
                loadImageWithGlide(holder.ivMediaThumbnail, media.getThumbnailPath());
            } else if (media.getFilePath() != null && new File(media.getFilePath()).exists()) {
                // 否则尝试从视频中提取首帧
                Bitmap bitmap = VideoFrameExtractor.extractVideoFrameFromVideo(context, media.getFilePath());
                if (bitmap != null) {
                    holder.ivMediaThumbnail.setImageBitmap(bitmap);
                } else {
                    // 提取失败则显示默认图标
                    holder.ivMediaThumbnail.setImageResource(R.drawable.ic_empty_album);
                }
            } else {
                // 文件不存在时显示默认图标
                holder.ivMediaThumbnail.setImageResource(R.drawable.ic_empty_album);
            }
        } else {
            // 图片：隐藏播放图标和时长显示
            holder.ivPlayIcon.setVisibility(View.GONE);
            holder.tvVideoDuration.setVisibility(View.GONE);
            
            if (media.getThumbnailPath() != null && new File(media.getThumbnailPath()).exists()) {
                // 如果有缩略图则加载缩略图
                loadImageWithGlide(holder.ivMediaThumbnail, media.getThumbnailPath());
            } else if (media.getFilePath() != null && new File(media.getFilePath()).exists()) {
                // 否则加载原图
                loadImageWithGlide(holder.ivMediaThumbnail, media.getFilePath());
            } else {
                // 文件不存在时显示默认图标
                holder.ivMediaThumbnail.setImageResource(R.drawable.ic_empty_album);
            }
        }
        
        // 处理多选模式
        if (isMultiSelectMode) {
            holder.ivSelectCheckbox.setVisibility(View.VISIBLE);
            boolean isSelected = selectionManager.isMediaSelected(media.getId());
            holder.ivSelectCheckbox.setImageResource(
                isSelected ? 
                R.drawable.ic_gallery_select_checkbox_checked : 
                R.drawable.ic_gallery_select_checkbox_unchecked
            );
            // 显示或隐藏选中背景
            holder.viewSelectedBackground.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        } else {
            holder.ivSelectCheckbox.setVisibility(View.GONE);
            holder.viewSelectedBackground.setVisibility(View.GONE);
        }
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (isMultiSelectMode) {
                boolean isSelected = selectionManager.toggleMediaSelection(media);
                holder.ivSelectCheckbox.setImageResource(
                    isSelected ? 
                    R.drawable.ic_gallery_select_checkbox_checked : 
                    R.drawable.ic_gallery_select_checkbox_unchecked
                );
                holder.viewSelectedBackground.setVisibility(isSelected ? View.VISIBLE : View.GONE);
            } else if (itemClickListener != null) {
                itemClickListener.onItemClick(media, position);
            }
        });
        
        // 设置长按事件触发多选模式
        holder.itemView.setOnLongClickListener(v -> {
            // 如果已经处于多选模式，不做任何操作
            if (isMultiSelectMode) {
                return false;
            }
            
            // 找到父级Fragment并尝试进入多选模式
            if (context instanceof androidx.fragment.app.FragmentActivity) {
                androidx.fragment.app.FragmentActivity activity = (androidx.fragment.app.FragmentActivity) context;
                
                // 尝试查找AlbumFragment
                androidx.fragment.app.Fragment albumFragment = activity.getSupportFragmentManager()
                        .findFragmentById(R.id.fragment_container);
                
                if (albumFragment instanceof com.ggec.glasses.fragments.AlbumFragment) {
                    // 进入多选模式
                    com.ggec.glasses.fragments.AlbumFragment fragment = 
                            (com.ggec.glasses.fragments.AlbumFragment) albumFragment;
                    fragment.enterMultiSelectMode();
                    
                    // 自动选中当前长按的项
                    selectionManager.toggleMediaSelection(media);
                    notifyItemChanged(position);
                    
                    return true;
                }
            }
            
            return false;
        });
    }
    
    /**
     * 使用Glide加载图片
     */
    private void loadImageWithGlide(ImageView imageView, String path) {
        glide.load(new File(path))
                .apply(new RequestOptions()
                        .centerCrop()
                        .diskCacheStrategy(DiskCacheStrategy.ALL))
                .into(imageView);
    }
    
    /**
     * 设置数据列表
     * @param mediaList 媒体列表
     */
    public void setMediaList(List<Media> mediaList) {
        submitList(mediaList);
    }
    
    /**
     * 设置项点击监听器
     * @param listener 点击监听器
     */
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.itemClickListener = listener;
    }
    
    /**
     * 切换多选模式
     * @param enabled 是否启用多选模式
     */
    public void setMultiSelectMode(boolean enabled) {
        if (this.isMultiSelectMode != enabled) {
            this.isMultiSelectMode = enabled;
            if (!enabled) {
                selectionManager.clearSelection();
            }
            notifyDataSetChanged();
        }
    }
    
    /**
     * 更新所有项的选中状态
     */
    public void notifySelectionChanged() {
        if (isMultiSelectMode) {
            notifyDataSetChanged();
        }
    }
    
    /**
     * 媒体项ViewHolder
     */
    static class MediaViewHolder extends RecyclerView.ViewHolder {
        final ImageView ivMediaThumbnail;
        final ImageView ivPlayIcon;
        final TextView tvVideoDuration;
        final ImageView ivSelectCheckbox;
        final View viewSelectedBackground;
        
        MediaViewHolder(@NonNull View itemView) {
            super(itemView);
            ivMediaThumbnail = itemView.findViewById(R.id.iv_media_thumbnail);
            ivPlayIcon = itemView.findViewById(R.id.iv_play_icon);
            tvVideoDuration = itemView.findViewById(R.id.tv_video_duration);
            ivSelectCheckbox = itemView.findViewById(R.id.iv_select_checkbox);
            viewSelectedBackground = itemView.findViewById(R.id.view_selected_background);
        }
    }
    
    /**
     * 项点击监听器接口
     */
    public interface OnItemClickListener {
        void onItemClick(Media media, int position);
    }
} 