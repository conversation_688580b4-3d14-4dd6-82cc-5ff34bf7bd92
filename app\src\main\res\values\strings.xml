<resources>
    <string name="app_name">Glasses</string>
    <string name="image_detail">图片详情</string>
    <string name="btn_download">下载</string>
    <string name="btn_share">分享</string>
    <string name="btn_delete">删除</string>
    <string name="function_under_development">功能开发中</string>
    <string name="btn_back">返回</string>
    <string name="image_content">图片内容</string>
    <string name="click_again_to_collapse">再次点击可收起</string>
    <string name="btn_more">更多</string>
    
    <!-- AI模块相关 -->
    <string name="ai_assistant">GGEC_AI助手</string>
    <string name="ai_voice_interaction">语音交互</string>
    <string name="ai_voice_feature_developing">语音功能开发中...</string>
    <string name="ai_more_options">更多选项</string>
    <string name="ai_load_example_messages">加载示例消息</string>
    <string name="ai_user_test_message">User测试消息</string>
    <string name="ai_test_message">AI测试消息</string>
    <string name="ai_clear_messages">清空消息</string>
    <string name="ai_sample_messages_loaded">示例消息已加载</string>
    <string name="ai_loading_sample_messages">正在加载示例消息...</string>
    <string name="ai_test_message_sent">测试消息已发送</string>
    <string name="ai_confirm_clear_title">确认清空消息</string>
    <string name="ai_confirm_clear_message">确定要清空所有消息吗？此操作不可恢复。</string>
    <string name="ai_messages_cleared">所有消息已清空</string>
    
    <!-- 相册模块相关 -->
    <string name="album_clear_album">清空相册</string>
    <string name="album_confirm_clear_title">确认清空相册</string>
    <string name="album_confirm_clear_message">确定要清空相册中的所有媒体文件吗？此操作不可撤销。</string>
    
    <!-- 为兼容现有代码，保留旧的string ID -->
    <string name="clear_album">@string/album_clear_album</string>
    <string name="voice_interaction">@string/ai_voice_interaction</string>
    <string name="voice_feature_developing">@string/ai_voice_feature_developing</string>
    <string name="more_options">@string/ai_more_options</string>
    <string name="load_example_messages">@string/ai_load_example_messages</string>
    <string name="user_test_message">@string/ai_user_test_message</string>
    <string name="test_message_sent">@string/ai_test_message_sent</string>
    <string name="sample_messages_loaded">@string/ai_sample_messages_loaded</string>
    <string name="loading_sample_messages">@string/ai_loading_sample_messages</string>
    
    <!-- 对话框颜色 -->
    <string name="dialog_background">#F2F2F2</string>
    
    <!-- 视频播放相关 -->
    <string name="play_button">播放</string>
    <string name="pause_button">暂停</string>
    <string name="fullscreen_button">全屏</string>
    <string name="video_player_error">视频播放错误</string>
    <string name="retry">重试</string>
    <string name="video_detail">视频详情</string>
    <string name="no_device_connected">未连接设备</string>
    
    <!-- 蓝牙状态相关 -->
    <string name="bluetooth_not_enabled">系统蓝牙未开启，点我开启</string>
    <string name="bluetooth_enabled_not_connected">蓝牙已开启未连接</string>
    <string name="bluetooth_connecting">蓝牙正在连接</string>
    <string name="bluetooth_enabled_connected">蓝牙已连接</string>
    <string name="bluetooth_permission_required">需要蓝牙权限才能使用此功能</string>
    <string name="bluetooth_already_connected">蓝牙已连接到设备</string>
    <string name="bluetooth_connecting_message">蓝牙正在连接中，请稍候</string>
    
    <!-- 录音和权限相关 -->
    <string name="permission_record_denied">需要录音权限才能使用语音功能</string>
    <string name="recording">正在录音...</string>
    <string name="recognizing">正在识别...</string>
    
    <!-- AI大模型设置 -->
    <string name="ai_model_settings_title">AI大模型设置</string>
    
    <!-- 按键设置 -->
    <string name="key_settings_title">按键设置</string>
    
    <!-- Debug设置 -->
    <string name="debug_settings_title">Debug</string>
    
    <!-- 眼镜设置 -->
    <string name="glasses_settings_title">眼镜设置</string>
    
    <!-- 蓝牙连接 -->
    <string name="bluetooth_connection_title">蓝牙连接</string>
    <string name="paired_devices_title">已配对设备</string>
    <string name="available_devices_title">可用设备</string>
    <string name="no_paired_devices">暂无已配对设备</string>
    <string name="no_available_devices">暂无可用设备</string>
    <string name="searching_devices">正在搜索设备...</string>
    <string name="bluetooth_disabled">蓝牙已关闭</string>
    <string name="refresh_devices">刷新设备</string>
</resources>