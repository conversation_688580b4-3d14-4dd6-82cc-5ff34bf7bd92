/*
 * Copyright (c) 2017-2023. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.sdk.audioconnect.support;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.os.ParcelUuid;

import com.realsil.sdk.core.bluetooth.scanner.ExtendedBluetoothDevice;
import com.realsil.sdk.core.bluetooth.scanner.LeScannerPresenter;
import com.realsil.sdk.core.bluetooth.scanner.ScannerCallback;
import com.realsil.sdk.core.bluetooth.scanner.ScannerParams;
import com.realsil.sdk.core.bluetooth.scanner.SpecScanRecord;
import com.realsil.sdk.core.bluetooth.scanner.compat.CompatScanFilter;
import com.realsil.sdk.core.bluetooth.utils.BluetoothHelper;
import com.realsil.sdk.core.logger.ZLogger;
import com.realsil.sdk.core.utility.DataConverter;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public final class ConnectionManager {
    private Context mContext;
    private LeScannerPresenter mScannerPresenter;
    private ScannerParams scannerParams;

    /**
     * Realtek:0x005D
     */
    public static final int MANUFACTURER_ID_REALTEK = 0x005D;
    private final static ParcelUuid AUDIO_CONNECT_SERVICE_UUID = ParcelUuid.fromString("000002FD-3C17-D293-8E48-14FE2E4DA212");
    public final static ParcelUuid AUDIO_CONNECT_SERVICE_UUID_PRI = ParcelUuid.fromString("010002FD-3C17-D293-8E48-14FE2E4DA212");
    public final static ParcelUuid AUDIO_CONNECT_SERVICE_UUID_SEC = ParcelUuid.fromString("020002FD-3C17-D293-8E48-14FE2E4DA212");
    public final static ParcelUuid AUDIO_CONNECT_SERVICE_UUID_MASK = ParcelUuid.fromString("FCFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF");

    private static volatile ConnectionManager mInstance;

    private ConnectionManager(Context context) {
        mContext = context;

        List<CompatScanFilter> scanFilters = new ArrayList<>();
        scanFilters.add(new CompatScanFilter.Builder()
                .setServiceUuid(AUDIO_CONNECT_SERVICE_UUID)
                .build()
        );

        scannerParams = new ScannerParams(ScannerParams.SCAN_MODE_GATT);
//            scannerParams.setNameNullable(false);
        scannerParams.setScanPeriod(60 * 1000);
        scannerParams.setConnectable(false);
        scannerParams.setAutoDiscovery(true);
        scannerParams.setAutoScanDelay(10 * 1000);
        scannerParams.setScanFilters(scanFilters);

        mScannerPresenter = new LeScannerPresenter(mContext, scannerParams, mScannerCallback);
    }

    /**
     * get instance of {@link ConnectionManager}
     *
     * @param context Application Context
     * @return {@link ConnectionManager}
     */
    public static ConnectionManager getInstance(Context context) {
        if (mInstance == null) {
            synchronized (ConnectionManager.class) {
                if (mInstance == null) {
                    mInstance = new ConnectionManager(context);
                }
            }
        }

        return mInstance;
    }


    public void startScan() {
        if (mScannerPresenter != null) {
            ZLogger.v("startScan ...");
            scannerParams.setAutoDiscovery(true);
            mScannerPresenter.setScannerParams(scannerParams);
            mScannerPresenter.startScan();
        }
    }

    public void stopScan() {
        if (mScannerPresenter != null) {
            ZLogger.v("stopScan ...");
            scannerParams.setAutoDiscovery(false);
            mScannerPresenter.setScannerParams(scannerParams);
            mScannerPresenter.stopScan();
        }
    }

    public void destroy() {
        ZLogger.v("destroy ConnectionManager");
        if (mScannerPresenter != null) {
            mScannerPresenter.onDestroy();
        }

//        if (mBeeProManager != null) {
//            mBeeProManager.removeManagerCallback(bumblebeeCallback);
//        }
    }

    private final ScannerCallback mScannerCallback = new ScannerCallback() {
        @Override
        public void onNewDevice(final ExtendedBluetoothDevice device) {
            super.onNewDevice(device);

            BluetoothDevice bluetoothDevice = device.getDevice();
            SpecScanRecord specScanRecord = device.getSpecScanRecord();
            if (specScanRecord == null) {
                ZLogger.d("ignore , specScanRecord is null");
                return;
            }

            ZLogger.v(specScanRecord.toString());

            byte[] manufacturerSpecificData = specScanRecord.getManufacturerSpecificData(MANUFACTURER_ID_REALTEK);
            if (manufacturerSpecificData == null) {
                ZLogger.d("no match manufacture data found");
                return;
            }
            ZLogger.v("manufacturerSpecificData=" + DataConverter.bytes2HexWithSeparate(manufacturerSpecificData));

            String manufacturerAddr = BluetoothHelper.formatAddressPositive(manufacturerSpecificData);
            ZLogger.v("manufacturerAddr= " + manufacturerAddr);

//            if (isTargetDevice(manufacturerAddr)) {
//                stopScan();
//
//                if (!getBeeProManager().isConnected()) {
////                    BeeError ret = BeeProManager.getInstance(mContext).connect(manufacturerAddr);
////                    if (ret.code != BeeError.SUCCESS) {
////                        ZLogger.w("connect failed with:" + manufacturerAddr);
////                        startScan();
////                    }
//                } else {
//                    ZLogger.v("already connected with: " + manufacturerAddr);
//                }
//            }
        }

        @Override
        public void onScanStateChanged(final int state) {
            super.onScanStateChanged(state);
        }
    };

    /**
     * check if the scan device is the target device
     *
     * @param bdAddr
     * @return
     */
//    private boolean isTargetDevice(String bdAddr) {
//        if (TextUtils.isEmpty(bdAddr)) {
//            return false;
//        }
//        BluetoothDevice device = getBeeProManager().getCurDevice();
//        if (device == null) {
//            return false;
//        }
//
//        return (bdAddr.equals(device.getAddress()));
//    }
}
