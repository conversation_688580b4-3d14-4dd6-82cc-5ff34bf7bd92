package com.ggec.glasses.ai.data.repository;

import android.content.Context;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;

import com.ggec.glasses.ai.data.cache.api.CacheCallback;
import com.ggec.glasses.ai.data.cache.message.MessageCache;
import com.ggec.glasses.ai.data.db.entity.ChatMessage;
import com.ggec.glasses.ai.data.db.entity.MessageStatus;
import com.ggec.glasses.ai.data.db.repository.ChatMessageRepository;
import com.ggec.glasses.ai.data.db.repository.RepositoryCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * 带缓存功能的聊天消息存储库
 * 在数据库访问层上增加缓存层，提高访问速度
 */
public class CachedChatMessageRepository {
    private static final String TAG = "CachedChatMessageRepo";
    
    private final ChatMessageRepository messageRepository;
    private final MessageCache messageCache;
    
    /**
     * 构造函数
     * @param context 应用上下文
     */
    public CachedChatMessageRepository(Context context) {
        this.messageRepository = new ChatMessageRepository(context);
        this.messageCache = MessageCache.getInstance();
        
        // 添加缓存回调以监控缓存变化
        messageCache.addCallback(new CacheCallback<Long, ChatMessage>() {
            @Override
            public void onEntryAdded(Long key, ChatMessage value) {
                Log.d(TAG, "消息已添加到缓存: " + key);
            }

            @Override
            public void onEntryAccessed(Long key, ChatMessage value) {
                // 不需要处理
            }

            @Override
            public void onEntryUpdated(Long key, ChatMessage oldValue, ChatMessage newValue) {
                Log.d(TAG, "缓存中的消息已更新: " + key);
            }

            @Override
            public void onEntryRemoved(Long key, ChatMessage value, RemovalReason reason) {
                Log.d(TAG, "消息已从缓存移除: " + key + ", 原因: " + reason);
            }

            @Override
            public void onCacheCleared() {
                Log.d(TAG, "消息缓存已清空");
            }
        });
    }
    
    /**
     * 插入消息
     * @param message 要插入的消息
     * @param callback 回调
     */
    public void insertMessage(ChatMessage message, RepositoryCallback<Long> callback) {
        // 先插入数据库
        messageRepository.insertMessage(message, new RepositoryCallback<Long>() {
            @Override
            public void onSuccess(Long messageId) {
                if (messageId > 0) {
                    // 设置消息ID
                    message.setId(messageId);
                    // 插入成功后加入缓存
                    messageCache.cacheMessage(message);
                }
                callback.onSuccess(messageId);
            }

            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * 批量插入消息
     * @param messages 要插入的消息列表
     * @param callback 回调
     */
    public void insertMessages(List<ChatMessage> messages, RepositoryCallback<List<Long>> callback) {
        // 先插入数据库
        messageRepository.insertMessages(messages, new RepositoryCallback<List<Long>>() {
            @Override
            public void onSuccess(List<Long> messageIds) {
                // 设置消息ID并加入缓存
                for (int i = 0; i < messageIds.size() && i < messages.size(); i++) {
                    messages.get(i).setId(messageIds.get(i));
                }
                messageCache.cacheMessages(messages);
                callback.onSuccess(messageIds);
            }

            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * 更新消息
     * @param message 要更新的消息
     * @param callback 回调
     */
    public void updateMessage(ChatMessage message, RepositoryCallback<Void> callback) {
        // 先更新数据库
        messageRepository.updateMessage(message, new RepositoryCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                // 更新缓存
                messageCache.updateMessage(message);
                callback.onSuccess(null);
            }

            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * 删除消息
     * @param message 要删除的消息
     * @param callback 回调
     */
    public void deleteMessage(ChatMessage message, RepositoryCallback<Void> callback) {
        // 先从缓存移除
        messageCache.removeMessage(message.getId());
        
        // 然后从数据库删除
        messageRepository.deleteMessage(message, callback);
    }
    
    /**
     * 根据ID获取消息
     * @param messageId 消息ID
     * @param callback 回调
     */
    public void getMessageById(long messageId, RepositoryCallback<ChatMessage> callback) {
        // 先从缓存获取
        ChatMessage cachedMessage = messageCache.getMessage(messageId);
        
        if (cachedMessage != null) {
            // 缓存命中，直接返回
            callback.onSuccess(cachedMessage);
        } else {
            // 缓存未命中，从数据库获取
            messageRepository.getMessageById(messageId, new RepositoryCallback<ChatMessage>() {
                @Override
                public void onSuccess(ChatMessage message) {
                    if (message != null) {
                        // 加入缓存
                        messageCache.cacheMessage(message);
                    }
                    callback.onSuccess(message);
                }

                @Override
                public void onError(String error) {
                    callback.onError(error);
                }
            });
        }
    }
    
    /**
     * 根据ID获取消息（LiveData版本）
     * @param messageId 消息ID
     * @return 包含消息对象的LiveData
     */
    public LiveData<ChatMessage> getMessageByIdLive(long messageId) {
        // 创建合并的LiveData
        MediatorLiveData<ChatMessage> result = new MediatorLiveData<>();
        
        // 先检查缓存
        ChatMessage cachedMessage = messageCache.getMessage(messageId);
        if (cachedMessage != null) {
            result.setValue(cachedMessage);
        }
        
        // 添加数据库源
        LiveData<ChatMessage> dbSource = messageRepository.getMessageByIdLive(messageId);
        result.addSource(dbSource, message -> {
            // 更新返回值
            result.setValue(message);
            
            // 更新缓存
            if (message != null) {
                messageCache.cacheMessage(message);
            }
        });
        
        return result;
    }
    
    /**
     * 获取某个会话的所有消息
     * @param conversationId 会话ID
     * @param callback 回调
     */
    public void getMessagesByConversation(long conversationId, RepositoryCallback<List<ChatMessage>> callback) {
        // 先从缓存获取
        List<ChatMessage> cachedMessages = messageCache.getMessagesByConversation(conversationId);
        
        if (!cachedMessages.isEmpty()) {
            // 缓存命中，直接返回
            callback.onSuccess(cachedMessages);
        } else {
            // 缓存未命中，从数据库获取
            messageRepository.getMessagesByConversation(conversationId, new RepositoryCallback<List<ChatMessage>>() {
                @Override
                public void onSuccess(List<ChatMessage> messages) {
                    if (messages != null && !messages.isEmpty()) {
                        // 加入缓存
                        messageCache.cacheMessages(messages);
                    }
                    callback.onSuccess(messages);
                }

                @Override
                public void onError(String error) {
                    callback.onError(error);
                }
            });
        }
    }
    
    /**
     * 获取某个会话的所有消息（LiveData版本）
     * @param conversationId 会话ID
     * @return 包含消息列表的LiveData
     */
    public LiveData<List<ChatMessage>> getMessagesByConversationLive(long conversationId) {
        // 创建合并的LiveData
        MediatorLiveData<List<ChatMessage>> result = new MediatorLiveData<>();
        
        // 先检查缓存
        List<ChatMessage> cachedMessages = messageCache.getMessagesByConversation(conversationId);
        if (!cachedMessages.isEmpty()) {
            result.setValue(cachedMessages);
        }
        
        // 添加数据库源
        LiveData<List<ChatMessage>> dbSource = messageRepository.getMessagesByConversationLive(conversationId);
        result.addSource(dbSource, messages -> {
            // 更新返回值
            result.setValue(messages);
            
            // 更新缓存
            if (messages != null && !messages.isEmpty()) {
                messageCache.cacheMessages(messages);
            }
        });
        
        return result;
    }
    
    /**
     * 更新消息状态
     * @param messageId 消息ID
     * @param status 新状态
     * @param callback 回调
     */
    public void updateMessageStatus(long messageId, MessageStatus status, RepositoryCallback<Void> callback) {
        // 先更新缓存中的消息状态
        ChatMessage cachedMessage = messageCache.getMessage(messageId);
        if (cachedMessage != null) {
            cachedMessage.setStatus(status);
            messageCache.updateMessage(cachedMessage);
        }
        
        // 然后更新数据库
        messageRepository.updateMessageStatus(messageId, status, callback);
    }
    
    /**
     * 删除指定会话的所有消息
     * @param conversationId 会话ID
     * @param callback 回调
     */
    public void deleteMessagesByConversation(long conversationId, RepositoryCallback<Void> callback) {
        // 先从缓存清除
        messageCache.clearConversation(conversationId);
        
        // 然后从数据库删除
        messageRepository.deleteMessagesByConversation(conversationId, callback);
    }
    
    /**
     * 删除所有消息
     * @param callback 回调
     */
    public void deleteAllMessages(RepositoryCallback<Void> callback) {
        // 先清空缓存
        messageCache.clearAll();
        
        // 然后清空数据库
        messageRepository.deleteAllMessages(callback);
    }
    
    /**
     * 搜索消息
     * 注意：搜索不经过缓存，直接查询数据库
     * @param query 搜索关键词
     * @param callback 回调
     */
    public void searchMessages(String query, RepositoryCallback<List<ChatMessage>> callback) {
        // 搜索直接访问数据库
        messageRepository.searchMessages(query, new RepositoryCallback<List<ChatMessage>>() {
            @Override
            public void onSuccess(List<ChatMessage> messages) {
                // 搜索结果不放入缓存
                callback.onSuccess(messages);
            }

            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * 搜索消息（LiveData版本）
     * 注意：搜索不经过缓存，直接查询数据库
     * @param query 搜索关键词
     * @return 包含搜索结果的LiveData
     */
    public LiveData<List<ChatMessage>> searchMessagesLive(String query) {
        // 搜索直接访问数据库
        return messageRepository.searchMessagesLive(query);
    }
    
    /**
     * 预加载最近消息到缓存
     * @param limit 消息数量限制
     */
    public void preloadRecentMessages(int limit) {
        messageRepository.getLatestMessages(limit, new RepositoryCallback<List<ChatMessage>>() {
            @Override
            public void onSuccess(List<ChatMessage> messages) {
                if (messages != null && !messages.isEmpty()) {
                    // 加入缓存
                    messageCache.cacheMessages(messages);
                    Log.d(TAG, "预加载了 " + messages.size() + " 条最近消息到缓存");
                }
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "预加载消息失败: " + error);
            }
        });
    }
    
    /**
     * 导入示例消息
     * @param callback 回调
     */
    public void importSampleMessages(RepositoryCallback<List<ChatMessage>> callback) {
        // 生成示例消息
        try {
            // 使用正确的包名
            List<ChatMessage> sampleMessages = com.ggec.glasses.ai.util.SampleMessageProvider.getDBMessages(0);
            
            // 批量插入消息
            insertMessages(sampleMessages, new RepositoryCallback<List<Long>>() {
                @Override
                public void onSuccess(List<Long> messageIds) {
                    Log.d(TAG, "成功导入 " + messageIds.size() + " 条示例消息");
                    
                    // 返回导入的消息
                    getMessagesByConversation(0, callback);
                }
                
                @Override
                public void onError(String error) {
                    Log.e(TAG, "导入示例消息失败: " + error);
                    callback.onError("导入示例消息失败: " + error);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "创建示例消息时出错", e);
            callback.onError("创建示例消息时出错: " + e.getMessage());
        }
    }
} 