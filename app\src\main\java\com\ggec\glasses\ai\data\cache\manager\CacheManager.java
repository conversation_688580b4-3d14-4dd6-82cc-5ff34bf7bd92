package com.ggec.glasses.ai.data.cache.manager;

import com.ggec.glasses.ai.data.cache.api.ICache;
import com.ggec.glasses.ai.data.cache.message.MessageCache;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 缓存管理器
 * 管理所有缓存实例
 */
public class CacheManager {
    
    /** 单例实例 */
    private static volatile CacheManager INSTANCE;
    
    /** 缓存注册表 */
    private final Map<String, ICache<?, ?>> cacheRegistry;
    
    /** 定时清理线程池 */
    private final ScheduledExecutorService cleanerExecutor;
    
    /** 默认清理间隔(分钟) */
    private static final int DEFAULT_CLEAN_INTERVAL_MINUTES = 5;
    
    /**
     * 私有构造函数
     */
    private CacheManager() {
        this.cacheRegistry = new HashMap<>();
        this.cleanerExecutor = Executors.newSingleThreadScheduledExecutor();
        
        // 启动定时清理任务
        startCleanTask();
    }
    
    /**
     * 获取单例实例
     *
     * @return 缓存管理器实例
     */
    public static CacheManager getInstance() {
        if (INSTANCE == null) {
            synchronized (CacheManager.class) {
                if (INSTANCE == null) {
                    INSTANCE = new CacheManager();
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * 注册缓存
     *
     * @param <K> 缓存键类型
     * @param <V> 缓存值类型
     * @param name 缓存名称
     * @param cache 缓存实例
     */
    public <K, V> void registerCache(String name, ICache<K, V> cache) {
        if (name == null || name.isEmpty() || cache == null) {
            return;
        }
        
        synchronized (cacheRegistry) {
            cacheRegistry.put(name, cache);
        }
    }
    
    /**
     * 获取缓存
     *
     * @param <K> 缓存键类型
     * @param <V> 缓存值类型
     * @param name 缓存名称
     * @return 缓存实例，不存在则返回null
     */
    @SuppressWarnings("unchecked")
    public <K, V> ICache<K, V> getCache(String name) {
        if (name == null || name.isEmpty()) {
            return null;
        }
        
        synchronized (cacheRegistry) {
            return (ICache<K, V>) cacheRegistry.get(name);
        }
    }
    
    /**
     * 移除缓存
     *
     * @param name 缓存名称
     * @return 被移除的缓存，不存在则返回null
     */
    public ICache<?, ?> unregisterCache(String name) {
        if (name == null || name.isEmpty()) {
            return null;
        }
        
        synchronized (cacheRegistry) {
            return cacheRegistry.remove(name);
        }
    }
    
    /**
     * 清空所有缓存内容
     */
    public void clearAll() {
        synchronized (cacheRegistry) {
            for (ICache<?, ?> cache : cacheRegistry.values()) {
                cache.clear();
            }
        }
    }
    
    /**
     * 关闭缓存管理器
     * 清空所有缓存并停止清理任务
     */
    public void shutdown() {
        // 清空所有缓存
        clearAll();
        
        // 停止清理任务
        cleanerExecutor.shutdown();
        try {
            if (!cleanerExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanerExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanerExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // 清空注册表
        synchronized (cacheRegistry) {
            cacheRegistry.clear();
        }
    }
    
    /**
     * 启动定时清理任务
     */
    private void startCleanTask() {
        cleanerExecutor.scheduleAtFixedRate(
                this::cleanExpiredEntries,
                DEFAULT_CLEAN_INTERVAL_MINUTES,
                DEFAULT_CLEAN_INTERVAL_MINUTES,
                TimeUnit.MINUTES);
    }
    
    /**
     * 清理所有缓存中的过期条目
     */
    private void cleanExpiredEntries() {
        synchronized (cacheRegistry) {
            for (ICache<?, ?> cache : cacheRegistry.values()) {
                try {
                    // 触发缓存的过期清理
                    cache.get(null); // 空键会触发检查过期的逻辑
                } catch (Exception e) {
                    // 忽略清理异常
                }
            }
        }
    }
    
    /**
     * 创建消息缓存
     *
     * @return 消息缓存实例
     */
    public MessageCache getMessageCache() {
        return MessageCache.getInstance();
    }
} 