package com.ggec.glasses.asr.model;

/**
 * 语音识别结果模型
 * 封装从语音识别服务返回的结果
 */
public class AsrResult {

    private final String text;        // 识别的文本内容
    private final boolean isFinal;    // 是否为最终结果
    private final boolean isSuccess;  // 识别是否成功
    private final String errorMessage; // 错误信息，如果有

    /**
     * 创建成功的语音识别结果
     * @param text 识别的文本
     * @param isFinal 是否为最终结果
     * @return AsrResult实例
     */
    public static AsrResult success(String text, boolean isFinal) {
        return new AsrResult(text, isFinal, true, null);
    }

    /**
     * 创建失败的语音识别结果
     * @param errorMessage 错误信息
     * @return AsrResult实例
     */
    public static AsrResult error(String errorMessage) {
        return new AsrResult("", false, false, errorMessage);
    }

    private AsrResult(String text, boolean isFinal, boolean isSuccess, String errorMessage) {
        this.text = text;
        this.isFinal = isFinal;
        this.isSuccess = isSuccess;
        this.errorMessage = errorMessage;
    }

    /**
     * 获取识别出的文本
     * @return 文本内容
     */
    public String getText() {
        return text;
    }

    /**
     * 检查是否为最终结果
     * @return 如果是最终结果返回true，否则返回false
     */
    public boolean isFinal() {
        return isFinal;
    }

    /**
     * 检查识别是否成功
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return isSuccess;
    }

    /**
     * 获取错误信息
     * @return 错误信息，如果没有错误返回null
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    @Override
    public String toString() {
        if (isSuccess) {
            return "AsrResult{" +
                    "text='" + text + '\'' +
                    ", isFinal=" + isFinal +
                    '}';
        } else {
            return "AsrResult{error='" + errorMessage + "'}";
        }
    }
} 