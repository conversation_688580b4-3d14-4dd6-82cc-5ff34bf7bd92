/*
 * Copyright (c) 2017-2024. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.realsil.sdk.audioconnect.support.ui

import android.app.Dialog
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.realsil.sdk.audioconnect.support.R

/**
 * <AUTHOR>
 * @date 13/08/2017
 */
class SetVibratorModeFragment : DialogFragment() {
    private var mListener: OnDialogListener? = null

    private var etOnPeriodTime: EditText? = null
    private var etOffPeriodTime: EditText? = null
    private var etVibratorCount: EditText? = null
    private var btnSubmit: Button? = null

    /**
     * Interface required to be implemented by activity
     */
    interface OnDialogListener {
        /**
         * @param onPeriodTime
         * @param offPeriodTime
         * @param vibratorCount
         */
        fun onSubmit(onPeriodTime: Int, offPeriodTime: Int, vibratorCount: Int)
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val args = arguments
        if (args != null) {
        }
    }

    /**
     * When dialog is created then set AlertDialog with list and button views
     */
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder = MaterialAlertDialogBuilder(requireActivity(), R.style.RtkAlertDialogTheme)

        val dialogView =
            layoutInflater.inflate(R.layout.rtk_audioconnect_dialogview_set_vibrator_mode, null)

        btnSubmit = dialogView.findViewById(R.id.bt_change_name)
        etOnPeriodTime = dialogView.findViewById(R.id.et_on_period_time)
        etOffPeriodTime = dialogView.findViewById(R.id.et_off_period_time)
        etVibratorCount = dialogView.findViewById(R.id.et_vibrator_count)

        btnSubmit!!.setOnClickListener(View.OnClickListener { v: View? ->
            var onPeriodTime = 0
            var offPeriodTime = 0
            var vibratorCount = 0
            if (etOnPeriodTime!!.getText().length > 0) {
                onPeriodTime = etOnPeriodTime!!.getText().toString().toInt()
            }
            if (etOffPeriodTime!!.getText().length > 0) {
                offPeriodTime = etOffPeriodTime!!.getText().toString().toInt()
            }
            if (etVibratorCount!!.getText().length > 0) {
                vibratorCount = etVibratorCount!!.getText().toString().toInt()
            }
            dismiss()
            if (mListener != null) {
                mListener!!.onSubmit(onPeriodTime, offPeriodTime, vibratorCount)
            }
        })


        return builder.setView(dialogView)
            .setTitle(R.string.text_vibrator_mode)
            .create()
    }

    private var mToast: Toast? = null

    fun showShortToast(messageResId: Int) {
        if (mToast == null) {
            mToast = Toast.makeText(context, "", Toast.LENGTH_SHORT)
        }
        mToast!!.setText(messageResId)
        mToast!!.show()
    }

    companion object {
        const val TAG: String = "SetVibratorModeFragment"

        private const val MAX_LENGTH_LIMIT = 40 //bytes

        /**
         * Static implementation of fragment so that it keeps data when phone orientation is changed.
         * For standard BLE Service UUID, we can filter devices using normal android provided command
         * startScanLe() with required BLE Service UUID
         * For custom BLE Service UUID, we will use class ScannerServiceParser to filter out required
         * device.
         */
        fun getInstance(args: Bundle?, listener: OnDialogListener?): SetVibratorModeFragment {
            val fragment = SetVibratorModeFragment()

            if (args != null) {
                fragment.arguments = args
            }
            fragment.mListener = listener
            return fragment
        }
    }
}
