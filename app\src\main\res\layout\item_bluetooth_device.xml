<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:background="@drawable/bg_profile_setting_item_ripple"
    android:clickable="true"
    android:focusable="true">

    <!-- 设备图标 -->
    <ImageView
        android:id="@+id/iv_device_icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_public_bluetooth" />

    <!-- 设备信息容器 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_device_icon"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 设备名称 -->
        <TextView
            android:id="@+id/tv_device_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/font_primary"
            android:textSize="14sp"
            tools:text="GGEC眼镜001" />

        <!-- 设备地址 -->
        <TextView
            android:id="@+id/tv_device_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/font_secondary"
            android:textSize="10sp"
            tools:text="AA:BB:CC:DD:EE:FF" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginStart="56dp"
        android:background="#F0F0F0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 