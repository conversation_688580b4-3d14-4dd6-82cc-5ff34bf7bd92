/*
 * Copyright (c) 2017-2024. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */
package com.realsil.bbpro.sync

import android.content.Context
import android.text.TextUtils
import com.realsil.sample.audioconnect.durian.sync.DurianGlobalSyncManager
import com.realsil.sample.audioconnect.eq.mic.AptEqSyncManager
import com.realsil.sample.audioconnect.eq.spk.SpkEqSyncManager
import com.realsil.sample.audioconnect.eq.spk.v0.EqSyncManagerV0
import com.realsil.sample.audioconnect.eq.spk.v1.EqSyncManagerV1
import com.realsil.sample.audioconnect.eq.spk.v2.EqSyncManagerV2
import com.realsil.sdk.audioconnect.repository.RepositoryConstants
import com.realsil.sdk.audioconnect.repository.RepositoryViewModel
import com.realsil.sdk.audioconnect.repository.database.AudioConnectRoomDatabase
import com.realsil.sdk.audioconnect.repository.database.DeviceInfoRepository
import com.realsil.sdk.audioconnect.repository.database.eq.EqRoomDatabase
import com.realsil.sdk.audioconnect.repository.database.keymap.KeyMapRepository
import com.realsil.sdk.audioconnect.repository.database.keymap.KeyMapRoomDatabase
import com.realsil.sdk.audioconnect.support.sync.BaseSyncManager
import com.realsil.sdk.audioconnect.support.sync.DeviceInfoSyncManager
import com.realsil.sdk.audioconnect.tts.TtsInfo
import com.realsil.sdk.audioconnect.tts.TtsModelCallback
import com.realsil.sdk.audioconnect.tts.TtsModelClient
import com.realsil.sdk.audioconnect.tts.TtsModelProxy
import com.realsil.sdk.bbpro.anc.GetAncScenarioChooseInfoReq
import com.realsil.sdk.bbpro.apt.GetAptNrOnOffReq
import com.realsil.sdk.bbpro.apt.GetAptPowerOnDelayTimeReq
import com.realsil.sdk.bbpro.apt.GetAptVolumeInfoReq
import com.realsil.sdk.bbpro.core.BeeError
import com.realsil.sdk.bbpro.core.peripheral.ConnectionParameters
import com.realsil.sdk.bbpro.core.protocol.Contract
import com.realsil.sdk.bbpro.core.protocol.params.ConfigType
import com.realsil.sdk.bbpro.core.transportlayer.AckPacket
import com.realsil.sdk.bbpro.equalizer.EqConstants
import com.realsil.sdk.bbpro.llapt.GetLlAptBrightnessInfoReq
import com.realsil.sdk.bbpro.llapt.GetLlAptScenarioChooseInfoReq
import com.realsil.sdk.bbpro.model.DeviceInfo
import com.realsil.sdk.bbpro.multilink.GetMultiLinkConnNumReq
import com.realsil.sdk.bbpro.multilink.MultiLinkInfo
import com.realsil.sdk.bbpro.params.StatusIndex
import com.realsil.sdk.bbpro.profile.GetStatusReq
import com.realsil.sdk.bbpro.vendor.ChargingCaseCallback
import com.realsil.sdk.bbpro.vendor.VendorConstants
import com.realsil.sdk.bbpro.vp.GetVpToneVolumeLevelReq
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.core.utility.StringUtils

/**
 * Device Info Sync Manager
 * <AUTHOR>
 */
class SettingsSyncManager private constructor(context: Context, address: String) :
    BaseSyncManager() {
    var deviceInfoSyncManager: DeviceInfoSyncManager? = null
    private var mTtsModelClient: TtsModelClient? = null
    private var spkEqSyncManager: SpkEqSyncManager? = null
    private var aptEqSyncManager: AptEqSyncManager? = null

    private var durianGlobalSyncManager: DurianGlobalSyncManager? = null

    override fun getTag(): String {
        return "Settings"
    }

    override fun innerCheck(): Boolean {
        val ret = super.innerCheck()
        if (!ret) {
            return false
        }
        synchronized(mStateLock) {
            if ((mState and STATE_IDLE_MASK) == STATE_IDLE_MASK) {
                ZLogger.v("sync already in idle state")
                return false
            }
        }
        return true
    }

    /**
     * used to check if support spp ota function or not
     *
     * @return
     */
    private fun loadOtaDeviceInfo(): Boolean {
        ZLogger.v("sppOtaSupported: ${audioConnectDeviceEntity!!.sppOtaSupported}")
        if (audioConnectDeviceEntity!!.sppOtaSupported == RepositoryConstants.CAPABILITY_FLAG_NOT_SYNC) {
            // check whether device support spp ota or not
            syncDeviceInfo(
                "ota info",
                STATE_DATA_SYNC_OTA_DEVICE_INFO
            ) { beeProManager.otaDeviceInfo }
        } else if (audioConnectDeviceEntity!!.sppOtaSupported == RepositoryConstants.CAPABILITY_FLAG_SUPPORTED) {
            return true
        }

        if (audioConnectDeviceEntity!!.transportChannel == ConnectionParameters.CHANNEL_TYPE_SPP) {
            val leAddr = audioConnectDeviceEntity!!.leDeviceAddress
            if (!TextUtils.isEmpty(leAddr)) {
                ZLogger.v("leAddr: $leAddr")
                return true
            }

            return syncDeviceInfo("le addr", STATE_DATA_SYNC_LE_ADDR) { beeProManager.leAddr }
        } else {
            return true
        }

    }

    private val chargingCaseCallback = object : ChargingCaseCallback() {
        override fun onChargingCaseInfoReported() {
            super.onChargingCaseInfoReported()
            deviceInfoRepository.updateChargingCaseAddress(
                deviceAddress,
                beeProManager.deviceInfo.chargingCaseAddress
            )

            val chargingCaseEntity = deviceInfoRepository.getChargingCaseEntity(deviceAddress)
            if (beeProManager.deviceInfo.isChargingCaseSupported) {
                if (TextUtils.isEmpty(chargingCaseEntity.deviceAddress)) {
                    deviceInfoRepository.updateChargingCaseSyncFlag(
                        deviceAddress,
                        RepositoryConstants.SYNC_FLAG_NOT_SYNC
                    )
                } else {
                    deviceInfoRepository.updateChargingCaseSyncFlag(
                        deviceAddress,
                        RepositoryConstants.SYNC_FLAG_SYNC_OK
                    )
                }
            } else {
                deviceInfoRepository.updateChargingCaseSyncFlag(
                    deviceAddress,
                    RepositoryConstants.SYNC_FLAG_NOT_SUPPORTED
                )
            }

            notifySyncAck()
        }
    }

    private fun loadChargingCaseInfo(): Boolean {
        val chargingCaseEntity =
            deviceInfoRepository.getChargingCaseEntity(headsetDeviceAddress = deviceAddress)
        ZLogger.v("loadChargingCaseInfo,syncFlag=${chargingCaseEntity.syncFlag}")

        if (chargingCaseEntity.syncFlag == RepositoryConstants.SYNC_FLAG_SYNC_OK) {
            if (!StringUtils.isEmpty(chargingCaseEntity.deviceAddress)) {
                return true
            }
        }

        val deviceInfo = beeProManager.deviceInfo
        deviceInfoRepository.updateChargingCaseFeature(
            headsetDeviceAddress = deviceAddress,
            deviceInfo
        )

        var ret = true
        if (deviceInfo.isChargingCaseSupported) {
            beeProManager.vendorClient.setChargingCaseCallback(chargingCaseCallback)

            ret = syncDeviceInfo(
                "getChargingCaseBtAddress",
                STATE_DATA_SYNC_CHARGING_CASE_GET_BT_ADDR
            ) { beeProManager.vendorClient.chargingCaseBtAddress }
        }
        if (ret) {
//            deviceInfoRepository.updateChargingCaseSyncFlag(deviceAddress, RepositoryConstants.SYNC_FLAG_SYNC_OK)
        }
        return ret
    }

    override fun processSyncProcedure(): Boolean {
        ZLogger.i("pending to sync device info: $deviceAddress")
        audioConnectDeviceEntity = getAudioConnectDevice(deviceAddress)

        deviceInfoSyncManager!!.updateSyncOtaData(deviceAddress, true)

        syncDeviceInfo(
            "gaming mode info",
            STATE_DATA_SYNC_GAMING_MODE
        ) { beeProManager.vendorClient.gamingModeStatus }

        if (!loadOtaDeviceInfo()) {
            return false
        }

        var ret = false

        if (audioConnectDeviceEntity!!.transportChannel == ConnectionParameters.CHANNEL_TYPE_GATT) {
            ret = syncDeviceInfo("le name", STATE_DATA_SYNC_LE_NAME) {
                beeProManager.reqDeviceName(ConfigType.LE_NAME)
            }
            if (!ret) {
                return false
            }
        } else {
            ZLogger.i("br/edr: ${audioConnectDeviceEntity!!.deviceName}")
            ret = syncDeviceInfo("br/edr name", STATE_DATA_SYNC_BREDR_NAME) {
                beeProManager.reqDeviceName(ConfigType.LEGACY_NAME)
            }
            if (!ret) {
                return false
            }
        }

        ret = syncDeviceInfo("bud info", STATE_DATA_SYNC_RWS_INFO) { beeProManager.vendorClient.budInfo }
        if (!ret) {
            return false
        }

        ret = syncDeviceInfo(
            "channel info",
            STATE_DATA_SYNC_RWS_CHANNEL
        ) { beeProManager.getStatus(StatusIndex.STATUS_INDEX_RWS_CHANNEL) }
        if (!ret) {
            return false
        }

        val deviceInfo = beeProManager.deviceInfo

        if (loadChargingCaseInfo()) {
//            return false
        }
        if (deviceInfo.isMultiLinkSupported) {
            ret = syncDeviceInfo("multi-link info", STATE_DATA_SYNC_MULTI_LINK) {
                beeProManager.vendorClient.sendAppReq(GetMultiLinkConnNumReq.Builder().build())
            }
            if (!ret) {
                return false
            }
        }

        ret = syncDeviceInfo("language", STATE_DATA_SYNC_LANGUAGE
        ) { beeProManager.vendorClient.language }
        if (!ret) {
            return false
        }

        audioConnectDeviceEntity = getAudioConnectDevice(deviceAddress)

        // sync local language settings to device
        ttsModelClient.setTtsLanguage(audioConnectDeviceEntity!!.activeLanguage.toByte())

        syncListeningModeInfo()

        val eqModeClient = beeProManager.eqModelClient
        ZLogger.i("pending to sync spk eq:${eqModeClient.eqMechanism}")
        if (spkEqSyncManager == null) {
            if (eqModeClient.eqMechanism == EqConstants.FLOW_MECHANISM_V1) {
                spkEqSyncManager = EqSyncManagerV1.getInstance(mContext, deviceAddress)
            } else if (eqModeClient.eqMechanism == EqConstants.FLOW_MECHANISM_V2) {
                spkEqSyncManager = EqSyncManagerV2.getInstance(mContext, deviceAddress)
            } else {
                spkEqSyncManager = EqSyncManagerV0.getInstance(mContext, deviceAddress)
            }
        }
        spkEqSyncManager?.sync()

        if (deviceInfo.isAptEqSupported) {
            ZLogger.i("pending to sync mic eq")
            if (aptEqSyncManager == null) {
                aptEqSyncManager = AptEqSyncManager.getInstance(mContext, deviceAddress)
            }
            aptEqSyncManager?.sync()
        }

        if (deviceInfo.isEarDetectionSupported) {
            syncDeviceInfo(
                "ear detection info",
                STATE_DATA_SYNC_EAR_DETECTION
            ) { beeProManager.vendorClient.earDetectionStatus }
        }

        if (deviceInfo.isVpRingtoneSupported) {
            syncDeviceInfo(
                "vp/tone info",
                STATE_DATA_SYNC_QUERY_VP_TONE_STATUS
            ) { beeProManager.vendorClient.sendAppReq(GetVpToneVolumeLevelReq.Builder().build()) }
        }

        if (deviceInfo.isKeyMapSupported) {
            loadKeyMap()
        }

        if (deviceInfo.isDurianSupported) {
            if (durianGlobalSyncManager == null) {
                durianGlobalSyncManager =
                    DurianGlobalSyncManager.getInstance(mContext, deviceAddress)
            }
            durianGlobalSyncManager?.startSync()
        }

        if (isDeviceConnected()) {
            deviceInfoRepository.updateDeviceInfo(deviceAddress, beeProManager.deviceInfo)
        }
        ZLogger.i("sync device info complete: $deviceAddress")

        return true
    }

    override fun processOperationComplete(operation: Int, status: Byte) {
        super.processOperationComplete(operation, status)
        when (operation) {
            VendorConstants.Operation.GET_LE_ADDR -> if (status != AckPacket.ACK_STATUS_COMPLETE) {
                deviceInfoRepository.updateLeAddress(deviceAddress, deviceInfo)
                reloadDeviceInfo()
                notifySyncAck()
            }

            VendorConstants.Operation.GET_STATUS -> if (status != AckPacket.ACK_STATUS_COMPLETE) {
                if (mState == STATE_DATA_SYNC_RWS_CHANNEL || mState == STATE_DATA_SYNC_RWS_INFO) {
                    notifySyncAck()
                }
            }

            VendorConstants.Operation.GET_MULTI_LINK_CONN_NUM -> if (status != AckPacket.ACK_STATUS_COMPLETE) {
                if (mState == STATE_DATA_SYNC_MULTI_LINK) {
                    notifySyncAck()
                }
            }

            VendorConstants.Operation.GET_GAMING_MODE_STATE,
            VendorConstants.Operation.SET_LOW_LATENCY_LEVEL,
            -> {
                if (status != AckPacket.ACK_STATUS_COMPLETE && mState == STATE_DATA_SYNC_GAMING_MODE) {
                    notifySyncAck()
                }
            }

            VendorConstants.Operation.GET_EAR_DETECTION_STATUS
            -> {
                if (status != AckPacket.ACK_STATUS_COMPLETE && mState == STATE_DATA_SYNC_EAR_DETECTION) {
                    notifySyncAck()
                }
            }

            VendorConstants.Operation.GET_KEY_MMI_MAP -> if (checkSyncState(
                    STATE_DATA_SYNC_KEY_MMI_MAP
                ) && status != AckPacket.ACK_STATUS_COMPLETE
            ) {
                notifySyncAck()
            }

            VendorConstants.Operation.GET_RWS_KEY_MMI_MAP -> if (checkSyncState(
                    STATE_DATA_SYNC_RWS_KEY_MMI_MAP
                ) && status != AckPacket.ACK_STATUS_COMPLETE
            ) {
                notifySyncAck()
            }

            VendorConstants.Operation.GET_SUPPORTED_CALL_STATUS -> if (checkSyncState(
                    STATE_DATA_SYNC_SUPPORTED_CALL_STATUS
                ) && status != AckPacket.ACK_STATUS_COMPLETE
            ) {
                notifySyncAck()
            }

            VendorConstants.Operation.GET_SUPPORTED_CLICK_TYPE -> if (checkSyncState(
                    STATE_DATA_SYNC_SUPPORTED_CLICK_TYPE
                ) && status != AckPacket.ACK_STATUS_COMPLETE
            ) {
                notifySyncAck()
            }

            VendorConstants.Operation.GET_SUPPORTED_MMI_LIST -> if (checkSyncState(
                    STATE_DATA_SYNC_SUPPORTED_MMI_LIST
                ) && status != AckPacket.ACK_STATUS_COMPLETE
            ) {
                notifySyncAck()
            }

            else -> {}
        }
    }


    override fun processMultiLinkInfoChanged(multilinkInfo: MultiLinkInfo) {
        super.processMultiLinkInfoChanged(multilinkInfo)
        synchronized(mStateLock) {
            if (checkSyncState(STATE_DATA_SYNC_MULTI_LINK)) {
                notifySyncAck()
            }
        }
    }

    override fun processDeviceInfoChanged(deviceInfo: DeviceInfo, indicator: Int) {
        super.processDeviceInfoChanged(deviceInfo, indicator)
        when (indicator) {
            DeviceInfo.INDICATOR_BUD_INFO -> {
                synchronized(mStateLock) {
                    if (checkSyncState(STATE_DATA_SYNC_RWS_INFO)) {
                        notifySyncAck()
                    }
                }
            }

            DeviceInfo.INDICATOR_SPP_OTA_DEVICE_INFO -> {
                if (checkSyncState(STATE_DATA_SYNC_OTA_DEVICE_INFO)) {
                    deviceInfoRepository.updateOtaCapability(deviceAddress, deviceInfo)
                    reloadDeviceInfo()
                    notifySyncAck()
                }
            }

            DeviceInfo.INDICATOR_ADDR_LE -> {
                if (checkSyncState(STATE_DATA_SYNC_LE_ADDR)) {
                    deviceInfoRepository.updateLeAddress(deviceAddress, deviceInfo)
                    reloadDeviceInfo()
                    notifySyncAck()
                }
            }

            DeviceInfo.INDICATOR_NAME_BREDR,
            DeviceInfo.INDICATOR_NAME_LE, DeviceInfo.INDICATOR_CMD_SET_VERSION,
            DeviceInfo.INDICATOR_APT_STATUS,
            DeviceInfo.INDICATOR_RWS_CHANNEL -> {
                notifySyncAck()
            }

            DeviceInfo.INDICATOR_GAMING_MODE -> {
                if (checkSyncState(STATE_DATA_SYNC_GAMING_MODE)) {
                    notifySyncAck()
                }
            }

            DeviceInfo.INDICATOR_EAR_DETECTION_STATUS -> {
                if (checkSyncState(STATE_DATA_SYNC_EAR_DETECTION)) {
                    notifySyncAck()
                }
            }

            DeviceInfo.INDICATOR_VP_RINGTONE_STATUS -> {
                RepositoryViewModel.instance!!.getDevice(deviceAddress)
                    .updateVpVolumeInfo(deviceInfo.vpVolumeInfo)
                if (checkSyncState(STATE_DATA_SYNC_QUERY_VP_TONE_STATUS)) {
                    notifySyncAck()
                }
            }

            DeviceInfo.INDICATOR_SUPPORTED_MMI_LIST -> {
                saveSupportedMmis()
                if (checkSyncState(STATE_DATA_SYNC_SUPPORTED_MMI_LIST)) {
                    notifySyncAck()
                } else {
                    notifyDataChanged()
                }
            }

            DeviceInfo.INDICATOR_SUPPORTED_CLICK_TYPE -> {
                saveSupportedClickType()
                if (checkSyncState(STATE_DATA_SYNC_SUPPORTED_CLICK_TYPE)) {
                    notifySyncAck()
                } else {
                    notifyDataChanged()
                }
            }

            DeviceInfo.INDICATOR_SUPPORTED_CALL_STATE -> {
                saveSupportedCallStatus()
                if (checkSyncState(STATE_DATA_SYNC_SUPPORTED_CALL_STATUS)) {
                    notifySyncAck()
                } else {
                    notifyDataChanged()
                }
            }

            DeviceInfo.INDICATOR_LOCK_BUTTON_STATUS ->
                notifySyncAck()

            DeviceInfo.INDICATOR_LISTENING_MODE_CYCLE -> if (checkSyncState(
                    STATE_DATA_SYNC_QUERY_LISTENING_MODE_CYCLE
                )
            ) {
                notifySyncAck()
            }

            DeviceInfo.INDICATOR_LISTENING_MODE_BASIC_INFO -> if (checkSyncState(
                    STATE_DATA_SYNC_QUERY_LISTENING_MODE_INFO
                )
            ) {
                notifySyncAck()
            }

            DeviceInfo.INDICATOR_APT_VOLUME_INFO ->
                if (mState == STATE_DATA_SYNC_GET_APT_VOLUME_INFO
                    || mState == STATE_DATA_SYNC_GET_APT_VOLUME_SYNC_STATE
                ) {
                    notifySyncAck()
                }

            DeviceInfo.INDICATOR_LLAPT_BRIGHTNESS_INFO -> if (checkSyncState(
                    STATE_DATA_SYNC_GET_LLAPT_BRIGHTNESS_INFO
                )
            ) {
                notifySyncAck()
            }

            DeviceInfo.INDICATOR_APT_NR_STATUS -> if (checkSyncState(
                    STATE_DATA_SYNC_GET_APT_NR_STATE
                )
            ) {
                notifySyncAck()
            }

            DeviceInfo.INDICATOR_APT_POWER_ON_DELAY_TIME -> if (checkSyncState(
                    STATE_DATA_SYNC_GET_APT_POWER_ON_DELAY_TIME
                )
            ) {
                notifySyncAck()
            }

            DeviceInfo.INDICATOR_LLAPT_SCENARIO_CHOOSE_INFO -> if (checkSyncState(
                    STATE_DATA_SYNC_GET_LLAPT_SCENARIO_CHOOSE_INFO
                )
            ) {
                deviceInfoRepository.saveLlAptScenarioChooseInfo(
                    deviceAddress,
                    deviceInfo.llaptScenarioChooseInfo
                )
                notifySyncAck()
            }

            DeviceInfo.INDICATOR_ANC_SCENARIO_CHOOSE_INFO -> if (checkSyncState(
                    STATE_DATA_SYNC_GET_ANC_SCENARIO_CHOOSE_INFO
                )
            ) {
                deviceInfoRepository.saveAncScenarioChooseInfo(
                    deviceAddress,
                    deviceInfo.ancScenarioChooseInfo
                )
                notifySyncAck()
            }

            else -> {}
        }
    }

    fun cleanData() {
        ZLogger.v("cleaning data")
        EqRoomDatabase.getDatabase(mContext).clearData()
        KeyMapRoomDatabase.getDatabase(mContext).clearData()
        AudioConnectRoomDatabase.getDatabase(mContext).clearData()
        durianGlobalSyncManager?.factoryReset()
    }

    private val mTtsModelCallback: TtsModelCallback = object : TtsModelCallback() {
        override fun onDeviceInfoChanged(indicator: Int, ttsInfo: TtsInfo) {
            super.onDeviceInfoChanged(indicator, ttsInfo)
            when (indicator) {
                TtsInfo.INDICATOR_LANGUAGE -> {
                    ZLogger.v(ttsInfo.toString())
                    deviceInfoSyncManager!!.updateDeviceLanguage(
                        deviceAddress,
                        ttsInfo.dspCurrentLanguage.toInt(), ttsInfo.dspSupportedLanguage.toInt()
                    )

                    synchronized(mStateLock) {
                        if ((mState and STATE_DATA_SYNC_LANGUAGE) == STATE_DATA_SYNC_LANGUAGE) {
                            notifySyncAck()
                        } else {
                            notifyDataChanged()
                        }
                    }
                }

                else -> {}
            }
        }
    }

    private fun syncListeningModeInfo(): Boolean {
        ZLogger.i("pending to sync listening mode info")
        var ret = true
        val deviceInfo = beeProManager.deviceInfo
        if (deviceInfo.isListeningModeCycleSupported) {
            ret = syncDeviceInfo(
                "listening mode cycle info",
                STATE_DATA_SYNC_QUERY_LISTENING_MODE_CYCLE
            ) {
                beeProManager.vendorClient.listeningModeCycle
            }
            if (!ret) {
                return false
            }
        }
        ret = syncDeviceInfo(
            "listening mode info",
            STATE_DATA_SYNC_QUERY_LISTENING_MODE_INFO
        ) {
            beeProManager.vendorClient.queryListeningModeInfo()
        }
        if (!ret) {
            return false
        }
        if (deviceInfo.isAptSupported) {
            ret = syncDeviceInfo(
                "apt volume info",
                STATE_DATA_SYNC_GET_APT_VOLUME_INFO
            ) {
//                val deviceInfo = beeProManager.deviceInfo
                val req = GetAptVolumeInfoReq.Builder(deviceInfo.aptVolumeVersion).build()
                beeProManager.vendorClient.sendAppReq(req)
            }
            if (!ret) {
                return false
            }

            if (deviceInfo.cmdSetVersion >= Contract.CMD_SET_VERSION.V1_6) {
                ret = syncDeviceInfo(
                    "apt volume sync info",
                    STATE_DATA_SYNC_GET_APT_VOLUME_SYNC_STATE
                ) {
                    val req = GetStatusReq.Builder(StatusIndex.STATUS_INDEX_APT_VOLUME_SYNC).build()
                    beeProManager.vendorClient.sendAppReq(req)
                }
                if (!ret) {
                    return false
                }
            }
        }
        if (deviceInfo.isAptNrSupported) {
            ret = syncDeviceInfo(
                "apt nr state",
                STATE_DATA_SYNC_GET_APT_NR_STATE
            ) {
                val req = GetAptNrOnOffReq.Builder().build()
                beeProManager.vendorClient.sendAppReq(req)
            }
            if (!ret) {
                return false
            }
        }
        if (deviceInfo.isAptPowerOnDelayTimeSupported) {
            ret = syncDeviceInfo(
                "apt power on delay time",
                STATE_DATA_SYNC_GET_APT_POWER_ON_DELAY_TIME
            ) {
                val req = GetAptPowerOnDelayTimeReq.Builder().build()
                beeProManager.vendorClient.sendAppReq(req)
            }
            if (!ret) {
                return false
            }
        }

        if (deviceInfo.isLlAptVolumeHeSupported) {
            ret = syncDeviceInfo(
                "ll-apt brightness info",
                STATE_DATA_SYNC_GET_LLAPT_BRIGHTNESS_INFO
            ) {
                val req = GetLlAptBrightnessInfoReq.Builder().build()
                beeProManager.vendorClient.sendAppReq(req)
            }
            if (!ret) {
                return false
            }
        }
        if (deviceInfo.isLlaptScenarioGroupSettingsSupported) {
            val aptInfoEntity = deviceInfoRepository.getAptInfoEntity(deviceAddress)
            if (!TextUtils.isEmpty(aptInfoEntity.scenarioGroupInfo)) {
                ZLogger.v("apt scenario choose info has already been stored:\n$aptInfoEntity")
                return true
            }
            ret = syncDeviceInfo(
                "ll-apt scenario choose info",
                STATE_DATA_SYNC_GET_LLAPT_SCENARIO_CHOOSE_INFO
            ) {
                val req = GetLlAptScenarioChooseInfoReq.Builder().build()
                beeProManager.vendorClient.sendAppReq(req)
            }
            if (!ret) {
                return false
            }
        }

        if (deviceInfo.isAncScenarioGroupSettingsSupported) {
            val ancInfoEntity = deviceInfoRepository.getAncInfoEntity(deviceAddress)
            if (!TextUtils.isEmpty(ancInfoEntity.scenarioGroupInfo)) {
                ZLogger.v("anc scenario choose info has already been stored:\n$ancInfoEntity")
                return true
            }
            ret = syncDeviceInfo(
                "anc scenario choose info",
                STATE_DATA_SYNC_GET_ANC_SCENARIO_CHOOSE_INFO
            ) {
                val req = GetAncScenarioChooseInfoReq.Builder().build()
                beeProManager.vendorClient.sendAppReq(req)
            }
            if (!ret) {
                return false
            }
        }

        ZLogger.i("listening mode info sync completed")
        return true
    }


    private fun loadKeyMap(): Boolean {
        val keyMapStatus = keyMapRepository!!.getKeyMapStatus(deviceAddress)
        ZLogger.d("keyMapStatus=$keyMapStatus")
        //是否做过ota升级，或者是否有保存过配置数据（没有）
        if (keyMapStatus.syncFlag == RepositoryConstants.SYNC_FLAG_NOT_SYNC) {
            val ret1 = syncDeviceInfo(
                "supported mmi list",
                STATE_DATA_SYNC_SUPPORTED_MMI_LIST
            ) { beeProManager.vendorClient.supportedMmiList }
            val ret2 = syncDeviceInfo(
                "supported call status",
                STATE_DATA_SYNC_SUPPORTED_CALL_STATUS
            ) { beeProManager.vendorClient.supportedCallStatus }
            val ret3 = syncDeviceInfo(
                "supported click type",
                STATE_DATA_SYNC_SUPPORTED_CLICK_TYPE
            ) { beeProManager.vendorClient.supportedClickType }

            val deviceInfo = beeProManager.deviceInfo
            keyMapStatus.rwsKeyMappingSupported = deviceInfo.isRwsKeyMappingSupported

            var ret4 = false
            if (keyMapStatus.rwsKeyMappingSupported) {
                ret4 = syncDeviceInfo(
                    "rws key mmi map",
                    STATE_DATA_SYNC_RWS_KEY_MMI_MAP
                ) { beeProManager.vendorClient.rwsKeyMmiMap }
            } else {
                ret4 = syncDeviceInfo(
                    "key mmi map",
                    STATE_DATA_SYNC_KEY_MMI_MAP
                ) { beeProManager.vendorClient.keyMmiMap }
            }

            if (ret1 && ret2 && ret3 && ret4) {
                keyMapStatus.syncFlag = RepositoryConstants.SYNC_FLAG_SYNC_OK
            }

            keyMapRepository!!.updateKeyMapStatus(keyMapStatus)
            ZLogger.d("keyMapStatus=$keyMapStatus")
        } else {
            if (manualSyncEnabled) {
                if (keyMapStatus.rwsKeyMappingSupported) {
                    syncDeviceInfo(
                        "rws key mmi map",
                        STATE_DATA_SYNC_RWS_KEY_MMI_MAP
                    ) { beeProManager.vendorClient.rwsKeyMmiMap }
                } else {
                    syncDeviceInfo(
                        "key mmi map",
                        STATE_DATA_SYNC_KEY_MMI_MAP
                    ) { beeProManager.vendorClient.keyMmiMap }
                }
            }
        }

        return true
    }


    override fun changePeripheral(address: String) {
        val deviceChanged = (deviceAddress != address)
        super.changePeripheral(address)

        spkEqSyncManager?.changePeripheral(address)
        aptEqSyncManager?.changePeripheral(address)
//        durianGlobalSyncManager?.changePeripheral(address)
        if (deviceChanged) {
            mTtsModelClient = TtsModelProxy.getInstance().getModelClient(deviceAddress)
            mTtsModelClient?.registerCallback(mTtsModelCallback)
        }
    }

    override fun destroy() {
        super.destroy()
        mTtsModelClient?.unregisterCallback(mTtsModelCallback)
        spkEqSyncManager?.destroy()
        aptEqSyncManager?.destroy()
        durianGlobalSyncManager?.destroy()
    }

    private val ttsModelClient: TtsModelClient
        get() {
            if (mTtsModelClient == null) {
                mTtsModelClient = TtsModelProxy.getInstance().getModelClient(deviceAddress)
                mTtsModelClient?.registerCallback(mTtsModelCallback)
            }
            return mTtsModelClient!!
        }

    init {
        mContext = context
        deviceAddress = address
        beeProManager
        autoStartSyncEnabled = true

        deviceInfoRepository = DeviceInfoRepository(mContext)
        keyMapRepository = KeyMapRepository(mContext)

        deviceInfoSyncManager = DeviceInfoSyncManager.getInstance(mContext)

        ttsModelClient
    }

    companion object {

        /**
         * get instance of [SettingsSyncManager]
         *
         * @param context Application Context
         * @return [SettingsSyncManager]
         */
        fun getInstance(context: Context, address: String): SettingsSyncManager {
            return SettingsSyncManager(context, address)
        }
    }
}
