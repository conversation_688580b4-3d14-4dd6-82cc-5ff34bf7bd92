package com.ggec.glasses.album;

import android.os.Bundle;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;

import com.ggec.glasses.R;
import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.fragments.MediaDisplayFragment;
import com.ggec.glasses.album.viewmodel.MediaDisplayViewModel;

/**
 * 媒体展示Activity，作为媒体展示的容器
 */
public class MediaDisplayActivity extends AppCompatActivity {
    
    public static final String EXTRA_MEDIA = "media";
    
    private MediaDisplayViewModel viewModel;
    private Media media;
    
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_media_display);
        
        // 从Intent获取媒体对象
        if (getIntent() != null && getIntent().hasExtra(EXTRA_MEDIA)) {
            media = getIntent().getParcelableExtra(EXTRA_MEDIA);
        }
        
        // 初始化ViewModel
        viewModel = new ViewModelProvider(this).get(MediaDisplayViewModel.class);
        
        // 初始化媒体数据
        if (media != null) {
            viewModel.setMedia(media);
        }
        
        // 设置沉浸式状态栏
        setupStatusBar();
        
        // 如果是第一次创建，添加Fragment
        if (savedInstanceState == null) {
            getSupportFragmentManager().beginTransaction()
                .replace(R.id.fragment_container, MediaDisplayFragment.newInstance())
                .commit();
        }
        
        // 观察返回事件
        viewModel.getNavigateBack().observe(this, shouldNavigateBack -> {
            if (shouldNavigateBack) {
                onBackPressed();
                viewModel.resetNavigateBack();
            }
        });
    }
    
    /**
     * 设置沉浸式状态栏
     */
    private void setupStatusBar() {
        // 实现沉浸式状态栏逻辑
        getWindow().setFlags(
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        );
    }
    
    @Override
    public void onBackPressed() {
        super.onBackPressed();
        // 移除自定义退出动画，使用系统默认动画
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 恢复正常的状态栏布局
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
    }
} 