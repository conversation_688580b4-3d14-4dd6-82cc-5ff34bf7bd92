package com.ggec.glasses.bluetooth.bridge;

import android.bluetooth.BluetoothDevice;
import com.ggec.glasses.bluetooth.model.BluetoothDeviceModel;
import com.ggec.glasses.bluetooth.model.BluetoothDeviceType;
import com.realtek.sdk.device.DeviceInfo;

/**
 * 蓝牙设备模型转换工具类
 * 用于在不同蓝牙实现的设备模型之间进行转换
 */
public class BluetoothDeviceConverter {
    
    /**
     * 将Realtek设备信息转换为应用内部设备模型
     * @param deviceInfo Realtek设备信息
     * @return 内部设备模型
     */
    public static BluetoothDeviceModel fromRealtekDeviceInfo(DeviceInfo deviceInfo) {
        if (deviceInfo == null) return null;
        
        BluetoothDeviceType type = mapRealtekDeviceType(deviceInfo.getType());
        boolean isPaired = deviceInfo.isPaired();
        
        return new BluetoothDeviceModel(
                deviceInfo.getName(),
                deviceInfo.getAddress(),
                type,
                isPaired
        );
    }
    
    /**
     * 将应用内部设备模型转换为Realtek设备信息
     * @param model 内部设备模型
     * @return Realtek设备信息
     */
    public static DeviceInfo toRealtekDeviceInfo(BluetoothDeviceModel model) {
        if (model == null) return null;
        
        return new DeviceInfo(
                model.getName(),
                model.getAddress(),
                mapToRealtekDeviceType(model.getDeviceType()),
                model.isPaired()
        );
    }
    
    /**
     * 映射Realtek设备类型到应用内部设备类型
     * @param realtekType Realtek设备类型
     * @return 内部设备类型
     */
    private static BluetoothDeviceType mapRealtekDeviceType(int realtekType) {
        switch (realtekType) {
            case com.realtek.sdk.bluetooth.util.BluetoothConstants.DeviceType.TYPE_GLASSES:
                return BluetoothDeviceType.GLASSES;
            case com.realtek.sdk.bluetooth.util.BluetoothConstants.DeviceType.TYPE_HEADSET:
                return BluetoothDeviceType.EARPHONE;
            case com.realtek.sdk.bluetooth.util.BluetoothConstants.DeviceType.TYPE_AUDIO:
                return BluetoothDeviceType.UNKNOWN; // Map to appropriate type
            case com.realtek.sdk.bluetooth.util.BluetoothConstants.DeviceType.TYPE_WATCH:
                return BluetoothDeviceType.WATCH;
            default:
                return BluetoothDeviceType.UNKNOWN;
        }
    }
    
    /**
     * 映射应用内部设备类型到Realtek设备类型
     * @param type 内部设备类型
     * @return Realtek设备类型
     */
    private static int mapToRealtekDeviceType(BluetoothDeviceType type) {
        switch (type) {
            case GLASSES:
                return com.realtek.sdk.bluetooth.util.BluetoothConstants.DeviceType.TYPE_GLASSES;
            case EARPHONE:
                return com.realtek.sdk.bluetooth.util.BluetoothConstants.DeviceType.TYPE_HEADSET;
            case WATCH:
                return com.realtek.sdk.bluetooth.util.BluetoothConstants.DeviceType.TYPE_WATCH;
            default:
                return com.realtek.sdk.bluetooth.util.BluetoothConstants.DeviceType.TYPE_UNKNOWN;
        }
    }
    
    /**
     * 将Android原生蓝牙设备转换为应用内部设备模型
     * @param device Android蓝牙设备
     * @param isPaired 是否已配对
     * @return 内部设备模型
     */
    public static BluetoothDeviceModel fromAndroidBluetoothDevice(BluetoothDevice device, boolean isPaired) {
        if (device == null) return null;
        
        String name = device.getName();
        if (name == null || name.isEmpty()) {
            name = "Unknown Device";
        }
        
        BluetoothDeviceModel model = new BluetoothDeviceModel(
                name,
                device.getAddress(),
                BluetoothDeviceType.UNKNOWN,
                isPaired
        );
        
        model.autoDetectDeviceType();
        return model;
    }
} 