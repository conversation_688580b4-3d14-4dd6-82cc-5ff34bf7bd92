package com.ggec.glasses.aimodelchat.aimodel.config;

import android.content.Context;
import android.util.Log;

import com.ggec.glasses.utils.SecretsLoader;

/**
 * DeepSeek 大模型相关配置参数
 */
public final class DeepSeekConfig {

    private static final String TAG = "DeepSeekConfig";
    
    // 私有构造函数，防止实例化
    private DeepSeekConfig() {}

    /**
     * DeepSeek API 密钥在 secrets.properties 中的键名
     */
    private static final String DEEPSEEK_API_KEY_NAME = "DEEPSEEK_API_KEY";

    /**
     * 默认使用的 DeepSeek 模型名称
     */
    public static final String MODEL_NAME = "deepseek-chat";

    /**
     * DeepSeek API 的基础 URL
     */
    public static final String BASE_URL = "https://api.deepseek.com/";

    /**
     * 默认的系统提示语
     * 定义助手角色和行为约束
     */
    public static final String SYSTEM_PROMPT = "# Role\\n" +
            "你是一位由国光电器股份有限公司（GGEC）开发的语音助手，专为AIGlass设计，能够回答用户的各种问题。\\n\\n" +
            "## Skills\\n" +
            "### 技能1：解答问题\\n" +
            "- 在回答用户问题时，确保信息准确且易于理解。\\n" +
            "- 使用简洁的语言，避免冗长的解释。\\n" +
            "- 回答时注意用户的情绪，提供友好和支持性的回应。\\n\\n" +
            "## Constraints:\\n" +
            "- 回答中避免使用代码、长文本、网址等格式。\\n" +
            "- 回答应尽量简短，直接切入主题。\\n" +
            "- 始终关注用户的情绪，提供积极的互动体验。";

    // --- 未来可扩展参数 ---

    /**
     * 默认的采样温度 (控制随机性)
     * 值越高越随机，越低越确定。
     */
    public static final float DEFAULT_TEMPERATURE = 1.5f;

    /**
     * 默认的最大生成 token 数量 (暂未使用API默认值)
     */
    // public static final int DEFAULT_MAX_TOKENS = 1024;

    /**
     * 获取 DeepSeek API Key
     * @param context 应用上下文
     * @return API Key 字符串，如果未找到则返回 null
     */
    public static String getApiKey(Context context) {
        String apiKey = SecretsLoader.getSecret(context, DEEPSEEK_API_KEY_NAME);
        if (apiKey == null || apiKey.isEmpty()) {
            Log.e(TAG, "DeepSeek API Key 未在 secrets.properties 中找到！DeepSeek 服务将被禁用。");
            return null;
        }
        return apiKey;
    }
} 