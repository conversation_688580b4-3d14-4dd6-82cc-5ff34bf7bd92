package com.ggec.glasses.tts.service;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.alibaba.dashscope.audio.tts.SpeechSynthesisResult;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisAudioFormat;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.alibaba.dashscope.common.ResultCallback;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.UUID;

/**
 * 使用阿里云 CosyVoice 实现的 TTS 服务。
 * 支持异步文件合成和流式播放。
 */
public class CosyVoiceTtsService {

    private static final String TAG = "CosyVoiceTtsService";
    private static final String DEFAULT_MODEL = "cosyvoice-v2";
    private static final String DEFAULT_VOICE = "longwan_v2";
    // 使用 WAV 格式，MediaPlayer 可以直接播放
    private static final SpeechSynthesisAudioFormat DEFAULT_FORMAT = SpeechSynthesisAudioFormat.WAV_22050HZ_MONO_16BIT;
    // 流式播放使用 PCM 格式
    private static final SpeechSynthesisAudioFormat STREAMING_FORMAT = SpeechSynthesisAudioFormat.PCM_22050HZ_MONO_16BIT;

    private final Context context;
    private final String apiKey;

    public CosyVoiceTtsService(Context context, String apiKey) {
        this.context = context.getApplicationContext(); // 使用 Application Context
        this.apiKey = apiKey;
    }

    /**
     * 异步合成语音
     * @param text 要合成的文本
     * @param callback 回调接口，用于接收结果（文件路径）或错误
     */
    public void synthesizeAsync(@NonNull String text, @NonNull TtsServiceCallback callback) {
        if (apiKey == null || apiKey.isEmpty()) {
            Log.e(TAG, "API Key is missing.");
            callback.onError("API Key is missing.");
            return;
        }

        File tempAudioFile = null;
        FileOutputStream fos = null;
        SpeechSynthesizer synthesizer = null;

        try {
            // 创建临时文件
            String fileName = UUID.randomUUID().toString() + ".wav"; // 使用 .wav 扩展名
            tempAudioFile = new File(context.getCacheDir(), fileName);
            fos = new FileOutputStream(tempAudioFile, true); // 使用追加模式写入
            Log.d(TAG, "Created temporary audio file: " + tempAudioFile.getAbsolutePath());

            // 配置请求参数
            SpeechSynthesisParam param = SpeechSynthesisParam.builder()
                    .apiKey(apiKey)
                    .model(DEFAULT_MODEL)
                    .voice(DEFAULT_VOICE)
                    .format(DEFAULT_FORMAT)
                    .build();

            // 确保 final 变量可以在内部类中使用
            final File finalTempAudioFile = tempAudioFile;
            final FileOutputStream finalFos = fos;

            // 实现回调接口
            ResultCallback<SpeechSynthesisResult> cosyVoiceCallback = new ResultCallback<SpeechSynthesisResult>() {
                private String currentRequestId = null;

                @Override
                public void onEvent(SpeechSynthesisResult result) {
                    if (currentRequestId == null && result.getRequestId() != null) {
                        currentRequestId = result.getRequestId();
                        Log.d(TAG, "TTS RequestId: " + currentRequestId);
                    }
                    ByteBuffer audioChunk = result.getAudioFrame();
                    if (audioChunk != null) {
                        try {
                            // 将音频块写入文件
                            byte[] chunkBytes = new byte[audioChunk.remaining()];
                            audioChunk.get(chunkBytes);
                            finalFos.write(chunkBytes);
                            Log.d(TAG, "Written audio chunk, size: " + chunkBytes.length);
                        } catch (IOException e) {
                            Log.e(TAG, "Error writing audio chunk to file", e);
                            // 尝试关闭流并回调错误
                            cleanupAndError("Error writing audio chunk", e);
                        }
                    }
                }

                @Override
                public void onComplete() {
                    Log.d(TAG, "CosyVoice synthesis completed. RequestId: " + currentRequestId);
                    cleanupAndSuccess(finalTempAudioFile.getAbsolutePath());
                }

                @Override
                public void onError(Exception e) {
                    Log.e(TAG, "CosyVoice synthesis error. RequestId: " + currentRequestId, e);
                    cleanupAndError("TTS synthesis error: " + e.getMessage(), e);
                }

                private void cleanupAndSuccess(String filePath) {
                    closeStream(finalFos);
                    callback.onSuccess(filePath);
                }

                private void cleanupAndError(String errorMessage, Exception e) {
                    closeStream(finalFos);
                    deleteFile(finalTempAudioFile);
                    callback.onError(errorMessage);
                }
            };

            // 实例化核心类并开始异步调用
            synthesizer = new SpeechSynthesizer(param, cosyVoiceCallback);
            Log.d(TAG, "Starting CosyVoice synthesis for text: " + text);
            synthesizer.call(text); // 异步调用，立即返回

        } catch (IOException e) {
            Log.e(TAG, "Error creating temporary audio file or stream", e);
            closeStream(fos); // 确保在异常时关闭流
            deleteFile(tempAudioFile); // 删除可能创建的文件
            callback.onError("Error creating temporary file: " + e.getMessage());
        } catch (Exception e) {
            // 处理其他可能的同步异常 (如参数错误)
            Log.e(TAG, "Error initiating TTS synthesis", e);
            closeStream(fos);
            deleteFile(tempAudioFile);
            callback.onError("Error initiating TTS: " + e.getMessage());
        }
        // 注意：不能在这里关闭 fos，因为异步回调还需要写入
        // 也不需要显式关闭 synthesizer，SDK 内部会处理
    }

    /**
     * 异步流式合成语音
     * @param text 要合成的文本
     * @param streamingCallback 回调接口，用于接收实时音频块或错误
     */
    public void synthesizeStreaming(@NonNull String text, @NonNull StreamingTtsCallback streamingCallback) {
        if (apiKey == null || apiKey.isEmpty()) {
            Log.e(TAG, "API Key is missing.");
            streamingCallback.onError("API Key is missing.", null);
            return;
        }

        SpeechSynthesizer synthesizer = null;

        try {
            // 配置流式请求参数
            SpeechSynthesisParam param = SpeechSynthesisParam.builder()
                    .apiKey(apiKey)
                    .model(DEFAULT_MODEL)
                    .voice(DEFAULT_VOICE)
                    .format(STREAMING_FORMAT) // Use PCM for streaming
                    .build();

            // 实现内部 ResultCallback 用于接收 CosyVoice SDK 的事件
            ResultCallback<SpeechSynthesisResult> cosyVoiceCallback = new ResultCallback<SpeechSynthesisResult>() {
                private String currentRequestId = null;

                @Override
                public void onEvent(SpeechSynthesisResult result) {
                    if (currentRequestId == null && result.getRequestId() != null) {
                        currentRequestId = result.getRequestId();
                        Log.d(TAG, "流式 TTS RequestId: " + currentRequestId);
                    }
                    ByteBuffer audioChunk = result.getAudioFrame();
                    if (audioChunk != null && audioChunk.hasRemaining()) {
                         Log.d(TAG, "收到流式音频块，大小: " + audioChunk.remaining());
                        // 将块传递给外部回调
                        streamingCallback.onAudioChunk(audioChunk);
                    }
                    // 注意：此处不再写入文件
                }

                @Override
                public void onComplete() {
                    Log.d(TAG, "CosyVoice 流式合成完成。RequestId: " + currentRequestId);
                    // 通知外部回调流已完成
                    streamingCallback.onStreamingComplete();
                }

                @Override
                public void onError(Exception e) {
                    Log.e(TAG, "CosyVoice 流式合成错误。RequestId: " + currentRequestId, e);
                    // 通知外部回调错误
                    streamingCallback.onError("TTS 合成错误: " + e.getMessage(), e);
                }
            };

            // 实例化核心类，传入流式回调
            synthesizer = new SpeechSynthesizer(param, cosyVoiceCallback);

            Log.d(TAG, "开始 CosyVoice 流式合成，文本: " + text);
            // 对流式模式使用 streamingCall 和 streamingComplete
            synthesizer.streamingCall(text); // 一次性发送所有文本
            synthesizer.streamingComplete(); // 表明所有文本已发送完毕

        } catch (Exception e) {
            // 处理设置或调用期间可能的同步异常
            Log.e(TAG, "初始化 TTS 流式合成时出错", e);
            streamingCallback.onError("初始化 TTS 流式合成时出错: " + e.getMessage(), e);
        }
        // Synthesizer 由 SDK 内部或通过回调管理
    }

    private void closeStream(FileOutputStream fos) {
        if (fos != null) {
            try {
                fos.close();
                Log.d(TAG, "Closed audio file output stream.");
            } catch (IOException e) {
                Log.e(TAG, "Error closing audio file output stream", e);
            }
        }
    }

    private void deleteFile(File file) {
        if (file != null && file.exists()) {
            if (file.delete()) {
                Log.d(TAG, "Deleted temporary audio file: " + file.getAbsolutePath());
            } else {
                Log.w(TAG, "Failed to delete temporary audio file: " + file.getAbsolutePath());
            }
        }
    }
} 