package com.ggec.glasses.bluetooth.manager;

import android.Manifest;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import com.ggec.glasses.bluetooth.adapter.GlassesBluetoothAdapter;
import com.ggec.glasses.bluetooth.receiver.BluetoothBroadcastReceiver;

import java.util.List;
import java.util.Set;

/**
 * 蓝牙管理器类
 * 负责监测和管理蓝牙状态
 */
public class BluetoothManager implements BluetoothBroadcastReceiver.BluetoothStateListener {
    
    private static final String TAG = "BluetoothManager";
    private static final long DEBOUNCE_TIME = 800; // 状态稳定延迟时间 (毫秒)
    private static final long STATUS_UPDATE_DELAY = 500; // 状态更新延迟
    private static final long PERIODIC_CHECK_INTERVAL = 30000; // 30秒检查一次
    
    private static BluetoothManager instance;
    private final Context context;
    private final GlassesBluetoothAdapter bluetoothAdapter;
    private final BluetoothBroadcastReceiver broadcastReceiver;
    private BluetoothStateListener bluetoothStateListener;
    
    private final Handler handler = new Handler(Looper.getMainLooper());
    private boolean isInTransition = false; // 是否处于状态转换中
    private long lastStateChangeTime = 0; // 上次状态改变时间戳
    private BluetoothState lastReportedState = null; // 上次报告的状态
    private Runnable pendingStatusUpdate = null; // 待处理的状态更新任务
    private boolean periodicCheckEnabled = false;
    private Runnable periodicCheckRunnable;
    
    /**
     * 蓝牙状态枚举
     */
    public enum BluetoothState {
        NOT_ENABLED,     // 蓝牙未开启
        ENABLED_NOT_CONNECTED,  // 蓝牙已开启但未连接设备
        CONNECTING,      // 蓝牙已开启且正在连接设备
        ENABLED_CONNECTED       // 蓝牙已开启且已连接设备
    }
    
    // 私有构造函数，初始化蓝牙适配器
    private BluetoothManager(Context context) {
        this.context = context.getApplicationContext();
        this.bluetoothAdapter = GlassesBluetoothAdapter.getInstance(context);
        
        // 初始化广播接收器
        this.broadcastReceiver = new BluetoothBroadcastReceiver(context);
        this.broadcastReceiver.setListener(this);
    }
    
    /**
     * 获取蓝牙管理器实例
     * @param context 上下文
     * @return 蓝牙管理器实例
     */
    public static synchronized BluetoothManager getInstance(Context context) {
        if (instance == null) {
            instance = new BluetoothManager(context);
        }
        return instance;
    }
    
    /**
     * 注册蓝牙状态监听器
     */
    public void registerBluetoothStateReceiver() {
        broadcastReceiver.register();
        // 注册后立即检查一次状态
        checkBluetoothState();
        
        // 启用周期性检查
        enablePeriodicStateCheck();
    }
    
    /**
     * 注销蓝牙状态监听器
     */
    public void unregisterBluetoothStateReceiver() {
        broadcastReceiver.unregister();
        // 禁用周期性检查
        disablePeriodicStateCheck();
        // 注销时移除所有待处理任务
        handler.removeCallbacksAndMessages(null);
    }
    
    /**
     * 检查蓝牙状态并触发更新（带防抖动）
     */
    public void checkBluetoothState() {
        scheduleStatusUpdate(0); // 立即调度状态检查
    }
    
    /**
     * 实际执行状态检查并通知监听器
     */
    private void performStatusCheckAndNotify() {
        BluetoothState currentState = getCurrentBluetoothState();
        
        long currentTime = System.currentTimeMillis();
        boolean stateChanged = lastReportedState == null || currentState != lastReportedState;
        
        // 状态未改变，或处于转换期且时间不足，则忽略本次更新
        if (!stateChanged && isInTransition && (currentTime - lastStateChangeTime < DEBOUNCE_TIME)) {
             Log.d(TAG, "状态未变或在转换期，忽略更新: " + currentState);
            return;
        }
        
        // 如果状态确实改变，或者已经过了防抖动时间
        if (stateChanged || (currentTime - lastStateChangeTime >= DEBOUNCE_TIME)) {
            isInTransition = false; // 重置转换标志
            if (stateChanged) {
                Log.d(TAG, "蓝牙状态改变: " + lastReportedState + " -> " + currentState);
                lastReportedState = currentState;
                notifyBluetoothStateChanged(currentState);
            }
        }
        // 如果仍在转换期但时间不足，则等待下一次检查或事件触发
    }
    
    /**
     * 获取当前的蓝牙状态（不触发通知）
     */
    public BluetoothState getCurrentBluetoothState() {
        // 直接从蓝牙适配器获取最新状态，不依赖缓存
        if (!bluetoothAdapter.isSupported()) {
            return BluetoothState.NOT_ENABLED;
        }
        
        // 强制检查蓝牙是否真的启用
        if (!bluetoothAdapter.isEnabled()) {
            return BluetoothState.NOT_ENABLED;
        }
        
        // 检查连接状态
        if (bluetoothAdapter.isAnyProfileConnected()) {
            return BluetoothState.ENABLED_CONNECTED;
        }
        
        // 检查是否有正在连接的设备
        List<BluetoothDevice> connectingDevices = bluetoothAdapter.getConnectingDevices();
        if (!connectingDevices.isEmpty()) {
            return BluetoothState.CONNECTING;
        }
        
        // 既未连接也未正在连接
        return BluetoothState.ENABLED_NOT_CONNECTED;
    }
    
    /**
     * 开启蓝牙
     */
    public void enableBluetooth() {
        bluetoothAdapter.enable();
        // 开启操作后，标记状态可能变化，并安排检查
        markStatePotentiallyChanged();
        scheduleStatusUpdate(STATUS_UPDATE_DELAY); // 延迟检查，等待状态稳定
    }
    
    /**
     * 获取跳转到系统蓝牙设置页面的Intent
     */
    public Intent getBluetoothSettingsIntent() {
        return bluetoothAdapter.getBluetoothSettingsIntent();
    }
    
    /**
     * 获取蓝牙开启请求Intent
     */
    public Intent getEnableBluetoothIntent() {
        return bluetoothAdapter.getEnableBluetoothIntent();
    }
    
    /**
     * 设置蓝牙状态监听器
     * @param listener 监听器
     */
    public void setBluetoothStateListener(BluetoothStateListener listener) {
        this.bluetoothStateListener = listener;
    }
    
    /**
     * 通知蓝牙状态变化
     * @param state 蓝牙状态
     */
    private void notifyBluetoothStateChanged(BluetoothState state) {
        if (bluetoothStateListener != null) {
             Log.d(TAG, "通知监听器，状态: " + state);
            bluetoothStateListener.onBluetoothStateChanged(state);
        }
    }
    
    /**
     * 标记状态可能已改变，并设置转换标志和时间戳
     */
    private void markStatePotentiallyChanged() {
        isInTransition = true;
        lastStateChangeTime = System.currentTimeMillis();
        Log.d(TAG, "标记状态可能改变，进入转换期");
    }
    
    /**
     * 调度状态更新任务
     * @param delayMillis 延迟时间（毫秒）
     */
    private void scheduleStatusUpdate(long delayMillis) {
        // 如果已有待处理任务，先移除
        if (pendingStatusUpdate != null) {
            handler.removeCallbacks(pendingStatusUpdate);
        }
        
        pendingStatusUpdate = this::performStatusCheckAndNotify;
        
        if (delayMillis <= 0) {
             Log.d(TAG, "立即调度状态检查");
            handler.post(pendingStatusUpdate);
        } else {
             Log.d(TAG, "延迟 " + delayMillis + "ms 调度状态检查");
            handler.postDelayed(pendingStatusUpdate, delayMillis);
        }
    }
    
    /**
     * 蓝牙状态监听器接口
     */
    public interface BluetoothStateListener {
        /**
         * 蓝牙状态变化回调
         * @param state 蓝牙状态
         */
        void onBluetoothStateChanged(BluetoothState state);
    }
    
    // ======== 实现BluetoothBroadcastReceiver.BluetoothStateListener接口 ======== 
    
    @Override
    public void onBluetoothAdapterStateChanged(boolean enabled) {
        Log.d(TAG, "接收到适配器状态变化: enabled=" + enabled);
        markStatePotentiallyChanged();
        // 适配器状态变化通常比较明确，可以较快更新
        scheduleStatusUpdate(STATUS_UPDATE_DELAY);
    }
    
    @Override
    public void onBluetoothConnectionStateChanged(boolean isConnecting, boolean isConnected) {
        Log.d(TAG, "接收到连接状态变化: connecting=" + isConnecting + ", connected=" + isConnected);
        markStatePotentiallyChanged();
        // 连接状态变化需要更长的稳定时间
        scheduleStatusUpdate(DEBOUNCE_TIME);
    }
    
    @Override
    public void onDeviceConnected(BluetoothDevice device) {
        Log.d(TAG, "接收到设备连接事件: " + (device != null ? device.getAddress() : "null"));
        markStatePotentiallyChanged();
        scheduleStatusUpdate(DEBOUNCE_TIME);
    }
    
    @Override
    public void onDeviceDisconnected(BluetoothDevice device) {
         Log.d(TAG, "接收到设备断开事件: " + (device != null ? device.getAddress() : "null"));
        markStatePotentiallyChanged();
        scheduleStatusUpdate(DEBOUNCE_TIME);
    }
    
    @Override
    public void onDeviceBondStateChanged(BluetoothDevice device, int bondState) {
        Log.d(TAG, "接收到配对状态变化: " + (device != null ? device.getAddress() : "null") + ", state=" + bondState);
        // 配对状态变化也可能影响连接状态，触发检查
        markStatePotentiallyChanged();
        scheduleStatusUpdate(DEBOUNCE_TIME);
    }
    
    /**
     * 启用周期性状态检查
     */
    public void enablePeriodicStateCheck() {
        if (periodicCheckEnabled) {
            return;
        }
        
        periodicCheckEnabled = true;
        
        if (periodicCheckRunnable == null) {
            periodicCheckRunnable = new Runnable() {
                @Override
                public void run() {
                    if (periodicCheckEnabled) {
                        checkBluetoothState();
                        handler.postDelayed(this, PERIODIC_CHECK_INTERVAL);
                    }
                }
            };
        }
        
        // 启动周期性检查
        handler.postDelayed(periodicCheckRunnable, PERIODIC_CHECK_INTERVAL);
        Log.d(TAG, "已启用周期性蓝牙状态检查");
    }

    /**
     * 禁用周期性状态检查
     */
    public void disablePeriodicStateCheck() {
        if (!periodicCheckEnabled) {
            return;
        }
        
        periodicCheckEnabled = false;
        
        if (periodicCheckRunnable != null) {
            handler.removeCallbacks(periodicCheckRunnable);
        }
        
        Log.d(TAG, "已禁用周期性蓝牙状态检查");
    }
} 