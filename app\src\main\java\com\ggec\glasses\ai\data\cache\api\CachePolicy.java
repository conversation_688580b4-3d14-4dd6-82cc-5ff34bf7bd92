package com.ggec.glasses.ai.data.cache.api;

/**
 * 缓存策略接口
 * 定义缓存的行为策略
 */
public interface CachePolicy {
    
    /**
     * 获取最大缓存容量
     *
     * @return 最大容量
     */
    int getMaxSize();
    
    /**
     * 获取缓存项的过期时间(毫秒)
     * 返回0表示永不过期
     *
     * @return 过期时间
     */
    long getExpireTime();
    
    /**
     * 检查是否应该清理过期项
     *
     * @return 是否应该清理
     */
    boolean shouldCleanExpired();
    
    /**
     * 获取清理间隔时间(毫秒)
     *
     * @return 清理间隔
     */
    long getCleanInterval();
    
    /**
     * 检查缓存是否已满
     *
     * @param currentSize 当前大小
     * @return 是否已满
     */
    boolean isFull(int currentSize);
    
    /**
     * 默认缓存策略
     * 无容量限制，永不过期
     */
    class DefaultPolicy implements CachePolicy {
        
        @Override
        public int getMaxSize() {
            return Integer.MAX_VALUE; // 无容量限制
        }
        
        @Override
        public long getExpireTime() {
            return 0; // 永不过期
        }
        
        @Override
        public boolean shouldCleanExpired() {
            return false; // 无需清理
        }
        
        @Override
        public long getCleanInterval() {
            return 0; // 无清理间隔
        }
        
        @Override
        public boolean isFull(int currentSize) {
            return false; // 永不满
        }
    }
    
    /**
     * LRU缓存策略
     * 限制最大容量，移除最近最少使用的项
     */
    class LruPolicy implements CachePolicy {
        private final int maxSize;
        private final long expireTime;
        private final long cleanInterval;
        
        /**
         * 创建LRU缓存策略
         *
         * @param maxSize 最大容量
         */
        public LruPolicy(int maxSize) {
            this(maxSize, 0, 0);
        }
        
        /**
         * 创建LRU缓存策略
         *
         * @param maxSize 最大容量
         * @param expireTime 过期时间(毫秒)
         * @param cleanInterval 清理间隔(毫秒)
         */
        public LruPolicy(int maxSize, long expireTime, long cleanInterval) {
            this.maxSize = maxSize;
            this.expireTime = expireTime;
            this.cleanInterval = cleanInterval;
        }
        
        @Override
        public int getMaxSize() {
            return maxSize;
        }
        
        @Override
        public long getExpireTime() {
            return expireTime;
        }
        
        @Override
        public boolean shouldCleanExpired() {
            return expireTime > 0;
        }
        
        @Override
        public long getCleanInterval() {
            return cleanInterval;
        }
        
        @Override
        public boolean isFull(int currentSize) {
            return currentSize >= maxSize;
        }
    }
    
    /**
     * 基于时间的缓存策略
     * 限制缓存项的生存时间
     */
    class TimeBasedPolicy implements CachePolicy {
        private final long expireTime;
        private final long cleanInterval;
        
        /**
         * 创建基于时间的缓存策略
         *
         * @param expireTime 过期时间(毫秒)
         * @param cleanInterval 清理间隔(毫秒)
         */
        public TimeBasedPolicy(long expireTime, long cleanInterval) {
            this.expireTime = expireTime;
            this.cleanInterval = cleanInterval;
        }
        
        @Override
        public int getMaxSize() {
            return Integer.MAX_VALUE; // 无容量限制
        }
        
        @Override
        public long getExpireTime() {
            return expireTime;
        }
        
        @Override
        public boolean shouldCleanExpired() {
            return true;
        }
        
        @Override
        public long getCleanInterval() {
            return cleanInterval;
        }
        
        @Override
        public boolean isFull(int currentSize) {
            return false; // 基于时间的策略没有容量限制
        }
    }
} 