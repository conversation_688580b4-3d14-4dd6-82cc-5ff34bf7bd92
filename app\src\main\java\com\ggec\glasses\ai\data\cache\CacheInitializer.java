package com.ggec.glasses.ai.data.cache;

import android.content.Context;
import android.util.Log;

import com.ggec.glasses.ai.data.cache.manager.CacheManager;
import com.ggec.glasses.ai.data.repository.CachedChatMessageRepository;
import com.ggec.glasses.ai.data.repository.MessageRepositoryFactory;

/**
 * 缓存初始化器
 * 负责在应用启动时初始化缓存系统并预加载数据
 */
public class CacheInitializer {
    private static final String TAG = "CacheInitializer";
    private static final int DEFAULT_PRELOAD_MESSAGE_COUNT = 50;
    
    private static volatile boolean isInitialized = false;
    
    /**
     * 初始化缓存系统
     * 该方法应在应用启动时被调用一次，例如在Application的onCreate方法中
     *
     * @param context 应用上下文
     */
    public static void init(Context context) {
        if (isInitialized) {
            Log.w(TAG, "缓存系统已经初始化，跳过重复初始化");
            return;
        }
        
        synchronized (CacheInitializer.class) {
            if (isInitialized) {
                return;
            }
            
            Log.d(TAG, "开始初始化缓存系统...");
            
            // 1. 初始化缓存管理器
            CacheManager cacheManager = CacheManager.getInstance();
            
            // 2. 获取消息缓存
            cacheManager.getMessageCache();
            
            // 3. 预加载最近的消息
            preloadRecentMessages(context);
            
            isInitialized = true;
            Log.d(TAG, "缓存系统初始化完成");
        }
    }
    
    /**
     * 预加载最近的消息到缓存
     *
     * @param context 应用上下文
     */
    private static void preloadRecentMessages(Context context) {
        Log.d(TAG, "开始预加载最近消息...");
        try {
            CachedChatMessageRepository repository = MessageRepositoryFactory.getCachedRepository(context);
            repository.preloadRecentMessages(DEFAULT_PRELOAD_MESSAGE_COUNT);
        } catch (Exception e) {
            Log.e(TAG, "预加载消息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 关闭缓存系统
     * 该方法应在应用关闭时被调用，例如在Application的onTerminate方法中
     */
    public static void shutdown() {
        if (!isInitialized) {
            return;
        }
        
        Log.d(TAG, "正在关闭缓存系统...");
        
        // 关闭缓存管理器
        CacheManager.getInstance().shutdown();
        
        isInitialized = false;
        Log.d(TAG, "缓存系统已关闭");
    }
    
    /**
     * 清空所有缓存
     * 可用于登出操作或清除应用数据时
     */
    public static void clearAll() {
        if (!isInitialized) {
            init(null); // 确保已初始化
        }
        
        Log.d(TAG, "正在清空所有缓存...");
        
        // 清空所有缓存
        CacheManager.getInstance().clearAll();
        
        Log.d(TAG, "所有缓存已清空");
    }
} 