package com.ggec.glasses.device.manager;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.ggec.glasses.device.model.Device;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备管理器
 * 负责管理设备连接和信息
 */
public class DeviceManager {
    
    private static DeviceManager instance;
    private Context context;
    private List<Device> deviceList;
    private Device currentDevice;
    private DeviceStateListener deviceStateListener;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 通过单例模式实现设备管理器
    private DeviceManager(Context context) {
        this.context = context.getApplicationContext();
        this.deviceList = new ArrayList<>();
        
        // 在开发阶段，添加一个默认设备
        initDefaultDevice();
    }
    
    /**
     * 获取设备管理器实例
     * @param context 上下文
     * @return 设备管理器实例
     */
    public static synchronized DeviceManager getInstance(Context context) {
        if (instance == null) {
            instance = new DeviceManager(context);
        }
        return instance;
    }
    
    /**
     * 初始化默认设备（开发阶段使用）
     */
    private void initDefaultDevice() {
        Device defaultDevice = new Device(
                "12345678",
                "GGEC_AIGlass",
                "GG2023",
                "1.0.0",
                true,
                100
        );
        
        deviceList.add(defaultDevice);
        currentDevice = defaultDevice;
    }
    
    /**
     * 获取当前设备
     * @return 当前设备
     */
    public Device getCurrentDevice() {
        return currentDevice;
    }
    
    /**
     * 获取设备列表
     * @return 设备列表
     */
    public List<Device> getDeviceList() {
        return deviceList;
    }
    
    /**
     * 连接设备
     * @param device 要连接的设备
     */
    public void connectDevice(Device device) {
        // 真实场景中，这里应该实现实际的设备连接逻辑
        // 开发阶段，我们只模拟连接过程
        
        // 模拟连接延迟
        mainHandler.postDelayed(() -> {
            device.setConnected(true);
            currentDevice = device;
            
            // 通知监听器
            if (deviceStateListener != null) {
                deviceStateListener.onDeviceConnected(device);
            }
        }, 1000);
    }
    
    /**
     * 断开设备连接
     */
    public void disconnectCurrentDevice() {
        if (currentDevice != null) {
            // 真实场景中，这里应该实现实际的设备断开连接逻辑
            // 开发阶段，我们只模拟断开连接过程
            
            // 模拟断开连接延迟
            final Device deviceToDisconnect = currentDevice;
            mainHandler.postDelayed(() -> {
                deviceToDisconnect.setConnected(false);
                
                // 通知监听器
                if (deviceStateListener != null) {
                    deviceStateListener.onDeviceDisconnected(deviceToDisconnect);
                }
            }, 1000);
        }
    }
    
    /**
     * 设置设备状态监听器
     * @param listener 设备状态监听器
     */
    public void setDeviceStateListener(DeviceStateListener listener) {
        this.deviceStateListener = listener;
    }
    
    /**
     * 设备状态监听器接口
     */
    public interface DeviceStateListener {
        /**
         * 设备连接成功时调用
         * @param device 连接的设备
         */
        void onDeviceConnected(Device device);
        
        /**
         * 设备断开连接时调用
         * @param device 断开连接的设备
         */
        void onDeviceDisconnected(Device device);
        
        /**
         * 设备状态变化时调用
         * @param device 状态变化的设备
         */
        void onDeviceStateChanged(Device device);
    }
} 