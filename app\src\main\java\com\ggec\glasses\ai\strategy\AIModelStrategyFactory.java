package com.ggec.glasses.ai.strategy;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import java.util.HashMap;
import java.util.Map;

/**
 * AI模型策略工厂
 * 负责创建、缓存和管理不同AI模型的策略实例
 */
public class AIModelStrategyFactory {

    private static final String TAG = "AIModelStrategyFactory";
    private static final String PREFS_NAME = "ai_model_prefs";
    private static final String KEY_CURRENT_MODEL = "current_model_type";
    
    // 单例实例
    private static volatile AIModelStrategyFactory instance;
    
    // 策略缓存
    private final Map<AIModelType, AIModelStrategy> strategyCache = new HashMap<>();
    
    // 当前激活的模型类型
    private AIModelType currentModelType;
    
    // SharedPreferences用于持久化选择
    private final SharedPreferences preferences;
    
    /**
     * 私有构造函数
     * @param context 应用上下文
     */
    private AIModelStrategyFactory(Context context) {
        Context appContext = context.getApplicationContext();
        preferences = appContext.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        
        // 从SharedPreferences加载上次使用的模型类型
        String savedModelName = preferences.getString(
                KEY_CURRENT_MODEL, 
                AIModelType.getDefault().name()
        );
        
        try {
            currentModelType = AIModelType.valueOf(savedModelName);
        } catch (IllegalArgumentException e) {
            // 如果保存的值无效，使用默认值
            currentModelType = AIModelType.getDefault();
        }
        
        Log.d(TAG, "初始化AI模型策略工厂，当前模型类型: " + currentModelType);
    }
    
    /**
     * 获取工厂实例（单例模式）
     * @param context 应用上下文
     * @return 工厂实例
     */
    public static AIModelStrategyFactory getInstance(Context context) {
        if (instance == null) {
            synchronized (AIModelStrategyFactory.class) {
                if (instance == null) {
                    instance = new AIModelStrategyFactory(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取当前策略
     * @param context 应用上下文
     * @return 当前激活的策略实例
     */
    public AIModelStrategy getCurrentStrategy(Context context) {
        return getStrategy(context, currentModelType);
    }
    
    /**
     * 获取指定类型的策略
     * @param context 应用上下文
     * @param modelType 模型类型
     * @return 对应的策略实例
     */
    public AIModelStrategy getStrategy(Context context, AIModelType modelType) {
        // 从缓存中获取
        AIModelStrategy strategy = strategyCache.get(modelType);
        
        // 如果缓存中没有，创建新实例
        if (strategy == null) {
            strategy = createStrategy(modelType);
            if (strategy != null) {
                // 初始化策略
                boolean initSuccess = strategy.initialize(context);
                if (initSuccess) {
                    // 加入缓存
                    strategyCache.put(modelType, strategy);
                    Log.d(TAG, modelType + " 策略初始化成功并加入缓存");
                } else {
                    Log.e(TAG, "初始化 " + modelType + " 策略失败");
                    return null;
                }
            }
        }
        
        return strategy;
    }
    
    /**
     * 创建指定类型的策略实例
     * @param modelType 模型类型
     * @return 新创建的策略实例
     */
    private AIModelStrategy createStrategy(AIModelType modelType) {
        switch (modelType) {
            case DEEPSEEK:
                return new DeepSeekStrategy();
            case LOCAL_MODEL:
                // return new LocalModelStrategy(); // 尚未实现
                Log.w(TAG, "本地模型尚未实现");
                return null;
            case OTHER_CLOUD:
                // return new OtherCloudStrategy(); // 尚未实现
                Log.w(TAG, "其他云端模型尚未实现");
                return null;
            default:
                Log.e(TAG, "未知的模型类型: " + modelType);
                return null;
        }
    }
    
    /**
     * 切换当前使用的模型类型
     * @param context 应用上下文
     * @param newModelType 新的模型类型
     * @return 切换是否成功
     */
    public boolean switchModelType(Context context, AIModelType newModelType) {
        if (newModelType == currentModelType) {
            Log.d(TAG, "已经使用 " + newModelType + " 模型，无需切换");
            return true;
        }
        
        // 尝试获取新模型的策略
        AIModelStrategy newStrategy = getStrategy(context, newModelType);
        if (newStrategy != null && newStrategy.isAvailable()) {
            // 保存当前选择
            preferences.edit().putString(KEY_CURRENT_MODEL, newModelType.name()).apply();
            
            // 更新当前模型类型
            currentModelType = newModelType;
            Log.d(TAG, "成功切换到 " + newModelType + " 模型");
            return true;
        } else {
            Log.e(TAG, "切换到 " + newModelType + " 模型失败：策略不可用");
            return false;
        }
    }
    
    /**
     * 获取当前模型类型
     * @return 当前激活的模型类型
     */
    public AIModelType getCurrentModelType() {
        return currentModelType;
    }
    
    /**
     * 释放所有策略资源
     */
    public void releaseAll() {
        Log.d(TAG, "释放所有AI模型策略资源");
        
        for (AIModelStrategy strategy : strategyCache.values()) {
            try {
                strategy.release();
            } catch (Exception e) {
                Log.e(TAG, "释放策略资源时出错", e);
            }
        }
        
        strategyCache.clear();
    }
} 