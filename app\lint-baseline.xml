<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.8.2" type="baseline" client="gradle" dependencies="false" name="AGP (8.8.2)" variant="all" version="8.8.2">

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        Toast.makeText(this, &quot;设备已连接: &quot; + device.getName(), Toast.LENGTH_SHORT).show();"
        errorLine2="                                         ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/BluetoothConnectionActivity.java"
            line="513"
            column="42"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        Toast.makeText(this, &quot;设备已断开连接: &quot; + device.getName(), Toast.LENGTH_SHORT).show();"
        errorLine2="                                           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/BluetoothConnectionActivity.java"
            line="519"
            column="44"/>
    </issue>

    <issue
        id="ScopedStorage"
        message="READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: `READ_MEDIA_IMAGES`, `READ_MEDIA_VIDEO` or `READ_MEDIA_AUDIO`."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="6"
            column="36"/>
    </issue>

    <issue
        id="ScopedStorage"
        message="WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the `MediaStore.createWriteRequest` intent."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.WRITE_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="7"
            column="36"/>
    </issue>

    <issue
        id="CheckResult"
        message="The result of `subscribe` is not used"
        errorLine1="            audioDataProcessor.subscribe("
        errorLine2="            ^">
        <location
            file="src/main/java/com/example/glasses/asr/service/impl/DashScopeAsrService.java"
            line="186"
            column="13"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        String name = deviceName.toLowerCase();"
        errorLine2="                                 ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/model/BluetoothDeviceType.java"
            line="50"
            column="34"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                extension = originalFileName.substring(dotIndex + 1).toLowerCase();"
        errorLine2="                                                                     ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/data/manager/MediaFileManager.java"
            line="312"
            column="70"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        extension = extension.toLowerCase();"
        errorLine2="                              ~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/data/manager/MediaFileManager.java"
            line="373"
            column="31"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            tvSelectedCount.setText(String.format(&quot;已选择 %d 项&quot;, count));"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/manager/MultiSelectModeManager.java"
            line="390"
            column="37"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            return String.format(&quot;%02d:%02d:%02d&quot;, hours, minutes, seconds);"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/util/TimeFormatUtil.java"
            line="29"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            return String.format(&quot;%02d:%02d&quot;, minutes, seconds);"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/util/TimeFormatUtil.java"
            line="32"
            column="20"/>
    </issue>

    <issue
        id="InlinedApi"
        message="Field requires API level 28 (current min is 24): `android.bluetooth.BluetoothProfile#HID_DEVICE`"
        errorLine1="            BluetoothProfile.HID_DEVICE, // HID设备配置文件"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/GlassesBluetoothAdapter.java"
            line="44"
            column="13"/>
    </issue>

    <issue
        id="InlinedApi"
        message="Field requires API level 28 (current min is 24): `android.bluetooth.BluetoothProfile#HID_DEVICE`"
        errorLine1="            BluetoothProfile hidProfile = profiles.get(BluetoothProfile.HID_DEVICE);"
        errorLine2="                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/GlassesBluetoothAdapter.java"
            line="680"
            column="56"/>
    </issue>

    <issue
        id="InternalInsetResource"
        message="Using internal inset dimension resource `status_bar_height` is not supported"
        errorLine1="        int resourceId = context.getResources().getIdentifier(&quot;status_bar_height&quot;, &quot;dimen&quot;, &quot;android&quot;);"
        errorLine2="                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/utils/StatusBarUtils.java"
            line="25"
            column="26"/>
    </issue>

    <issue
        id="NewApi"
        message="Class requires API level 28 (current min is 24): `android.bluetooth.BluetoothHidDevice`"
        errorLine1="            if (hidProfile != null &amp;&amp; hidProfile instanceof BluetoothHidDevice) {"
        errorLine2="                                                            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/GlassesBluetoothAdapter.java"
            line="681"
            column="61"/>
    </issue>

    <issue
        id="NewApi"
        message="Class requires API level 28 (current min is 24): `android.bluetooth.BluetoothHidDevice`"
        errorLine1="                BluetoothHidDevice hid = (BluetoothHidDevice) hidProfile;"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/GlassesBluetoothAdapter.java"
            line="682"
            column="43"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        View popupView = LayoutInflater.from(context).inflate(R.layout.ai_popup_menu, null);"
        errorLine2="                                                                                      ~~~~">
        <location
            file="src/main/java/com/example/glasses/ai/ui/AIPopupMenuHandler.java"
            line="52"
            column="87"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        View popupView = LayoutInflater.from(requireContext()).inflate(R.layout.album_popup_menu, null);"
        errorLine2="                                                                                                  ~~~~">
        <location
            file="src/main/java/com/example/glasses/fragments/AlbumFragment.java"
            line="212"
            column="99"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        View dialogView = LayoutInflater.from(context).inflate(R.layout.layout_custom_dialog, null);"
        errorLine2="                                                                                              ~~~~">
        <location
            file="src/main/java/com/example/glasses/utils/DialogUtils.java"
            line="52"
            column="95"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.squareup.okhttp3:logging-interceptor than 4.9.3 is available: 4.12.0"
        errorLine1="    implementation &apos;com.squareup.okhttp3:logging-interceptor:4.9.3&apos; // 可选：日志拦截器"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="94"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycle = &quot;2.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycle = &quot;2.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycle = &quot;2.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycle = &quot;2.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycle = &quot;2.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel than 2.7.0 is available: 2.8.7"
        errorLine1="lifecycle = &quot;2.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="LockedOrientationActivity"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices"
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="60"
            column="13"/>
    </issue>

    <issue
        id="LockedOrientationActivity"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices"
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="66"
            column="13"/>
    </issue>

    <issue
        id="LockedOrientationActivity"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices"
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="72"
            column="13"/>
    </issue>

    <issue
        id="LockedOrientationActivity"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices"
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="78"
            column="13"/>
    </issue>

    <issue
        id="LockedOrientationActivity"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices"
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="84"
            column="13"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="60"
            column="13"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="66"
            column="13"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="72"
            column="13"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="78"
            column="13"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        errorLine1="            android:screenOrientation=&quot;portrait&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="84"
            column="13"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Use of `scheduleAtFixedRate` is strongly discouraged because it can lead to unexpected behavior when Android processes become cached (tasks may unexpectedly execute hundreds or thousands of times in quick succession when a process changes from cached to uncached); prefer using `scheduleWithFixedDelay`"
        errorLine1="        cleanerExecutor.scheduleAtFixedRate("
        errorLine2="        ^">
        <location
            file="src/main/java/com/example/glasses/ai/data/cache/manager/CacheManager.java"
            line="150"
            column="9"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`)."
        errorLine1="        int resourceId = context.getResources().getIdentifier(&quot;status_bar_height&quot;, &quot;dimen&quot;, &quot;android&quot;);"
        errorLine2="                                                ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/utils/StatusBarUtils.java"
            line="25"
            column="49"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/icon_primary&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_ai_model_settings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/icon_primary&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_bluetooth_connection.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/icon_primary&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_debug.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/icon_primary&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_glasses_settings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/icon_primary&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_key_settings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="HardwareIds"
        message="Using `getAddress` to get device identifiers is not recommended"
        errorLine1="            return nativeAdapter.getAddress();"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/GlassesBluetoothAdapter.java"
            line="439"
            column="20"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        availableDevicesAdapter.notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/BluetoothConnectionActivity.java"
            line="279"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="            pairedDevicesAdapter.notifyDataSetChanged();"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/BluetoothConnectionActivity.java"
            line="407"
            column="13"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="            availableDevicesAdapter.notifyDataSetChanged();"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/BluetoothConnectionActivity.java"
            line="430"
            column="13"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        availableDevicesAdapter.notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/BluetoothConnectionActivity.java"
            line="484"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/bluetooth/adapter/BluetoothDeviceAdapter.java"
            line="93"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="                notifyDataSetChanged();"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/adapter/MediaAdapter.java"
            line="64"
            column="17"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="            notifyDataSetChanged();"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/adapter/MediaAdapter.java"
            line="232"
            column="13"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="            notifyDataSetChanged();"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/adapter/MediaAdapter.java"
            line="241"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is never true here (`SDK_INT` ≥ 24 and &lt; 30)"
        errorLine1="        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/utils/StatusBarUtils.java"
            line="50"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is never true here (`SDK_INT` ≥ 24 and &lt; 30)"
        errorLine1="        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/utils/StatusBarUtils.java"
            line="77"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is never true here (`SDK_INT` ≥ 24 and &lt; 30)"
        errorLine1="        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/util/UIAnimationHelper.java"
            line="158"
            column="20"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `AlbumRepository` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static AlbumRepository INSTANCE;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/data/repository/AlbumRepository.java"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `MediaFileManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static MediaFileManager mediaFileManager;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/example/glasses/GlassesApplication.java"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `TagRepository` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static TagRepository INSTANCE;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/example/glasses/album/data/repository/TagRepository.java"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (870 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M21.949,12.393C22.242,12.397 22.477,12.64 22.473,12.936L22.465,13.239C22.316,16.843 20.311,20.044 17.239,21.752C16.982,21.895 16.659,21.8 16.517,21.541C16.375,21.281 16.469,20.954 16.726,20.811C19.476,19.282 21.269,16.42 21.403,13.194L21.41,12.923C21.414,12.626 21.655,12.389 21.949,12.393ZM2.051,12.393C2.345,12.389 2.586,12.626 2.59,12.923L2.597,13.194C2.731,16.42 4.524,19.282 7.274,20.811C7.531,20.954 7.625,21.281 7.483,21.541C7.341,21.8 7.018,21.895 6.761,21.752C3.689,20.044 1.684,16.843 1.535,13.239L1.527,12.936C1.523,12.64 1.758,12.397 2.051,12.393ZM12.501,2.191C14.175,2.261 15.776,2.732 17.194,3.554C17.452,3.703 17.54,4.032 17.391,4.29C17.242,4.547 16.912,4.635 16.654,4.485C15.385,3.75 13.955,3.329 12.456,3.266C10.668,3.191 8.935,3.634 7.428,4.523C7.172,4.675 6.842,4.59 6.69,4.333C6.539,4.077 6.624,3.747 6.88,3.596C8.566,2.601 10.504,2.107 12.501,2.191Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_ai.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (889 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M8.25,7.25C7.284,7.25 6.5,8.034 6.5,9C6.5,9.966 7.284,10.75 8.25,10.75C9.216,10.75 10,9.966 10,9C10,8.034 9.216,7.25 8.25,7.25ZM17.586,11.83C17.928,11.934 18.214,12.089 18.801,12.667L20.353,14.22C20.447,14.314 20.5,14.441 20.5,14.573L20.5,16.334L20.5,16.334C20.5,17.782 20.349,18.308 20.066,18.837C19.783,19.367 19.367,19.783 18.837,20.066L18.758,20.107C18.253,20.364 17.71,20.5 16.334,20.5L7.666,20.5L7.456,20.499C6.165,20.485 5.666,20.335 5.163,20.066C4.633,19.783 4.217,19.367 3.934,18.837C3.897,18.768 3.852,18.671 3.798,18.545C3.758,18.451 3.779,18.342 3.851,18.27L6.687,15.434L6.687,15.434C7.317,14.804 7.612,14.641 7.965,14.534C8.319,14.427 8.681,14.427 9.035,14.534L9.107,14.557L9.179,14.582C9.464,14.69 9.746,14.875 10.249,15.371L10.363,15.485C10.949,16.07 11.899,16.07 12.485,15.485L15.239,12.731C15.869,12.101 16.163,11.938 16.517,11.83C16.871,11.723 17.233,11.723 17.586,11.83Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_album.xml"
            line="14"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (1102 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M22.5,19.609C22.5,21.42 21.082,22.898 19.295,22.995L19.109,23L18.891,23C17.08,23 15.602,21.582 15.505,19.795L15.5,19.609L15.5,16.391C15.5,14.58 16.918,13.102 18.705,13.005L18.891,13L19.109,13C19.81,13 20.461,13.212 21.001,13.576L21,11.5C21,6.529 16.971,2.5 12,2.5C7.079,2.5 3.081,6.449 3.001,11.351L3,13.576C3.48,13.253 4.047,13.049 4.659,13.008L4.891,13L5.109,13C6.92,13 8.398,14.418 8.495,16.205L8.5,16.391L8.5,19.609C8.5,21.42 7.082,22.898 5.295,22.995L5.109,23L4.891,23C3.08,23 1.602,21.582 1.505,19.795L1.5,19.609L1.5,11.5C1.5,5.701 6.201,1 12,1C17.741,1 22.406,5.607 22.499,11.326L22.5,11.5L22.5,19.609ZM19.109,14.5L18.891,14.5C17.896,14.5 17.081,15.268 17.006,16.243L17,16.391L17,19.609C17,20.604 17.768,21.419 18.743,21.494L18.891,21.5L19.109,21.5C20.104,21.5 20.919,20.732 20.994,19.757L21,19.609L21,16.391C21,15.396 20.232,14.581 19.257,14.506L19.109,14.5ZM5.109,14.5L4.891,14.5C3.896,14.5 3.081,15.268 3.006,16.243L3,16.391L3,19.609C3,20.604 3.768,21.419 4.743,21.494L4.891,21.5L5.109,21.5C6.104,21.5 6.919,20.732 6.994,19.757L7,19.609L7,16.391C7,15.396 6.232,14.581 5.257,14.506L5.109,14.5Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable-nodpi/ic_device_earphone.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (4022 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M19.144,7.155C19.611,6.768 20.789,7.155 21.533,7.774C22.029,8.186 22.753,8.824 23.706,9.688L23.625,9.616C23.644,9.619 23.662,9.622 23.68,9.625C23.733,9.633 23.784,9.647 23.836,9.661C23.91,9.679 23.946,9.727 23.951,9.803C23.962,9.97 23.979,10.137 23.984,10.305C23.991,10.538 23.991,10.771 23.991,11.004C23.991,11.056 23.978,11.109 23.966,11.161C23.951,11.229 23.915,11.278 23.844,11.303C23.68,11.361 23.58,11.491 23.482,11.624C23.384,11.756 23.348,11.911 23.305,12.064C23.259,12.227 23.208,12.389 23.167,12.554C23.09,12.869 23.015,13.185 22.943,13.501C22.795,14.147 22.632,14.789 22.378,15.404C22.257,15.696 22.111,15.973 21.913,16.222C21.607,16.607 21.198,16.832 20.733,16.971C20.28,17.105 19.814,17.165 19.343,17.185C18.563,17.219 17.788,17.183 17.02,17.035C16.509,16.936 16.019,16.778 15.561,16.53C14.913,16.179 14.424,15.67 14.053,15.04C13.606,14.282 13.253,13.484 12.987,12.647C12.925,12.453 12.847,12.264 12.764,12.078C12.722,11.982 12.661,11.891 12.595,11.81C12.444,11.622 12.226,11.574 11.999,11.561C11.901,11.555 11.802,11.559 11.705,11.574C11.403,11.62 11.2,11.794 11.096,12.075C11.025,12.266 10.967,12.462 10.906,12.657C10.694,13.334 10.457,14.002 10.12,14.631C9.783,15.26 9.343,15.809 8.797,16.271C8.44,16.573 8.019,16.76 7.573,16.894C6.973,17.074 6.357,17.168 5.733,17.213C5.117,17.257 4.503,17.245 3.892,17.149C3.551,17.095 3.219,17.011 2.899,16.879C2.371,16.661 1.999,16.281 1.738,15.785C1.505,15.341 1.358,14.867 1.245,14.382C1.127,13.874 1.02,13.362 0.905,12.853C0.88,12.744 0.839,12.639 0.809,12.531C0.759,12.358 0.721,12.181 0.661,12.012C0.577,11.774 0.472,11.546 0.254,11.395C0.209,11.364 0.157,11.342 0.106,11.32C0.058,11.299 0.031,11.264 0.023,11.215C0.014,11.156 -0,11.097 0.001,11.038C0.011,10.648 0.025,10.258 0.038,9.868C0.04,9.786 0.089,9.737 0.163,9.718L0.256,9.694C0.248,9.692 0.241,9.69 0.233,9.688C1.186,8.824 1.91,8.186 2.406,7.774C3.15,7.155 4.328,6.768 4.795,7.155C5.262,7.542 5.204,7.97 5.013,8.077C4.821,8.183 4.557,7.886 4.167,7.886C3.778,7.886 3.417,8.168 3.023,8.598C2.779,8.866 2.426,9.182 2.101,9.425L2.577,9.364L2.577,9.364C3.388,9.267 4.204,9.237 5.112,9.235C6.225,9.223 7.42,9.323 8.605,9.54C9.215,9.652 9.802,9.838 10.39,10.027C10.715,10.131 11.045,10.226 11.38,10.289C11.937,10.392 12.488,10.316 13.025,10.148C13.369,10.039 13.706,9.91 14.05,9.8C14.73,9.582 15.429,9.455 16.136,9.366C17.293,9.221 18.454,9.196 19.618,9.247C20.222,9.274 20.826,9.301 21.429,9.343L21.772,9.374C21.465,9.139 21.144,8.847 20.916,8.598C20.522,8.168 20.162,7.886 19.772,7.886C19.382,7.886 19.118,8.183 18.927,8.077C18.735,7.97 18.677,7.542 19.144,7.155ZM5.598,10.25C5.354,10.264 4.978,10.278 4.602,10.308C4.095,10.347 3.593,10.42 3.1,10.544C2.561,10.679 2.225,11.009 2.1,11.528C2.065,11.674 2.053,11.826 2.033,11.975C1.995,12.271 1.989,12.568 2.02,12.865C2.073,13.379 2.143,13.89 2.268,14.392C2.364,14.777 2.507,15.142 2.739,15.473C2.95,15.773 3.227,15.976 3.592,16.066C3.769,16.109 3.946,16.154 4.126,16.18C5.045,16.312 5.954,16.255 6.852,16.031C7.296,15.919 7.7,15.738 8.036,15.432C8.29,15.2 8.491,14.929 8.667,14.64C8.991,14.111 9.233,13.551 9.38,12.954C9.494,12.49 9.541,12.022 9.457,11.548C9.417,11.318 9.32,11.114 9.131,10.965C9.016,10.875 8.889,10.795 8.758,10.728C8.491,10.592 8.195,10.535 7.903,10.471C7.189,10.313 6.463,10.26 5.598,10.25ZM18.152,10.25C17.287,10.26 16.561,10.313 15.847,10.471C15.555,10.535 15.259,10.592 14.992,10.728C14.861,10.795 14.734,10.875 14.619,10.965C14.43,11.114 14.333,11.318 14.293,11.548C14.209,12.022 14.256,12.49 14.37,12.954C14.517,13.551 14.759,14.111 15.083,14.64C15.259,14.929 15.46,15.2 15.714,15.432C16.05,15.738 16.454,15.919 16.898,16.031C17.796,16.255 18.705,16.312 19.624,16.18C19.804,16.154 19.981,16.109 20.158,16.066C20.523,15.976 20.8,15.773 21.011,15.473C21.243,15.142 21.386,14.777 21.482,14.392C21.607,13.89 21.677,13.379 21.73,12.865C21.761,12.568 21.755,12.271 21.717,11.975C21.697,11.826 21.685,11.674 21.65,11.528C21.525,11.009 21.189,10.679 20.65,10.544C20.157,10.42 19.655,10.347 19.148,10.308C18.772,10.278 18.396,10.264 18.152,10.25Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable-nodpi/ic_device_glass.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (1622 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M14.439,0.75C15.476,0.75 16.349,1.519 16.484,2.541L16.492,2.605C16.623,3.898 17.044,5.302 17.761,6.817C17.838,6.901 17.912,6.988 17.985,7.076C17.997,7.068 18.009,7.06 18.022,7.052C18.257,6.91 18.566,6.987 18.708,7.223L19.225,8.084C19.367,8.319 19.29,8.628 19.054,8.77L19.048,8.773C19.499,9.755 19.75,10.848 19.75,12C19.75,13.184 19.484,14.307 19.009,15.311C19.025,15.317 19.04,15.326 19.055,15.335C19.279,15.469 19.359,15.755 19.245,15.985L19.226,16.021L18.707,16.883C18.566,17.119 18.257,17.195 18.021,17.054C17.989,17.034 17.959,17.011 17.933,16.986C17.877,17.053 17.82,17.119 17.761,17.184C17.072,18.639 16.655,19.994 16.508,21.245L16.492,21.395C16.385,22.448 15.498,23.25 14.439,23.25L9.533,23.25C8.458,23.25 7.563,22.424 7.477,21.352C7.358,19.878 6.965,18.51 6.295,17.245C5.025,15.865 4.25,14.023 4.25,12C4.25,9.977 5.025,8.135 6.294,6.755C6.964,5.49 7.358,4.122 7.477,2.647C7.562,1.597 8.423,0.783 9.469,0.751L9.533,0.75L14.439,0.75ZM15.473,18.93L15.454,18.939C14.414,19.458 13.241,19.75 12,19.75C10.77,19.75 9.608,19.464 8.575,18.954C8.777,19.688 8.909,20.448 8.972,21.232C8.996,21.524 9.24,21.75 9.533,21.75L14.439,21.75C14.728,21.75 14.97,21.531 14.999,21.243L15.017,21.08C15.098,20.384 15.251,19.667 15.473,18.93ZM12,5.75C8.548,5.75 5.75,8.548 5.75,12C5.75,15.452 8.548,18.25 12,18.25C15.452,18.25 18.25,15.452 18.25,12C18.25,8.548 15.452,5.75 12,5.75ZM14.439,2.25L9.533,2.25C9.24,2.25 8.996,2.475 8.972,2.768C8.909,3.552 8.777,4.312 8.576,5.046C9.608,4.536 10.77,4.25 12,4.25C13.249,4.25 14.429,4.545 15.474,5.07C15.27,4.397 15.125,3.741 15.04,3.102L15.017,2.92L14.999,2.756C14.97,2.469 14.728,2.25 14.439,2.25Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable-nodpi/ic_device_watch.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (1213 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M14.654,0.5C15.991,0.5 16.476,0.639 16.965,0.901C17.454,1.162 17.838,1.546 18.099,2.035L18.14,2.112C18.375,2.577 18.5,3.079 18.5,4.346L18.5,7.75L18.5,7.75C18.776,7.75 19,7.974 19,8.25L19,10.25C19,10.526 18.776,10.75 18.5,10.75L18.5,19.654L18.499,19.858C18.485,21.043 18.347,21.502 18.099,21.965C17.838,22.454 17.454,22.838 16.965,23.099L16.888,23.14C16.423,23.375 15.921,23.5 14.654,23.5L9.346,23.5L9.142,23.499C7.957,23.485 7.498,23.347 7.035,23.099C6.546,22.838 6.162,22.454 5.901,21.965L5.86,21.888C5.625,21.423 5.5,20.921 5.5,19.654L5.5,4.346C5.5,3.009 5.639,2.524 5.901,2.035C6.162,1.546 6.546,1.162 7.035,0.901L7.112,0.86C7.577,0.625 8.079,0.5 9.346,0.5L14.654,0.5ZM14.743,2L9.346,2L9.092,2.001C8.298,2.011 8.023,2.073 7.742,2.223C7.515,2.345 7.345,2.515 7.223,2.742C7.063,3.041 7.004,3.335 7,4.257L7,19.743L7.002,19.913C7.012,20.703 7.074,20.978 7.223,21.258C7.345,21.485 7.515,21.655 7.742,21.777C8.023,21.927 8.298,21.989 9.092,21.999L9.346,22L14.654,22C15.647,22 15.949,21.942 16.258,21.777C16.485,21.655 16.655,21.485 16.777,21.258C16.927,20.977 16.989,20.702 16.999,19.908L17,19.654L17,4.346C17,3.353 16.942,3.051 16.777,2.742C16.655,2.515 16.485,2.345 16.258,2.223C15.959,2.063 15.665,2.004 14.743,2Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable-nodpi/ic_devices_phone.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (1108 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M5.629,7.5L6.726,18.474C6.839,19.602 7.772,20.466 8.898,20.499L8.965,20.5L15.034,20.5C16.168,20.5 17.121,19.657 17.266,18.539L17.273,18.473L18.192,9.253L18.369,7.5L19.877,7.5L19.685,9.403L18.766,18.622C18.577,20.512 17.007,21.958 15.117,21.999L15.034,22L8.965,22C7.066,22 5.471,20.582 5.243,18.705L5.234,18.623L4.121,7.5L5.629,7.5ZM10.25,11.75C10.664,11.75 11,12.086 11,12.5L11,18.5C11,18.914 10.664,19.25 10.25,19.25C9.836,19.25 9.5,18.914 9.5,18.5L9.5,12.5C9.5,12.086 9.836,11.75 10.25,11.75ZM13.75,11.75C14.164,11.75 14.5,12.086 14.5,12.5L14.5,18.5C14.5,18.914 14.164,19.25 13.75,19.25C13.336,19.25 13,18.914 13,18.5L13,12.5C13,12.086 13.336,11.75 13.75,11.75ZM12,1.75C13.769,1.75 15.208,3.164 15.249,4.923L15.25,5L21,5C21.414,5 21.75,5.336 21.75,5.75C21.75,6.149 21.438,6.476 21.044,6.499L21,6.5L14.5,6.5C14.101,6.5 13.774,6.188 13.751,5.794L13.75,5.75L13.75,5C13.75,4.034 12.966,3.25 12,3.25C11.054,3.25 10.283,4.001 10.251,4.94L10.25,5L10.25,5.75C10.25,6.149 9.938,6.476 9.544,6.499L9.5,6.5L2.75,6.5C2.336,6.5 2,6.164 2,5.75C2,5.351 2.312,5.024 2.706,5.001L2.75,5L8.75,5C8.75,3.205 10.205,1.75 12,1.75Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_public_delete.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (1700 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M15.872,1C17.617,1 18.274,1.178 18.912,1.512L18.954,1.534C19.575,1.866 20.068,2.346 20.415,2.954L20.466,3.046C20.786,3.644 20.968,4.238 20.996,5.708C20.702,5.803 20.425,5.967 20.192,6.2L19.5,6.891L19.5,6.128C19.5,4.729 19.407,4.248 19.143,3.754C18.934,3.363 18.637,3.066 18.246,2.857C17.777,2.606 17.32,2.51 16.075,2.501L8.025,2.5L7.823,2.502C6.655,2.515 6.21,2.613 5.754,2.857C5.363,3.066 5.066,3.363 4.857,3.754C4.593,4.248 4.5,4.729 4.5,6.128L4.5,17.975L4.502,18.177C4.515,19.345 4.613,19.79 4.857,20.246C5.066,20.637 5.363,20.934 5.754,21.143C6.223,21.394 6.68,21.49 7.925,21.499L8.128,21.5L15.872,21.5C17.271,21.5 17.753,21.407 18.246,21.143C18.637,20.934 18.934,20.637 19.143,20.246C19.394,19.777 19.49,19.32 19.499,18.075L19.5,17.872L19.5,12.548L21,11.048L21,17.872L20.999,18.094C20.984,19.674 20.808,20.302 20.488,20.912L20.466,20.954C20.134,21.575 19.654,22.068 19.046,22.415L18.954,22.466C18.302,22.814 17.655,23 15.872,23L8.128,23L7.906,22.999C6.326,22.984 5.698,22.808 5.088,22.488L5.046,22.466C4.425,22.134 3.932,21.654 3.585,21.046L3.534,20.954C3.186,20.302 3,19.655 3,17.872L3,6.128C3,4.383 3.178,3.726 3.512,3.088L3.534,3.046C3.866,2.425 4.346,1.932 4.954,1.585L5.046,1.534C5.698,1.186 6.345,1 8.128,1L15.872,1ZM22.314,6.907C22.704,7.298 22.704,7.931 22.314,8.321L13.121,17.514C12.731,17.904 12.098,17.904 11.707,17.514C11.317,17.123 11.317,16.49 11.707,16.099L20.899,6.907C21.29,6.517 21.923,6.517 22.314,6.907ZM15.142,11.25L13.642,12.75L7.5,12.75C7.086,12.75 6.75,12.414 6.75,12C6.75,11.586 7.086,11.25 7.5,11.25L15.142,11.25ZM16.5,6.25C16.914,6.25 17.25,6.586 17.25,7C17.25,7.414 16.914,7.75 16.5,7.75L7.5,7.75C7.086,7.75 6.75,7.414 6.75,7C6.75,6.586 7.086,6.25 7.5,6.25L16.5,6.25Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_public_feedback.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (1137 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M10.691,1.301C11.518,0.9 12.482,0.9 13.309,1.301L13.309,1.301L19.732,4.415C20.507,4.791 21,5.578 21,6.44L21,6.44L21,11.25C21,16.516 17.646,21.11 12.751,22.797C12.738,22.802 12.716,22.809 12.685,22.819C12.239,22.961 11.76,22.961 11.314,22.819L11.314,22.819L11.277,22.807L11.277,22.807L11.252,22.798C6.355,21.112 3,16.517 3,11.25L3,11.25L3,6.44C3,5.578 3.493,4.791 4.268,4.415L4.268,4.415ZM12.654,2.651C12.241,2.45 11.759,2.45 11.346,2.651L11.346,2.651L4.923,5.765C4.664,5.89 4.5,6.152 4.5,6.44L4.5,6.44L4.5,11.25C4.5,15.874 7.448,19.908 11.752,21.384C11.754,21.385 11.756,21.385 11.757,21.386C11.913,21.439 12.081,21.44 12.237,21.388L12.237,21.388L12.246,21.385C16.51,19.92 19.44,15.955 19.499,11.391L19.499,11.391L19.5,11.25L19.5,6.44C19.5,6.152 19.336,5.89 19.077,5.765L19.077,5.765ZM17.756,8.195C18.038,8.478 18.048,8.929 17.786,9.224L17.756,9.256L12,15.012C11.331,15.681 10.255,15.695 9.568,15.054L9.525,15.012L6.757,12.243C6.464,11.951 6.464,11.476 6.757,11.183C7.039,10.9 7.491,10.89 7.785,11.152L7.817,11.183L10.586,13.951C10.676,14.042 10.819,14.048 10.918,13.971L10.939,13.951L16.695,8.195C16.988,7.902 17.463,7.902 17.756,8.195Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_public_security.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (1958 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M15.013,4.5C16.573,4.5 17.139,4.662 17.709,4.967C18.28,5.273 18.727,5.72 19.033,6.291L19.134,6.493C19.374,7.008 19.5,7.61 19.5,8.987L19.5,17.513C19.5,19.073 19.338,19.639 19.033,20.209C18.727,20.78 18.28,21.227 17.709,21.533L17.507,21.634C16.992,21.874 16.39,22 15.013,22L6.487,22C4.927,22 4.361,21.838 3.791,21.533C3.22,21.227 2.773,20.78 2.467,20.209L2.366,20.007C2.126,19.492 2,18.89 2,17.513L2,8.987C2,7.427 2.162,6.861 2.467,6.291C2.773,5.72 3.22,5.273 3.791,4.967L3.993,4.866C4.473,4.642 5.03,4.517 6.222,4.502L15.013,4.5ZM6.487,6C5.291,6 4.899,6.076 4.498,6.29C4.189,6.455 3.955,6.689 3.79,6.998L3.712,7.16C3.569,7.49 3.509,7.884 3.501,8.758L3.5,17.513C3.5,18.709 3.576,19.101 3.79,19.502C3.955,19.811 4.189,20.045 4.498,20.21L4.66,20.288C4.99,20.431 5.384,20.491 6.258,20.499L6.487,20.5L15.013,20.5L15.451,20.496C16.314,20.477 16.654,20.396 17.002,20.21C17.311,20.045 17.545,19.811 17.71,19.502L17.788,19.34C17.931,19.01 17.991,18.616 17.999,17.742L18,17.513L18,8.987L17.996,8.549C17.977,7.686 17.896,7.346 17.71,6.998C17.545,6.689 17.311,6.455 17.002,6.29L16.84,6.212C16.51,6.069 16.116,6.009 15.242,6.001L6.487,6ZM15.59,2C17.819,2 18.627,2.232 19.442,2.668C20.257,3.104 20.896,3.743 21.332,4.558C21.768,5.373 22,6.181 22,8.41L22,14.372C22,16.155 21.814,16.802 21.466,17.454C21.22,17.913 20.894,18.302 20.495,18.614C20.498,18.47 20.5,18.317 20.5,18.154L20.5,8.346C20.5,6.976 20.366,6.283 19.981,5.563C19.626,4.9 19.1,4.374 18.437,4.019C17.765,3.659 17.117,3.519 15.919,3.502L5.846,3.5C5.683,3.5 5.53,3.502 5.385,3.506C5.698,3.106 6.087,2.78 6.546,2.534C7.198,2.186 7.845,2 9.628,2L15.59,2ZM15.808,10.255C16.091,10.537 16.101,10.989 15.839,11.284L15.808,11.316L10.609,16.515C9.939,17.185 8.863,17.199 8.177,16.557L8.134,16.515L5.684,14.066C5.391,13.773 5.391,13.298 5.684,13.005C5.966,12.723 6.418,12.713 6.713,12.975L6.745,13.005L8.841,15.101C9.123,15.384 9.575,15.394 9.869,15.131L9.901,15.101L14.748,10.255C15.041,9.962 15.515,9.962 15.808,10.255Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_public_select_all.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (2780 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M13.489,1.25C14.19,1.25 14.823,1.668 15.099,2.312L15.099,2.312L15.921,4.236L16.772,4.727L18.856,4.476C19.529,4.395 20.186,4.71 20.545,5.28L20.545,5.28L20.58,5.337L22.068,7.91C22.419,8.518 22.374,9.277 21.952,9.838L21.952,9.838L20.694,11.511L20.694,12.488L21.952,14.162C22.36,14.705 22.416,15.432 22.102,16.029L22.102,16.029L22.068,16.09L20.58,18.663C20.229,19.269 19.551,19.608 18.856,19.524L18.856,19.524L16.772,19.272L15.921,19.764L15.099,21.688C14.832,22.311 14.231,22.723 13.557,22.749L13.557,22.749L13.489,22.75L10.511,22.75C9.81,22.75 9.177,22.332 8.901,21.688L8.901,21.688L8.078,19.764L7.228,19.272L5.144,19.524C4.471,19.605 3.814,19.29 3.455,18.72L3.455,18.72L3.42,18.663L1.932,16.09C1.581,15.482 1.626,14.723 2.048,14.162L2.048,14.162L3.306,12.488L3.306,11.511L2.048,9.838C1.64,9.295 1.584,8.568 1.898,7.971L1.898,7.971L1.932,7.91L3.42,5.337C3.771,4.731 4.449,4.392 5.144,4.476L5.144,4.476L7.228,4.727L8.078,4.236L8.901,2.312C9.168,1.689 9.769,1.277 10.443,1.251L10.443,1.251L10.511,1.25ZM13.489,2.75L10.511,2.75C10.41,2.75 10.32,2.81 10.281,2.902L10.281,2.902L9.425,4.902C9.319,5.151 9.135,5.358 8.901,5.494L8.901,5.494L7.906,6.068C7.672,6.203 7.4,6.259 7.131,6.227L7.131,6.227L4.965,5.965C4.865,5.953 4.769,6.002 4.718,6.088L4.718,6.088L3.23,8.661C3.18,8.748 3.187,8.856 3.247,8.936L3.247,8.936L4.555,10.677C4.718,10.893 4.806,11.157 4.806,11.428L4.806,11.428L4.806,12.572C4.806,12.843 4.718,13.107 4.555,13.323L4.555,13.323L3.247,15.064C3.187,15.144 3.18,15.252 3.23,15.339L3.23,15.339L4.718,17.912C4.769,17.998 4.865,18.047 4.965,18.035L4.965,18.035L7.131,17.773C7.4,17.741 7.672,17.797 7.906,17.932L7.906,17.932L8.901,18.506C9.135,18.642 9.319,18.849 9.425,19.098L9.425,19.098L10.281,21.098C10.32,21.19 10.41,21.25 10.511,21.25L10.511,21.25L13.489,21.25C13.59,21.25 13.68,21.19 13.719,21.098L13.719,21.098L14.575,19.097C14.681,18.849 14.865,18.642 15.099,18.506L15.099,18.506L16.094,17.932C16.328,17.797 16.6,17.741 16.869,17.773L16.869,17.773L19.035,18.035C19.135,18.047 19.231,17.998 19.282,17.912L19.282,17.912L20.77,15.339C20.82,15.252 20.813,15.144 20.753,15.064L20.753,15.064L19.445,13.323C19.282,13.107 19.194,12.843 19.194,12.572L19.194,12.572L19.194,11.428C19.194,11.157 19.282,10.893 19.445,10.677L19.445,10.677L20.753,8.936C20.813,8.856 20.82,8.748 20.77,8.661L20.77,8.661L19.282,6.088C19.231,6.002 19.135,5.953 19.035,5.965L19.035,5.965L16.869,6.227C16.6,6.259 16.328,6.203 16.094,6.068L16.094,6.068L15.099,5.494C14.865,5.358 14.681,5.151 14.575,4.903L14.575,4.903L13.719,2.902C13.68,2.81 13.59,2.75 13.489,2.75L13.489,2.75ZM12,7.5C14.485,7.5 16.5,9.515 16.5,12C16.5,14.485 14.485,16.5 12,16.5C9.515,16.5 7.5,14.485 7.5,12C7.5,9.515 9.515,7.5 12,7.5ZM12,9C10.343,9 9,10.343 9,12C9,13.657 10.343,15 12,15C13.657,15 15,13.657 15,12C15,10.343 13.657,9 12,9Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_public_settings.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (883 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M12.25,2C13.907,2 15.25,3.343 15.25,5C15.25,6.657 13.907,8 12.25,8C11.98,8 11.719,7.964 11.471,7.898L5.932,16.205C6.402,16.601 6.748,17.137 6.905,17.75L17.095,17.75C17.428,16.456 18.602,15.5 20,15.5C21.657,15.5 23,16.843 23,18.5C23,20.157 21.657,21.5 20,21.5C18.603,21.5 17.428,20.545 17.095,19.251L6.905,19.251C6.572,20.545 5.397,21.5 4,21.5C2.343,21.5 1,20.157 1,18.5C1,16.843 2.343,15.5 4,15.5C4.192,15.5 4.381,15.518 4.563,15.553L10.162,7.154C9.6,6.609 9.25,5.845 9.25,5C9.25,3.343 10.593,2 12.25,2ZM4,17C3.172,17 2.5,17.672 2.5,18.5C2.5,19.328 3.172,20 4,20C4.828,20 5.5,19.328 5.5,18.5C5.5,17.672 4.828,17 4,17ZM20,17C19.172,17 18.5,17.672 18.5,18.5C18.5,19.328 19.172,20 20,20C20.828,20 21.5,19.328 21.5,18.5C21.5,17.672 20.828,17 20,17ZM12.25,3.5C11.422,3.5 10.75,4.172 10.75,5C10.75,5.828 11.422,6.5 12.25,6.5C13.078,6.5 13.75,5.828 13.75,5C13.75,4.172 13.078,3.5 12.25,3.5Z&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_public_share.xml"
            line="7"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (1007 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="      android:pathData=&quot;M384,213.3 L640,213.3 512,341.3 384,213.3M448,625.5C435.2,640 426.7,661.3 426.7,682.7 426.7,729.6 465.1,768 512,768 558.9,768 597.3,729.6 597.3,682.7 597.3,659.2 587.9,637.9 572.2,622.5L632.7,561.9C663.5,593.1 682.7,635.7 682.7,682.7 682.7,777 606.3,853.3 512,853.3 417.7,853.3 341.3,777 341.3,682.7 341.3,637 359.3,595.6 388.3,565.3L387.8,564.9 689.9,263.3 689.9,263.3C720.6,232.5 763.3,213.3 810.7,213.3 905,213.3 981.3,289.7 981.3,384 981.3,478.3 905,554.7 810.7,554.7 763.7,554.7 721.1,535.5 689.9,504.7L750.5,444.2C765.9,459.9 787.2,469.3 810.7,469.3 857.6,469.3 896,430.9 896,384 896,337.1 857.6,298.7 810.7,298.7 787.2,298.7 765.9,308.1 750.5,323.8L448,625.5M273.5,323.8C258.1,308.1 236.8,298.7 213.3,298.7 166.4,298.7 128,337.1 128,384 128,430.9 166.4,469.3 213.3,469.3 236.8,469.3 258.1,459.9 273.5,444.2L334.1,504.7C302.9,535.5 260.3,554.7 213.3,554.7 119,554.7 42.7,478.3 42.7,384 42.7,289.7 119,213.3 213.3,213.3 260.7,213.3 303.4,232.5 334.1,263.3L334.1,263.3 451.8,381 391.3,441.6 273.5,323.8Z&quot;/>"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_vpn.xml"
            line="8"
            column="25"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme_Glasses`)"
        errorLine1="    android:background=&quot;@color/background_primary&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_ai_model_settings.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme_Glasses`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_bluetooth_connection.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme_Glasses`)"
        errorLine1="    android:background=&quot;@color/background_primary&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_debug.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme_Glasses`)"
        errorLine1="    android:background=&quot;@color/background_primary&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_glasses_settings.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme_Glasses`)"
        errorLine1="    android:background=&quot;@color/background_primary&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_key_settings.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.Glasses`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_proxy_settings.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.Glasses`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_ai.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.Glasses`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_album.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.Glasses`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.Glasses`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_media_content.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.Glasses`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_media_display.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.Glasses`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_profile.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.Glasses`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_header.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/comp_background_gray` with a theme that also paints a background (inferred theme is `@style/Theme.Glasses`)"
        errorLine1="    android:background=&quot;@color/comp_background_gray&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_toolbar.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.array.proxy_types` appears to be unused"
        errorLine1="    &lt;string-array name=&quot;proxy_types&quot;>"
        errorLine2="                  ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/arrays.xml"
            line="4"
            column="19"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.bg_profile_setting_item` appears to be unused"
        errorLine1="&lt;ripple xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/bg_profile_setting_item.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.brand_button_bg` appears to be unused"
        errorLine1="&lt;ripple xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/brand_button_bg.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_fourth` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_fourth&quot;>#33000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="18"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_emphasize` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_emphasize&quot;>#ff0a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_on_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_on_secondary&quot;>#99ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="21"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_on_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_on_tertiary&quot;>#66ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="22"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.font_on_fourth` appears to be unused"
        errorLine1="    &lt;color name=&quot;font_on_fourth&quot;>#33ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_tertiary&quot;>#66000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_fourth` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_fourth&quot;>#33000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="29"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_sub_emphasize` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_sub_emphasize&quot;>#660a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="31"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_on_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_on_primary&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="32"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_on_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_on_secondary&quot;>#99ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="33"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_on_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_on_tertiary&quot;>#66ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="34"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.icon_on_fourth` appears to be unused"
        errorLine1="    &lt;color name=&quot;icon_on_fourth&quot;>#33ffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="35"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_secondary&quot;>#fff1f3f5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="39"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_fourth` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_fourth&quot;>#ffd1d1d6&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="41"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_emphasize` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_emphasize&quot;>#ff0a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="42"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_foreground_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_foreground_primary&quot;>#ff000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="45"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_primary&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="46"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_primary_trans` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_primary_trans&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="47"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_primary_contrary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_primary_contrary&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="48"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_secondary&quot;>#19000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="50"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_tertiary&quot;>#0c000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="51"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_emphasize` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_emphasize&quot;>#ff0a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="52"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_neutral` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_neutral&quot;>#ff000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="53"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_emphasize_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_emphasize_secondary&quot;>#330a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="54"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_common_contrary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_common_contrary&quot;>#ffffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="57"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_background_focus` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_background_focus&quot;>#fff1f3f5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="58"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_focused_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_focused_primary&quot;>#e5000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="59"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_focused_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_focused_secondary&quot;>#99000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="60"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.comp_focused_tertiary` appears to be unused"
        errorLine1="    &lt;color name=&quot;comp_focused_tertiary&quot;>#66000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="61"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.interactive_focus` appears to be unused"
        errorLine1="    &lt;color name=&quot;interactive_focus&quot;>#ff0a59f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="66"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.interactive_select` appears to be unused"
        errorLine1="    &lt;color name=&quot;interactive_select&quot;>#33000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="68"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.interactive_click` appears to be unused"
        errorLine1="    &lt;color name=&quot;interactive_click&quot;>#19000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="69"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.anim.fade_in` appears to be unused"
        errorLine1="&lt;alpha xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/anim/fade_in.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.anim.fade_out` appears to be unused"
        errorLine1="&lt;alpha xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/anim/fade_out.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.ai_voice_feature_developing` appears to be unused"
        errorLine1="    &lt;string name=&quot;ai_voice_feature_developing&quot;>语音功能开发中...&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.ai_sample_messages_loaded` appears to be unused"
        errorLine1="    &lt;string name=&quot;ai_sample_messages_loaded&quot;>示例消息已加载&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.clear_album` appears to be unused"
        errorLine1="    &lt;string name=&quot;clear_album&quot;>@string/album_clear_album&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.voice_feature_developing` appears to be unused"
        errorLine1="    &lt;string name=&quot;voice_feature_developing&quot;>@string/ai_voice_feature_developing&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sample_messages_loaded` appears to be unused"
        errorLine1="    &lt;string name=&quot;sample_messages_loaded&quot;>@string/ai_sample_messages_loaded&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="42"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.dialog_background` appears to be unused"
        errorLine1="    &lt;string name=&quot;dialog_background&quot;>#F2F2F2&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fullscreen_button` appears to be unused"
        errorLine1="    &lt;string name=&quot;fullscreen_button&quot;>全屏&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="51"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.retry` appears to be unused"
        errorLine1="    &lt;string name=&quot;retry&quot;>重试&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.bluetooth_already_connected` appears to be unused"
        errorLine1="    &lt;string name=&quot;bluetooth_already_connected&quot;>蓝牙已连接到设备&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="63"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.bluetooth_connecting_message` appears to be unused"
        errorLine1="    &lt;string name=&quot;bluetooth_connecting_message&quot;>蓝牙正在连接中，请稍候&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="64"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.recording` appears to be unused"
        errorLine1="    &lt;string name=&quot;recording&quot;>正在录音...&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="68"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.recognizing` appears to be unused"
        errorLine1="    &lt;string name=&quot;recognizing&quot;>正在识别...&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="69"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.CustomBottomNavigationView` appears to be unused"
        errorLine1="    &lt;style name=&quot;CustomBottomNavigationView&quot; parent=&quot;Widget.MaterialComponents.BottomNavigationView&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.tab_text_selector` appears to be unused"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/color/tab_text_selector.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.view_settings_card` appears to be unused"
        errorLine1="&lt;FrameLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/view_settings_card.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UselessParent"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view"
        errorLine1="        &lt;LinearLayout"
        errorLine2="         ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/custom_player_control_view.xml"
            line="25"
            column="10"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;ai_voice_feature_developing&quot;>语音功能开发中...&lt;/string>"
        errorLine2="                                               ~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="16"
            column="48"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;ai_loading_sample_messages&quot;>正在加载示例消息...&lt;/string>"
        errorLine2="                                              ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="23"
            column="47"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;recording&quot;>正在录音...&lt;/string>"
        errorLine2="                             ~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="68"
            column="30"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;recognizing&quot;>正在识别...&lt;/string>"
        errorLine2="                               ~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="69"
            column="32"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;searching_devices&quot;>正在搜索设备...&lt;/string>"
        errorLine2="                                     ~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="89"
            column="38"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/ggec.png` in densityless folder">
        <location
            file="src/main/res/drawable/ggec.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/glasses.png` in densityless folder">
        <location
            file="src/main/res/drawable/glasses.png"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `11sp`: `10sp`"
        errorLine1="            android:textSize=&quot;10sp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_bluetooth_device.xml"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `11sp`: `10sp`"
        errorLine1="            android:textSize=&quot;10sp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_media.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.github.chrisbanes:PhotoView:2.3.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="74"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.google.android.exoplayer:exoplayer-core:2.19.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="77"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.google.android.exoplayer:exoplayer-ui:2.19.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="78"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.google.android.exoplayer:exoplayer:2.19.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="79"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;io.reactivex.rxjava2:rxjava:2.2.21&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="88"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;io.reactivex.rxjava2:rxandroid:2.1.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="89"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.squareup.retrofit2:retrofit:2.9.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="92"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.squareup.retrofit2:converter-gson:2.9.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="93"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.squareup.okhttp3:logging-interceptor:4.9.3&apos; // 可选：日志拦截器"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="94"
            column="20"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view ``ImageView`` has `setOnTouchListener` called on it but does not override `performClick`"
        errorLine1="            btnVoice.setOnTouchListener((v, event) -> {"
        errorLine2="            ^">
        <location
            file="src/main/java/com/example/glasses/ai/ui/AIVoiceUIHandler.java"
            line="136"
            column="13"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="`onTouch` lambda should call `View#performClick` when a click is detected"
        errorLine1="            btnVoice.setOnTouchListener((v, event) -> {"
        errorLine2="                                        ^">
        <location
            file="src/main/java/com/example/glasses/ai/ui/AIVoiceUIHandler.java"
            line="136"
            column="41"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_bluetooth_device.xml"
            line="12"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_volume_settings.xml"
            line="22"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_profile_setting_item.xml"
            line="12"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_profile_setting_item.xml"
            line="38"
            column="6"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;返回&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;返回&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_ai_model_settings.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;AI大模型设置&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;AI大模型设置&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_ai_model_settings.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;返回&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;返回&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_bluetooth_connection.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;返回&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;返回&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_debug.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;返回&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;返回&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_glasses_settings.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;返回&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;返回&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_key_settings.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;返回&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;返回&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_proxy_settings.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;代理设置&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;代理设置&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_proxy_settings.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;VPN图标&quot;, should use `@string` resource"
        errorLine1="        android:contentDescription=&quot;VPN图标&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_proxy_settings.xml"
            line="61"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;此处代理只适用于优化上网体验，并不具备翻墙功能。&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;此处代理只适用于优化上网体验，并不具备翻墙功能。&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_proxy_settings.xml"
            line="71"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;连接眼镜时开启代理模式&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;连接眼镜时开启代理模式&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_proxy_settings.xml"
            line="102"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;下载图片&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;下载图片&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/album_popup_menu.xml"
            line="19"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;下载视频&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;下载视频&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/album_popup_menu.xml"
            line="33"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;加载测试数据&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;加载测试数据&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/album_popup_menu.xml"
            line="49"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;首页&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;首页&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/bottom_navigation_menu.xml"
            line="6"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;相册&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;相册&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/bottom_navigation_menu.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;AI&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;AI&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/bottom_navigation_menu.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;我的&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;我的&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/bottom_navigation_menu.xml"
            line="18"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;相册&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;相册&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_album.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;更多选项&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;更多选项&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_album.xml"
            line="40"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;GGEC_AIGlass&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;GGEC_AIGlass&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="39"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;系统蓝牙未开启，点我开启&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;系统蓝牙未开启，点我开启&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="52"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;下拉菜单&quot;, should use `@string` resource"
        errorLine1="                android:contentDescription=&quot;下拉菜单&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="67"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;设置&quot;, should use `@string` resource"
        errorLine1="                android:contentDescription=&quot;设置&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="80"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;眼镜图片&quot;, should use `@string` resource"
        errorLine1="        android:contentDescription=&quot;眼镜图片&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="99"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;配对眼镜&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;配对眼镜&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="121"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;眼镜功能设置&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;眼镜功能设置&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="156"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;AI大模型设置&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;AI大模型设置&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="192"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;待开发&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;待开发&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="201"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;按键设置&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;按键设置&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="233"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;待开发&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;待开发&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="242"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Debug&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Debug&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="275"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;待开发&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;待开发&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="284"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;待开发&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;待开发&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="326"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;暂无内容&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;暂无内容&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_media_content.xml"
            line="25"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;我的&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;我的&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_profile.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;用户头像&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;用户头像&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_profile.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;GGEC_001&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;GGEC_001&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_profile.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;编辑个人资料&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;编辑个人资料&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_profile.xml"
            line="86"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;使用自定义HarmonySwitch组件&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;使用自定义HarmonySwitch组件&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/harmony_switch_demo.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;使用HarmonySwitchStyle样式&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;使用HarmonySwitchStyle样式&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/harmony_switch_demo.xml"
            line="44"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;默认开关样式(对比)&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;默认开关样式(对比)&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/harmony_switch_demo.xml"
            line="69"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;媒体缩略图&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;媒体缩略图&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_media.xml"
            line="26"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;选中状态背景&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;选中状态背景&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_media.xml"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;播放视频&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;播放视频&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_media.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;00:00&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;00:00&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_media.xml"
            line="63"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;选择框&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;选择框&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_media.xml"
            line="75"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;取消选择&quot;, should use `@string` resource"
        errorLine1="        android:contentDescription=&quot;取消选择&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_header.xml"
            line="19"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;已选择 0 项&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;已选择 0 项&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_header.xml"
            line="34"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;全选&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;全选&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_header.xml"
            line="60"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;下载&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;下载&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_toolbar.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;下载&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;下载&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_toolbar.xml"
            line="40"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;分享&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;分享&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_toolbar.xml"
            line="66"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;分享&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;分享&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_toolbar.xml"
            line="75"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;删除&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;删除&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_toolbar.xml"
            line="101"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;删除&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;删除&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_toolbar.xml"
            line="110"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;更多&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;更多&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_toolbar.xml"
            line="136"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;更多&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;更多&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_album_multi_select_toolbar.xml"
            line="145"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;取消&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;取消&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_custom_dialog.xml"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;确认&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;确认&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/layout_custom_dialog.xml"
            line="72"
            column="13"/>
    </issue>

</issues>
