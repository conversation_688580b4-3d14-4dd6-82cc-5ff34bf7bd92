<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    
    <!-- 录音权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    
    <!-- 蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

    <!-- 位置权限(Android 6.0+蓝牙扫描需要) -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- 声明蓝牙功能 -->
    <uses-feature android:name="android.hardware.bluetooth" android:required="true" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:name=".GlassesApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Glasses"
        android:requestLegacyExternalStorage="true"
        tools:targetApi="31">
        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.Glasses.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <activity
            android:name=".MainActivity"
            android:exported="true">
        </activity>
        
        <!-- 代理设置活动 -->
        <activity
            android:name=".profile.ProxySettingsActivity"
            android:exported="false" />
            
        <!-- 媒体展示活动 -->
        <activity
            android:name=".album.MediaDisplayActivity"
            android:theme="@style/Theme.Glasses.Fullscreen"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:exported="false" />

        <activity
            android:name=".ai.AiModelSettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Glasses" />
            
        <activity
            android:name=".device.KeySettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Glasses" />
            
        <activity
            android:name=".device.DebugActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Glasses" />
            
        <activity
            android:name=".device.GlassesSettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Glasses" />
            
        <activity
            android:name=".bluetooth.BluetoothConnectionActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Glasses" />
            
        <!-- Realtek蓝牙演示界面 -->
        <activity
            android:name="com.realtek.sdk.demo.RealtekBluetoothDemoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Glasses" />
    </application>

</manifest>