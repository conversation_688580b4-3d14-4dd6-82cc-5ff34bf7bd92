package com.ggec.glasses.ai.data.db.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Transaction;
import androidx.room.Update;

import com.ggec.glasses.ai.data.db.entity.ChatMessage;
import com.ggec.glasses.ai.data.db.entity.MessageStatus;

import java.util.List;

/**
 * 聊天消息数据访问对象接口
 */
@Dao
public interface ChatMessageDao extends BaseDao<ChatMessage> {
    
    /**
     * 插入一条消息
     * @param message 要插入的消息
     * @return 新插入记录的ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertMessage(ChatMessage message);
    
    /**
     * 批量插入消息
     * @param messages 要插入的消息列表
     * @return 新插入记录的ID列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insertMessages(List<ChatMessage> messages);
    
    /**
     * 更新消息
     * @param message 要更新的消息
     */
    @Update
    void updateMessage(ChatMessage message);
    
    /**
     * 删除消息
     * @param message 要删除的消息
     */
    @Delete
    void deleteMessage(ChatMessage message);
    
    /**
     * 根据ID获取消息
     * @param messageId 消息ID
     * @return 消息对象
     */
    @Query("SELECT * FROM chat_message WHERE id = :messageId")
    ChatMessage getMessageById(long messageId);
    
    /**
     * 根据ID获取消息（LiveData版本）
     * @param messageId 消息ID
     * @return 包含消息对象的LiveData
     */
    @Query("SELECT * FROM chat_message WHERE id = :messageId")
    LiveData<ChatMessage> getMessageByIdLive(long messageId);
    
    /**
     * 获取某个会话的所有消息
     * @param conversationId 会话ID
     * @return 消息列表
     */
    @Query("SELECT * FROM chat_message WHERE conversation_id = :conversationId ORDER BY timestamp ASC")
    List<ChatMessage> getMessagesByConversation(long conversationId);
    
    /**
     * 获取某个会话的所有消息（LiveData版本）
     * @param conversationId 会话ID
     * @return 包含消息列表的LiveData
     */
    @Query("SELECT * FROM chat_message WHERE conversation_id = :conversationId ORDER BY timestamp ASC")
    LiveData<List<ChatMessage>> getMessagesByConversationLive(long conversationId);
    
    /**
     * 获取所有会话的最新消息（每个会话一条）
     * @return 最新消息列表
     */
    @Query("SELECT m.* FROM chat_message m INNER JOIN " +
           "(SELECT conversation_id, MAX(timestamp) as max_timestamp FROM chat_message " +
           "GROUP BY conversation_id) t " +
           "ON m.conversation_id = t.conversation_id AND m.timestamp = t.max_timestamp " +
           "ORDER BY m.timestamp DESC")
    List<ChatMessage> getLatestMessagesByConversation();
    
    /**
     * 获取所有会话的最新消息（每个会话一条，LiveData版本）
     * @return 包含最新消息列表的LiveData
     */
    @Query("SELECT m.* FROM chat_message m INNER JOIN " +
           "(SELECT conversation_id, MAX(timestamp) as max_timestamp FROM chat_message " +
           "GROUP BY conversation_id) t " +
           "ON m.conversation_id = t.conversation_id AND m.timestamp = t.max_timestamp " +
           "ORDER BY m.timestamp DESC")
    LiveData<List<ChatMessage>> getLatestMessagesByConversationLive();
    
    /**
     * 根据状态获取消息
     * @param status 消息状态
     * @return 指定状态的消息列表
     */
    @Query("SELECT * FROM chat_message WHERE status = :status ORDER BY timestamp DESC")
    List<ChatMessage> getMessagesByStatus(int status);
    
    /**
     * 根据状态获取消息（LiveData版本）
     * @param status 消息状态
     * @return 包含指定状态的消息列表的LiveData
     */
    @Query("SELECT * FROM chat_message WHERE status = :status ORDER BY timestamp DESC")
    LiveData<List<ChatMessage>> getMessagesByStatusLive(int status);
    
    /**
     * 获取最新的N条消息
     * @param limit 消息数量限制
     * @return 最新的N条消息
     */
    @Query("SELECT * FROM chat_message ORDER BY timestamp DESC LIMIT :limit")
    List<ChatMessage> getLatestMessages(int limit);
    
    /**
     * 获取最新的N条消息（LiveData版本）
     * @param limit 消息数量限制
     * @return 包含最新的N条消息的LiveData
     */
    @Query("SELECT * FROM chat_message ORDER BY timestamp DESC LIMIT :limit")
    LiveData<List<ChatMessage>> getLatestMessagesLive(int limit);
    
    /**
     * 获取某个会话中的最新N条消息
     * @param conversationId 会话ID
     * @param limit 消息数量限制
     * @return 指定会话中的最新N条消息
     */
    @Query("SELECT * FROM chat_message WHERE conversation_id = :conversationId " +
           "ORDER BY timestamp DESC LIMIT :limit")
    List<ChatMessage> getLatestMessagesByConversation(long conversationId, int limit);
    
    /**
     * 获取某个会话中的最新N条消息（LiveData版本）
     * @param conversationId 会话ID
     * @param limit 消息数量限制
     * @return 包含指定会话中的最新N条消息的LiveData
     */
    @Query("SELECT * FROM chat_message WHERE conversation_id = :conversationId " +
           "ORDER BY timestamp DESC LIMIT :limit")
    LiveData<List<ChatMessage>> getLatestMessagesByConversationLive(long conversationId, int limit);
    
    /**
     * 更新消息状态
     * @param messageId 消息ID
     * @param status 新状态
     */
    @Query("UPDATE chat_message SET status = :status WHERE id = :messageId")
    void updateMessageStatus(long messageId, int status);
    
    /**
     * 批量更新消息状态
     * @param messageIds 消息ID列表
     * @param status 新状态
     */
    @Transaction
    default void updateMessagesStatus(List<Long> messageIds, MessageStatus status) {
        int statusValue = status.getValue();
        for (Long id : messageIds) {
            updateMessageStatus(id, statusValue);
        }
    }
    
    /**
     * 删除指定会话的所有消息
     * @param conversationId 会话ID
     */
    @Query("DELETE FROM chat_message WHERE conversation_id = :conversationId")
    void deleteMessagesByConversation(long conversationId);
    
    /**
     * 删除所有消息
     */
    @Query("DELETE FROM chat_message")
    void deleteAllMessages();
    
    /**
     * 获取消息总数
     * @return 消息总数
     */
    @Query("SELECT COUNT(*) FROM chat_message")
    int getMessageCount();
    
    /**
     * 获取指定会话的消息数量
     * @param conversationId 会话ID
     * @return 指定会话的消息数量
     */
    @Query("SELECT COUNT(*) FROM chat_message WHERE conversation_id = :conversationId")
    int getMessageCountByConversation(long conversationId);
    
    /**
     * 获取指定状态的消息数量
     * @param status 消息状态
     * @return 指定状态的消息数量
     */
    @Query("SELECT COUNT(*) FROM chat_message WHERE status = :status")
    int getMessageCountByStatus(int status);
    
    /**
     * 按内容搜索消息
     * @param query 搜索关键词
     * @return 匹配的消息列表
     */
    @Query("SELECT * FROM chat_message WHERE content LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    List<ChatMessage> searchMessages(String query);
    
    /**
     * 按内容搜索消息（LiveData版本）
     * @param query 搜索关键词
     * @return 包含匹配消息的LiveData
     */
    @Query("SELECT * FROM chat_message WHERE content LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    LiveData<List<ChatMessage>> searchMessagesLive(String query);
    
    /**
     * 在指定会话中按内容搜索消息
     * @param conversationId 会话ID
     * @param query 搜索关键词
     * @return 匹配的消息列表
     */
    @Query("SELECT * FROM chat_message WHERE conversation_id = :conversationId AND content LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    List<ChatMessage> searchMessagesInConversation(long conversationId, String query);
} 