package com.ggec.glasses;

import android.content.Context;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.GlideBuilder;
import com.bumptech.glide.Registry;
import com.bumptech.glide.annotation.GlideModule;
import com.bumptech.glide.load.engine.cache.DiskLruCacheFactory;
import com.bumptech.glide.load.engine.cache.LruResourceCache;
import com.bumptech.glide.module.AppGlideModule;

import java.io.File;

/**
 * 自定义Glide模块，配置Glide使用外部缓存目录
 */
@GlideModule
public class GlassesGlideModule extends AppGlideModule {

    // 内存缓存大小（默认值的1.5倍，约24MB）
    private static final int MEMORY_CACHE_SIZE = 1024 * 1024 * 24; // 24 MB
    
    // 磁盘缓存大小
    private static final int DISK_CACHE_SIZE = 1024 * 1024 * 250; // 250 MB
    
    // 缓存目录名称
    private static final String GLIDE_CACHE_DIR = "glide_cache";

    @Override
    public void applyOptions(@NonNull Context context, @NonNull GlideBuilder builder) {
        // 配置内存缓存
        builder.setMemoryCache(new LruResourceCache(MEMORY_CACHE_SIZE));
        
        // 配置磁盘缓存使用外部缓存目录
        File externalCacheDir = context.getExternalCacheDir();
        if (externalCacheDir != null) {
            File cacheDir = new File(externalCacheDir, GLIDE_CACHE_DIR);
            if (!cacheDir.exists()) {
                cacheDir.mkdirs();
            }
            
            builder.setDiskCache(new DiskLruCacheFactory(cacheDir.getAbsolutePath(), DISK_CACHE_SIZE));
        }
    }

    @Override
    public void registerComponents(@NonNull Context context, @NonNull Glide glide, @NonNull Registry registry) {
        // 这里可以注册自定义的组件，如果需要的话
    }
    
    @Override
    public boolean isManifestParsingEnabled() {
        // 禁用清单解析以提高性能
        return false;
    }
} 