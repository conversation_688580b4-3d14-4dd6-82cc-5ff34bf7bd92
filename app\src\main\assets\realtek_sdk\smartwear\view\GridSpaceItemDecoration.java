/*
 * Copyright (c) 2024 Realsil.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Authors: <AUTHORS>
 */

package com.realsil.sample.audioconnect.smartwear.view;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.util.TypedValueCompat;
import androidx.recyclerview.widget.RecyclerView;

public class GridSpaceItemDecoration extends RecyclerView.ItemDecoration {

    private static final String TAG = "Grid";

    private final int mSpanCount;//横条目数量
    private final int mRowSpacing;//行间距
    private final int mColumnSpacing;// 列间距

    /**
     * @param spanCount     列数
     * @param rowSpacing    行间距
     * @param columnSpacing 列间距
     */
    public GridSpaceItemDecoration(Context context, int spanCount, int rowSpacing, int columnSpacing) {
        this.mSpanCount = spanCount;
        this.mRowSpacing = dp2Px(context, rowSpacing);
        this.mColumnSpacing = dp2Px(context, columnSpacing);
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view); // 获取view 在adapter中的位置。
        int column = position % mSpanCount; // view 所在的列

        outRect.left = column * mColumnSpacing / mSpanCount; // column * (列间距 * (1f / 列数))
        outRect.right = mColumnSpacing - (column + 1) * mColumnSpacing / mSpanCount; // 列间距 - (column + 1) * (列间距 * (1f /列数))

        // Log.e(TAG, "position:" + position
        //         + "    columnIndex: " + column
        //         + "    left,right ->" + outRect.left + "," + outRect.right);

        if (position >= mSpanCount) {
            outRect.top = mRowSpacing; // item top
        }
    }

    private static int dp2Px(Context context, int dpValue) {
        return (int) TypedValueCompat.dpToPx(dpValue, context.getResources().getDisplayMetrics());
    }

}
