<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 开启状态 -->
    <item android:state_checked="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/brand"/> <!-- 使用项目品牌色 -->
            <corners android:radius="15dp"/>
            <size android:height="30dp" />
        </shape>
    </item>
    <!-- 关闭状态 -->
    <item android:state_checked="false">
        <shape android:shape="rectangle">
            <solid android:color="#E5E5E5"/> <!-- 鸿蒙关闭背景色 -->
            <corners android:radius="15dp"/>
            <size android:height="30dp" />
        </shape>
    </item>
</selector> 