<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="180dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="10dp"
    android:background="@drawable/bg_popup_menu"
    android:elevation="4dp">

    <!-- 加载示例消息选项 -->
    <TextView
        android:id="@+id/btn_load_example_messages"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:text="@string/load_example_messages"
        android:textColor="@color/font_primary"
        android:textSize="17sp"
        android:textStyle="normal"
        android:background="@drawable/bg_menu_item_selector" />

    <!-- User测试消息选项 -->
    <TextView
        android:id="@+id/btn_user_test_message"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:text="@string/user_test_message"
        android:textColor="@color/font_primary"
        android:textSize="17sp"
        android:textStyle="normal"
        android:background="@drawable/bg_menu_item_selector" />

    <!-- AI测试消息选项 -->
    <TextView
        android:id="@+id/btn_ai_test_message"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:text="@string/ai_test_message"
        android:textColor="@color/font_primary"
        android:textSize="17sp"
        android:textStyle="normal"
        android:background="@drawable/bg_menu_item_selector" />

    <!-- 清空消息选项 -->
    <TextView
        android:id="@+id/btn_clear_messages"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:text="@string/ai_clear_messages"
        android:textColor="@color/warning"
        android:textSize="17sp"
        android:textStyle="normal"
        android:background="@drawable/bg_menu_item_selector" />

</LinearLayout> 