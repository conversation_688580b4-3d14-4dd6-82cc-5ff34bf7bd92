/*
 * Copyright (c) 2025 Realsil.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Authors: <AUTHORS>
 */

package com.realsil.sample.audioconnect.smartwear;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.widget.Toolbar;

import com.realsil.sample.audioconnect.smartwear.gallery.GlassGalleryActivity;
import com.realsil.sdk.audioconnect.smartwear.SmartWearModelCallback;
import com.realsil.sdk.audioconnect.smartwear.SmartWearModelClient;
import com.realsil.sdk.audioconnect.smartwear.SmartWearModelProxy;
import com.realsil.sdk.audioconnect.support.AudioConnectActivity;
import com.realsil.sdk.support.base.BaseActivity;

public class SmartGlassActivity extends BaseActivity {

    private String mBtAddress = "";

    private Button btn_ai_voice_chat;
    private Button btn_gallery;

    private SmartWearModelClient mSmartWearModelClient;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_smart_wear);
        Toolbar view_toolbar = findViewById(R.id.view_toolbar);
        view_toolbar.setTitle(R.string.title_smart_glass);
        view_toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material);
        view_toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        mBtAddress = getIntent().getStringExtra(AudioConnectActivity.EXTRA_KEY_BT_ADDR);

        btn_ai_voice_chat = findViewById(R.id.btn_ai_voice_chat);
        btn_ai_voice_chat.setOnClickListener(v -> {
            Intent intent = new Intent(getApplicationContext(), AIVoiceChatActivity.class);
            intent.putExtra(AudioConnectActivity.EXTRA_KEY_BT_ADDR, mBtAddress);
            startActivity(intent);
        });

        btn_gallery = findViewById(R.id.btn_gallery);
        btn_gallery.setOnClickListener(v -> {
            Intent intent = new Intent(getApplicationContext(), GlassGalleryActivity.class);
            intent.putExtra(AudioConnectActivity.EXTRA_KEY_BT_ADDR, mBtAddress);
            startActivity(intent);
        });

        mSmartWearModelClient = SmartWearModelProxy.getInstance().getModelClient(mBtAddress);
        mSmartWearModelClient.registerCallback(mSmartWearModelCallback);

        // Try to init device.
        mSmartWearModelClient.initSmartWearDevice();
    }

    private final SmartWearModelCallback mSmartWearModelCallback = new SmartWearModelCallback() {
        @Override
        public void onSmartWearDeviceInitSuccess() {
            super.onSmartWearDeviceInitSuccess();
            runOnUiThread(() -> {
                btn_ai_voice_chat.setEnabled(true);
                btn_gallery.setEnabled(true);
            });
        }
    };

    @Override
    public void onDestroy() {
        super.onDestroy();
        mSmartWearModelClient.unregisterCallback(mSmartWearModelCallback);
    }
}