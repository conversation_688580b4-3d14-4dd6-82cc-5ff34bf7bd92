/*
 * Copyright (c) 2025 Realsil.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Authors: <AUTHORS>
 */

package com.realsil.sample.audioconnect.smartwear;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.MenuItem;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.realsil.sample.audioconnect.smartwear.adapter.MsgListAdapter;
import com.realsil.sample.audioconnect.smartwear.entity.MessageInfo;
import com.realsil.sample.audioconnect.smartwear.view.AppEmptyRecyclerView;
import com.realsil.sdk.audioconnect.ai.realtime.CommandName;
import com.realsil.sdk.audioconnect.ai.realtime.RealtimeApiConfig;
import com.realsil.sdk.audioconnect.ai.realtime.RealtimeApiConstants;
import com.realsil.sdk.audioconnect.ai.realtime.device.callback.SmartGlassDeviceCallback;
import com.realsil.sdk.audioconnect.smartwear.SmartWearConfig;
import com.realsil.sdk.audioconnect.smartwear.SmartWearConstants;
import com.realsil.sdk.audioconnect.smartwear.SmartWearModelCallback;
import com.realsil.sdk.audioconnect.smartwear.SmartWearModelClient;
import com.realsil.sdk.audioconnect.smartwear.SmartWearModelProxy;
import com.realsil.sdk.audioconnect.support.AudioConnectActivity;
import com.realsil.sdk.support.base.BaseActivity;

import java.io.File;
import java.util.HashMap;

/**
 * AI Voice Chat Window.
 */
public class AIVoiceChatActivity extends BaseActivity {

    public static final String TAG = "VoiceChat";

    private SmartWearModelClient mSmartWearModelClient;

    private MenuItem mDisconnectMenu;

    private MsgListAdapter mMsgListAdapter;

    private Handler mHandler;

    /**
     * Cache received message list.
     */
    private final HashMap<String, MessageInfo> mChatMessageMap = new HashMap<>();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_ai_voice_chat);

        // Init Toolbar
        Toolbar view_toolbar = findViewById(R.id.view_toolbar);
        view_toolbar.setTitle(R.string.title_ai_voice_chat);
        view_toolbar.setNavigationIcon(androidx.appcompat.R.drawable.abc_ic_ab_back_material);
        view_toolbar.setNavigationOnClickListener(v -> finish());
        mDisconnectMenu = view_toolbar.getMenu().findItem(R.id.menu_disconnect);
        mDisconnectMenu.setOnMenuItemClickListener(item -> {

            int connectState = mSmartWearModelClient.getConnectStateWithAI();
            if (connectState == RealtimeApiConstants.CONNECT_STATE_DISCONNECTED) {
                mSmartWearModelClient.connectToAI();
            } else if (connectState == RealtimeApiConstants.CONNECT_STATE_CONNECTED) {
                mSmartWearModelClient.disconnectFromAI();
            }
            return false;
        });

        // Create a Handler to update the UI in the child thread.
        mHandler = new Handler(Looper.getMainLooper());

        String btAddress = getIntent().getStringExtra(AudioConnectActivity.EXTRA_KEY_BT_ADDR);
        mSmartWearModelClient = SmartWearModelProxy.getInstance().getModelClient(btAddress);

        // Listen for messages or status returned from the AI Model.
        mSmartWearModelClient.addAIStateCallback(mAiStateCallback);
        mSmartWearModelClient.registerCallback(mSmartWearModelCallback);

        initRecyclerView();


        // Configure custom model parameters (such as access address, access key, etc.)
        RealtimeApiConfig config = new RealtimeApiConfig.Builder()
//                .setModelApiAddress("Your Model Access Address")
//                .setModelApiKey("Your Model API Key")
                .setSmartDeviceType(RealtimeApiConstants.DeviceType.TYPE_SMART_GLASS)
                .setServerVadWakeThreshold(0.8f)
                .build();

        // Automatically connect to AI.
        // Note: Please make sure the network is turned on and the network permissions have been added.
        mSmartWearModelClient.setAIModelParam(config);
        mSmartWearModelClient.connectToAI();

        // Configure the output audio parameters
        SmartWearConfig smartWearConfig = new SmartWearConfig.Builder()
                .setOutputAudioSampleRate(SmartWearConstants.AudioSampleRate.SAMPLE_RATE_16000)
                .setOutputAudioSampleChannel(SmartWearConstants.AudioChannel.CHANNEL_MONO)
                .build();
        mSmartWearModelClient.setSmartWearDeviceParam(smartWearConfig);

        // (Optional) Support dump user voice
        mSmartWearModelClient.startDumpAudio();
    }

    private void initRecyclerView() {
        AppEmptyRecyclerView rv_msg_list = findViewById(R.id.rv_msg_list);
        mMsgListAdapter = new MsgListAdapter(getApplicationContext());
        rv_msg_list.setAdapter(mMsgListAdapter);

        LinearLayoutManager layoutManager = new LinearLayoutManager(getApplicationContext());
        layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        rv_msg_list.setLayoutManager(layoutManager);

        rv_msg_list.setEmptyView(findViewById(R.id.iv_empty_view));
        rv_msg_list.addOnLayoutChangeListener((v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom) ->
                layoutManager.smoothScrollToPosition(rv_msg_list, new RecyclerView.State(), mMsgListAdapter.getItemCount() - 1)
        );

        // Disable the RecyclerView's default blinking animation when executing notifyItemChanged() method.
        SimpleItemAnimator animator = (SimpleItemAnimator) rv_msg_list.getItemAnimator();
        if (animator != null) {
            animator.setSupportsChangeAnimations(false);
        }
    }

    private final SmartGlassDeviceCallback mAiStateCallback = new SmartGlassDeviceCallback() {

        /**
         * Called when AI connects successfully.
         */
        @Override
        public void onConnectSuccess() {
            super.onConnectSuccess();
            mHandler.post(() -> {
                mDisconnectMenu.setTitle("Disconnect");
                mMsgListAdapter.addSystemMsg("Connected with AI");
            });
        }

        @Override
        public void onConnectToAI() {
            mHandler.post(() -> {
                mMsgListAdapter.addSystemMsg("Connect to AI...");
            });
        }

        /**
         * Called when the AI connection fails.
         */
        @Override
        public void onConnectFail() {
            super.onConnectFail();
            mHandler.post(() -> {
                mMsgListAdapter.addSystemMsg("Connect Fail");
            });
        }

        /**
         * Called when the AI connection is lost. Usually the AI disconnects abnormally after connected.
         */
        @Override
        public void onConnectLost() {
            super.onConnectLost();
            mHandler.post(() -> {
                mDisconnectMenu.setTitle("Connect");
                mMsgListAdapter.addSystemMsg("Disconnected with AI");
            });
        }

        /**
         * Called when a text message sent by the user is recognized.
         * The text message here is the result of transcribing the voice message sent by the user.
         *
         * @param itemId  The message ID of the response.
         * @param sendMsg Transcribed text of a voice message.
         */
        @Override
        public void onSendTextMessage(@NonNull String itemId, String sendMsg) {
            super.onSendTextMessage(itemId, sendMsg);
            if (TextUtils.isEmpty(sendMsg)) return;
            mHandler.post(() -> {
                mMsgListAdapter.addUserMsg(sendMsg);
            });
        }

        /**
         * Called when receiving the response text returned by the AI model.
         *
         * @param msgId       The message ID of the response.
         * @param receivedMsg The full response text sent by the AI.
         */
        @Override
        public void onReceiveTextMessage(@NonNull String msgId, String receivedMsg) {
            super.onReceiveTextMessage(msgId, receivedMsg);
        }

        /**
         * Called when receiving a response text fragment from AI.
         *
         * @param msgId       The message ID of the response.
         * @param textSnippet The response text snippet returned by AI.
         */
        @Override
        public void onReceiveTextSnippet(@NonNull String msgId, String textSnippet) {
            super.onReceiveTextSnippet(msgId, textSnippet);

            MessageInfo receivedTextMsg;
            if (mChatMessageMap.get(msgId) == null) {
                receivedTextMsg = new MessageInfo();
                receivedTextMsg.setMsgType(MessageInfo.MSG_TYPE_RECEIVED_TEXT_MESSAGE);
                mChatMessageMap.put(msgId, receivedTextMsg);
                mHandler.post(() -> {
                    mMsgListAdapter.addReceivedTextMsg(receivedTextMsg);
                });
            } else {
                receivedTextMsg = mChatMessageMap.get(msgId);
            }

            if (receivedTextMsg != null) {
                receivedTextMsg.setMsgContent(receivedTextMsg.getMsgContent() + textSnippet);
                receivedTextMsg.appendTextSnippet(textSnippet);
                mHandler.post(() -> {
                    mMsgListAdapter.appendTextMsg(receivedTextMsg);
                });
            }
        }

        /**
         * Called when the AI starts to perform the action specified by the user.
         *
         * @param msgId      The message ID bound to this action.
         * @param actionName Type of action performed.
         *                   All supported action types are defined in:{@link com.realsil.sdk.audioconnect.ai.realtime.CommandName}
         */
        @Override
        public void onStartExecuteAction(@NonNull String msgId, @NonNull String actionName) {
            super.onStartExecuteAction(msgId, actionName);
            switch (actionName) {
                case CommandName.NAME_TAKE_PHOTO:
                    mHandler.post(() -> {
                        mMsgListAdapter.addReceivedActionMsg("Capture current image...");
                    });
                    break;
                case CommandName.NAME_QUERY_BATTERY:
                    mHandler.post(() -> {
                        mMsgListAdapter.addReceivedActionMsg("Query the system power...");
                    });
                case CommandName.NAME_ANALYZE_IMG:
                    mHandler.post(() -> {
                        mMsgListAdapter.addReceivedActionMsg("Analyzing the image...");
                    });
                    break;
                default:
                    break;
            }
        }

        /**
         * Called when an image captured by a smart device is received.
         *
         * @param msgId   The message ID associated with performing the capture image action.
         * @param imgFile Captured images. May be <code>null</code> if image capture failed.
         */
        @Override
        public void onReceiveCapturedImg(@NonNull String msgId, @NonNull File imgFile) {
            super.onReceiveCapturedImg(msgId, imgFile);
            MessageInfo receivedImgMsg = new MessageInfo();
            receivedImgMsg.setMsgType(MessageInfo.MSG_TYPE_RECEIVED_IMG_MESSAGE);
            receivedImgMsg.setMsgImgUrl(imgFile.getAbsolutePath());
            mChatMessageMap.put(msgId, receivedImgMsg);

            mHandler.post(() -> {
                mMsgListAdapter.addReceivedImgMsg(receivedImgMsg);
            });
        }

    };

    private final SmartWearModelCallback mSmartWearModelCallback = new SmartWearModelCallback() {
        @Override
        public void onReceivedUserVoice(@NonNull byte[] bytes) {
            super.onReceivedUserVoice(bytes);
            // Processing user voice data

        }
    };

    @Override
    public void onDestroy() {
        super.onDestroy();
        mHandler.removeCallbacksAndMessages(null);

        // Remove the previously added AI status listener.
        mSmartWearModelClient.removeAIStateCallback(mAiStateCallback);

        // Remove the previously added callback.
        mSmartWearModelClient.unregisterCallback(mSmartWearModelCallback);

        // Disconnect from AI
        mSmartWearModelClient.disconnectFromAI();

        // Stop dumping user audio
        mSmartWearModelClient.stopDumpAudio();
    }
}