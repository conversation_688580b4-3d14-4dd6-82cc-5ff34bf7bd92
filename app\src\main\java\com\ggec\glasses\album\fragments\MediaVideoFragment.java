package com.ggec.glasses.album.fragments;

import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.media.MediaMetadataRetriever;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.ggec.glasses.R;
import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.util.VideoPlayerUtil;
import com.ggec.glasses.album.viewmodel.MediaDisplayViewModel;
import com.google.android.exoplayer2.ui.PlayerView;

import java.io.File;

/**
 * 视频播放Fragment，专门用于处理视频的加载和播放
 */
public class MediaVideoFragment extends Fragment {

    private static final String ARG_MEDIA = "media";
    
    private Media media;
    private FrameLayout videoContainer;
    private VideoPlayerUtil videoPlayerUtil;
    private PlayerView playerView;
    private MediaDisplayViewModel viewModel;
    
    /**
     * 创建MediaVideoFragment实例的工厂方法
     * @param media 媒体对象
     * @return 新的MediaVideoFragment实例
     */
    public static MediaVideoFragment newInstance(Media media) {
        MediaVideoFragment fragment = new MediaVideoFragment();
        Bundle args = new Bundle();
        args.putParcelable(ARG_MEDIA, media);
        fragment.setArguments(args);
        return fragment;
    }
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            media = getArguments().getParcelable(ARG_MEDIA);
        }
        
        // 获取ViewModel，从Activity共享
        viewModel = new ViewModelProvider(requireActivity()).get(MediaDisplayViewModel.class);
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_media_video, container, false);
        
        // 初始化视图
        videoContainer = view.findViewById(R.id.video_container);
        
        return view;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 初始化视频播放器
        initVideoPlayer();
        
        // 如果Media通过参数传入，使用它
        if (media != null) {
            loadVideo(media);
        } else {
            // 否则观察ViewModel中的媒体数据变化
            viewModel.getMedia().observe(getViewLifecycleOwner(), this::loadVideo);
        }
        
        // 订阅操作层可见性变化
        viewModel.getOperationLayerVisible().observe(getViewLifecycleOwner(), this::updateBackgroundColor);
    }
    
    /**
     * 更新背景颜色
     * @param isOperationLayerVisible 操作层是否可见
     */
    private void updateBackgroundColor(boolean isOperationLayerVisible) {
        if (videoContainer == null) return;
        
        // 获取雪域灰颜色
        int grayColor = ContextCompat.getColor(requireContext(), R.color.comp_background_gray);
        
        // 根据操作层状态设置背景色
        int targetColor = isOperationLayerVisible ? grayColor : Color.BLACK;
        
        // 创建背景色过渡动画
        ValueAnimator colorAnimation = ValueAnimator.ofObject(
                new ArgbEvaluator(), 
                videoContainer.getBackground() != null ? 
                        ((ColorDrawable)videoContainer.getBackground()).getColor() : Color.BLACK, 
                targetColor);
        colorAnimation.setDuration(300); // 与UIAnimationHelper中保持一致
        colorAnimation.addUpdateListener(animator -> {
            int animatedColor = (int) animator.getAnimatedValue();
            videoContainer.setBackgroundColor(animatedColor);
        });
        colorAnimation.start();
    }
    
    /**
     * 初始化视频播放器
     */
    private void initVideoPlayer() {
        if (getContext() == null || videoContainer == null) return;
        
        // 清空容器
        videoContainer.removeAllViews();
        
        // 释放之前的播放器实例（如果有）
        if (videoPlayerUtil != null) {
            videoPlayerUtil.cleanup();
        }
        
        // 创建PlayerView
        LayoutInflater inflater = LayoutInflater.from(getContext());
        View videoPlayerLayout = inflater.inflate(R.layout.video_player_layout, videoContainer, false);
        videoContainer.addView(videoPlayerLayout);
        
        // 确保视频容器立即可见
        videoContainer.setAlpha(1f);
        
        // 初始时设置遮罩层完全不透明，作为视频加载前的占位
        View transitionOverlay = videoPlayerLayout.findViewById(R.id.transition_overlay);
        if (transitionOverlay != null) {
            transitionOverlay.setAlpha(1f);
            // 始终使用黑色背景，不需要与当前页面背景色同步
            transitionOverlay.setBackgroundColor(Color.BLACK);
        }
        
        // 确保播放器背景为黑色
        View playerBackground = videoPlayerLayout.findViewById(R.id.player_background);
        if (playerBackground != null) {
            playerBackground.setBackgroundColor(Color.BLACK);
        }
        
        // 获取PlayerView
        playerView = videoPlayerLayout.findViewById(R.id.player_view);
        
        // 初始化VideoPlayerUtil
        videoPlayerUtil = new VideoPlayerUtil(requireContext());
        videoPlayerUtil.initializePlayer(playerView);
        
        // 设置点击事件用于切换操作层显示/隐藏
        playerView.setOnClickListener(v -> {
            viewModel.toggleOperationLayerVisibility();
        });
        
        // 同时设置整个视频容器的点击事件，增加可点击区域
        videoContainer.setOnClickListener(v -> {
            viewModel.toggleOperationLayerVisibility();
        });
    }
    
    /**
     * 加载视频
     * @param media 媒体数据
     */
    private void loadVideo(Media media) {
        if (media == null || videoPlayerUtil == null) return;
        
        if (!"VIDEO".equals(media.getType())) {
            // 如果不是视频类型，显示警告并返回
            Toast.makeText(requireContext(), "不是有效的视频文件", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 设置视频播放路径
        if (media.getFilePath() != null && new File(media.getFilePath()).exists()) {
            // 提前预加载视频路径
            final String videoPath = media.getFilePath();
            // 使用后台线程预热视频文件
            new Thread(() -> {
                try {
                    // 尝试预热媒体信息
                    MediaMetadataRetriever retriever = new MediaMetadataRetriever();
                    retriever.setDataSource(videoPath);
                    retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
                    retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
                    retriever.release();
                } catch (Exception e) {
                    // 忽略预热过程中的错误
                }
            }).start();
            
            // 延迟一帧，确保视图已经完全初始化
            videoContainer.post(() -> {
                // 获取遮罩层以便控制过渡效果
                View transitionOverlay = videoContainer.findViewById(R.id.transition_overlay);
                
                if (videoPlayerUtil != null) {
                    // 设置播放器已初始化的回调
                    videoPlayerUtil.setOnPlayerPreparedListener(() -> {
                        // 视频准备好后，淡出遮罩层
                        if (transitionOverlay != null) {
                            transitionOverlay.animate()
                                    .alpha(0f)
                                    .setDuration(300)
                                    .start();
                        }
                    });
                    
                    // 设置视频播放路径
                    videoPlayerUtil.setVideoPath(media.getFilePath(), false);
                }
            });
        } else {
            // 视频文件不存在
            Toast.makeText(requireContext(), "视频文件不存在", Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        // 恢复视频播放
        if (videoPlayerUtil != null) {
            videoPlayerUtil.resume();
        }
    }
    
    @Override
    public void onPause() {
        super.onPause();
        // 暂停视频播放
        if (videoPlayerUtil != null) {
            videoPlayerUtil.pause();
        }
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 释放视频播放器资源
        if (videoPlayerUtil != null) {
            videoPlayerUtil.cleanup();
            videoPlayerUtil = null;
        }
    }
} 