/*
 * Copyright (c) 2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.sample.audioconnect.smartwear.gallery

import android.content.Context
import android.content.Intent
import android.net.Uri
import java.io.File

/**
 * <AUTHOR>
 * @date 2025/03/26
 */
class GalleryHelper(private val mContext: Context) {
    fun saveToGallery(filePath:String) :Boolean{
        val file = File(filePath)
        if (!file.exists()) {
            return false
        }

        val intent = Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
        val uri = Uri.fromFile(file)
        intent.setData(uri)
        mContext.sendBroadcast(intent)

        return true;
    }
}
