package com.ggec.glasses.album.data.entity;

import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.ColumnInfo;

/**
 * 媒体和标签的关联实体类，对应数据库中的media_tag表
 * 使用复合主键（mediaId和tagId）
 */
@Entity(
    tableName = "media_tag",
    primaryKeys = {"media_id", "tag_id"},
    foreignKeys = {
        @ForeignKey(
            entity = Media.class,
            parentColumns = "id",
            childColumns = "media_id",
            onDelete = ForeignKey.CASCADE
        ),
        @ForeignKey(
            entity = Tag.class,
            parentColumns = "id",
            childColumns = "tag_id",
            onDelete = ForeignKey.CASCADE
        )
    },
    indices = {
        @Index("media_id"),
        @Index("tag_id")
    }
)
public class MediaTag {
    
    @ColumnInfo(name = "media_id")
    private long mediaId;
    
    @ColumnInfo(name = "tag_id")
    private long tagId;
    
    // 构造函数
    public MediaTag() {
    }
    
    public MediaTag(long mediaId, long tagId) {
        this.mediaId = mediaId;
        this.tagId = tagId;
    }
    
    // Getters and Setters
    
    public long getMediaId() {
        return mediaId;
    }
    
    public void setMediaId(long mediaId) {
        this.mediaId = mediaId;
    }
    
    public long getTagId() {
        return tagId;
    }
    
    public void setTagId(long tagId) {
        this.tagId = tagId;
    }
} 