package com.ggec.glasses;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.Window;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsControllerCompat;

import com.ggec.glasses.utils.DialogUtils;
import com.ggec.glasses.utils.PermissionManager;
import java.util.List;

/**
 * 应用启动页面
 * 展示公司logo，停留1秒后请求权限，然后跳转到主页面
 */
public class SplashActivity extends AppCompatActivity {

    private static final long SPLASH_DELAY = 1000; // 1秒延迟
    private PermissionManager permissionManager;
    private Handler handler;
    private boolean isCheckingPermissions = false;
    private boolean isFromSettings = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 设置沉浸式状态栏（视图无关部分）
        setupImmersiveStatusBar();
        
        setContentView(R.layout.activity_splash);
        
        // 设置窗口插入适配 - 保持对系统栏的处理（视图相关部分）
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(android.R.id.content), (v, insets) -> {
            return insets;
        });

        // 初始化权限管理器
        permissionManager = new PermissionManager(this);
        handler = new Handler(Looper.getMainLooper());
        
        // 延迟一秒后检查权限
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!isFinishing() && !isDestroyed()) {
                    checkPermissionsAndProceed();
                }
            }
        }, SPLASH_DELAY);
    }
    
    /**
     * 检查权限并继续应用流程
     */
    private void checkPermissionsAndProceed() {
        isCheckingPermissions = true;
        // 请求所有权限（包括必要和非必要的）
        permissionManager.requestAllPermissions(new PermissionManager.PermissionCallback() {
            @Override
            public void onPermissionGranted() {
                // 所有权限已获取，跳转到MainActivity
                if (!isFinishing() && !isDestroyed()) {
                    navigateToMainActivity();
                }
            }

            @Override
            public void onPermissionDenied(List<String> deniedPermissions) {
                // 只有当必要权限被拒绝时才显示权限解释对话框
                if (!isFinishing() && !isDestroyed()) {
                    if (!permissionManager.hasAllEssentialPermissions()) {
                        // 显示权限解释对话框
                        showPermissionExplanationDialog();
                    } else {
                        // 如果只是非必要权限被拒绝，直接跳转到MainActivity
                        navigateToMainActivity();
                    }
                }
            }
            
            @Override
            public void onPermissionPermanentlyDenied(List<String> permanentlyDeniedPermissions) {
                // 显示引导用户去设置页面的对话框
                if (!isFinishing() && !isDestroyed()) {
                    showSettingsDialog();
                }
            }
        });
    }
    
    /**
     * 显示权限解释对话框（使用自定义对话框样式）
     */
    private void showPermissionExplanationDialog() {
        DialogUtils.showConfirmDialog(
            this,
            "需要权限",
            "应用需要蓝牙、存储和位置权限才能正常工作。请在设置中启用这些权限。",
            "重试",
            "退出",
            (dialog, which) -> {
                // 只重试请求必要权限
                retryEssentialPermissions();
            },
            (dialog, which) -> {
                finish();
            }
        );
    }
    
    /**
     * 显示引导用户去设置页面的对话框（使用自定义对话框样式）
     */
    private void showSettingsDialog() {
        DialogUtils.showConfirmDialog(
            this,
            "需要手动授予权限",
            "您已拒绝某些必要权限并选择了\"不再询问\"。请前往设置页面手动授予这些权限，否则应用无法正常工作。",
            "去设置",
            "退出",
            (dialog, which) -> {
                // 打开应用设置页面
                isFromSettings = true;
                permissionManager.openAppSettings();
            },
            (dialog, which) -> {
                finish();
            }
        );
    }
    
    /**
     * 重试请求必要权限
     */
    private void retryEssentialPermissions() {
        if (isFinishing() || isDestroyed()) return;
        
        permissionManager.requestEssentialPermissions(new PermissionManager.PermissionCallback() {
            @Override
            public void onPermissionGranted() {
                // 必要权限已获取，跳转到MainActivity
                if (!isFinishing() && !isDestroyed()) {
                    navigateToMainActivity();
                }
            }

            @Override
            public void onPermissionDenied(List<String> deniedPermissions) {
                // 继续显示权限解释对话框
                if (!isFinishing() && !isDestroyed()) {
                    showPermissionExplanationDialog();
                }
            }
            
            @Override
            public void onPermissionPermanentlyDenied(List<String> permanentlyDeniedPermissions) {
                // 显示引导用户去设置页面的对话框
                if (!isFinishing() && !isDestroyed()) {
                    showSettingsDialog();
                }
            }
        });
    }
    
    /**
     * 跳转到主界面
     */
    private void navigateToMainActivity() {
        Intent intent = new Intent(SplashActivity.this, MainActivity.class);
        startActivity(intent);
        finish(); // 结束当前Activity
    }
    
    /**
     * 设置沉浸式状态栏
     */
    private void setupImmersiveStatusBar() {
        // 使内容延伸到状态栏和导航栏后面
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        
        // 设置状态栏颜色为雪域灰
        Window window = getWindow();
        window.setStatusBarColor(ContextCompat.getColor(this, R.color.comp_background_gray));
        
        // 设置状态栏图标为深色（因为背景是浅色）
        WindowInsetsControllerCompat windowInsetsController = WindowCompat.getInsetsController(window, window.getDecorView());
        if (windowInsetsController != null) {
            windowInsetsController.setAppearanceLightStatusBars(true);
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (permissionManager != null) {
            permissionManager.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        // 只有从设置页面返回并且正在检查权限的情况下，才检查权限状态并跳转
        if (isFromSettings && isCheckingPermissions && permissionManager != null && 
            permissionManager.hasAllEssentialPermissions()) {
            // 重置标志
            isFromSettings = false;
            navigateToMainActivity();
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 进入后台时，如果是为了去设置页面，则设置标志为true
        // 否则重置标志，表示不是从设置页面返回
        if (!isFromSettings) {
            isFromSettings = false;
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 移除所有回调和消息，防止内存泄漏
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
            handler = null;
        }
        permissionManager = null;
    }
} 