package com.ggec.glasses.ai.data.db.repository;

/**
 * 数据操作结果包装类
 * @param <T> 数据类型
 */
public class DataResult<T> {
    
    // 状态常量
    public static final int STATUS_SUCCESS = 0;
    public static final int STATUS_ERROR = 1;
    public static final int STATUS_LOADING = 2;
    
    // 结果数据
    private final T data;
    
    // 操作状态
    private final int status;
    
    // 错误信息
    private final String message;
    
    /**
     * 私有构造函数
     * @param data 数据
     * @param status 状态
     * @param message 消息
     */
    private DataResult(T data, int status, String message) {
        this.data = data;
        this.status = status;
        this.message = message;
    }
    
    /**
     * 创建成功结果
     * @param data 结果数据
     * @param <T> 数据类型
     * @return 成功的DataResult
     */
    public static <T> DataResult<T> success(T data) {
        return new DataResult<>(data, STATUS_SUCCESS, null);
    }
    
    /**
     * 创建错误结果
     * @param message 错误信息
     * @param <T> 数据类型
     * @return 错误的DataResult
     */
    public static <T> DataResult<T> error(String message) {
        return new DataResult<>(null, STATUS_ERROR, message);
    }
    
    /**
     * 创建错误结果，带数据
     * @param data 结果数据
     * @param message 错误信息
     * @param <T> 数据类型
     * @return 错误的DataResult
     */
    public static <T> DataResult<T> error(T data, String message) {
        return new DataResult<>(data, STATUS_ERROR, message);
    }
    
    /**
     * 创建加载中结果
     * @param <T> 数据类型
     * @return 加载中的DataResult
     */
    public static <T> DataResult<T> loading() {
        return new DataResult<>(null, STATUS_LOADING, null);
    }
    
    /**
     * 获取结果数据
     * @return 结果数据
     */
    public T getData() {
        return data;
    }
    
    /**
     * 获取操作状态
     * @return 操作状态
     */
    public int getStatus() {
        return status;
    }
    
    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * 判断操作是否成功
     * @return 是否成功
     */
    public boolean isSuccess() {
        return status == STATUS_SUCCESS;
    }
    
    /**
     * 判断操作是否出错
     * @return 是否出错
     */
    public boolean isError() {
        return status == STATUS_ERROR;
    }
    
    /**
     * 判断操作是否正在加载
     * @return 是否正在加载
     */
    public boolean isLoading() {
        return status == STATUS_LOADING;
    }
} 