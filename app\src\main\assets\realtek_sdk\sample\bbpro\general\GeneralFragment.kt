/*
 * Copyright (c) 2017-2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.bbpro.general

import android.os.Bundle
import android.view.View
import com.google.android.material.slider.Slider
import com.realsil.bbpro.R
import com.realsil.bbpro.databinding.FragmentGeneralBinding
import com.realsil.sdk.audioconnect.support.AudioConnectFragment
import com.realsil.sdk.bbpro.BumblebeeCallback
import com.realsil.sdk.bbpro.MultiPeripheralConnectionManager
import com.realsil.sdk.bbpro.PeripheralConnectionManager
import com.realsil.sdk.bbpro.core.BeeError
import com.realsil.sdk.bbpro.core.peripheral.Peripheral
import com.realsil.sdk.bbpro.core.protocol.params.GetConfigType
import com.realsil.sdk.bbpro.core.transportlayer.AckPacket
import com.realsil.sdk.bbpro.model.DeviceInfo
import com.realsil.sdk.bbpro.profile.GetCfgSettingsReq
import com.realsil.sdk.bbpro.vendor.VendorConstants
import com.realsil.sdk.bbpro.vendor.VendorModelCallback
import com.realsil.sdk.bbpro.vendor.VendorModelClient
import com.realsil.sdk.core.bluetooth.connection.legacy.BluetoothSpp
import com.realsil.sdk.core.logger.ZLogger
import kotlin.math.max
import kotlin.math.min

/**
 * <AUTHOR>
 * @date 2018/3/13
 */
class GeneralFragment : AudioConnectFragment<FragmentGeneralBinding>(FragmentGeneralBinding::inflate) {
    private var mVendorClient: VendorModelClient? = null

    private var mBeeProManager: PeripheralConnectionManager? = null
    private val beeProManager: PeripheralConnectionManager
        get() {
            if (mBeeProManager == null) {
                mBeeProManager = MultiPeripheralConnectionManager.getInstance(context)
                    .getPeripheralConnectionManager(mDeviceAddress)
            }
            return mBeeProManager as PeripheralConnectionManager
        }

    private val vendorClient: VendorModelClient
        get() {
            if (mVendorClient == null) {
                mVendorClient = VendorModelClient.getInstance()
            }
            return mVendorClient as VendorModelClient
        }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.btnSyncCompanyId.setOnClickListener {
            loadCompanyModelId()
        }

        binding.btnEnterPairingMode.setOnClickListener {
            showProgressBar(R.string.toast_processing)
            val ret: BeeError = beeProManager.enterPairingMode()

            if (ret.code != BeeError.SUCCESS) {
                cancelProgressBar()
                showShortToast(ret.message)
            }
        }
        binding.btnExitPairingMode.setOnClickListener {
            showProgressBar(R.string.toast_processing)
            val ret: BeeError = beeProManager.exitPairingMode()

            if (ret.code != BeeError.SUCCESS) {
                cancelProgressBar()
                showShortToast(ret.message)
            }
        }

        binding.itemPlayRingtone.setOnClickListener {
            showProgressBar(R.string.toast_processing)
            /*val ret: BeeError = vendorClient.playRingtoneDuringSoundPressCalibration()

            if (ret.code != BeeError.SUCCESS) {
                binding.logView.w("playRingtone failed")
                cancelProgressBar()
                showShortToast(ret.message)
            }*/
        }

        binding.btnSyncFindMe.setOnClickListener {
            loadFindMe()
        }
        binding.btnFindmeLeft.setOnClickListener {
            setFindMeState(VendorConstants.BudRole.LEFT, !binding.btnFindmeLeft.isSelected)
        }
        binding.btnFindmeRight.setOnClickListener {
            setFindMeState(VendorConstants.BudRole.RIGHT, !binding.btnFindmeRight.isSelected)
        }

        setupVolumeFunc()

        beeProManager
        mBeeProManager!!.addManagerCallback(mBeeProManagerCallback)

        vendorClient
        mVendorClient!!.registerCallback(mVendorModelCallback)
    }

    private fun setupVolumeFunc() {
        binding.btnSyncVolume.setOnClickListener {
            showProgressBar(getString(R.string.toast_sync_data_processing))
            val ret: BeeError = beeProManager.vendorClient.volume
            if (ret.code != BeeError.SUCCESS) {
                cancelProgressBar()
                showShortToast(ret.message)
            }
        }
        binding.btnVolumeUp.setOnClickListener {
            showProgressBar(R.string.toast_processing)
            val ret = beeProManager.vendorClient.volumeUp()
            if (ret.code != BeeError.SUCCESS) {
                cancelProgressBar()
                showShortToast(ret.message)
            }
        }
        binding.btnVolumeDown.setOnClickListener {
            showProgressBar(R.string.toast_processing)
            val ret = beeProManager.vendorClient.volumeDown()
            if (ret.code != BeeError.SUCCESS) {
                cancelProgressBar()
                showShortToast(ret.message)
            }
        }
        binding.sliderVolumeLevel.addOnSliderTouchListener(object : Slider.OnSliderTouchListener {
            override fun onStartTrackingTouch(slider: Slider) {

            }

            override fun onStopTrackingTouch(slider: Slider) {
                showProgressBar(R.string.toast_processing)
                val ret = beeProManager.vendorClient.setVolume(slider.value.toInt())
                if (ret.code != BeeError.SUCCESS) {
                    cancelProgressBar()
                    showShortToast(ret.message)
                }

            }
        })
        binding.sliderVolumeLevel.addOnChangeListener { slider, value, fromUser ->
            binding.tvVolumeLevel.text = String.format("%.0f", value)
        }
        binding.sliderVolumeLevel.valueFrom = 0.0f
        binding.tvVolumeLevel.text = String.format("%.0f", binding.sliderVolumeLevel.valueFrom)
    }


    private fun loadFindMe() {
        showProgressBar(R.string.toast_processing)
        val ret: BeeError = beeProManager.vendorClient.findMeStatus

        if (ret.code != BeeError.SUCCESS) {
            cancelProgressBar()
            showShortToast(ret.message)
        }
    }

    private fun setFindMeState(bud: Byte, enabled: Boolean) {
        var status = 0x00.toByte()
        if (enabled) {
            status = 0x01.toByte()
        }
        showProgressBar(R.string.toast_processing)
        val ret = beeProManager.vendorClient.setFindMeState(bud, status)
        if (ret.code != BeeError.SUCCESS) {
            cancelProgressBar()
            showShortToast(ret.message)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ZLogger.v("onDestroyView: RTK")

        mBeeProManager?.removeManagerCallback(mBeeProManagerCallback)
        mVendorClient?.unregisterCallback(mVendorModelCallback)
    }

    /**
     * a callback {@link BumblebeeCallback#onDeviceInfoChanged()} will be triggered
     * @see DeviceInfo.INDICATOR_NAME_BREDR
     * */
    private fun loadCompanyModelId() {
        showProgressBar(getString(R.string.toast_sync_data_processing))
        val req = GetCfgSettingsReq.Builder(GetConfigType.COMPANY_ID_AND_MODEL_ID).build()
        val ret: BeeError = beeProManager.sendVendorCommand(req.encode())
        if (ret.code != BeeError.SUCCESS) {
            cancelProgressBar()
            showShortToast(ret.message)
        }
    }

    fun refresh() {
        activity?.runOnUiThread {
            cancelProgressBar()
        }
    }

    private val mBeeProManagerCallback = object : BumblebeeCallback() {

        override fun onConnectionStateChanged(peripheral: Peripheral?, state: Int) {
//            super.onConnectionStateChanged(peripheral, state)
            if (state == BluetoothSpp.STATE_DISCONNECTED) {
                cancelProgressBar()
            }
        }

        override fun onStateChanged(state: Int) {
            super.onStateChanged(state)

            if (state == PeripheralConnectionManager.STATE_DATA_PREPARED) {
                refresh()
            }
        }

        override fun onDeviceInfoChanged(deviceInfo: DeviceInfo?, indicator: Int) {
            super.onDeviceInfoChanged(deviceInfo, indicator)
            if (deviceInfo == null) {
                return
            }
            when (indicator) {
                DeviceInfo.INDICATOR_NAME_BREDR,
                DeviceInfo.INDICATOR_NAME_LE -> {
                    activity?.runOnUiThread {
                        cancelProgressBar()
                    }
                }
                DeviceInfo.INDICATOR_COMPANY_MODEL_ID -> {
                    activity?.runOnUiThread {
                        cancelProgressBar()
                    }
                }
                DeviceInfo.INDICATOR_FIND_ME -> {
                    activity?.runOnUiThread {
                        cancelProgressBar()
                        binding.btnFindmeLeft.isSelected = deviceInfo.isLeftBudFindMeEnabled
                        binding.btnFindmeRight.isSelected = deviceInfo.isRightBudFindMeEnabled;
                    }
                }
                DeviceInfo.INDICATOR_VOLUME -> {
                    activity?.runOnUiThread {
                        cancelProgressBar()
                        binding.sliderVolumeLevel.valueTo = max(deviceInfo.maxVolumeLevel.toFloat(), 1f)
                        binding.sliderVolumeLevel.value =
                            min(deviceInfo.curVolumeLevel, deviceInfo.maxVolumeLevel).toFloat()
                    }
                }
                else -> {
                }
            }
        }
    }

    private val mVendorModelCallback = object : VendorModelCallback() {
        override fun onPlayRingtongResponse(status: Byte) {
            super.onPlayRingtongResponse(status)

            if (status != AckPacket.ACK_STATUS_COMPLETE) {
                cancelProgressBar()
            }
        }

        override fun onPlayRingtongResult(status: Byte) {
            super.onPlayRingtongResult(status)
            activity?.runOnUiThread {
                cancelProgressBar()
            }
        }

        override fun onOperationComplete(operation: Int, status: Byte) {
            super.onOperationComplete(operation, status)
            when (operation) {
                VendorConstants.Operation.GET_CFG_SETTINGS,
                VendorConstants.Operation.SET_MMI,
                VendorConstants.Operation.SET_CONFIGURATION,
                VendorConstants.Operation.GET_GAMING_MODE_STATE,
//                AudioConstants.Operation.SET_MERIDIAN_SOUND_EFFECT_MODE,
                VendorConstants.Operation.SET_FIND_ME_STATE,
//                AudioConstants.Operation.GET_FLASH_BKP_DATA,
                VendorConstants.Operation.SET_VOLUME,
                VendorConstants.Operation.SET_APT_VOLUME -> {
                    activity?.runOnUiThread {
                        cancelProgressBar()
                    }

                }
            }
        }
    }


    companion object {

        const val TAG = "GeneralFragment"
        private const val D = true

        fun newInstance(args: Bundle?): GeneralFragment {

            val fragment = GeneralFragment()
            if (args != null) {
                fragment.arguments = args
            }
            return fragment
        }
    }


}
