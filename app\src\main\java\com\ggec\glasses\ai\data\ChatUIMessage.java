package com.ggec.glasses.ai.data;

/**
 * 聊天UI消息模型
 * 用于在UI层表示聊天消息
 */
public class ChatUIMessage {
    
    // 消息类型常量
    public static final int TYPE_USER = 1;
    public static final int TYPE_AI = 2;
    
    // 消息ID
    private long id;
    
    // 消息类型
    private int type;
    
    // 消息内容
    private String content;
    
    // 消息时间戳
    private long timestamp;
    
    // 是否正在加载
    private boolean isLoading;
    
    /**
     * 构造函数
     * @param id 消息ID
     * @param type 消息类型
     * @param content 消息内容
     * @param timestamp 时间戳
     */
    public ChatUIMessage(long id, int type, String content, long timestamp) {
        this.id = id;
        this.type = type;
        this.content = content;
        this.timestamp = timestamp;
        this.isLoading = false;
    }
    
    /**
     * 构造函数，设置为加载状态
     * @param type 消息类型
     */
    public ChatUIMessage(int type) {
        this.id = System.currentTimeMillis();
        this.type = type;
        this.content = "";
        this.timestamp = System.currentTimeMillis();
        this.isLoading = true;
    }
    
    // Getters and Setters
    
    public long getId() {
        return id;
    }
    
    public void setId(long id) {
        this.id = id;
    }
    
    public int getType() {
        return type;
    }
    
    public void setType(int type) {
        this.type = type;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public boolean isLoading() {
        return isLoading;
    }
    
    public void setLoading(boolean loading) {
        isLoading = loading;
    }
    
    /**
     * 追加内容到现有消息
     * @param additionalContent 要追加的内容
     */
    public void appendContent(String additionalContent) {
        this.content += additionalContent;
    }
    
    /**
     * 判断是否为用户消息
     * @return 如果是用户消息返回true，否则返回false
     */
    public boolean isUserMessage() {
        return type == TYPE_USER;
    }
} 