/*
 * Copyright (c) 2017-2024. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.bbpro.ui.settings

import android.app.Application
import android.bluetooth.BluetoothDevice
import androidx.lifecycle.MutableLiveData
import com.realsil.bbpro.sync.SettingsSyncManager
import com.realsil.sdk.audioconnect.repository.RepositoryViewModel
import com.realsil.sdk.audioconnect.repository.database.durian.DurianDeviceInfoRepository
import com.realsil.sdk.audioconnect.support.AudioConnectViewModel
import com.realsil.sdk.audioconnect.support.sync.BaseSyncManager
import com.realsil.sdk.audioconnect.support.sync.SyncManagerCallback
import com.realsil.sdk.core.bluetooth.compat.BluetoothDeviceCompat
import com.realsil.sdk.core.logger.ZLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * The ViewModel for [SettingsFragment].
 * <AUTHOR>
 */
class SettingsViewModel(application: Application) : AudioConnectViewModel(application) {

    private lateinit var durianDeviceInfoRepository: DurianDeviceInfoRepository

    private var mSettingsSyncManager: SettingsSyncManager? = null

    private val settingsSyncManager: SettingsSyncManager
        get() {
            if (mSettingsSyncManager == null) {
                mSettingsSyncManager = SettingsSyncManager.getInstance(getApplication(), mDeviceAddress)
                mSettingsSyncManager!!.registerCallback(mSettingsSyncManagerCallback)
            }
            return mSettingsSyncManager as SettingsSyncManager
        }

    val syncProcessLiveData: MutableLiveData<Boolean> by lazy {
        MutableLiveData<Boolean>()
    }
    fun updateNoiseCycleMode(deviceAddress: String, mode: Byte) {
        durianDeviceInfoRepository.updateNoiseCycleMode(deviceAddress, mode)
    }

//     fun removeDevice(device:BluetoothDevice, removeBond:Boolean = false) {
//         ZLogger.v("pending to remove device")
//         getPeripheralConnectionManager().disconnect()
//         if (removeBond) {
//             BluetoothDeviceCompat.removeBond(device)
//         }
// //        MultiPeripheralConnectionManager.getInstance(getApplication()).removePeripheralConnectionManager(mDeviceAddress)
//         deviceInfoRepository.removeDevice(mDeviceAddress)
//     }

    fun updateBrEdrName(deviceAddress: String, name: String) {
        CoroutineScope(Dispatchers.IO).launch {
            deviceInfoRepository.updateBrEdrName(deviceAddress , name)
            reloadData()
        }
    }

    fun syncStateLiveData(): MutableLiveData<Int> {
        return settingsSyncManager.syncStateLiveData
    }
    fun isBusy():Boolean {
        return settingsSyncManager.isBusy
    }
    fun transportChannel():Int {
        return deviceInfoEntity.transportChannel
    }

    fun getOtaChannel():Int {
        return deviceInfoEntity.otaChannel
    }

    fun getSupportedLanguages():Int {
        return deviceInfoEntity.supportedLanguages
    }

    fun startSyncData(manualSync:Boolean = false) {
        settingsSyncManager.startSync(manualSync)
    }

    override fun changePeripheral(address: String) {
        RepositoryViewModel.instance!!.setMultiLinkConflict(mDeviceAddress, settingsSyncManager.isMultiLinkConflict())
        super.changePeripheral(address)
        mSettingsSyncManager?.changePeripheral(address)
    }

    private val mSettingsSyncManagerCallback = object : SyncManagerCallback() {
        override fun onStateChanged(state: Int) {
            super.onStateChanged(state)

            if (state == BaseSyncManager.STATE_CONNECTED
                || state == BaseSyncManager.STATE_SYNC_COMPLETED
                || state == BaseSyncManager.STATE_DISCONNECTED
                || state == BaseSyncManager.STATE_DATA_SYNC_FAILED
            ) {
                CoroutineScope(Dispatchers.IO).launch {
                    reloadData()
                }
            } else {
                /*if (launchSyncManager.isBusy) {
                   runOnUiThread {
                        showProgressBar(getString(R.string.toast_sync_data_processing))
                    }
                } else {
                    runOnUiThread {
                        cancelProgressBar()
                    }
                }*/
            }
        }

        override fun onDataChanged() {
            super.onDataChanged()
            CoroutineScope(Dispatchers.IO).launch {
                reloadData()
            }
        }
    }

    suspend fun cleanData() {
        mSettingsSyncManager?.cleanData()
        reloadData()
    }
    init {
        durianDeviceInfoRepository = DurianDeviceInfoRepository(getApplication())
        settingsSyncManager
    }

    override fun onCleared() {
        super.onCleared()

        unregisterVendorModelCallback()
        mSettingsSyncManager?.destroy()
    }

    companion object
}
