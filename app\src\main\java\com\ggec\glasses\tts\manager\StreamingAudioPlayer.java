package com.ggec.glasses.tts.manager;

import android.media.AudioAttributes;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.util.Log;

import java.nio.ByteBuffer;

/**
 * 使用 Android 的 AudioTrack API 处理原始 PCM 音频流的播放。
 * 主要设计用于实时播放流式传输的 TTS（文本转语音）音频数据块。
 *
 * <p>典型生命周期:
 * <ol>
 *     <li>创建实例: {@code new StreamingAudioPlayer(...)}</li>
 *     <li>初始化底层 AudioTrack: {@link #initialize()}</li>
 *     <li>开始播放（准备接收数据）: {@link #play()}</li>
 *     <li>循环写入音频数据块: {@link #write(ByteBuffer)}</li>
 *     <li>停止播放: {@link #stop()}</li>
 *     <li>释放资源: {@link #release()}</li>
 * </ol>
 */
public class StreamingAudioPlayer {

    private static final String TAG = "StreamingAudioPlayer";

    private AudioTrack audioTrack;
    private final int sampleRate;
    private final int channelConfig;
    private final int audioFormat;
    private final int bufferSize;
    private volatile boolean isPlaying = false; // 使用 volatile 保证多线程间的可见性
    private volatile boolean isInitialized = false;

    /**
     * 构造函数：配置音频参数并计算缓冲区大小。
     *
     * @param sampleRate 采样率 (Hz)，例如 22050。
     * @param channelConfig 声道配置，例如 {@link AudioFormat#CHANNEL_OUT_MONO}。
     * @param audioFormat 音频数据编码格式，例如 {@link AudioFormat#ENCODING_PCM_16BIT}。
     */
    public StreamingAudioPlayer(int sampleRate, int channelConfig, int audioFormat) {
        this.sampleRate = sampleRate;
        this.channelConfig = channelConfig;
        this.audioFormat = audioFormat;

        // 计算所需的最小缓冲区大小
        int minBufferSize = AudioTrack.getMinBufferSize(sampleRate, channelConfig, audioFormat);
        if (minBufferSize == AudioTrack.ERROR_BAD_VALUE || minBufferSize == AudioTrack.ERROR) {
            Log.e(TAG, "无效的 AudioTrack 缓冲区大小计算参数。");
            // 如果计算失败，使用一个合理的默认大小，例如能容纳2秒音频数据
            bufferSize = sampleRate * 2 * (audioFormat == AudioFormat.ENCODING_PCM_16BIT ? 2 : 1); // 考虑字节大小
        } else {
            // 通常建议使用比最小值稍大的缓冲区，以减少音频播放中断的可能性
            bufferSize = minBufferSize * 2;
        }
        Log.d(TAG, "AudioTrack 缓冲区大小: " + bufferSize + " (最小: " + minBufferSize + ")");
    }

    /**
     * 初始化底层的 AudioTrack 实例。
     * 必须在调用 {@link #play()} 或 {@link #write(ByteBuffer)} 之前成功调用此方法。
     * 此方法是幂等的，重复调用是安全的。
     *
     * @return 如果初始化成功或已经初始化，则返回 true，否则返回 false。
     */
    public boolean initialize() {
        if (isInitialized) {
            Log.w(TAG, "AudioTrack 已初始化，无需重复操作。");
            return true;
        }
        try {
            // 使用 AudioAttributes 配置音频播放属性 (适用于现代 Android 版本)
            AudioAttributes attributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA) // 用途：媒体播放
                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH) // 内容类型：语音
                    .build();

            // 使用 AudioFormat.Builder 配置音频流格式
            AudioFormat format = new AudioFormat.Builder()
                    .setSampleRate(sampleRate)
                    .setChannelMask(channelConfig)
                    .setEncoding(audioFormat)
                    .build();

            // 使用 AudioTrack.Builder 创建流式播放实例
            audioTrack = new AudioTrack.Builder()
                    .setAudioAttributes(attributes)
                    .setAudioFormat(format)
                    .setBufferSizeInBytes(bufferSize)
                    .setTransferMode(AudioTrack.MODE_STREAM) // 设置为流模式
                    .build();

            if (audioTrack.getState() == AudioTrack.STATE_INITIALIZED) {
                Log.d(TAG, "AudioTrack 初始化成功。");
                isInitialized = true;
                return true;
            } else {
                Log.e(TAG, "AudioTrack 初始化失败。状态: " + audioTrack.getState());
                releaseInternal(); // 如果初始化失败则清理资源
                return false;
            }
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "初始化 AudioTrack 时出错，参数可能无效", e);
            releaseInternal(); // 清理资源
            return false;
        }
    }

    /**
     * 开始播放音频流，使 AudioTrack 进入播放状态。
     * 必须在成功调用 {@link #initialize()} 之后调用。
     * 调用后，可以通过 {@link #write(ByteBuffer)} 方法写入音频数据。
     */
    public void play() {
        if (!isInitialized || audioTrack == null) {
            Log.e(TAG, "无法播放。AudioTrack 未初始化或已释放。");
            return;
        }
        if (isPlaying) {
            Log.w(TAG, "已在播放中，无需重复调用 play()。");
            return;
        }
        try {
            audioTrack.play();
            isPlaying = true;
            Log.d(TAG, "AudioTrack 播放已开始。");
        } catch (IllegalStateException e) {
            Log.e(TAG, "开始 AudioTrack 播放时出错，状态异常", e);
        }
    }

    /**
     * 将 PCM 音频数据块写入 AudioTrack 的内部缓冲区以供播放。
     * 如果在调用此方法时播放器尚未开始播放 ({@link #isPlaying()} 为 false)，
     * 此方法会尝试自动调用 {@link #play()}。
     *
     * @param chunk 包含要写入的原始 PCM 音频数据的 ByteBuffer。
     */
    public void write(ByteBuffer chunk) {
        if (!isInitialized || audioTrack == null) {
            Log.e(TAG, "无法写入。AudioTrack 未初始化或已释放。");
            return;
        }

        // 如果尚未播放，尝试自动开始播放
        if (!isPlaying) {
           Log.w(TAG, "尝试在未播放状态下写入，将自动调用 play()。");
           play(); // 尝试自动开始播放
           if(!isPlaying) {
               Log.e(TAG, "自动调用 play() 失败，无法写入数据。");
               return; // 如果 play() 失败，则不继续写入
           }
        }

        if (chunk == null || !chunk.hasRemaining()) {
            // Log.v(TAG, "写入的音频块为空或无剩余数据，已忽略。");
            return; // 无内容可写
        }

        // 从 ByteBuffer 获取 byte 数组
        // 注意：如果 ByteBuffer 是 direct buffer，这种方式效率不高，但对于音频块通常影响不大
        byte[] buffer = new byte[chunk.remaining()];
        chunk.get(buffer);

        // 写入数据到 AudioTrack
        int bytesWritten = audioTrack.write(buffer, 0, buffer.length);

        if (bytesWritten < 0) {
            // 写入错误，记录错误码
            Log.e(TAG, "写入 AudioTrack 时出错。错误码: " + bytesWritten);
            // 根据错误码可以进行更细致的处理，例如：
            // case AudioTrack.ERROR_INVALID_OPERATION:
            // case AudioTrack.ERROR_BAD_VALUE:
            // case AudioTrack.ERROR_DEAD_OBJECT:
            // case AudioTrack.ERROR:
        } else if (bytesWritten < buffer.length) {
            // 可能缓冲区已满或发生其他问题导致未完全写入
            Log.w(TAG, "未能将所有字节写入 AudioTrack。尝试写入 " + buffer.length + "字节，实际写入 " + bytesWritten + "字节。");
        }
        // else: 写入成功
    }

    /**
     * 停止播放。
     * 调用后，AudioTrack 将停止播放，并且内部的播放位置会被重置。
     * 要继续播放（即使是相同的数据流），通常需要重新初始化或创建一个新的实例。
     * 此实现将播放器标记为未初始化。
     * <p>
     * 注意：{@code stop()} 会立即停止播放。如果需要更平滑地结束（例如，等待缓冲区播放完毕），
     * 可以考虑使用 {@code pause()} 和 {@code flush()} 的组合。
     */
    public void stop() {
        if (!isInitialized || audioTrack == null) {
            // Log.d(TAG, "调用 stop() 时 AudioTrack 未初始化或已释放。");
            return;
        }
        if (!isPlaying) {
            // Log.d(TAG, "调用 stop() 时未在播放状态。");
            // 即使未在播放，也需要确保状态被重置
        }
        try {
            audioTrack.stop();
            isPlaying = false;
            isInitialized = false; // 调用 stop() 后认为此实例不再可用，需要重新初始化
            Log.d(TAG, "AudioTrack 播放已停止。");
        } catch (IllegalStateException e) {
            Log.e(TAG, "停止 AudioTrack 播放时出错，状态异常", e);
            // 即使出错，也尝试将状态标记为停止
            isPlaying = false;
            isInitialized = false;
        }
    }

    /**
     * 释放与此 AudioTrack 实例关联的所有本地和系统资源。
     * 调用此方法后，对象将不可用。
     * 必须调用此方法以避免资源泄漏，尤其是在 Activity 或 Service 销毁时。
     */
    public void release() {
        releaseInternal();
    }

    /**
     * 内部资源释放逻辑。
     */
    private void releaseInternal(){
         if (audioTrack != null) {
            // 确保在释放前停止播放
            if (audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING) {
                try {
                    // 这里直接调用内部 stop 逻辑以更新状态
                    stop();
                } catch (Exception e){
                    // 即使停止出错，也要继续尝试释放
                    Log.e(TAG, "释放过程中调用 stop() 时出错", e);
                }
            }
             try {
                 audioTrack.release(); // 释放本地 AudioTrack 对象
                 Log.d(TAG, "AudioTrack 已释放。");
             } catch (Exception e) {
                 Log.e(TAG, "释放 AudioTrack 时出错", e);
             }
            audioTrack = null; // 帮助垃圾回收
        }
        // 重置状态标志
        isPlaying = false;
        isInitialized = false;
    }

    /**
     * 检查播放器当前是否处于播放状态。
     * @return 如果已初始化、未释放且 AudioTrack 报告正在播放，则返回 true。
     */
    public boolean isPlaying() {
        // 检查所有相关状态确保准确性
        return isPlaying && isInitialized && audioTrack != null && audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING;
    }

     /**
     * 检查播放器是否已成功初始化且尚未释放。
     * @return 如果 {@link #initialize()} 已成功调用且 {@link #release()} 或 {@link #stop()} 尚未完成，则返回 true。
     */
    public boolean isInitialized() {
        return isInitialized && audioTrack != null;
    }
}