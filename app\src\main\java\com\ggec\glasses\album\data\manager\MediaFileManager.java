package com.ggec.glasses.album.data.manager;

import android.content.ContentResolver;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.MediaMetadataRetriever;
import android.media.ThumbnailUtils;
import android.net.Uri;
import android.provider.MediaStore;
import android.util.Log;
import android.util.Size;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.UUID;

/**
 * 媒体文件管理器，负责媒体文件的存储、缩略图生成和文件操作
 */
public class MediaFileManager {
    
    private static final String TAG = "MediaFileManager";
    
    // 常量定义
    public static final String TYPE_IMAGE = "IMAGE";
    public static final String TYPE_VIDEO = "VIDEO";
    
    // 文件夹路径常量
    private static final String ROOT_DIR = "media";
    private static final String IMAGES_DIR = "images";
    private static final String VIDEOS_DIR = "videos";
    private static final String THUMBNAILS_DIR = "thumbnails";
    private static final String ORIGINALS_DIR = "originals";
    private static final String TEMP_DIR = "temp";
    
    // 缩略图尺寸
    private static final int THUMBNAIL_SIZE = 320;
    
    // 上下文
    private final Context context;
    
    // 各种文件目录
    private final File rootDir;
    private final File imagesDir;
    private final File videosDir;
    private final File imageThumbnailsDir;
    private final File imageOriginalsDir;
    private final File videoThumbnailsDir;
    private final File videoOriginalsDir;
    private final File tempDir;
    
    /**
     * 构造函数，初始化文件目录
     * @param context 应用上下文
     */
    public MediaFileManager(Context context) {
        this.context = context;
        
        // 获取应用外部存储目录（Android/data/com.example.Glasses）
        File externalFilesDir = context.getExternalFilesDir(null);
        
        // 创建根目录和子目录
        rootDir = new File(externalFilesDir, ROOT_DIR);
        
        // 图片目录
        imagesDir = new File(rootDir, IMAGES_DIR);
        imageThumbnailsDir = new File(imagesDir, THUMBNAILS_DIR);
        imageOriginalsDir = new File(imagesDir, ORIGINALS_DIR);
        
        // 视频目录
        videosDir = new File(rootDir, VIDEOS_DIR);
        videoThumbnailsDir = new File(videosDir, THUMBNAILS_DIR);
        videoOriginalsDir = new File(videosDir, ORIGINALS_DIR);
        
        // 临时目录使用外部缓存目录
        tempDir = new File(context.getExternalCacheDir(), TEMP_DIR);
        
        // 确保目录存在
        createDirectories();
    }
    
    /**
     * 创建必要的目录结构
     */
    private void createDirectories() {
        rootDir.mkdirs();
        imageThumbnailsDir.mkdirs();
        imageOriginalsDir.mkdirs();
        videoThumbnailsDir.mkdirs();
        videoOriginalsDir.mkdirs();
        tempDir.mkdirs();
    }
    
    /**
     * 保存媒体文件
     * @param sourceUri 源文件URI
     * @param mediaType 媒体类型（IMAGE或VIDEO）
     * @return 保存的文件，如果保存失败则返回null
     */
    public File saveMediaFile(Uri sourceUri, String mediaType) {
        ContentResolver resolver = context.getContentResolver();
        InputStream inputStream = null;
        OutputStream outputStream = null;
        File outputFile = null;
        
        try {
            // 创建目标文件
            String fileName = generateUniqueFileName(mediaType, sourceUri);
            File targetDir = TYPE_IMAGE.equals(mediaType) ? imageOriginalsDir : videoOriginalsDir;
            outputFile = new File(targetDir, fileName);
            
            // 检查文件是否已存在（处理重复导入的情况）
            if (outputFile.exists()) {
                // 文件已存在，查找对应的缩略图是否存在
                String thumbnailName = getThumbnailName(fileName);
                File thumbnailDir = TYPE_IMAGE.equals(mediaType) ? imageThumbnailsDir : videoThumbnailsDir;
                File thumbnailFile = new File(thumbnailDir, thumbnailName);
                
                if (thumbnailFile.exists()) {
                    // 文件和缩略图都已存在，直接返回已存在的文件
                    Log.d(TAG, "File already exists: " + outputFile.getAbsolutePath());
                    return outputFile;
                }
            }
            
            // 打开输入流和输出流
            inputStream = resolver.openInputStream(sourceUri);
            outputStream = new FileOutputStream(outputFile);
            
            if (inputStream == null) {
                return null;
            }
            
            // 复制文件
            byte[] buffer = new byte[8192];
            int read;
            while ((read = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, read);
            }
            outputStream.flush();
            
            // 生成缩略图
            generateThumbnail(outputFile, mediaType);
            
            return outputFile;
        } catch (IOException e) {
            Log.e(TAG, "Error saving media file", e);
            if (outputFile != null && outputFile.exists()) {
                outputFile.delete();
            }
            return null;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "Error closing streams", e);
            }
        }
    }
    
    /**
     * 生成缩略图
     * @param originalFile 原始文件
     * @param mediaType 媒体类型
     * @return 缩略图文件，如果生成失败则返回null
     */
    public File generateThumbnail(File originalFile, String mediaType) {
        if (originalFile == null || !originalFile.exists()) {
            return null;
        }
        
        String fileName = originalFile.getName();
        String thumbnailName = getThumbnailName(fileName);
        File thumbnailDir = TYPE_IMAGE.equals(mediaType) ? imageThumbnailsDir : videoThumbnailsDir;
        File thumbnailFile = new File(thumbnailDir, thumbnailName);
        
        // 检查缩略图是否已存在
        if (thumbnailFile.exists()) {
            Log.d(TAG, "Thumbnail already exists: " + thumbnailFile.getAbsolutePath());
            return thumbnailFile;
        }
        
        try (FileOutputStream out = new FileOutputStream(thumbnailFile)) {
            Bitmap thumbnail;
            
            if (TYPE_IMAGE.equals(mediaType)) {
                // 为图片生成缩略图，添加解码选项以避免内存问题
                BitmapFactory.Options options = new BitmapFactory.Options();
                options.inJustDecodeBounds = true;
                BitmapFactory.decodeFile(originalFile.getAbsolutePath(), options);
                
                // 计算缩放系数
                int scale = 1;
                while (options.outWidth / scale > THUMBNAIL_SIZE * 2 || options.outHeight / scale > THUMBNAIL_SIZE * 2) {
                    scale *= 2;
                }
                
                // 使用缩放系数解码图片
                options.inJustDecodeBounds = false;
                options.inSampleSize = scale;
                Bitmap originalBitmap = BitmapFactory.decodeFile(originalFile.getAbsolutePath(), options);
                if (originalBitmap == null) {
                    return null;
                }
                
                thumbnail = ThumbnailUtils.extractThumbnail(
                        originalBitmap,
                        THUMBNAIL_SIZE,
                        THUMBNAIL_SIZE,
                        ThumbnailUtils.OPTIONS_RECYCLE_INPUT);
            } else {
                // 为视频生成缩略图，使用错误处理增强的方法
                try {
                    MediaMetadataRetriever retriever = new MediaMetadataRetriever();
                    retriever.setDataSource(originalFile.getAbsolutePath());
                    
                    // 尝试获取视频的第一帧
                    Bitmap frame = retriever.getFrameAtTime(0, MediaMetadataRetriever.OPTION_CLOSEST_SYNC);
                    
                    // 如果第一帧获取失败，尝试获取任意帧
                    if (frame == null) {
                        frame = retriever.getFrameAtTime();
                    }
                    
                    // 如果仍然失败，返回null
                    if (frame == null) {
                        retriever.release();
                        return null;
                    }
                    
                    thumbnail = ThumbnailUtils.extractThumbnail(
                            frame,
                            THUMBNAIL_SIZE,
                            THUMBNAIL_SIZE,
                            ThumbnailUtils.OPTIONS_RECYCLE_INPUT);
                    
                    retriever.release();
                } catch (Exception e) {
                    Log.e(TAG, "Error extracting video frame", e);
                    return null;
                }
            }
            
            if (thumbnail == null) {
                return null;
            }
            
            // 使用更高质量的JPEG压缩
            thumbnail.compress(Bitmap.CompressFormat.JPEG, 90, out);
            thumbnail.recycle();
            
            return thumbnailFile;
        } catch (IOException e) {
            Log.e(TAG, "Error generating thumbnail", e);
            if (thumbnailFile.exists()) {
                thumbnailFile.delete();
            }
            return null;
        }
    }
    
    /**
     * 生成缩略图文件名
     * @param originalFileName 原始文件名
     * @return 生成的缩略图文件名
     */
    private String getThumbnailName(String originalFileName) {
        return originalFileName.substring(0, originalFileName.lastIndexOf(".")) + "_thumb.jpg";
    }
    
    /**
     * 生成唯一的文件名
     * @param mediaType 媒体类型
     * @return 生成的文件名
     */
    private String generateFileName(String mediaType) {
        String prefix = TYPE_IMAGE.equals(mediaType) ? "img" : "vid";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().substring(0, 8); // 使用UUID的前8位
        String extension = TYPE_IMAGE.equals(mediaType) ? "jpg" : "mp4";
        
        return String.format("%s_%s_%s.%s", prefix, timestamp, uuid, extension);
    }
    
    /**
     * 根据源URI生成唯一的文件名
     * @param mediaType 媒体类型
     * @param sourceUri 源文件URI
     * @return 生成的唯一文件名
     */
    private String generateUniqueFileName(String mediaType, Uri sourceUri) {
        // 首先尝试从URI获取原始文件名
        String originalFileName = getFileNameFromUri(sourceUri);
        String extension = TYPE_IMAGE.equals(mediaType) ? "jpg" : "mp4";
        
        // 如果能获取到原始文件名，提取扩展名
        if (originalFileName != null && !originalFileName.isEmpty()) {
            int dotIndex = originalFileName.lastIndexOf(".");
            if (dotIndex > 0) {
                extension = originalFileName.substring(dotIndex + 1).toLowerCase();
            }
        }
        
        // 确保扩展名有效
        if (!isValidExtension(extension, mediaType)) {
            extension = TYPE_IMAGE.equals(mediaType) ? "jpg" : "mp4";
        }
        
        // 生成基本文件名
        String prefix = TYPE_IMAGE.equals(mediaType) ? "img" : "vid";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        
        // 返回最终文件名
        return String.format("%s_%s_%s.%s", prefix, timestamp, uuid, extension);
    }
    
    /**
     * 从URI获取文件名
     * @param uri 文件URI
     * @return 文件名，如果无法获取则返回null
     */
    private String getFileNameFromUri(Uri uri) {
        if (uri == null) return null;
        
        String fileName = null;
        String scheme = uri.getScheme();
        
        if (scheme != null && scheme.equals("content")) {
            try {
                String[] projection = {MediaStore.MediaColumns.DISPLAY_NAME};
                try (android.database.Cursor cursor = context.getContentResolver().query(uri, projection, null, null, null)) {
                    if (cursor != null && cursor.moveToFirst()) {
                        int columnIndex = cursor.getColumnIndex(MediaStore.MediaColumns.DISPLAY_NAME);
                        if (columnIndex != -1) {
                            fileName = cursor.getString(columnIndex);
                        }
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error getting file name from URI", e);
            }
        } else if (scheme != null && scheme.equals("file")) {
            fileName = uri.getLastPathSegment();
        }
        
        return fileName;
    }
    
    /**
     * 检查扩展名是否有效
     * @param extension 文件扩展名
     * @param mediaType 媒体类型
     * @return 是否有效
     */
    private boolean isValidExtension(String extension, String mediaType) {
        if (extension == null || extension.isEmpty()) {
            return false;
        }
        
        extension = extension.toLowerCase();
        
        if (TYPE_IMAGE.equals(mediaType)) {
            return extension.equals("jpg") || extension.equals("jpeg") || 
                   extension.equals("png") || extension.equals("gif") || 
                   extension.equals("webp") || extension.equals("bmp");
        } else {
            return extension.equals("mp4") || extension.equals("3gp") || 
                   extension.equals("webm") || extension.equals("mkv") || 
                   extension.equals("mov");
        }
    }
    
    /**
     * 检查该名称的文件是否已存在
     * @param fileName 文件名
     * @param mediaType 媒体类型
     * @return 文件是否已存在
     */
    public boolean isFileExists(String fileName, String mediaType) {
        File targetDir = TYPE_IMAGE.equals(mediaType) ? imageOriginalsDir : videoOriginalsDir;
        return new File(targetDir, fileName).exists();
    }
    
    /**
     * 删除媒体文件及其缩略图
     * @param filePath 文件路径
     * @param mediaType 媒体类型
     * @return 是否成功删除
     */
    public boolean deleteMediaFile(String filePath, String mediaType) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        
        // 删除原文件
        File originalFile = new File(filePath);
        boolean originalDeleted = !originalFile.exists() || originalFile.delete();
        
        // 删除缩略图
        String fileName = originalFile.getName();
        String thumbnailName = fileName.substring(0, fileName.lastIndexOf(".")) + "_thumb.jpg";
        File thumbnailDir = TYPE_IMAGE.equals(mediaType) ? imageThumbnailsDir : videoThumbnailsDir;
        File thumbnailFile = new File(thumbnailDir, thumbnailName);
        boolean thumbnailDeleted = !thumbnailFile.exists() || thumbnailFile.delete();
        
        return originalDeleted && thumbnailDeleted;
    }
    
    /**
     * 获取临时文件，用于处理中间操作
     * @param extension 文件扩展名
     * @return 临时文件
     */
    public File getTempFile(String extension) {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String fileName = "temp_" + timeStamp + "." + extension;
        return new File(tempDir, fileName);
    }
    
    /**
     * 清理临时文件
     * @return 是否成功清理
     */
    public boolean cleanTempFiles() {
        File[] tempFiles = tempDir.listFiles();
        if (tempFiles == null || tempFiles.length == 0) {
            return true;
        }
        
        boolean allDeleted = true;
        for (File file : tempFiles) {
            if (!file.delete()) {
                allDeleted = false;
            }
        }
        
        return allDeleted;
    }
    
    /**
     * 获取图片原始文件目录
     * @return 图片原始文件目录
     */
    public File getImageOriginalsDir() {
        return imageOriginalsDir;
    }
    
    /**
     * 获取视频原始文件目录
     * @return 视频原始文件目录
     */
    public File getVideoOriginalsDir() {
        return videoOriginalsDir;
    }
    
    /**
     * 获取图片缩略图目录
     * @return 图片缩略图目录
     */
    public File getImageThumbnailsDir() {
        return imageThumbnailsDir;
    }
    
    /**
     * 获取视频缩略图目录
     * @return 视频缩略图目录
     */
    public File getVideoThumbnailsDir() {
        return videoThumbnailsDir;
    }
} 