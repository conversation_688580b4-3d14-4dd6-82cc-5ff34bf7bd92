/*
 * Copyright (c) 2017-2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */
package com.realsil.sdk.audioconnect.support.ui

import android.app.Dialog
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.realsil.sdk.audioconnect.support.R

/**
 * Set Device Name
 * <AUTHOR>
 * @date 13/08/2017
 */
class SetNameDialogFragment : DialogFragment() {
    private var mListener: OnDialogListener? = null

    private var etName: EditText? = null
    private var btnSubmit: Button? = null

    private var previousBrEdrName: String? = null

    /**
     * Interface required to be implemented by activity
     */
    interface OnDialogListener {
        /**
         * @param name
         */
        fun onSubmit(name: String)
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val args = arguments
        if (args != null) {
            previousBrEdrName = args.getString(EXTRA_KEY_PREVIOUS_NAME)
        }
    }

    /**
     * When dialog is created then set AlertDialog with list and button views
     */
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val builder = MaterialAlertDialogBuilder(requireContext(), R.style.RtkAlertDialogTheme)

        val dialogView =
            layoutInflater.inflate(R.layout.rtk_audioconnect_dialogview_change_name, null)

        btnSubmit = dialogView.findViewById(R.id.bt_change_name)
        etName = dialogView.findViewById(R.id.et_change_name)

        etName!!.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable) {
                onDatasetChanged()
            }
        })

        btnSubmit!!.setOnClickListener { v: View? ->
            val name = etName!!.getText().toString()
            if (name.isEmpty()) {
                showShortToast(R.string.toast_enter_name)
                return@setOnClickListener
            } else if (name.toByteArray().size > MAX_LENGTH_LIMIT) {
                showShortToast(R.string.toast_name_bytes_limit)
                return@setOnClickListener
            }

            dismiss()
            if (mListener != null) {
                mListener!!.onSubmit(name)
            }
        }
        onDatasetChanged()

        return builder.setView(dialogView)
            .setTitle(R.string.title_change_name)
            .create()
    }

    private fun onDatasetChanged() {
        btnSubmit!!.isEnabled = etName!!.length() > 0
        etName!!.hint = previousBrEdrName
    }

    private var mToast: Toast? = null

    fun showShortToast(messageResId: Int) {
        if (mToast == null) {
            mToast = Toast.makeText(context, "", Toast.LENGTH_SHORT)
        }
        mToast!!.setText(messageResId)
        mToast!!.show()
    }

    companion object {
        const val TAG: String = "SetNameDialogFragment"
        const val EXTRA_KEY_PREVIOUS_NAME: String = "previousName"

        private const val MAX_LENGTH_LIMIT = 40 //bytes

        /**
         * Static implementation of fragment so that it keeps data when phone orientation is changed.
         * For standard BLE Service UUID, we can filter devices using normal android provided command
         * startScanLe() with required BLE Service UUID
         * For custom BLE Service UUID, we will use class ScannerServiceParser to filter out required
         * device.
         */
        fun getInstance(previousName: String?, listener: OnDialogListener): SetNameDialogFragment {
            val fragment = SetNameDialogFragment()

            val args = Bundle()
            args.putString(
                EXTRA_KEY_PREVIOUS_NAME,
                previousName
            )
            fragment.arguments = args
            fragment.mListener = listener
            return fragment
        }
    }
}
