package com.ggec.glasses.album.data.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.ForeignKey;
import androidx.room.Index;

import java.util.Date;

/**
 * 相册实体类，对应数据库中的Album表
 */
@Entity(
    tableName = "album",
    foreignKeys = {
        @ForeignKey(
            entity = Media.class,
            parentColumns = "id",
            childColumns = "cover_media_id",
            onDelete = ForeignKey.SET_NULL
        )
    },
    indices = {
        @Index("cover_media_id")
    }
)
public class Album {
    
    @PrimaryKey(autoGenerate = true)
    private long id;
    
    @ColumnInfo(name = "name")
    private String name;
    
    @ColumnInfo(name = "cover_media_id")
    private Long coverMediaId; // 外键，使用Long而不是long，允许为null
    
    @ColumnInfo(name = "creation_date")
    private Date creationDate;
    
    @ColumnInfo(name = "modification_date")
    private Date modificationDate;
    
    @ColumnInfo(name = "description")
    private String description;
    
    // 构造函数
    public Album() {
    }
    
    public Album(String name, String description) {
        this.name = name;
        this.description = description;
        this.creationDate = new Date();
        this.modificationDate = new Date();
    }
    
    // Getters and Setters
    
    public long getId() {
        return id;
    }
    
    public void setId(long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Long getCoverMediaId() {
        return coverMediaId;
    }
    
    public void setCoverMediaId(Long coverMediaId) {
        this.coverMediaId = coverMediaId;
    }
    
    public Date getCreationDate() {
        return creationDate;
    }
    
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
    
    public Date getModificationDate() {
        return modificationDate;
    }
    
    public void setModificationDate(Date modificationDate) {
        this.modificationDate = modificationDate;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
} 