package com.ggec.glasses.ai.manager;

import android.util.Log;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.ggec.glasses.ai.data.ChatUIMessage;
// import com.example.glasses.ai.util.SampleMessageProvider; // 不再需要

import java.util.ArrayList;
import java.util.List;

/**
 * 聊天界面管理器 (Refactored)
 * 现在主要负责管理 UI 状态（加载、错误）和动画控制，数据由 ViewModel 提供。
 */
public class ChatUIManager {
    private static final String TAG = "ChatUIManager";

    private final MessageDisplayManager messageDisplayManager;
    private final MessageAppendManager messageAppendManager;

    // UI 状态 LiveData
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    // displayMessagesLiveData 已移除，数据源来自 ViewModel -> ChatMessageManager
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();

    /**
     * 构造函数
     */
    public ChatUIManager() {
        this.messageDisplayManager = new MessageDisplayManager();
        this.messageAppendManager = new MessageAppendManager();
        setupListeners();
    }

    /**
     * 设置内部管理器的监听器，用于更新 isLoading 状态
     */
    private void setupListeners() {
        // 监听 MessageDisplayManager
        messageDisplayManager.setOnMessageDisplayListener(new MessageDisplayManager.OnMessageDisplayListener() {
            @Override
            public void onMessageDisplayed(List<ChatUIMessage> currentMessages) {
                // 不再需要更新 displayMessagesLiveData
                // Log.d(TAG, "DisplayManager: 消息已显示，更新 LiveData");
                // displayMessagesLiveData.postValue(new ArrayList<>(currentMessages));
            }

            @Override
            public void onAllMessagesDisplayed() {
                Log.d(TAG, "DisplayManager: 所有消息已显示完毕");
                isLoading.postValue(false);
            }
        });

        // 监听 MessageAppendManager
        messageAppendManager.setOnMessageAppendListener(new MessageAppendManager.OnMessageAppendListener() {
            @Override
            public void onMessageAppended(ChatUIMessage appendedMessage) {
                 // 不再需要更新 displayMessagesLiveData
                 // Log.d(TAG, "AppendManager: 消息已追加: " + appendedMessage.getContent());
                 // ... (原列表更新逻辑移除)
                 // displayMessagesLiveData.postValue(updatedMessages);
            }

            @Override
            public void onAllMessagesAppended() {
                Log.d(TAG, "AppendManager: 所有消息已追加完毕");
                isLoading.postValue(false);
            }
        });
    }

    /**
     * 触发消息列表的追加动画 (使用 AppendManager)
     * @param messages 要追加的 UI 消息列表
     */
    public void appendMessagesSequentially(List<ChatUIMessage> messages) {
        Log.d(TAG, "请求追加动画 " + (messages != null ? messages.size() : 0) + " 条消息");
        if (messages == null || messages.isEmpty()) {
             Log.w(TAG, "要追加动画的消息列表为空");
             return;
         }
        isLoading.postValue(true);
        messageAppendManager.reset();
        messageAppendManager.appendMessagesSequentially(messages);
    }

    /**
     * 触发消息列表的逐条显示动画 (使用 DisplayManager)
     * @param messages 要显示的 UI 消息列表
     */
    public void displayMessagesSequentially(List<ChatUIMessage> messages) {
        Log.d(TAG, "请求显示动画 " + (messages != null ? messages.size() : 0) + " 条消息");
         if (messages == null || messages.isEmpty()) {
             Log.w(TAG, "要显示动画的消息列表为空");
             // displayMessagesLiveData.postValue(new ArrayList<>()); // 移除
             isLoading.postValue(false); // 如果列表为空，结束加载
             return;
         }
        isLoading.postValue(true);
        messageDisplayManager.reset();
        messageDisplayManager.displayMessagesSequentially(messages);
    }

    /**
     * 直接加载/设置消息列表到UI (无动画) - 此方法已废弃
     * 数据应通过 ViewModel 的 LiveData 驱动
     * @param messages 要设置的 UI 消息列表
     */
    @Deprecated
    public void loadMessagesToUI(List<ChatUIMessage> messages) {
        Log.w(TAG, "loadMessagesToUI is deprecated. UI should observe LiveData from ViewModel.");
        // isLoading.postValue(true); // 不再由此方法管理加载状态
        // messageDisplayManager.reset();
        // messageAppendManager.reset();
        // if (messages == null) {
        //     messages = new ArrayList<>();
        // }
        // displayMessagesLiveData.postValue(new ArrayList<>(messages)); // 移除
        // isLoading.postValue(false);
    }

     /**
     * 在UI上追加单条消息 (无动画) - 此方法已废弃
     * 数据应通过 ViewModel 的 LiveData 驱动
     * @param message 要追加的 UI 消息
     */
    @Deprecated
    public void addMessageToUI(ChatUIMessage message) {
         Log.w(TAG, "addMessageToUI is deprecated. UI should observe LiveData from ViewModel.");
        // if (message == null) return;
        // ... (原列表更新逻辑移除)
        // displayMessagesLiveData.postValue(updatedMessages);
    }

    /**
     * 清空UI上的所有消息 - 此方法已废弃
     * 数据库删除操作会通过 LiveData 自动更新 UI
     * 如需触发清空动画或状态，应提供专门方法
     */
    @Deprecated
    public void clearMessagesUI() {
        Log.w(TAG, "clearMessagesUI is deprecated. Deleting messages from DB will update UI via LiveData.");
        // isLoading.postValue(true); // 不再由此方法管理加载状态
        // messageDisplayManager.reset();
        // messageAppendManager.reset();
        // displayMessagesLiveData.postValue(new ArrayList<>()); // 移除
        // isLoading.postValue(false);
        // 如果确实需要单独清空UI（不删数据），可以保留部分逻辑或重构
    }

    /**
     * 取消正在进行的消息显示/追加动画
     */
    public void cancelMessageDisplay() {
        Log.d(TAG, "取消消息显示/追加动画");
        messageDisplayManager.reset();
        messageAppendManager.reset();
        isLoading.postValue(false);
    }

    /**
     * 重置 UI 管理器状态 (主要是错误和加载状态)
     */
    public void reset() {
        Log.d(TAG, "重置 ChatUIManager 状态");
        cancelMessageDisplay(); // 停止动画
        errorMessage.postValue(null); // 清除错误
        isLoading.postValue(false); // 停止加载
    }

    /**
     * 设置错误消息
     * @param error 错误信息
     */
    public void postError(String error) {
        Log.e(TAG, "发生错误: " + error);
        errorMessage.postValue(error);
    }

    /**
     * 设置加载状态
     * @param loading 是否正在加载
     */
    public void setLoading(boolean loading) {
        isLoading.postValue(loading);
    }


    // --- LiveData Getters ---

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    // getDisplayMessages() 方法已移除
    // public LiveData<List<ChatUIMessage>> getDisplayMessages() {
    //     return displayMessagesLiveData;
    // }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }
} 