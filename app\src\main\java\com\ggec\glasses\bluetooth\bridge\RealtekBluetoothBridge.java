package com.ggec.glasses.bluetooth.bridge;

import android.content.Context;
import android.util.Log;

import com.ggec.glasses.bluetooth.model.BluetoothDeviceModel;
import com.realtek.sdk.RealtekBluetoothManager;
import com.realtek.sdk.device.DeviceInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 使用Realtek SDK的蓝牙桥接实现
 * 将Realtek蓝牙SDK的功能通过桥接模式集成到应用中
 */
public class RealtekBluetoothBridge implements BluetoothBridge, RealtekBluetoothManager.RealtekBluetoothListener {
    private static final String TAG = "RealtekBluetoothBridge";
    
    private final Context context;
    private final RealtekBluetoothManager bluetoothManager;
    
    private DeviceDiscoveryListener discoveryListener;
    private ConnectionListener connectionListener;
    
    private final List<BluetoothDeviceModel> pairedDevices = new ArrayList<>();
    private final List<BluetoothDeviceModel> availableDevices = new ArrayList<>();
    
    /**
     * 构造方法
     * @param context 应用上下文
     */
    public RealtekBluetoothBridge(Context context) {
        this.context = context;
        this.bluetoothManager = RealtekBluetoothManager.getInstance(context);
        this.bluetoothManager.setListener(this);
    }
    
    @Override
    public boolean startDeviceDiscovery() {
        // 清除可用设备列表但保留已配对设备
        availableDevices.clear();
        // 开始扫描
        return bluetoothManager.startScan();
    }
    
    @Override
    public boolean stopDeviceDiscovery() {
        bluetoothManager.stopScan();
        return true;
    }
    
    @Override
    public boolean isDiscovering() {
        return bluetoothManager.isScanning();
    }
    
    @Override
    public List<BluetoothDeviceModel> getPairedDevices() {
        List<DeviceInfo> realtekPairedDevices = bluetoothManager.getPairedDevices();
        pairedDevices.clear();
        
        for (DeviceInfo deviceInfo : realtekPairedDevices) {
            BluetoothDeviceModel model = BluetoothDeviceConverter.fromRealtekDeviceInfo(deviceInfo);
            if (model != null) {
                pairedDevices.add(model);
            }
        }
        
        return new ArrayList<>(pairedDevices);
    }
    
    @Override
    public List<BluetoothDeviceModel> getAvailableDevices() {
        return new ArrayList<>(availableDevices);
    }
    
    @Override
    public boolean connectToDevice(BluetoothDeviceModel device) {
        if (device == null) return false;
        return bluetoothManager.connect(device.getAddress());
    }
    
    @Override
    public boolean pairWithDevice(BluetoothDeviceModel device) {
        // Realtek SDK会处理配对过程，这里直接尝试连接
        return connectToDevice(device);
    }
    
    @Override
    public boolean disconnectFromDevice(BluetoothDeviceModel device) {
        bluetoothManager.disconnect();
        return true;
    }
    
    @Override
    public boolean isBluetoothEnabled() {
        return bluetoothManager.isBluetoothEnabled();
    }
    
    @Override
    public boolean isDeviceConnected(BluetoothDeviceModel device) {
        if (!bluetoothManager.isConnected()) return false;
        
        DeviceInfo connectedDevice = bluetoothManager.getConnectedDeviceInfo();
        return connectedDevice != null && 
               device != null && 
               connectedDevice.getAddress().equals(device.getAddress());
    }
    
    @Override
    public void setDeviceDiscoveryListener(DeviceDiscoveryListener listener) {
        this.discoveryListener = listener;
    }
    
    @Override
    public void setConnectionListener(ConnectionListener listener) {
        this.connectionListener = listener;
    }
    
    @Override
    public void registerReceivers() {
        // Realtek SDK会自行管理接收器，无需额外注册
    }
    
    @Override
    public void unregisterReceivers() {
        // Realtek SDK会自行管理接收器，无需额外注销
    }
    
    @Override
    public void release() {
        bluetoothManager.release();
    }
    
    // ===== 实现RealtekBluetoothListener接口 =====
    
    @Override
    public void onBluetoothStateChanged(boolean enabled) {
        if (connectionListener != null) {
            connectionListener.onBluetoothStateChanged(enabled);
        }
    }
    
    @Override
    public void onScanStarted() {
        if (discoveryListener != null) {
            discoveryListener.onDiscoveryStarted();
        }
    }
    
    @Override
    public void onDeviceFound(DeviceInfo device) {
        BluetoothDeviceModel model = BluetoothDeviceConverter.fromRealtekDeviceInfo(device);
        if (model != null) {
            // 检查设备是否已在列表中
            boolean deviceExists = false;
            for (int i = 0; i < availableDevices.size(); i++) {
                if (availableDevices.get(i).getAddress().equals(model.getAddress())) {
                    availableDevices.set(i, model);
                    deviceExists = true;
                    break;
                }
            }
            
            // 如果是新设备，添加到列表
            if (!deviceExists && !device.isPaired()) {
                availableDevices.add(model);
            }
            
            if (discoveryListener != null) {
                discoveryListener.onDeviceFound(model);
            }
        }
    }
    
    @Override
    public void onScanFinished(List<DeviceInfo> devices) {
        if (discoveryListener != null) {
            discoveryListener.onDiscoveryFinished();
        }
    }
    
    @Override
    public void onPairedDevicesLoaded(List<DeviceInfo> devices) {
        pairedDevices.clear();
        for (DeviceInfo deviceInfo : devices) {
            BluetoothDeviceModel model = BluetoothDeviceConverter.fromRealtekDeviceInfo(deviceInfo);
            if (model != null) {
                pairedDevices.add(model);
            }
        }
    }
    
    @Override
    public void onConnected(DeviceInfo device) {
        BluetoothDeviceModel model = BluetoothDeviceConverter.fromRealtekDeviceInfo(device);
        if (model != null && connectionListener != null) {
            connectionListener.onDeviceConnected(model);
        }
    }
    
    @Override
    public void onDisconnected(DeviceInfo device) {
        BluetoothDeviceModel model = BluetoothDeviceConverter.fromRealtekDeviceInfo(device);
        if (model != null && connectionListener != null) {
            connectionListener.onDeviceDisconnected(model);
        }
    }
    
    @Override
    public void onConnecting(DeviceInfo device) {
        // 处理连接中状态（如需要）
    }
    
    @Override
    public void onConnectionFailed(DeviceInfo device, int errorCode) {
        // 处理连接失败
        Log.e(TAG, "连接设备失败: " + device.getName() + ", 错误码: " + errorCode);
    }
    
    @Override
    public void onDataReceived(DeviceInfo device, byte[] data) {
        // 处理接收到的数据
    }
    
    @Override
    public void onDataSent(DeviceInfo device, byte[] data) {
        // 处理已发送的数据
    }
    
    @Override
    public void onSendDataFailed(DeviceInfo device, byte[] data, Exception exception) {
        // 处理数据发送失败
    }
    
    @Override
    public void onError(int errorType, int errorCode) {
        Log.e(TAG, "Realtek蓝牙错误: 类型 " + errorType + ", 错误码 " + errorCode);
    }
} 