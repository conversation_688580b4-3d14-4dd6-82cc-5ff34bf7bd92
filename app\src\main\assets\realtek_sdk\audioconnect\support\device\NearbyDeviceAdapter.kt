/*
 * Copyright (c) 2024. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */
package com.realsil.sdk.audioconnect.support.device

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.realsil.sdk.audioconnect.repository.database.general.DeviceInfoEntity
import com.realsil.sdk.audioconnect.support.R
import com.realsil.sdk.bbpro.MultiPeripheralConnectionManager
import com.realsil.sdk.bbpro.core.peripheral.ConnectionParameters
import com.realsil.sdk.support.recyclerview.BaseRecyclerViewAdapter

/**
 * <AUTHOR>
 * @date 2024/09/10
 */
class NearbyDeviceAdapter(context: Context, entityList: ArrayList<DeviceInfoEntity> = ArrayList()) :
    BaseRecyclerViewAdapter<DeviceInfoEntity, NearbyDeviceAdapter.BluetoothDeviceViewHolder>(
        context, entityList) {
    interface OnAdapterListener {
        fun onItemClick(entity: DeviceInfoEntity)
    }

    private var adapterListener: OnAdapterListener? = null

    fun setOnAdapterListener(listener: OnAdapterListener?) {
        adapterListener = listener
    }

    inner class BluetoothDeviceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvDeviceName: TextView = itemView.findViewById(R.id.tvDeviceName)
        val tvDeviceAddress: TextView = itemView.findViewById(R.id.tvDeviceAddress)
        val tvTransportChannel: TextView = itemView.findViewById(R.id.tvTransportChannel)
        val tvConnState: TextView = itemView.findViewById(R.id.tvConnState)

        init {
            itemView.setOnClickListener { v: View? ->
                val position = adapterPosition
                val entity = getEntity(position) ?: return@setOnClickListener
                if (adapterListener != null) {
                    adapterListener!!.onItemClick(entity)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BluetoothDeviceViewHolder {
        return BluetoothDeviceViewHolder(
            layoutInflater.inflate(
                R.layout.rtk_pair_itemview_nearby_device,
                parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: BluetoothDeviceViewHolder, position: Int) {
        val entity = getEntity(position) ?: return
        holder.tvDeviceName.text = entity.deviceName
        holder.tvDeviceAddress.text = entity.deviceAddress
        when (entity.transportChannel) {
            ConnectionParameters.CHANNEL_TYPE_SPP -> {
                holder.tvTransportChannel.text = "Channel: SPP"
            }
            ConnectionParameters.CHANNEL_TYPE_GATT -> {
                holder.tvTransportChannel.text = "Channel: GATT"
            }
            else -> {
                holder.tvTransportChannel.text = "Channel: "
            }
        }
        val deviceAddr = entity.deviceAddress

        if ( /*entity.isConnected &&*/MultiPeripheralConnectionManager.getInstance(context)
                .getPeripheralConnectionManager(deviceAddr).isConnected(deviceAddr)
        ) {
            holder.tvConnState.setText(R.string.text_nearby_device_conn_state_connected)
            holder.tvConnState.setTextColor(
                ContextCompat.getColor(
                    context,
                    R.color.material_green_500
                )
            )
        } else {
            holder.tvConnState.setText(R.string.text_nearby_device_conn_state_disconnected)
            holder.tvConnState.setTextColor(
                ContextCompat.getColor(
                    context,
                    R.color.material_grey_500
                )
            )
        }
    }
}
