<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 播放控制器背景渐变层，从底部到顶部透明 -->
    <View
        android:id="@+id/exo_controller_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        android:alpha="0.3" />

    <!-- 底部控制栏 - 减小高度 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="#80000000"
        android:orientation="vertical"
        android:padding="4dp">

        <!-- 进度条 - 降低高度 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- 当前播放时间 -->
            <TextView
                android:id="@+id/exo_position"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:paddingHorizontal="4dp"
                android:includeFontPadding="false" />

            <!-- 进度条 - 降低高度 -->
            <com.google.android.exoplayer2.ui.DefaultTimeBar
                android:id="@+id/exo_progress"
                android:layout_width="0dp"
                android:layout_height="20dp"
                android:layout_weight="1"
                app:played_color="@color/interactive_active"
                app:scrubber_color="@color/interactive_active"
                app:buffered_color="@color/white" />

            <!-- 总时长 -->
            <TextView
                android:id="@+id/exo_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:paddingHorizontal="4dp"
                android:includeFontPadding="false" />
        </LinearLayout>

    </LinearLayout>
    
    <!-- 播放/暂停按钮 - 移至中心位置 -->
    <FrameLayout
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_gravity="center">
        
        <!-- 播放按钮 -->
        <ImageView
            android:id="@+id/exo_play"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/ic_public_play"
            android:padding="8dp"
            app:tint="@color/white"
            android:contentDescription="@string/play_button" />
        
        <!-- 暂停按钮 -->
        <ImageView
            android:id="@+id/exo_pause"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/ic_public_pause"
            android:padding="8dp"
            app:tint="@color/white"
            android:contentDescription="@string/pause_button" />
    </FrameLayout>

    <!-- 加载中指示器 - 设置为默认隐藏，只在缓冲时显示 -->
    <ProgressBar
        android:id="@+id/exo_buffering"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_gravity="center"
        android:indeterminate="true"
        android:visibility="gone"
        android:indeterminateTint="@color/interactive_active" />

</FrameLayout> 