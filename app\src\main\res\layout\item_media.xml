<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="1dp">

    <!-- 媒体图像容器，保持1:1比例 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_media_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 媒体图像 -->
        <ImageView
            android:id="@+id/iv_media_thumbnail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:contentDescription="媒体缩略图" />

        <!-- 选中状态背景层 -->
        <View
            android:id="@+id/view_selected_background"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#80FFFFFF"
            android:visibility="gone"
            android:contentDescription="选中状态背景" />

        <!-- 视频播放图标，初始设为不可见 -->
        <ImageView
            android:id="@+id/iv_play_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_public_play_norm"
            android:visibility="gone"
            android:alpha="0.9"
            android:contentDescription="播放视频"
            app:tint="@color/white" />

        <!-- 视频时长显示 -->
        <TextView
            android:id="@+id/tv_video_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|start"
            android:layout_margin="4dp"
            android:paddingStart="4dp"
            android:paddingEnd="4dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            android:text="00:00" />

        <!-- 选择框，初始设为不可见 -->
        <ImageView
            android:id="@+id/iv_select_checkbox"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="4dp"
            android:layout_marginBottom="4dp"
            android:src="@drawable/ic_gallery_select_checkbox_unchecked"
            android:visibility="gone"
            android:contentDescription="选择框" />

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 