package com.ggec.glasses.asr.config;

import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 语音识别服务配置类
 * 负责管理ASR服务的配置参数，包括API密钥和模型参数
 */
public class AsrConfig {
    private static final String TAG = "AsrConfig";
    private static final String SECRETS_FILE = "secrets.properties";
    private static final String DASHSCOPE_API_KEY = "DASHSCOPE_API_KEY";
    
    // 默认配置
    private static final String DEFAULT_MODEL = "paraformer-realtime-v2";
    private static final String DEFAULT_FORMAT = "pcm";
    private static final int DEFAULT_SAMPLE_RATE = 16000;
    
    private String apiKey;
    private String model;
    private String format;
    private int sampleRate;
    
    private static AsrConfig instance;
    
    private AsrConfig(Context context) {
        loadDefaults();
        loadSecrets(context);
    }
    
    /**
     * 获取AsrConfig单例实例
     * @param context 应用上下文
     * @return AsrConfig实例
     */
    public static synchronized AsrConfig getInstance(Context context) {
        if (instance == null) {
            instance = new AsrConfig(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 加载默认配置
     */
    private void loadDefaults() {
        this.model = DEFAULT_MODEL;
        this.format = DEFAULT_FORMAT;
        this.sampleRate = DEFAULT_SAMPLE_RATE;
    }
    
    /**
     * 从assets目录的secrets.properties文件加载密钥
     * @param context 应用上下文
     */
    private void loadSecrets(Context context) {
        Properties properties = new Properties();
        AssetManager assetManager = context.getAssets();
        
        try (InputStream inputStream = assetManager.open(SECRETS_FILE)) {
            properties.load(inputStream);
            this.apiKey = properties.getProperty(DASHSCOPE_API_KEY);
            
            if (this.apiKey == null || this.apiKey.equals("your_api_key_here") || this.apiKey.isEmpty()) {
                Log.w(TAG, "API密钥未设置或使用了默认值，请在secrets.properties中设置有效的API密钥");
            } else {
                Log.d(TAG, "成功加载API密钥");
            }
        } catch (IOException e) {
            Log.e(TAG, "加载secrets.properties文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取DashScope API密钥
     * @return API密钥
     */
    public String getApiKey() {
        return apiKey;
    }
    
    /**
     * 设置API密钥
     * @param apiKey API密钥
     */
    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
    
    /**
     * 获取语音识别模型名称
     * @return 模型名称
     */
    public String getModel() {
        return model;
    }
    
    /**
     * 设置语音识别模型
     * @param model 模型名称
     */
    public void setModel(String model) {
        this.model = model;
    }
    
    /**
     * 获取音频格式
     * @return 音频格式
     */
    public String getFormat() {
        return format;
    }
    
    /**
     * 设置音频格式
     * @param format 音频格式
     */
    public void setFormat(String format) {
        this.format = format;
    }
    
    /**
     * 获取音频采样率
     * @return 采样率
     */
    public int getSampleRate() {
        return sampleRate;
    }
    
    /**
     * 设置音频采样率
     * @param sampleRate 采样率
     */
    public void setSampleRate(int sampleRate) {
        this.sampleRate = sampleRate;
    }
    
    /**
     * 检查配置是否有效
     * @return 如果配置有效返回true，否则返回false
     */
    public boolean isValid() {
        return apiKey != null && !apiKey.isEmpty() && 
               !apiKey.equals("your_api_key_here") &&
               model != null && !model.isEmpty() &&
               format != null && !format.isEmpty() &&
               sampleRate > 0;
    }
}