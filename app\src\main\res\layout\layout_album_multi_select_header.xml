<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/multi_select_header"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:layout_marginTop="36dp"
    android:background="@color/comp_background_gray"
    android:visibility="gone">

    <!-- 返回/取消按钮 -->
    <ImageView
        android:id="@+id/btn_cancel_select"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="8dp"
        android:background="@drawable/btn_ripple_small_corner"
        android:clickable="true"
        android:contentDescription="取消选择"
        android:focusable="true"
        android:padding="12dp"
        android:src="@drawable/ic_public_cancel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/icon_primary" />

    <!-- 已选择项计数 -->
    <TextView
        android:id="@+id/tv_selected_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="已选择 0 项"
        android:textColor="@color/font_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btn_cancel_select"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 全选按钮 -->
    <FrameLayout
        android:id="@+id/btn_select_all_container"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/bg_select_all_button"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/btn_select_all_top"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:contentDescription="全选"
            android:src="@drawable/selector_select_all_icon" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 