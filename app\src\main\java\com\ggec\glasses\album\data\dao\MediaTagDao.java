package com.ggec.glasses.album.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.ggec.glasses.album.data.entity.MediaTag;
import com.ggec.glasses.album.data.entity.Tag;
import com.ggec.glasses.album.data.entity.Media;

import java.util.List;

/**
 * 媒体标签关联数据访问对象接口
 */
@Dao
public interface MediaTagDao {
    
    /**
     * 为媒体添加标签
     * @param mediaTag 媒体标签关联对象
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertMediaTag(MediaTag mediaTag);
    
    /**
     * 批量为媒体添加标签
     * @param mediaTagList 媒体标签关联对象列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertMediaTagList(List<MediaTag> mediaTagList);
    
    /**
     * 移除媒体的标签
     * @param mediaTag 媒体标签关联对象
     */
    @Delete
    void deleteMediaTag(MediaTag mediaTag);
    
    /**
     * 移除媒体的指定标签
     * @param mediaId 媒体ID
     * @param tagId 标签ID
     */
    @Query("DELETE FROM media_tag WHERE media_id = :mediaId AND tag_id = :tagId")
    void removeTagFromMedia(long mediaId, long tagId);
    
    /**
     * 移除媒体的所有标签
     * @param mediaId 媒体ID
     */
    @Query("DELETE FROM media_tag WHERE media_id = :mediaId")
    void removeAllTagsFromMedia(long mediaId);
    
    /**
     * 移除标签与所有媒体的关联
     * @param tagId 标签ID
     */
    @Query("DELETE FROM media_tag WHERE tag_id = :tagId")
    void removeTagFromAllMedia(long tagId);
    
    /**
     * 获取媒体的所有标签
     * @param mediaId 媒体ID
     * @return 媒体的标签列表
     */
    @Query("SELECT t.* FROM tag t INNER JOIN media_tag mt ON t.id = mt.tag_id WHERE mt.media_id = :mediaId ORDER BY t.name ASC")
    List<Tag> getTagsForMedia(long mediaId);
    
    /**
     * 获取媒体的所有标签（LiveData版本）
     * @param mediaId 媒体ID
     * @return 包含媒体标签列表的LiveData
     */
    @Query("SELECT t.* FROM tag t INNER JOIN media_tag mt ON t.id = mt.tag_id WHERE mt.media_id = :mediaId ORDER BY t.name ASC")
    LiveData<List<Tag>> getTagsForMediaLive(long mediaId);
    
    /**
     * 获取带有指定标签的所有媒体
     * @param tagId 标签ID
     * @return 带有指定标签的媒体列表
     */
    @Query("SELECT m.* FROM media m INNER JOIN media_tag mt ON m.id = mt.media_id WHERE mt.tag_id = :tagId AND m.is_deleted = 0 ORDER BY m.creation_date DESC")
    List<Media> getMediaWithTag(long tagId);
    
    /**
     * 获取带有指定标签的所有媒体（LiveData版本）
     * @param tagId 标签ID
     * @return 包含带有指定标签的媒体列表的LiveData
     */
    @Query("SELECT m.* FROM media m INNER JOIN media_tag mt ON m.id = mt.media_id WHERE mt.tag_id = :tagId AND m.is_deleted = 0 ORDER BY m.creation_date DESC")
    LiveData<List<Media>> getMediaWithTagLive(long tagId);
    
    /**
     * 检查媒体是否有指定标签
     * @param mediaId 媒体ID
     * @param tagId 标签ID
     * @return 如果媒体有指定标签，则返回true；否则返回false
     */
    @Query("SELECT COUNT(*) > 0 FROM media_tag WHERE media_id = :mediaId AND tag_id = :tagId")
    boolean hasMediaTag(long mediaId, long tagId);
    
    /**
     * 获取媒体的标签数量
     * @param mediaId 媒体ID
     * @return 媒体的标签数量
     */
    @Query("SELECT COUNT(*) FROM media_tag WHERE media_id = :mediaId")
    int getTagCountForMedia(long mediaId);
    
    /**
     * 获取使用指定标签的媒体数量
     * @param tagId 标签ID
     * @return 使用指定标签的媒体数量
     */
    @Query("SELECT COUNT(*) FROM media_tag mt INNER JOIN media m ON mt.media_id = m.id WHERE mt.tag_id = :tagId AND m.is_deleted = 0")
    int getMediaCountWithTag(long tagId);
} 