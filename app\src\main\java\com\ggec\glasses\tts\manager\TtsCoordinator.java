package com.ggec.glasses.tts.manager;

import android.app.Application;
import android.media.AudioFormat;
import android.util.Log;
import androidx.annotation.NonNull;

import com.ggec.glasses.tts.service.CosyVoiceTtsService;
import com.ggec.glasses.tts.service.StreamingTtsCallback;
import com.ggec.glasses.tts.service.TtsServiceCallback;
import com.ggec.glasses.utils.SecretsLoader;

import java.nio.ByteBuffer;

/**
 * 协调 TTS 服务和音频播放的管理器。
 * 支持文件播放和流式播放。
 */
public class TtsCoordinator {

    private static final String TAG = "TtsCoordinator";
    private static final String COSYVOICE_API_KEY_NAME = "COSYVOICE_API_KEY";

    // --- 流式播放参数 ---
    // 必须与 CosyVoiceTtsService.STREAMING_FORMAT 请求的格式匹配
    private static final int STREAMING_SAMPLE_RATE = 22050;
    private static final int STREAMING_CHANNEL_CONFIG = AudioFormat.CHANNEL_OUT_MONO;
    private static final int STREAMING_AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;

    private final Application application;
    private final CosyVoiceTtsService cosyVoiceTtsService;
    private final AudioPlaybackManager audioPlaybackManager; // 用于基于文件的播放
    private StreamingAudioPlayer streamingAudioPlayer; // 用于流式播放
    private final String apiKey;

    public TtsCoordinator(Application application) {
        this.application = application;
        this.apiKey = SecretsLoader.getSecret(application, COSYVOICE_API_KEY_NAME);
        this.audioPlaybackManager = new AudioPlaybackManager(application);

        if (this.apiKey == null || this.apiKey.isEmpty() || this.apiKey.equals("your_cosyvoice_api_key")) {
            Log.e(TAG, "CosyVoice API Key not found or not configured in secrets.properties! TTS will be disabled.");
            this.cosyVoiceTtsService = null; // API Key无效，禁用服务
        } else {
            this.cosyVoiceTtsService = new CosyVoiceTtsService(application, this.apiKey);
        }
    }

    /**
     * 合成指定文本并播放。
     * @param text 要合成和播放的文本。
     */
    public void synthesizeAndPlay(String text) {
        if (cosyVoiceTtsService == null) {
            Log.w(TAG, "CosyVoiceTtsService is not available (check API Key configuration). Skipping TTS.");
            return;
        }
        if (text == null || text.trim().isEmpty()) {
            Log.w(TAG, "Text to synthesize is empty. Skipping TTS.");
            return;
        }

        Log.d(TAG, "Requesting TTS synthesis and playback for text: " + text);

        cosyVoiceTtsService.synthesizeAsync(text, new TtsServiceCallback() {
            @Override
            public void onSuccess(String audioFilePath) {
                Log.d(TAG, "TTS synthesis successful. Playing audio file: " + audioFilePath);
                audioPlaybackManager.playAudioFile(audioFilePath);
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "TTS 合成失败: " + error);
                // 这里可以根据需要添加错误处理逻辑，例如通知用户
            }
        });
    }

    /**
     * 流式合成并播放指定文本。
     * @param text 要合成和播放的文本。
     */
    public void streamAndPlay(String text) {
        if (cosyVoiceTtsService == null) {
            Log.w(TAG, "CosyVoiceTtsService 不可用 (请检查 API Key 配置)。跳过流式 TTS。");
            return;
        }
        if (text == null || text.trim().isEmpty()) {
            Log.w(TAG, "要流式合成的文本为空。跳过流式 TTS。");
            return;
        }

        Log.d(TAG, "请求流式 TTS 合成和播放，文本: " + text);

        // 确保释放任何之前的流式播放器
        releaseStreamingPlayer();

        // 创建并初始化流式播放器
        streamingAudioPlayer = new StreamingAudioPlayer(
                STREAMING_SAMPLE_RATE,
                STREAMING_CHANNEL_CONFIG,
                STREAMING_AUDIO_FORMAT
        );

        if (!streamingAudioPlayer.initialize()) {
            Log.e(TAG, "初始化 StreamingAudioPlayer 失败。中止流式 TTS。");
            releaseStreamingPlayer(); // 清理
            return;
        }

        // 开始流式合成
        cosyVoiceTtsService.synthesizeStreaming(text, new StreamingTtsCallback() {
            @Override
            public void onAudioChunk(ByteBuffer chunk) {
                if (streamingAudioPlayer != null && streamingAudioPlayer.isInitialized()) {
                    // 在收到第一个块时开始播放
                    if (!streamingAudioPlayer.isPlaying()) {
                        streamingAudioPlayer.play();
                    }
                    // 将块写入播放器
                    streamingAudioPlayer.write(chunk);
                } else {
                    Log.w(TAG, "收到音频块但播放器未就绪或已释放。");
                }
            }

            @Override
            public void onStreamingComplete() {
                Log.d(TAG, "流式 TTS 完成。");
                // 流式传输结束后释放播放器
                releaseStreamingPlayer();
            }

            @Override
            public void onError(String error, Exception e) {
                Log.e(TAG, "流式 TTS 失败: " + error, e);
                // 确保在出错时释放播放器
                releaseStreamingPlayer();
                // TODO: 考虑为错误添加用户反馈
            }
        });
    }

    /**
     * 安全地释放流式播放器资源。
     */
    private void releaseStreamingPlayer() {
        if (streamingAudioPlayer != null) {
            Log.d(TAG, "释放 StreamingAudioPlayer 资源。");
            try {
                streamingAudioPlayer.release();
            } catch (Exception e) {
                Log.e(TAG, "释放 StreamingAudioPlayer 时出错", e);
            }
            streamingAudioPlayer = null;
        }
    }

    /**
     * 释放所有资源（文件播放器和流式播放器）。
     */
    public void release() {
        Log.d(TAG, "释放 TtsCoordinator 资源。");
        if (audioPlaybackManager != null) {
            audioPlaybackManager.release();
        }
        releaseStreamingPlayer(); // 同时释放流式播放器
    }
} 