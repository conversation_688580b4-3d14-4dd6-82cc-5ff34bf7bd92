<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    android:background="@drawable/bg_settings_card"
    android:elevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="20dp"
        android:paddingBottom="20dp"
        android:minHeight="72dp">

        <ImageView
            android:id="@+id/iv_volume_icon"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/ic_public_volume"
            app:tint="@color/icon_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <SeekBar
            android:id="@+id/seekbar_volume"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:progressDrawable="@drawable/custom_seekbar_progress"
            android:thumb="@drawable/custom_seekbar_thumb"
            android:max="100"
            android:progress="50"
            android:minHeight="20dp"
            android:maxHeight="20dp"
            android:paddingStart="0dp"
            android:paddingEnd="0dp"
            android:splitTrack="false"
            android:background="@null"
            app:layout_constraintStart_toEndOf="@id/iv_volume_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout> 