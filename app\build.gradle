plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.ggec.glasses'
    compileSdk 35

    defaultConfig {
        applicationId "com.ggec.glasses"
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // Room数据库迁移相关配置
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += ["room.schemaLocation": "$projectDir/schemas".toString(),
                              "room.incremental": "true",
                              "room.expandProjection": "true"]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    
    // 解决依赖冲突
    configurations.all {
        resolutionStrategy {
            // 强制使用特定版本的依赖
            force 'com.google.guava:guava:31.1-android'
            force 'com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava'
        }
    }

    lint {
        baseline = file("lint-baseline.xml")
        abortOnError = false
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    
    // Room数据库相关依赖
    implementation libs.room.runtime
    annotationProcessor libs.room.compiler
    
    // LiveData相关依赖
    implementation libs.lifecycle.livedata
    implementation libs.lifecycle.viewmodel
    
    // Glide图片加载库
    implementation libs.glide
    annotationProcessor libs.glide.compiler
    
    // AndroidX Preference库 - 解决rtk-support库依赖问题
    implementation 'androidx.preference:preference:1.2.1'

    // 其他必要依赖
    implementation "androidx.recyclerview:recyclerview:1.2.1"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:2.5.1"
    
    // PhotoView库 - 支持图片缩放和手势操作
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'
    
    // ExoPlayer - 视频播放器
    implementation 'com.google.android.exoplayer:exoplayer-core:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'
    
    // DashScope SDK - 阿里云语音识别 (Updated Version for ttsv2)
    implementation('com.alibaba:dashscope-sdk-java:2.18.3') {
        // 排除冲突依赖
        exclude group: 'com.google.guava', module: 'guava'
    }
    
    // RxJava - 用于响应式异步处理
    implementation 'io.reactivex.rxjava2:rxjava:2.2.21'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    
    // Retrofit & Gson - 用于网络请求
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.3' // 可选：日志拦截器
    
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}