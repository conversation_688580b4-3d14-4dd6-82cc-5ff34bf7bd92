package com.ggec.glasses.device.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.ggec.glasses.device.manager.DeviceManager;
import com.ggec.glasses.device.model.Device;

import java.util.List;

/**
 * 设备视图模型
 * 负责连接UI和设备管理器
 */
public class DeviceViewModel extends AndroidViewModel implements DeviceManager.DeviceStateListener {

    private final DeviceManager deviceManager;
    private final MutableLiveData<Device> currentDeviceLiveData = new MutableLiveData<>();
    private final MutableLiveData<List<Device>> deviceListLiveData = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isConnectingLiveData = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> isDisconnectingLiveData = new MutableLiveData<>(false);

    public DeviceViewModel(@NonNull Application application) {
        super(application);
        
        // 获取设备管理器实例
        deviceManager = DeviceManager.getInstance(application);
        deviceManager.setDeviceStateListener(this);
        
        // 初始化数据
        updateDeviceData();
    }

    /**
     * 更新设备数据
     */
    private void updateDeviceData() {
        currentDeviceLiveData.setValue(deviceManager.getCurrentDevice());
        deviceListLiveData.setValue(deviceManager.getDeviceList());
    }

    /**
     * 获取当前设备LiveData
     * @return 当前设备LiveData
     */
    public LiveData<Device> getCurrentDevice() {
        return currentDeviceLiveData;
    }

    /**
     * 获取设备列表LiveData
     * @return 设备列表LiveData
     */
    public LiveData<List<Device>> getDeviceList() {
        return deviceListLiveData;
    }

    /**
     * 获取连接状态LiveData
     * @return 连接状态LiveData
     */
    public LiveData<Boolean> getIsConnecting() {
        return isConnectingLiveData;
    }

    /**
     * 获取断开连接状态LiveData
     * @return 断开连接状态LiveData
     */
    public LiveData<Boolean> getIsDisconnecting() {
        return isDisconnectingLiveData;
    }

    /**
     * 连接设备
     * @param device 要连接的设备
     */
    public void connectDevice(Device device) {
        isConnectingLiveData.setValue(true);
        deviceManager.connectDevice(device);
    }

    /**
     * 断开当前设备连接
     */
    public void disconnectCurrentDevice() {
        isDisconnectingLiveData.setValue(true);
        deviceManager.disconnectCurrentDevice();
    }

    // 实现DeviceManager.DeviceStateListener接口的方法
    @Override
    public void onDeviceConnected(Device device) {
        isConnectingLiveData.setValue(false);
        updateDeviceData();
    }

    @Override
    public void onDeviceDisconnected(Device device) {
        isDisconnectingLiveData.setValue(false);
        updateDeviceData();
    }

    @Override
    public void onDeviceStateChanged(Device device) {
        updateDeviceData();
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        // 移除监听器，避免内存泄漏
        deviceManager.setDeviceStateListener(null);
    }
} 