package com.ggec.glasses.album.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.ggec.glasses.album.data.entity.Album;

import java.util.List;

/**
 * 相册数据访问对象接口
 */
@Dao
public interface AlbumDao {
    
    /**
     * 插入一个相册对象
     * @param album 要插入的相册对象
     * @return 新插入记录的ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertAlbum(Album album);
    
    /**
     * 更新相册对象
     * @param album 要更新的相册对象
     */
    @Update
    void updateAlbum(Album album);
    
    /**
     * 删除相册对象
     * @param album 要删除的相册对象
     */
    @Delete
    void deleteAlbum(Album album);
    
    /**
     * 根据ID获取相册对象
     * @param albumId 相册ID
     * @return 相册对象
     */
    @Query("SELECT * FROM album WHERE id = :albumId")
    Album getAlbumById(long albumId);
    
    /**
     * 根据ID获取相册对象（LiveData版本）
     * @param albumId 相册ID
     * @return 包含相册对象的LiveData
     */
    @Query("SELECT * FROM album WHERE id = :albumId")
    LiveData<Album> getAlbumByIdLive(long albumId);
    
    /**
     * 获取所有相册
     * @return 相册列表
     */
    @Query("SELECT * FROM album ORDER BY name ASC")
    List<Album> getAllAlbums();
    
    /**
     * 获取所有相册（LiveData版本）
     * @return 包含相册列表的LiveData
     */
    @Query("SELECT * FROM album ORDER BY name ASC")
    LiveData<List<Album>> getAllAlbumsLive();
    
    /**
     * 根据名称模糊查询相册
     * @param name 相册名称（部分匹配）
     * @return 匹配的相册列表
     */
    @Query("SELECT * FROM album WHERE name LIKE '%' || :name || '%' ORDER BY name ASC")
    List<Album> searchAlbumsByName(String name);
    
    /**
     * 更新相册封面
     * @param albumId 相册ID
     * @param mediaId 媒体ID (可以为 null 来清除封面)
     */
    @Query("UPDATE album SET cover_media_id = :mediaId, modification_date = datetime('now') WHERE id = :albumId")
    void updateAlbumCover(long albumId, Long mediaId);
    
    /**
     * 获取相册数量
     * @return 相册总数
     */
    @Query("SELECT COUNT(*) FROM album")
    int getAlbumCount();
    
    /**
     * 根据名称查找相册
     * @param name 相册名称（精确匹配）
     * @return 匹配的相册对象，如果不存在则返回null
     */
    @Query("SELECT * FROM album WHERE name = :name LIMIT 1")
    Album getAlbumByName(String name);
} 