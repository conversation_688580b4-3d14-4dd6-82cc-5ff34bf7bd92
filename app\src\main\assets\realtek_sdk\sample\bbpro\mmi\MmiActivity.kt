/*
 * Copyright (c) 2017-2022. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.bbpro.mmi

import android.os.Bundle
import com.realsil.bbpro.R
import com.realsil.sdk.audioconnect.support.AudioConnectActivity
import com.realsil.sdk.audioconnect.support.databinding.ActivityFragmentBinding

/**
 *
 * <AUTHOR>
 * @date 2018/3/8
 */

class MmiActivity : AudioConnectActivity<ActivityFragmentBinding>(ActivityFragmentBinding::inflate) {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        supportFragmentManager.beginTransaction()
            .replace(
                R.id.fragment_content,
                MmiFragment.newInstance(null),
                MmiFragment.TAG
            )
            .commit()
    }

    companion object {
        const val EXTRA_KEY_BT_ADDR = "bt_addr"
    }

}
