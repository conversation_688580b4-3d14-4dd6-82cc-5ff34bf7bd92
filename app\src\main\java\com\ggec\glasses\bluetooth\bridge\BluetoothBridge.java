package com.ggec.glasses.bluetooth.bridge;

import android.bluetooth.BluetoothDevice;
import com.ggec.glasses.bluetooth.model.BluetoothDeviceModel;

import java.util.List;

/**
 * 蓝牙功能桥接接口
 * 定义蓝牙操作的通用接口，允许不同的蓝牙实现互换使用
 */
public interface BluetoothBridge {
    // 设备扫描
    boolean startDeviceDiscovery();
    boolean stopDeviceDiscovery();
    boolean isDiscovering();
    
    // 设备管理
    List<BluetoothDeviceModel> getPairedDevices();
    List<BluetoothDeviceModel> getAvailableDevices();
    
    // 连接管理
    boolean connectToDevice(BluetoothDeviceModel device);
    boolean pairWithDevice(BluetoothDeviceModel device);
    boolean disconnectFromDevice(BluetoothDeviceModel device);
    
    // 状态检查
    boolean isBluetoothEnabled();
    boolean isDeviceConnected(BluetoothDeviceModel device);
    
    // 监听器设置
    void setDeviceDiscoveryListener(DeviceDiscoveryListener listener);
    void setConnectionListener(ConnectionListener listener);
    
    // 资源管理
    void registerReceivers();
    void unregisterReceivers();
    void release();
    
    /**
     * 设备发现监听器接口
     */
    interface DeviceDiscoveryListener {
        void onDiscoveryStarted();
        void onDiscoveryFinished();
        void onDeviceFound(BluetoothDeviceModel device);
    }
    
    /**
     * 连接监听器接口
     */
    interface ConnectionListener {
        void onDeviceConnected(BluetoothDeviceModel device);
        void onDeviceDisconnected(BluetoothDeviceModel device);
        void onBluetoothStateChanged(boolean enabled);
        void onDevicePaired(BluetoothDeviceModel device);
    }
} 