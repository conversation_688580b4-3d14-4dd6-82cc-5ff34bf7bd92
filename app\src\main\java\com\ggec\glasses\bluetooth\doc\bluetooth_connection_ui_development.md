# 蓝牙连接页面UI开发文档

## 开发背景与目标

本文档记录蓝牙连接页面UI的开发过程和优化策略。该页面是眼镜应用蓝牙功能的核心入口，主要用于展示和管理与眼镜的蓝牙连接。

### 主要目标
- 开发蓝牙连接页面，实现设备列表展示
- 页面内容可上下滚动，但保持导航栏固定
- 复用"我的"页面的卡片样式，实现一致的UI风格
- 为后续功能实现奠定UI基础

## UI架构设计

### 页面结构
1. **顶部导航栏**：包含返回按钮和标题，固定在屏幕顶部
2. **内容区域**：包含两个主要卡片，可以上下滚动
   - **已配对设备卡片**：显示已与手机配对的蓝牙设备
   - **可用设备卡片**：显示周围可用但未配对的蓝牙设备

### 卡片设计
- 白色背景，圆角16dp
- 内边距16dp
- 顶部标题文字18sp，加粗
- 卡片间距16dp

## 实现步骤

### 1. 资源准备

添加了以下字符串资源：
- `paired_devices_title`: 已配对设备
- `available_devices_title`: 可用设备
- `no_paired_devices`: 暂无已配对设备
- `no_available_devices`: 暂无可用设备
- `searching_devices`: 正在搜索设备...

### 2. 布局实现

创建布局文件的主要部分：
1. 顶部导航栏（保留原有实现）
2. `NestedScrollView` 作为可滚动容器
3. 两个具有圆角背景的卡片（LinearLayout）
4. 每个卡片包含标题和内容区域
5. 内容区域目前包含提示文字（如"暂无已配对设备"）

### 3. 代码实现

在 `BluetoothConnectionActivity` 中：
1. 添加新增UI组件的引用
2. 初始化所有组件
3. 保留原有的沉浸式状态栏设置
4. 保留原有的窗口插入适配，确保导航栏位置正确

## 优化策略

### 1. 布局优化

- 使用 `NestedScrollView` 而非普通 `ScrollView`，以便支持嵌套滚动
- 将主容器设为 `ConstraintLayout`，优化布局性能
- 使用 `LinearLayout` 作为卡片容器，便于后续动态添加设备项

### 2. 组件复用设计

- 复用"我的"页面的卡片背景（`bg_profile_card.xml`）
- 卡片内部预留了容器，用于后续动态添加设备项
- 提示文本可通过代码控制显示/隐藏，适应不同状态

### 3. 性能考虑

- 使用 `android:clipToPadding="false"` 使滚动更自然
- 为列表容器设置 `android:minHeight`，避免空状态时卡片塌陷
- 使用 `View` 引用而非每次查找，提高性能

## 下一步开发计划

1. **设备列表项UI设计与实现**
   - 设计设备列表项布局
   - 支持显示设备名称、连接状态等信息

2. **适配器开发**
   - 创建蓝牙设备适配器，用于管理设备列表
   - 实现列表数据刷新机制

3. **扫描逻辑实现**
   - 添加蓝牙设备扫描功能
   - 实现扫描状态UI反馈

4. **连接功能开发**
   - 实现设备连接/断开功能
   - 添加连接状态UI反馈

## 注意事项

- 目前UI实现不包含实际的蓝牙功能逻辑，仅提供界面框架
- 所有文本使用字符串资源，便于后续国际化
- 布局设计遵循Material Design规范
- 保持与应用其他部分一致的UI风格 