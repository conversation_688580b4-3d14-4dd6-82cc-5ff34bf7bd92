package com.ggec.glasses.ai.data.cache.message;

import com.ggec.glasses.ai.data.cache.api.CacheCallback;
import com.ggec.glasses.ai.data.cache.api.CachePolicy;
import com.ggec.glasses.ai.data.cache.api.ICache;
import com.ggec.glasses.ai.data.cache.impl.LruCache;
import com.ggec.glasses.ai.data.db.entity.ChatMessage;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息缓存
 * 专用于缓存聊天消息
 */
public class MessageCache {
    
    /** 单例实例 */
    private static volatile MessageCache INSTANCE;
    
    /** 消息ID缓存 */
    private final ICache<Long, ChatMessage> messageCache;
    
    /** 会话消息缓存 */
    private final Map<Long, ICache<Long, ChatMessage>> conversationCaches;
    
    /** 消息缓存最大容量 */
    private static final int DEFAULT_MESSAGE_CACHE_SIZE = 100;
    
    /** 会话消息缓存最大容量 */
    private static final int DEFAULT_CONVERSATION_CACHE_SIZE = 50;
    
    /** 默认过期时间(10分钟) */
    private static final long DEFAULT_EXPIRE_TIME = 10 * 60 * 1000;
    
    /** 清理间隔(30秒) */
    private static final long DEFAULT_CLEAN_INTERVAL = 30 * 1000;
    
    /**
     * 私有构造函数
     */
    private MessageCache() {
        // 创建消息ID缓存
        CachePolicy messagePolicy = new CachePolicy.LruPolicy(
                DEFAULT_MESSAGE_CACHE_SIZE,
                DEFAULT_EXPIRE_TIME,
                DEFAULT_CLEAN_INTERVAL);
        this.messageCache = new LruCache<>(messagePolicy);
        
        // 创建会话消息缓存Map
        this.conversationCaches = new HashMap<>();
    }
    
    /**
     * 获取单例实例
     *
     * @return 消息缓存实例
     */
    public static MessageCache getInstance() {
        if (INSTANCE == null) {
            synchronized (MessageCache.class) {
                if (INSTANCE == null) {
                    INSTANCE = new MessageCache();
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * 缓存消息
     *
     * @param message 消息对象
     * @return 是否成功缓存
     */
    public boolean cacheMessage(ChatMessage message) {
        if (message == null) {
            return false;
        }
        
        // 缓存到消息ID缓存
        boolean result = messageCache.put(message.getId(), message);
        
        // 缓存到会话消息缓存
        long conversationId = message.getConversationId();
        if (conversationId > 0) {
            ICache<Long, ChatMessage> conversationCache = getConversationCache(conversationId);
            conversationCache.put(message.getId(), message);
        }
        
        return result;
    }
    
    /**
     * 批量缓存消息
     *
     * @param messages 消息列表
     * @return 成功缓存的消息数量
     */
    public int cacheMessages(List<ChatMessage> messages) {
        if (messages == null || messages.isEmpty()) {
            return 0;
        }
        
        int count = 0;
        for (ChatMessage message : messages) {
            if (cacheMessage(message)) {
                count++;
            }
        }
        
        return count;
    }
    
    /**
     * 按消息ID获取消息
     *
     * @param messageId 消息ID
     * @return 消息对象，不存在则返回null
     */
    public ChatMessage getMessage(long messageId) {
        return messageCache.get(messageId);
    }
    
    /**
     * 获取会话中的所有消息
     *
     * @param conversationId 会话ID
     * @return 消息列表
     */
    public List<ChatMessage> getMessagesByConversation(long conversationId) {
        ICache<Long, ChatMessage> conversationCache = conversationCaches.get(conversationId);
        if (conversationCache == null) {
            return new ArrayList<>();
        }
        
        // 将Map转换为List
        Map<Long, ChatMessage> messageMap = conversationCache.getAll(new ArrayList<>());
        return new ArrayList<>(messageMap.values());
    }
    
    /**
     * 从缓存移除消息
     *
     * @param messageId 消息ID
     * @return 被移除的消息，不存在则返回null
     */
    public ChatMessage removeMessage(long messageId) {
        ChatMessage message = messageCache.remove(messageId);
        
        if (message != null) {
            long conversationId = message.getConversationId();
            if (conversationId > 0) {
                ICache<Long, ChatMessage> conversationCache = conversationCaches.get(conversationId);
                if (conversationCache != null) {
                    conversationCache.remove(messageId);
                }
            }
        }
        
        return message;
    }
    
    /**
     * 更新消息缓存
     *
     * @param message 更新后的消息
     * @return 是否成功更新
     */
    public boolean updateMessage(ChatMessage message) {
        if (message == null) {
            return false;
        }
        
        // 移除旧消息
        removeMessage(message.getId());
        
        // 缓存新消息
        return cacheMessage(message);
    }
    
    /**
     * 清空所有缓存
     */
    public void clearAll() {
        messageCache.clear();
        
        for (ICache<Long, ChatMessage> cache : conversationCaches.values()) {
            cache.clear();
        }
        
        conversationCaches.clear();
    }
    
    /**
     * 清空指定会话的缓存
     *
     * @param conversationId 会话ID
     */
    public void clearConversation(long conversationId) {
        ICache<Long, ChatMessage> conversationCache = conversationCaches.get(conversationId);
        if (conversationCache != null) {
            // 从会话缓存中获取所有消息ID
            List<Long> messageIds = new ArrayList<>();
            for (Long messageId : conversationCache.getAll(new ArrayList<>()).keySet()) {
                messageIds.add(messageId);
            }
            
            // 从消息缓存中移除这些消息
            for (Long messageId : messageIds) {
                messageCache.remove(messageId);
            }
            
            // 清空会话缓存
            conversationCache.clear();
            conversationCaches.remove(conversationId);
        }
    }
    
    /**
     * 消息是否在缓存中
     *
     * @param messageId 消息ID
     * @return 是否存在于缓存中
     */
    public boolean containsMessage(long messageId) {
        return messageCache.contains(messageId);
    }
    
    /**
     * 获取会话缓存
     * 如果不存在则创建新的
     *
     * @param conversationId 会话ID
     * @return 会话缓存
     */
    private ICache<Long, ChatMessage> getConversationCache(long conversationId) {
        ICache<Long, ChatMessage> cache = conversationCaches.get(conversationId);
        
        if (cache == null) {
            // 创建新的会话缓存
            CachePolicy policy = new CachePolicy.LruPolicy(
                    DEFAULT_CONVERSATION_CACHE_SIZE,
                    DEFAULT_EXPIRE_TIME,
                    DEFAULT_CLEAN_INTERVAL);
            
            cache = new LruCache<>(policy);
            conversationCaches.put(conversationId, cache);
        }
        
        return cache;
    }
    
    /**
     * 获取缓存大小
     *
     * @return 消息缓存中的消息数量
     */
    public int size() {
        return messageCache.size();
    }
    
    /**
     * 获取会话缓存大小
     *
     * @param conversationId 会话ID
     * @return 指定会话缓存中的消息数量
     */
    public int conversationSize(long conversationId) {
        ICache<Long, ChatMessage> cache = conversationCaches.get(conversationId);
        return cache != null ? cache.size() : 0;
    }
    
    /**
     * 添加缓存回调
     *
     * @param callback 缓存回调
     */
    public void addCallback(CacheCallback<Long, ChatMessage> callback) {
        messageCache.addCallback(callback);
    }
    
    /**
     * 移除缓存回调
     *
     * @param callback 缓存回调
     */
    public void removeCallback(CacheCallback<Long, ChatMessage> callback) {
        messageCache.removeCallback(callback);
    }
} 