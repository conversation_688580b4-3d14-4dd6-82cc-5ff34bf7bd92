package com.ggec.glasses.album.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.data.repository.MediaRepository;

import java.util.List;

/**
 * 媒体视图模型类，负责处理媒体数据逻辑
 */
public class MediaViewModel extends AndroidViewModel {
    
    private final MediaRepository mediaRepository;
    private final LiveData<List<Media>> allMedia;
    private final LiveData<List<Media>> photoMedia;
    private final LiveData<List<Media>> videoMedia;
    
    /**
     * 构造函数
     * @param application 应用程序实例
     */
    public MediaViewModel(@NonNull Application application) {
        super(application);
        mediaRepository = MediaRepository.getInstance(application);
        allMedia = mediaRepository.getAllMediaLive();
        photoMedia = mediaRepository.getMediaByTypeLive("IMAGE");
        videoMedia = mediaRepository.getMediaByTypeLive("VIDEO");
    }
    
    /**
     * 获取所有媒体文件
     * @return 包含所有媒体文件的LiveData
     */
    public LiveData<List<Media>> getAllMedia() {
        return allMedia;
    }
    
    /**
     * 获取所有照片媒体文件
     * @return 包含所有照片的LiveData
     */
    public LiveData<List<Media>> getPhotoMedia() {
        return photoMedia;
    }
    
    /**
     * 获取所有视频媒体文件
     * @return 包含所有视频的LiveData
     */
    public LiveData<List<Media>> getVideoMedia() {
        return videoMedia;
    }
    
    /**
     * 永久删除媒体文件
     * @param mediaId 媒体ID
     * @param callback 删除回调
     */
    public void permanentlyDeleteMedia(long mediaId, MediaRepository.OnMediaDeletedCallback callback) {
        mediaRepository.permanentlyDeleteMedia(mediaId, callback);
    }
    
    /**
     * 加载测试数据
     * @return 加载结果LiveData
     */
    public LiveData<MediaRepository.LoadTestDataResult> loadTestData() {
        return mediaRepository.loadTestData();
    }
    
    /**
     * 清空所有媒体数据
     */
    public void clearAllMedia() {
        mediaRepository.clearAllMedia();
    }
} 