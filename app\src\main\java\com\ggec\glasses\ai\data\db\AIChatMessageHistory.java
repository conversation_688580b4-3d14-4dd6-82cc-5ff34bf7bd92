package com.ggec.glasses.ai.data.db;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.ggec.glasses.ai.data.db.converters.RoomConverters;
import com.ggec.glasses.ai.data.db.dao.ChatMessageDao;
import com.ggec.glasses.ai.data.db.entity.ChatMessage;
import com.ggec.glasses.ai.data.db.migration.MigrationFactory;

import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Room数据库抽象类，用于AI助手的聊天消息存储
 * 采用单例模式，支持数据库迁移和回调
 */
@Database(
    entities = {
        ChatMessage.class
    },
    version = 1,
    exportSchema = true
)
@TypeConverters(RoomConverters.class)
public abstract class AIChatMessageHistory extends RoomDatabase {
    
    // 单例模式
    private static volatile AIChatMessageHistory INSTANCE;
    
    // 数据库名称
    private static final String DATABASE_NAME = "glasses_ai_chat.db";
    
    // 线程池用于异步操作
    private static final ExecutorService databaseWriteExecutor = 
            Executors.newFixedThreadPool(4);
    
    // DAO访问方法
    public abstract ChatMessageDao chatMessageDao();
    
    /**
     * 获取数据库实例（默认路径）
     * @param context 应用上下文
     * @return 数据库实例
     */
    public static AIChatMessageHistory getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (AIChatMessageHistory.class) {
                if (INSTANCE == null) {
                    INSTANCE = buildDatabase(context, false);
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * 获取数据库实例（自定义路径）
     * @param context 应用上下文
     * @param useExternalStorage 是否使用外部存储
     * @return 数据库实例
     */
    public static AIChatMessageHistory getInstance(Context context, boolean useExternalStorage) {
        if (INSTANCE == null) {
            synchronized (AIChatMessageHistory.class) {
                if (INSTANCE == null) {
                    INSTANCE = buildDatabase(context, useExternalStorage);
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * 构建数据库实例
     * @param context 应用上下文
     * @param useExternalStorage 是否使用外部存储
     * @return 数据库实例
     */
    private static AIChatMessageHistory buildDatabase(Context context, boolean useExternalStorage) {
        Builder<AIChatMessageHistory> builder;
        
        if (useExternalStorage) {
            // 获取外部存储目录
            File externalFilesDir = context.getExternalFilesDir(null);
            File dbDir = new File(externalFilesDir, "database");
            
            // 确保数据库目录存在
            if (!dbDir.exists()) {
                dbDir.mkdirs();
            }
            
            // 指定数据库文件路径
            File dbFile = new File(dbDir, DATABASE_NAME);
            
            builder = Room.databaseBuilder(
                    context.getApplicationContext(),
                    AIChatMessageHistory.class,
                    dbFile.getAbsolutePath());
        } else {
            // 使用默认路径
            builder = Room.databaseBuilder(
                    context.getApplicationContext(),
                    AIChatMessageHistory.class,
                    DATABASE_NAME);
        }
        
        // 配置数据库
        return builder
                // 添加数据库迁移
                .addMigrations(MigrationFactory.getAllMigrations())
                // 配置回退策略
                .fallbackToDestructiveMigration()
                // 添加回调
                .addCallback(new Callback() {
                    @Override
                    public void onCreate(@NonNull SupportSQLiteDatabase db) {
                        super.onCreate(db);
                        // 数据库创建回调，可以在这里初始化数据
                    }
                    
                    @Override
                    public void onOpen(@NonNull SupportSQLiteDatabase db) {
                        super.onOpen(db);
                        // 数据库打开回调
                    }
                })
                .build();
    }
    
    /**
     * 获取数据库写入执行器
     * @return 执行器服务
     */
    public static ExecutorService getDatabaseWriteExecutor() {
        return databaseWriteExecutor;
    }
    
    /**
     * 销毁数据库实例，通常在应用退出时调用
     */
    public static void destroyInstance() {
        INSTANCE = null;
    }
} 