package com.ggec.glasses.album.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.ggec.glasses.album.data.entity.Tag;

import java.util.List;

/**
 * 标签数据访问对象接口
 */
@Dao
public interface TagDao {
    
    /**
     * 插入一个标签对象
     * @param tag 要插入的标签对象
     * @return 新插入记录的ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertTag(Tag tag);
    
    /**
     * 更新标签对象
     * @param tag 要更新的标签对象
     */
    @Update
    void updateTag(Tag tag);
    
    /**
     * 删除标签对象
     * @param tag 要删除的标签对象
     */
    @Delete
    void deleteTag(Tag tag);
    
    /**
     * 根据ID获取标签对象
     * @param tagId 标签ID
     * @return 标签对象
     */
    @Query("SELECT * FROM tag WHERE id = :tagId")
    Tag getTagById(long tagId);
    
    /**
     * 根据ID获取标签对象（LiveData版本）
     * @param tagId 标签ID
     * @return 包含标签对象的LiveData
     */
    @Query("SELECT * FROM tag WHERE id = :tagId")
    LiveData<Tag> getTagByIdLive(long tagId);
    
    /**
     * 获取所有标签
     * @return 标签列表
     */
    @Query("SELECT * FROM tag ORDER BY name ASC")
    List<Tag> getAllTags();
    
    /**
     * 获取所有标签（LiveData版本）
     * @return 包含标签列表的LiveData
     */
    @Query("SELECT * FROM tag ORDER BY name ASC")
    LiveData<List<Tag>> getAllTagsLive();
    
    /**
     * 根据名称模糊查询标签
     * @param name 标签名称（部分匹配）
     * @return 匹配的标签列表
     */
    @Query("SELECT * FROM tag WHERE name LIKE '%' || :name || '%' ORDER BY name ASC")
    List<Tag> searchTagsByName(String name);
    
    /**
     * 根据名称查找标签
     * @param name 标签名称（精确匹配）
     * @return 匹配的标签对象，如果不存在则返回null
     */
    @Query("SELECT * FROM tag WHERE name = :name LIMIT 1")
    Tag getTagByName(String name);
    
    /**
     * 根据ID删除标签
     * @param tagId 标签ID
     */
    @Query("DELETE FROM tag WHERE id = :tagId")
    void deleteTagById(long tagId);
} 