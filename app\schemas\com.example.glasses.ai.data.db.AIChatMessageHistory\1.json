{"formatVersion": 1, "database": {"version": 1, "identityHash": "a3457eb154294816c8d563aa27406b19", "entities": [{"tableName": "chat_message", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` INTEGER NOT NULL, `content` TEXT, `timestamp` INTEGER, `status` INTEGER, `conversation_id` INTEGER NOT NULL DEFAULT 0, `reference_id` INTEGER NOT NULL DEFAULT 0, `image_path` TEXT, `video_path` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "conversationId", "columnName": "conversation_id", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "referenceId", "columnName": "reference_id", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "imagePath", "columnName": "image_path", "affinity": "TEXT", "notNull": false}, {"fieldPath": "videoPath", "columnName": "video_path", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_chat_message_conversation_id", "unique": false, "columnNames": ["conversation_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_message_conversation_id` ON `${TABLE_NAME}` (`conversation_id`)"}, {"name": "index_chat_message_timestamp", "unique": false, "columnNames": ["timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_message_timestamp` ON `${TABLE_NAME}` (`timestamp`)"}, {"name": "index_chat_message_status", "unique": false, "columnNames": ["status"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chat_message_status` ON `${TABLE_NAME}` (`status`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'a3457eb154294816c8d563aa27406b19')"]}}