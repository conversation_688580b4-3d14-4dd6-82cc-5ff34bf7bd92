/*
 * Copyright (c) 2022 Realsil.com.cn. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Authors: <AUTHORS>
 */

package com.realsil.sample.audioconnect.smartwear.entity;

/**
 * Chat message entity.
 *
 * <AUTHOR>
 */
public class MessageInfo {

    /**
     * Msg Type: Msg sent by user.
     */
    public static final int MSG_TYPE_SEND_MESSAGE = 0;
    public static final int MSG_TYPE_RECEIVED_TEXT_MESSAGE = 1;
    public static final int MSG_TYPE_SYSTEM_MESSAGE = 2;
    public static final int MSG_TYPE_RECEIVED_IMG_MESSAGE = 3;
    public static final int MSG_TYPE_PROCESSING_MSG = 100;


    private String msgId;

    private int mMsgType;

    private String mMsgContent = "";

    private String mMsgOwner = "";

    private String mMsgImgUrl = "";

    private volatile boolean mIsTyping = false;

    private String mTypedText = "";

    private final StringBuffer mPendingText = new StringBuffer();

    private int mPositionInDisplayList;

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public int getMsgType() {
        return mMsgType;
    }

    public void setMsgType(int msgType) {
        mMsgType = msgType;
    }

    public String getMsgContent() {
        return mMsgContent;
    }

    public void setMsgContent(String msgContent) {
        mMsgContent = msgContent;
    }

    public String getMsgOwner() {
        return mMsgOwner;
    }

    public void setMsgOwner(String msgOwner) {
        mMsgOwner = msgOwner;
    }

    public int getPositionInDisplayList() {
        return mPositionInDisplayList;
    }

    public void setPositionInDisplayList(int positionInDisplayList) {
        mPositionInDisplayList = positionInDisplayList;
    }

    public String getMsgImgUrl() {
        return mMsgImgUrl;
    }

    public void setMsgImgUrl(String msgImgUrl) {
        mMsgImgUrl = msgImgUrl;
    }

    public StringBuffer getPendingText() {
        return mPendingText;
    }

    public void appendTextSnippet(String textSnippet) {
        mPendingText.append(textSnippet);
    }

    public String getTypedText() {
        return mTypedText;
    }

    public void setTypedText(String typedText) {
        mTypedText = typedText;
    }

    public boolean isTyping() {
        return mIsTyping;
    }

    public void setTyping(boolean typing) {
        mIsTyping = typing;
    }

}
