# ASR 技术实现概述

## 目录

1. [概述](#概述)
2. [技术栈](#技术栈)
3. [系统架构](#系统架构)
4. [关键参数](#关键参数)
5. [实现流程](#实现流程)
6. [性能考量](#性能考量)
7. [扩展方向](#扩展方向)

## 概述

本文档提供了智能眼镜项目中自动语音识别(ASR)功能的技术概述。ASR模块负责将用户语音转换为文本，支持实时流式识别，为智能交互提供基础。

## 技术栈

### 核心组件

| 组件 | 技术选型 | 版本 | 说明 |
|------|----------|------|------|
| ASR引擎 | 阿里云DashScope SDK | 2.18.3 | 提供高准确率的语音识别服务 |
| 响应式编程 | RxJava | 2.2.21 | 处理异步数据流 |
| 响应式Android扩展 | RxAndroid | 2.1.1 | 提供Android特定的调度器 |
| 音频处理 | Android原生音频API | - | 捕获和处理音频数据 |

### 依赖项

```groovy
implementation 'io.reactivex.rxjava2:rxjava:2.2.21'
implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
implementation 'com.alibaba:dashscope-sdk-java:2.18.3'
```

## 系统架构

ASR模块采用分层架构设计，确保各组件职责清晰，便于维护和扩展。

### 架构图

```
┌─────────────┐
│   应用层    │
└──────┬──────┘
       │
┌──────▼──────┐
│   服务层    │ ← AsrService接口及实现类
└──────┬──────┘
       │
┌──────▼──────┐
│   模型层    │ ← 数据模型(AsrRequest, AsrResult)
└──────┬──────┘
       │
┌──────▼──────┐
│   配置层    │ ← 配置管理(AsrConfig)
└──────┬──────┘
       │
┌──────▼──────┐
│ 第三方SDK层 │ ← DashScope SDK
└─────────────┘
```

### 核心组件

1. **配置层**
   - `AsrConfig`: 管理ASR服务配置，包括API密钥、模型参数等
   - 采用单例模式确保全局配置一致性

2. **模型层**
   - `AsrRequest`: 封装发送给识别服务的请求，包含音频数据
   - `AsrResult`: 封装识别服务返回的结果，包含识别文本和状态

3. **服务层**
   - `AsrService`: 定义语音识别服务接口
   - `DashScopeAsrService`: 基于阿里云DashScope SDK的实现
   - `AsrServiceFactory`: 创建和管理服务实例的工厂类

4. **工具层**
   - `AsrUtils`: 提供辅助工具方法

## 关键参数

### 音频参数

| 参数 | 值 | 说明 |
|------|-----|------|
| 格式 | PCM | 原始脉冲编码调制格式 |
| 采样率 | 16000 Hz | 每秒采样次数 |
| 位深度 | 16 bit | 每个采样的位数 |
| 通道 | 单声道 | 单声道音频 |

### 识别模型

当前使用阿里云DashScope的`paraformer-realtime-v2`模型，具有以下特点：

- 支持实时流式识别
- 针对中文语音优化
- 低延迟响应
- 高准确率

### 安全参数

- API密钥存储在`assets/secrets.properties`文件中
- 使用`Properties`类安全加载配置

## 实现流程

### 初始化流程

1. 应用启动时加载ASR配置
2. 通过`AsrServiceFactory`获取`AsrService`实例
3. 调用`initialize()`方法初始化服务
4. 验证配置有效性

### 识别流程

1. 调用`startRecognition()`方法启动识别会话，返回结果流
2. 开始音频捕获
3. 通过`processAudioData()`方法发送音频数据
4. 订阅结果流处理识别结果
5. 调用`stopRecognition()`方法结束会话

### 数据流图

```
┌──────────┐    ┌───────────┐    ┌────────────┐    ┌──────────┐
│ 音频捕获 │───>│ 音频处理  │───>│ ASR服务    │───>│ 结果处理 │
└──────────┘    └───────────┘    └────────────┘    └──────────┘
                                       │
                                       ▼
                                  ┌────────────┐
                                  │ DashScope  │
                                  │    SDK     │
                                  └────────────┘
```

## 性能考量

### 内存优化

1. 使用固定大小的缓冲区处理音频数据
2. 避免频繁的对象创建和GC
3. 及时释放不再使用的资源

### 线程管理

使用RxJava的线程调度器确保：
- 网络和IO操作在IO线程执行
- UI更新在主线程执行
- 音频处理在计算线程执行

```java
.subscribeOn(Schedulers.io())
.observeOn(AndroidSchedulers.mainThread())
```

### 错误处理

1. 自定义`AsrException`类统一异常处理
2. 利用RxJava的错误处理机制
3. 详细日志记录便于问题排查

## 扩展方向

1. **多服务提供商支持**
   - 添加Google Speech-to-Text实现
   - 支持本地离线识别模型

2. **性能优化**
   - 添加语音活动检测(VAD)
   - 优化音频缓冲策略

3. **功能增强**
   - 多语言支持
   - 关键词识别
   - 说话人识别

4. **安全增强**
   - API密钥加密存储
   - 动态获取凭证 