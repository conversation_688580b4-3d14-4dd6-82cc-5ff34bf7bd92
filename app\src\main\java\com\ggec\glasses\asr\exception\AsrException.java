package com.ggec.glasses.asr.exception;

/**
 * 语音识别异常类
 * 用于包装语音识别过程中的错误
 */
public class AsrException extends RuntimeException {

    /**
     * 创建语音识别异常
     * @param message 错误信息
     */
    public AsrException(String message) {
        super(message);
    }
    
    /**
     * 创建语音识别异常
     * @param message 错误信息
     * @param cause 原始异常
     */
    public AsrException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 创建语音识别异常
     * @param cause 原始异常
     */
    public AsrException(Throwable cause) {
        super(cause);
    }
} 