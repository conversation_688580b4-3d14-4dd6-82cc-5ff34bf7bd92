/*
 * Copyright (c) 2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.sample.audioconnect.smartwear.gallery

import android.app.Application
import android.content.Context
import android.os.Environment
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.alibaba.fastjson.JSONObject
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.core.utility.FileUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.Objects

/**
 * <AUTHOR>
 * @date 2025/03/26
 */
open class GalleryViewModel(application: Application) : AndroidViewModel(application) {
    var mContext: Context

    var fileType: Int = FileType.IMAGE
    var destinationPath = ""
    var galleryHelper:GalleryHelper

    var wifiHelper: WiFiHelper

    private var galleryImageFileList: ArrayList<LocaleMediaFile> = ArrayList()
    private var galleryVideoFileList: ArrayList<LocaleMediaFile> = ArrayList()
    var galleryFileListLiveData: MutableLiveData<ArrayList<LocaleMediaFile>> = MutableLiveData()

    private var mediaFolderDir: String = ""
    private var mediaFileListLoaded: Boolean = false


    fun getWifiConnectedLiveData():MutableLiveData<Boolean> {
        return wifiHelper.wifiConnectedLiveData
    }

    fun changeFileType(fileType: Int) {
        if (this.fileType != fileType) {
            ZLogger.v(
                "changeFileType from ${this.fileType} to ${fileType}"
            )
            this.fileType = fileType
            reloadActiveData()
        }
        if (!mediaFileListLoaded) {
            loadMediaList()
        }
    }
    fun getActiveFileList():ArrayList<LocaleMediaFile> {
        if (fileType == FileType.IMAGE) {
            return galleryImageFileList
        } else {
            return galleryVideoFileList
        }
    }
    fun getFileListByType(type:Int):ArrayList<LocaleMediaFile> {
        if (type == FileType.IMAGE) {
            return galleryImageFileList
        } else {
            return galleryVideoFileList
        }
    }

    fun loadMediaList() {
        galleryImageFileList = GalleryUtils.loadFileList(mediaFolderDir, FileType.IMAGE)
        galleryVideoFileList = GalleryUtils.loadFileList(mediaFolderDir, FileType.VIDEO)
        ZLogger.v("loadMediaList.image:${galleryImageFileList.size}")
        ZLogger.v("loadMediaList.video:${galleryVideoFileList.size}")

        mediaFileListLoaded = true
        reloadActiveData()
    }

    @Synchronized
    fun reloadActiveData() {
        if (fileType == FileType.IMAGE) {
            galleryFileListLiveData.postValue(galleryImageFileList)
        } else  {
            galleryFileListLiveData.postValue(galleryVideoFileList)
        }
    }

    fun downloadMediaList(url: String = "http://192.168.43.1:8080/media-list") = try {
        Log.d("GGEC","123")
        ZLogger.v("downloadMediaList:$url")
        val client = OkHttpClient()
        val request: Request =
            Request.Builder().url(url)
                .get()
                .build()
        val response = client.newCall(request).execute()

        if (response.code == 200 && response.body != null) {
            val result = Objects.requireNonNull(response.body)?.string()
            ZLogger.i("downloadMediaList success：\n$result")
            val mediaListData: MediaListData = JSONObject.parseObject(result, MediaListData::class.java)
            ZLogger.v("media file count:${mediaListData.contents.size}")

            if (mediaListData.contents.size > 0) {
                for (media in mediaListData.contents) {
                    downloadMediaFile(fileName = media.name)
                }
                ZLogger.v("save complete")
                loadMediaList()
            } else {

            }
        } else {
            ZLogger.w("downloadMediaList fail")
        }
    } catch (e: java.lang.Exception) {
        e.printStackTrace()
    }

    private fun downloadMediaFile(url: String = "http://192.168.43.1:8080/media", fileName:String) {
        ZLogger.v("downloadMediaFile:$url/$fileName")
        val client = OkHttpClient()
        val request: Request =
            Request.Builder().url("$url/$fileName")
                .get()
                .build()

        val filePath = "$mediaFolderDir/$fileName"

        try {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    throw IOException("Unexpected code $response")
                }
                response.body?.byteStream().use { inputStream ->
                    FileOutputStream(File(filePath)).use { outputStream ->
                        val buffer = ByteArray(4096)
                        var bytesRead: Int
                        var size = 0
                        while ((inputStream!!.read(buffer).also { bytesRead = it }) != -1) {
                            outputStream.write(buffer, 0, bytesRead)
                            size += bytesRead
                        }
                        ZLogger.v("file download success: $filePath, size=$size")

                        galleryHelper.saveToGallery(filePath)
                    }
                }
            }
        } catch (e: IOException) {
            e.printStackTrace()
            ZLogger.w("downloadMediaFile fail")
        }
    }

    fun connectWiFi(ssid: String = "AI_GLASS_AP", password: String = "rtkaiglass") {
        CoroutineScope(Dispatchers.IO).launch {
            val ret = wifiHelper.connectToWifi(ssid, password)
            ZLogger.v("connectWiFi:$ret")
        }
    }

    fun checkNetwork() {
        wifiHelper.updateSsid("AI_GLASS_AP")
    }

    init {
        this.mContext = application.applicationContext
        this.mediaFolderDir =
            mContext.getExternalFilesDir(null).toString() + "/$GALLERY_FOLDER_NAME"
        FileUtils.createDir(mediaFolderDir)

        val extBaseDir = Environment.getExternalStorageDirectory()
        destinationPath = extBaseDir.absolutePath + "/DCIM/" + GALLERY_FOLDER_NAME

        FileUtils.createDir(destinationPath)

        galleryHelper = GalleryHelper(mContext)
        wifiHelper = WiFiHelper(mContext)
    }

    companion object {
        var GALLERY_FOLDER_NAME = "test4"
    }
}
