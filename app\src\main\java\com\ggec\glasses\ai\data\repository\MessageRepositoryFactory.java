package com.ggec.glasses.ai.data.repository;

import android.content.Context;

import com.ggec.glasses.ai.data.db.repository.ChatMessageRepository;

/**
 * 消息仓库工厂
 * 用于创建和获取不同类型的消息仓库实例
 */
public class MessageRepositoryFactory {
    
    private static volatile CachedChatMessageRepository cachedRepository;
    private static volatile ChatMessageRepository standardRepository;
    
    /**
     * 获取带缓存的消息仓库实例（单例）
     * 
     * @param context 应用上下文
     * @return 带缓存的消息仓库实例
     */
    public static CachedChatMessageRepository getCachedRepository(Context context) {
        if (cachedRepository == null) {
            synchronized (MessageRepositoryFactory.class) {
                if (cachedRepository == null) {
                    cachedRepository = new CachedChatMessageRepository(context);
                }
            }
        }
        return cachedRepository;
    }
    
    /**
     * 获取标准消息仓库实例（单例）
     * 不使用缓存，直接访问数据库
     * 
     * @param context 应用上下文
     * @return 标准消息仓库实例
     */
    public static ChatMessageRepository getStandardRepository(Context context) {
        if (standardRepository == null) {
            synchronized (MessageRepositoryFactory.class) {
                if (standardRepository == null) {
                    standardRepository = new ChatMessageRepository(context);
                }
            }
        }
        return standardRepository;
    }
    
    /**
     * 获取默认的消息仓库实例
     * 默认使用带缓存的版本
     * 
     * @param context 应用上下文
     * @return 默认消息仓库实例
     */
    public static CachedChatMessageRepository getDefaultRepository(Context context) {
        return getCachedRepository(context);
    }
    
    /**
     * 释放所有仓库实例
     * 在应用退出时调用，以释放资源
     */
    public static void releaseAll() {
        cachedRepository = null;
        standardRepository = null;
    }
} 