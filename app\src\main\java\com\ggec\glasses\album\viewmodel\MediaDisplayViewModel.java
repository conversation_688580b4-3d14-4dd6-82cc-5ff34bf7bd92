package com.ggec.glasses.album.viewmodel;

import android.app.Application;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.data.repository.MediaRepository;
import com.ggec.glasses.album.manager.AlbumMediaExportManager;

/**
 * 媒体展示ViewModel，负责处理媒体展示相关的业务逻辑和UI状态管理
 */
public class MediaDisplayViewModel extends AndroidViewModel {
    
    // 媒体数据
    private final MutableLiveData<Media> mediaLiveData = new MutableLiveData<>();
    
    // UI状态数据
    private final MutableLiveData<Boolean> operationLayerVisible = new MutableLiveData<>(true);
    private final MutableLiveData<Boolean> navigateBack = new MutableLiveData<>(false);
    
    // 仓库
    private final MediaRepository mediaRepository;
    
    /**
     * 构造函数
     * @param application 应用上下文
     */
    public MediaDisplayViewModel(@NonNull Application application) {
        super(application);
        
        // 初始化媒体仓库
        mediaRepository = MediaRepository.getInstance(application);
    }
    
    /**
     * 设置媒体数据
     * @param media 媒体对象
     */
    public void setMedia(Media media) {
        mediaLiveData.setValue(media);
    }
    
    /**
     * 获取媒体LiveData
     * @return 媒体LiveData
     */
    public LiveData<Media> getMedia() {
        return mediaLiveData;
    }
    
    /**
     * 获取当前媒体
     * @return 媒体对象
     */
    public Media getCurrentMedia() {
        return mediaLiveData.getValue();
    }
    
    /**
     * 切换操作层可见性
     */
    public void toggleOperationLayerVisibility() {
        Boolean currentValue = operationLayerVisible.getValue();
        operationLayerVisible.setValue(currentValue != null ? !currentValue : false);
    }
    
    /**
     * 获取操作层可见性LiveData
     * @return 操作层可见性LiveData
     */
    public LiveData<Boolean> getOperationLayerVisible() {
        return operationLayerVisible;
    }
    
    /**
     * 设置操作层可见性
     * @param visible 是否可见
     */
    public void setOperationLayerVisible(boolean visible) {
        operationLayerVisible.setValue(visible);
    }
    
    /**
     * 删除当前媒体
     * @param callback 回调接口
     */
    public void deleteMedia(MediaRepository.OnMediaDeletedCallback callback) {
        Media media = mediaLiveData.getValue();
        if (media != null) {
            mediaRepository.permanentlyDeleteMedia(media.getId(), callback);
        }
    }
    
    /**
     * 将媒体导出到系统相册
     * @param context 上下文
     * @param callback 回调接口
     */
    public void exportMedia(Context context, AlbumMediaExportManager.SaveMediaCallback callback) {
        Media media = mediaLiveData.getValue();
        if (media != null) {
            AlbumMediaExportManager.saveMediaToGallery(context, media, callback);
        }
    }
    
    /**
     * 设置导航返回
     */
    public void navigateBack() {
        navigateBack.setValue(true);
    }
    
    /**
     * 重置导航返回状态
     */
    public void resetNavigateBack() {
        navigateBack.setValue(false);
    }
    
    /**
     * 获取导航返回LiveData
     * @return 导航返回LiveData
     */
    public LiveData<Boolean> getNavigateBack() {
        return navigateBack;
    }
    
    /**
     * 判断当前媒体是否为视频
     * @return 是否为视频
     */
    public boolean isVideoMedia() {
        Media media = mediaLiveData.getValue();
        return media != null && "VIDEO".equals(media.getType());
    }
    
    /**
     * 获取媒体文件路径
     * @return 文件路径
     */
    public String getMediaFilePath() {
        Media media = mediaLiveData.getValue();
        return media != null ? media.getFilePath() : null;
    }
    
    /**
     * 获取媒体缩略图路径
     * @return 缩略图路径
     */
    public String getMediaThumbnailPath() {
        Media media = mediaLiveData.getValue();
        return media != null ? media.getThumbnailPath() : null;
    }
    
    /**
     * 获取媒体文件名
     * @return 文件名
     */
    public String getMediaFileName() {
        Media media = mediaLiveData.getValue();
        return media != null ? media.getFileName() : null;
    }
} 