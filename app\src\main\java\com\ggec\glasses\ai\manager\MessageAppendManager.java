package com.ggec.glasses.ai.manager;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.ggec.glasses.ai.data.ChatUIMessage;

import java.util.ArrayList;
import java.util.List;

/**
 * 消息追加管理器
 * 负责管理消息的追加显示，避免清空已有消息
 */
public class MessageAppendManager {
    private static final String TAG = "MessageAppendManager";
    private static final int DEFAULT_DELAY = 800; // 默认延迟800毫秒
    
    private final Handler handler = new Handler(Looper.getMainLooper());
    private final List<ChatUIMessage> pendingMessages = new ArrayList<>();
    private boolean isAppending = false;
    private OnMessageAppendListener listener;
    
    /**
     * 消息追加监听器接口
     */
    public interface OnMessageAppendListener {
        /**
         * 当消息追加时回调
         * @param appendedMessage 新追加的消息
         */
        void onMessageAppended(ChatUIMessage appendedMessage);
        
        /**
         * 当所有消息都追加完毕时回调
         */
        void onAllMessagesAppended();
    }
    
    /**
     * 设置消息追加监听器
     * @param listener 监听器
     */
    public void setOnMessageAppendListener(OnMessageAppendListener listener) {
        this.listener = listener;
    }
    
    /**
     * 逐条追加消息列表
     * @param messages 要追加的消息列表
     */
    public void appendMessagesSequentially(List<ChatUIMessage> messages) {
        if (isAppending || messages == null || messages.isEmpty()) {
            Log.d(TAG, "无法追加消息：正在追加中或消息列表为空");
            return;
        }
        
        Log.d(TAG, "开始逐条追加 " + messages.size() + " 条消息");
        isAppending = true;
        pendingMessages.clear();
        pendingMessages.addAll(messages);
        
        // 追加第一条消息
        appendNextMessage();
    }
    
    /**
     * 追加下一条消息
     */
    private void appendNextMessage() {
        if (pendingMessages.isEmpty()) {
            Log.d(TAG, "所有消息已追加完毕");
            isAppending = false;
            if (listener != null) {
                listener.onAllMessagesAppended();
            }
            return;
        }
        
        // 获取下一条要追加的消息
        ChatUIMessage nextMessage = pendingMessages.remove(0);
        
        Log.d(TAG, "追加消息: " + (nextMessage.isUserMessage() ? "用户" : "AI") + 
                " - " + (nextMessage.getContent().length() > 20 ? 
                nextMessage.getContent().substring(0, 20) + "..." : 
                nextMessage.getContent()));
        
        // 通知UI追加
        if (listener != null) {
            listener.onMessageAppended(nextMessage);
        }
        
        // 延迟追加下一条消息
        handler.postDelayed(this::appendNextMessage, DEFAULT_DELAY);
    }
    
    /**
     * 重置状态
     */
    public void reset() {
        Log.d(TAG, "重置消息追加管理器状态");
        handler.removeCallbacksAndMessages(null);
        pendingMessages.clear();
        isAppending = false;
    }
    
    /**
     * 是否正在追加消息
     * @return 是否正在追加
     */
    public boolean isAppending() {
        return isAppending;
    }
} 