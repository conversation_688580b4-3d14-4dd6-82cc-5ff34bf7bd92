package com.ggec.glasses.asr.util;

import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import com.ggec.glasses.asr.model.AsrRequest;

import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

import io.reactivex.BackpressureStrategy;
import io.reactivex.Flowable;

/**
 * 语音识别工具类
 * 提供与语音识别相关的辅助方法
 */
public class AsrUtils {
    private static final String TAG = "AsrUtils";
    private static final int BUFFER_SIZE = 4096; // 4KB缓冲区

    /**
     * 从assets目录读取音频文件并创建音频数据流
     * 主要用于测试语音识别功能
     * 
     * @param context 应用上下文
     * @param assetFilePath assets目录下的音频文件路径
     * @return 包含音频数据的Flowable流
     */
    public static Flowable<AsrRequest> createAudioFlowableFromAsset(Context context, String assetFilePath) {
        return Flowable.create(emitter -> {
            AssetManager assetManager = context.getAssets();
            try (InputStream inputStream = assetManager.open(assetFilePath)) {
                byte[] buffer = new byte[BUFFER_SIZE];
                int bytesRead;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    if (emitter.isCancelled()) {
                        break;
                    }
                    
                    if (bytesRead > 0) {
                        // 创建ByteBuffer并填充数据
                        ByteBuffer byteBuffer = ByteBuffer.allocate(bytesRead);
                        byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
                        byteBuffer.put(buffer, 0, bytesRead);
                        byteBuffer.flip(); // 准备读取
                        
                        // 发送音频数据
                        emitter.onNext(AsrRequest.data(byteBuffer));
                        
                        // 休眠一小段时间，模拟实时音频流
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
                
                // 发送结束信号
                if (!emitter.isCancelled()) {
                    emitter.onNext(AsrRequest.end());
                    emitter.onComplete();
                }
            } catch (IOException e) {
                Log.e(TAG, "读取asset音频文件失败: " + assetFilePath, e);
                if (!emitter.isCancelled()) {
                    emitter.onError(e);
                }
            }
        }, BackpressureStrategy.BUFFER);
    }
    
    /**
     * 将原始PCM音频数据转换为ByteBuffer
     * 
     * @param pcmData 原始PCM音频数据
     * @return 包装了音频数据的ByteBuffer
     */
    public static ByteBuffer convertPcmToByteBuffer(byte[] pcmData) {
        ByteBuffer buffer = ByteBuffer.allocate(pcmData.length);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        buffer.put(pcmData);
        buffer.flip(); // 准备读取
        return buffer;
    }
} 