/*
 * Copyright (c) 2017-2022. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.bbpro.ui

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import com.realsil.bbpro.R
import com.realsil.bbpro.ui.nearby.NearbyActivity
import com.realsil.sdk.core.bluetooth.BluetoothProfileManager
import com.realsil.sdk.fastpair.FastPairManager
import com.realsil.sdk.support.base.BaseActivity
import com.realsil.sdk.support.permission.PermissionUtils


/**
 * <AUTHOR>
 */
class SplashScreenActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)

        //requestPermissions(bluetooth = true, location = true, readContacts = true, externalStorage = true)
        val permissions = PermissionUtils.getRequestPermissions(this, bluetooth = true,
        location = true, phone = false, externalStorage = true, accessNetwork = true, readContacts = true)
        requestCustomizePermissions(PermissionUtils.filterPermissions(this, permissions))
    }

    override fun onPermissionsGranted() {
        super.onPermissionsGranted()

        FastPairManager.startFastPairService(this)

        BluetoothProfileManager.getInstance().registerProfiles()

        Handler(Looper.getMainLooper()).postDelayed({
            redirect2Home()
        }, SPLASH_DELAY.toLong())
    }

    private fun redirect2Home() {
        val intent = Intent(this, NearbyActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
        startActivity(intent)
        finish()
    }

    companion object {
        /**
         * Splash screen duration time in milliseconds
         */
        private const val SPLASH_DELAY = 500
    }
}