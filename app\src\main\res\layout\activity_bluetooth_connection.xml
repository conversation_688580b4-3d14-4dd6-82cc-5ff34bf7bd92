<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    tools:context=".bluetooth.BluetoothConnectionActivity">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/comp_background_gray"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="返回"
            android:padding="12dp"
            android:src="@drawable/ic_public_back"
            android:tint="@color/icon_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- 标题文本 - 居中显示 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/bluetooth_connection_title"
            android:textColor="@color/font_primary"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- 刷新按钮 -->
        <ImageView
            android:id="@+id/iv_refresh"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="@string/refresh_devices"
            android:padding="12dp"
            android:src="@drawable/ic_refresh"
            android:tint="@color/icon_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 - 可滚动 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/content_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:clipToPadding="false"
        android:paddingHorizontal="16dp"
        android:paddingBottom="20dp"
        android:fillViewport="true"
        android:scrollbars="vertical"
        android:scrollbarStyle="outsideOverlay"
        android:fadeScrollbars="true"
        android:overScrollMode="always"
        app:layout_constraintTop_toBottomOf="@id/header_layout"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 设备列表容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 已配对设备标题 -->
            <TextView
                android:id="@+id/tv_paired_devices_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/paired_devices_title"
                android:textColor="@color/font_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <!-- 已配对设备卡片背景 -->
            <LinearLayout
                android:id="@+id/paired_devices_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_card"
                android:orientation="vertical"
                android:padding="8dp">

                <!-- 已配对设备列表 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_paired_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:overScrollMode="never"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="2"
                    tools:listitem="@layout/item_bluetooth_device" />

                <!-- 无已配对设备提示（可后续通过代码控制显示/隐藏） -->
                <TextView
                    android:id="@+id/tv_no_paired_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:padding="4dp"
                    android:text="@string/no_paired_devices"
                    android:textColor="@color/font_secondary"
                    android:textSize="14sp"
                    android:visibility="gone" />
                    
                <!-- 折叠/展开按钮 -->
                <LinearLayout
                    android:id="@+id/paired_devices_expand_button"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/bg_profile_setting_item_ripple"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal">
                    
                    <TextView
                        android:id="@+id/tv_paired_devices_expand_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="更多"
                        android:textColor="@color/font_primary"
                        android:textSize="14sp" />
                        
                    <ImageView
                        android:id="@+id/iv_paired_devices_expand_icon"
                        android:layout_width="14dp"
                        android:layout_height="14dp"
                        android:layout_marginStart="4dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_public_arrow_down"
                        android:tint="@color/icon_primary"
                        android:translationY="1dp" />
                </LinearLayout>

            </LinearLayout>

            <!-- 可用设备标题 -->
            <TextView
                android:id="@+id/tv_available_devices_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/available_devices_title"
                android:textColor="@color/font_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <!-- 可用设备卡片背景 -->
            <LinearLayout
                android:id="@+id/available_devices_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_card"
                android:orientation="vertical"
                android:padding="8dp">

                <!-- 可用设备列表 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_available_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:overScrollMode="never"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_bluetooth_device" />

                <!-- 正在搜索提示（可后续通过代码控制显示/隐藏） -->
                <TextView
                    android:id="@+id/tv_searching_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:padding="4dp"
                    android:text="@string/searching_devices"
                    android:textColor="@color/font_secondary"
                    android:textSize="14sp"
                    android:visibility="gone" />

                <!-- 无可用设备提示（可后续通过代码控制显示/隐藏） -->
                <TextView
                    android:id="@+id/tv_no_available_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:padding="4dp"
                    android:text="@string/no_available_devices"
                    android:textColor="@color/font_secondary"
                    android:textSize="14sp"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 