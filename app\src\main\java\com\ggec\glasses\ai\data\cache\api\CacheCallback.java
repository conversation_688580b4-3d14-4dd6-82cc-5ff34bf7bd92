package com.ggec.glasses.ai.data.cache.api;

/**
 * 缓存回调接口
 * 用于监听缓存操作事件
 *
 * @param <K> 缓存键类型
 * @param <V> 缓存值类型
 */
public interface CacheCallback<K, V> {
    
    /**
     * 当缓存项被添加时调用
     *
     * @param key 缓存键
     * @param value 缓存值
     */
    void onEntryAdded(K key, V value);
    
    /**
     * 当缓存项被访问时调用
     *
     * @param key 缓存键
     * @param value 缓存值
     */
    void onEntryAccessed(K key, V value);
    
    /**
     * 当缓存项被更新时调用
     *
     * @param key 缓存键
     * @param oldValue 旧缓存值
     * @param newValue 新缓存值
     */
    void onEntryUpdated(K key, V oldValue, V newValue);
    
    /**
     * 当缓存项被移除时调用
     *
     * @param key 缓存键
     * @param value 缓存值
     * @param reason 移除原因
     */
    void onEntryRemoved(K key, V value, RemovalReason reason);
    
    /**
     * 当缓存被清空时调用
     */
    void onCacheCleared();
    
    /**
     * 移除原因枚举
     */
    enum RemovalReason {
        /** 手动移除 */
        REMOVED,
        /** 过期移除 */
        EXPIRED,
        /** 容量溢出移除 */
        EVICTED
    }
} 