#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 498073600 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3551), pid=37000, tid=30664
#
# JRE version: Java(TM) SE Runtime Environment (17.0.14+8) (build 17.0.14+8-LTS-191)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.14+8-LTS-191, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.10.2-bin\a04bxjujx95o3nb99gddekhwo\gradle-8.10.2\lib\agents\gradle-instrumentation-agent-8.10.2.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10.2

Host: 13th Gen Intel(R) Core(TM) i7-13700H, 20 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3958)
Time: Fri May  9 16:57:12 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.3958) elapsed time: 20.522017 seconds (0d 0h 0m 20s)

---------------  T H R E A D  ---------------

Current thread (0x000001c6a13bb860):  VMThread "VM Thread" [stack: 0x0000007a28200000,0x0000007a28300000] [id=30664]

Stack: [0x0000007a28200000,0x0000007a28300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67a38a]
V  [jvm.dll+0x7da7ed]
V  [jvm.dll+0x7dc133]
V  [jvm.dll+0x7dc7a3]
V  [jvm.dll+0x24517f]
V  [jvm.dll+0x6773b9]
V  [jvm.dll+0x66c062]
V  [jvm.dll+0x302096]
V  [jvm.dll+0x309636]
V  [jvm.dll+0x359f8e]
V  [jvm.dll+0x35a1bf]
V  [jvm.dll+0x2d9168]
V  [jvm.dll+0x2d759d]
V  [jvm.dll+0x2d6bec]
V  [jvm.dll+0x31a74b]
V  [jvm.dll+0x7e109b]
V  [jvm.dll+0x7e1dd4]
V  [jvm.dll+0x7e22ed]
V  [jvm.dll+0x7e26c4]
V  [jvm.dll+0x7e2790]
V  [jvm.dll+0x78b04a]
V  [jvm.dll+0x679275]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5af08]

VM_Operation (0x0000007a30bfe770): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001c6e7078b30


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001c6ecd73220, length=292, elements={
0x000001c68217d810, 0x000001c6a13bd360, 0x000001c6a13be0f0, 0x000001c6a13dcd00,
0x000001c6a13de5e0, 0x000001c6a13deeb0, 0x000001c6a13df780, 0x000001c6a13e0430,
0x000001c6a13e0df0, 0x000001c6a13f4260, 0x000001c6a158c190, 0x000001c6a13aa4b0,
0x000001c6a15906a0, 0x000001c6a15a0fb0, 0x000001c6a1750ff0, 0x000001c6e841e180,
0x000001c6e796e020, 0x000001c6e8c05dc0, 0x000001c6e8c829f0, 0x000001c6e8c82ed0,
0x000001c6e8c75110, 0x000001c6e8c746f0, 0x000001c6e8c75620, 0x000001c6e8c732b0,
0x000001c6e8c72890, 0x000001c6e8c72da0, 0x000001c6e8c75b30, 0x000001c6e8c76040,
0x000001c6e8c74c00, 0x000001c6e8c73cd0, 0x000001c6e8c741e0, 0x000001c6e8c737c0,
0x000001c6e9915d70, 0x000001c6e9913f10, 0x000001c6e9914420, 0x000001c6e99180e0,
0x000001c6e9912ad0, 0x000001c6e9914e40, 0x000001c6e9917bd0, 0x000001c6e9910c70,
0x000001c6e9911180, 0x000001c6e9916790, 0x000001c6e9916ca0, 0x000001c6e99171b0,
0x000001c6e99120b0, 0x000001c6e99125c0, 0x000001c6e9912fe0, 0x000001c6e99134f0,
0x000001c6e9913a00, 0x000001c6eadfc0e0, 0x000001c6eadfaca0, 0x000001c6eadf9350,
0x000001c6eadf8420, 0x000001c6eadf9d70, 0x000001c6eadf6fe0, 0x000001c6eadf8e40,
0x000001c6eadf60b0, 0x000001c6eadf5ba0, 0x000001c6eadf74f0, 0x000001c6eadf8930,
0x000001c6eadfb6c0, 0x000001c6eadfbbd0, 0x000001c6eadf5180, 0x000001c6eadfc5f0,
0x000001c6eadf9860, 0x000001c6eadfb1b0, 0x000001c6eadfcb00, 0x000001c6eadf5690,
0x000001c6eadfa280, 0x000001c6eadf65c0, 0x000001c6eadf6ad0, 0x000001c6eadfa790,
0x000001c6eadf7a00, 0x000001c6eadf7f10, 0x000001c6eb700d50, 0x000001c6eb701c80,
0x000001c6eb701770, 0x000001c6eb702bb0, 0x000001c6eb703ae0, 0x000001c6eb703ff0,
0x000001c6eb700840, 0x000001c6eb701260, 0x000001c6eb702190, 0x000001c6eb7026a0,
0x000001c6eb7030c0, 0x000001c6eae489b0, 0x000001c6eae4b740, 0x000001c6eae48ec0,
0x000001c6eae4bc50, 0x000001c6eae493d0, 0x000001c6eae4a300, 0x000001c6eae498e0,
0x000001c6eae49df0, 0x000001c6eae4b230, 0x000001c6eae484a0, 0x000001c6eae4a810,
0x000001c6eae4ad20, 0x000001c6eb27a0a0, 0x000001c6eb27a5b0, 0x000001c6eb27bf00,
0x000001c6eb27f6b0, 0x000001c6eb27e270, 0x000001c6eb279680, 0x000001c6eb27afd0,
0x000001c6eb27ec90, 0x000001c6eb27f1a0, 0x000001c6eb278750, 0x000001c6eb279b90,
0x000001c6eb278240, 0x000001c6eb278c60, 0x000001c6e707c7f0, 0x000001c6e7079550,
0x000001c6e7078110, 0x000001c6e707b8c0, 0x000001c6e7075da0, 0x000001c6e70767c0,
0x000001c6e7078620, 0x000001c6e7079040, 0x000001c6e7078b30, 0x000001c6e707cd00,
0x000001c6e7079a60, 0x000001c6e7079f70, 0x000001c6e707aea0, 0x000001c6e707a480,
0x000001c6e707a990, 0x000001c6e707b3b0, 0x000001c6e707d720, 0x000001c6e707bdd0,
0x000001c6e7077c00, 0x000001c6e707c2e0, 0x000001c6e70776f0, 0x000001c6e7076cd0,
0x000001c6e707d210, 0x000001c6e70762b0, 0x000001c6e70771e0, 0x000001c6ea6c0a20,
0x000001c6ea6ba4e0, 0x000001c6ea6be6b0, 0x000001c6ea6b9fd0, 0x000001c6ea6bc850,
0x000001c6ea6bb920, 0x000001c6ea6bf0d0, 0x000001c6ea6bdc90, 0x000001c6ea6bcd60,
0x000001c6ea6c0f30, 0x000001c6ea6be1a0, 0x000001c6ea6bebc0, 0x000001c6ea6bfaf0,
0x000001c6ea6c0000, 0x000001c6ea6c0510, 0x000001c6ea6b9ac0, 0x000001c6ea6c1440,
0x000001c6ea6bbe30, 0x000001c6ea6bc340, 0x000001c6ea6bd780, 0x000001c6ea6bd270,
0x000001c6ea6baf00, 0x000001c6e829aa60, 0x000001c6e8294520, 0x000001c6e8294a30,
0x000001c6e82986f0, 0x000001c6e8295e70, 0x000001c6e829af70, 0x000001c6e8299110,
0x000001c6e8296da0, 0x000001c6e829a550, 0x000001c6e8295450, 0x000001c6e829b480,
0x000001c6e82977c0, 0x000001c6e8298c00, 0x000001c6e8295960, 0x000001c6e8294010,
0x000001c6e829b990, 0x000001c6e8299620, 0x000001c6e829a040, 0x000001c6e8296380,
0x000001c6e8296890, 0x000001c6e82981e0, 0x000001c6ea6ba9f0, 0x000001c6ea6bb410,
0x000001c6ec260c40, 0x000001c6ec261b70, 0x000001c6ecd83ba0, 0x000001c6ecd84ad0,
0x000001c6ecd83690, 0x000001c6ecd845c0, 0x000001c6ecd84fe0, 0x000001c6eb21db80,
0x000001c6eb21e090, 0x000001c6eb21e5a0, 0x000001c6ecd82920, 0x000001c6ecd82e30,
0x000001c6ecd7fb90, 0x000001c6ecd800a0, 0x000001c6ecd80ac0, 0x000001c6eb41c860,
0x000001c6ec261150, 0x000001c6ec261660, 0x000001c6ec262080, 0x000001c6ec262590,
0x000001c6a1708e80, 0x000001c6ecf4b470, 0x000001c6ecbacd90, 0x000001c6ecbae6e0,
0x000001c6ecbad7b0, 0x000001c6ecbad2a0, 0x000001c6ecbadcc0, 0x000001c6ecbae1d0,
0x000001c6eca10100, 0x000001c6eca10b20, 0x000001c6eca10610, 0x000001c6eca11030,
0x000001c6eca11540, 0x000001c6eca11a50, 0x000001c6eca11f60, 0x000001c6eca12470,
0x000001c6eca0fbf0, 0x000001c6eca0f1d0, 0x000001c6eca0ecc0, 0x000001c6eca0f6e0,
0x000001c6eb0be6a0, 0x000001c6eb0bebb0, 0x000001c6eb0bdc80, 0x000001c6eb0be190,
0x000001c6eb0bc330, 0x000001c6eb0bd260, 0x000001c6eb0bf0c0, 0x000001c6eb0bc840,
0x000001c6eb0bbe20, 0x000001c6eb0bf5d0, 0x000001c6eb0bcd50, 0x000001c6eb0bd770,
0x000001c6ea9d0870, 0x000001c6ea9d3b10, 0x000001c6ea9d2be0, 0x000001c6ea9d21c0,
0x000001c6ea9d17a0, 0x000001c6ea9d26d0, 0x000001c6ea9d1cb0, 0x000001c6eaa17870,
0x000001c6eaa17d80, 0x000001c6eaa191c0, 0x000001c6eaa18290, 0x000001c6eaa187a0,
0x000001c6ea9d30f0, 0x000001c6ea9d0d80, 0x000001c6ea9d3600, 0x000001c6ea9d1290,
0x000001c6ea9d4020, 0x000001c6ed516440, 0x000001c6ed514af0, 0x000001c6ed516e60,
0x000001c6ecf4c3a0, 0x000001c6ecf4c8b0, 0x000001c6ecf4be90, 0x000001c6ed51cf60,
0x000001c6ed51ca50, 0x000001c6ec296460, 0x000001c6ebcfd6b0, 0x000001c6ecf34620,
0x000001c6ecf34b30, 0x000001c6ecf35040, 0x000001c6ecf336f0, 0x000001c6ecf34110,
0x000001c6ed113260, 0x000001c6ed113770, 0x000001c6ebcfbd60, 0x000001c6ebcfc270,
0x000001c6ebcfc780, 0x000001c6ebcfd1a0, 0x000001c6ebcfcc90, 0x000001c6ec96eb70,
0x000001c6ec970ee0, 0x000001c6ec96e150, 0x000001c6ec96ffb0, 0x000001c6ec96faa0,
0x000001c6ec96f080, 0x000001c6ec96f590, 0x000001c6ec9704c0, 0x000001c6ec96dc40,
0x000001c6ec96d730, 0x000001c6ec96e660, 0x000001c6ec9709d0, 0x000001c6e71765e0,
0x000001c6ed112d50, 0x000001c6e7ee7cf0, 0x000001c6e7ee5980, 0x000001c6e7ee4a50,
0x000001c6e82b9f70, 0x000001c6ecd82410, 0x000001c6ed51d470, 0x000001c6ed51de90
}

Java Threads: ( => current thread )
  0x000001c68217d810 JavaThread "main" [_thread_blocked, id=2980, stack(0x0000007a27c00000,0x0000007a27d00000)]
  0x000001c6a13bd360 JavaThread "Reference Handler" daemon [_thread_blocked, id=24784, stack(0x0000007a28300000,0x0000007a28400000)]
  0x000001c6a13be0f0 JavaThread "Finalizer" daemon [_thread_blocked, id=36940, stack(0x0000007a28400000,0x0000007a28500000)]
  0x000001c6a13dcd00 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=24748, stack(0x0000007a28500000,0x0000007a28600000)]
  0x000001c6a13de5e0 JavaThread "Attach Listener" daemon [_thread_blocked, id=33364, stack(0x0000007a28600000,0x0000007a28700000)]
  0x000001c6a13deeb0 JavaThread "Service Thread" daemon [_thread_blocked, id=32828, stack(0x0000007a28700000,0x0000007a28800000)]
  0x000001c6a13df780 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=32308, stack(0x0000007a28800000,0x0000007a28900000)]
  0x000001c6a13e0430 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=30080, stack(0x0000007a28900000,0x0000007a28a00000)]
  0x000001c6a13e0df0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=11348, stack(0x0000007a28a00000,0x0000007a28b00000)]
  0x000001c6a13f4260 JavaThread "Sweeper thread" daemon [_thread_blocked, id=4800, stack(0x0000007a28b00000,0x0000007a28c00000)]
  0x000001c6a158c190 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=29548, stack(0x0000007a28c00000,0x0000007a28d00000)]
  0x000001c6a13aa4b0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=25688, stack(0x0000007a28d00000,0x0000007a28e00000)]
  0x000001c6a15906a0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=37188, stack(0x0000007a28e00000,0x0000007a28f00000)]
  0x000001c6a15a0fb0 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=37568, stack(0x0000007a28f00000,0x0000007a29000000)]
  0x000001c6a1750ff0 JavaThread "Notification Thread" daemon [_thread_blocked, id=23444, stack(0x0000007a29100000,0x0000007a29200000)]
  0x000001c6e841e180 JavaThread "Daemon health stats" [_thread_blocked, id=27764, stack(0x0000007a29800000,0x0000007a29900000)]
  0x000001c6e796e020 JavaThread "Incoming local TCP Connector on port 56873" [_thread_in_native, id=37764, stack(0x0000007a29000000,0x0000007a29100000)]
  0x000001c6e8c05dc0 JavaThread "Daemon periodic checks" [_thread_blocked, id=29804, stack(0x0000007a29f00000,0x0000007a2a000000)]
  0x000001c6e8c829f0 JavaThread "Daemon" [_thread_blocked, id=34532, stack(0x0000007a2a000000,0x0000007a2a100000)]
  0x000001c6e8c82ed0 JavaThread "Handler for socket connection from /127.0.0.1:56873 to /127.0.0.1:56879" [_thread_in_native, id=6916, stack(0x0000007a2a100000,0x0000007a2a200000)]
  0x000001c6e8c75110 JavaThread "Cancel handler" [_thread_blocked, id=3472, stack(0x0000007a2a200000,0x0000007a2a300000)]
  0x000001c6e8c746f0 JavaThread "Daemon worker" [_thread_blocked, id=11904, stack(0x0000007a2a300000,0x0000007a2a400000)]
  0x000001c6e8c75620 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:56873 to /127.0.0.1:56879" [_thread_blocked, id=25160, stack(0x0000007a2a400000,0x0000007a2a500000)]
  0x000001c6e8c732b0 JavaThread "Stdin handler" [_thread_blocked, id=36952, stack(0x0000007a2a500000,0x0000007a2a600000)]
  0x000001c6e8c72890 JavaThread "Daemon client event forwarder" [_thread_blocked, id=29340, stack(0x0000007a2a600000,0x0000007a2a700000)]
  0x000001c6e8c72da0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=37284, stack(0x0000007a2a700000,0x0000007a2a800000)]
  0x000001c6e8c75b30 JavaThread "File lock request listener" [_thread_in_native, id=37084, stack(0x0000007a2a800000,0x0000007a2a900000)]
  0x000001c6e8c76040 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.10.2\fileHashes)" [_thread_blocked, id=29432, stack(0x0000007a2a900000,0x0000007a2aa00000)]
  0x000001c6e8c74c00 JavaThread "Cache worker for file hash cache (D:\test_project\Glasses\.gradle\8.10.2\fileHashes)" [_thread_blocked, id=33992, stack(0x0000007a2aa00000,0x0000007a2ab00000)]
  0x000001c6e8c73cd0 JavaThread "Cache worker for Build Output Cleanup Cache (D:\test_project\Glasses\.gradle\buildOutputCleanup)" [_thread_blocked, id=4376, stack(0x0000007a2ab00000,0x0000007a2ac00000)]
  0x000001c6e8c741e0 JavaThread "File watcher server" daemon [_thread_blocked, id=32572, stack(0x0000007a2b300000,0x0000007a2b400000)]
  0x000001c6e8c737c0 JavaThread "File watcher consumer" daemon [_thread_blocked, id=35444, stack(0x0000007a2b400000,0x0000007a2b500000)]
  0x000001c6e9915d70 JavaThread "Cache worker for checksums cache (D:\test_project\Glasses\.gradle\8.10.2\checksums)" [_thread_blocked, id=3292, stack(0x0000007a2b500000,0x0000007a2b600000)]
  0x000001c6e9913f10 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.10.2\fileContent)" [_thread_blocked, id=36408, stack(0x0000007a2b600000,0x0000007a2b700000)]
  0x000001c6e9914420 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.10.2\md-rule)" [_thread_blocked, id=17712, stack(0x0000007a2b700000,0x0000007a2b800000)]
  0x000001c6e99180e0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.10.2\md-supplier)" [_thread_blocked, id=34924, stack(0x0000007a2b800000,0x0000007a2b900000)]
  0x000001c6e9912ad0 JavaThread "jar transforms" [_thread_blocked, id=25692, stack(0x0000007a2b900000,0x0000007a2ba00000)]
  0x000001c6e9914e40 JavaThread "jar transforms Thread 2" [_thread_blocked, id=8024, stack(0x0000007a2ba00000,0x0000007a2bb00000)]
  0x000001c6e9917bd0 JavaThread "jar transforms Thread 3" [_thread_blocked, id=11940, stack(0x0000007a2bb00000,0x0000007a2bc00000)]
  0x000001c6e9910c70 JavaThread "Unconstrained build operations" [_thread_blocked, id=29040, stack(0x0000007a2bc00000,0x0000007a2bd00000)]
  0x000001c6e9911180 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=14332, stack(0x0000007a2bd00000,0x0000007a2be00000)]
  0x000001c6e9916790 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=37612, stack(0x0000007a2be00000,0x0000007a2bf00000)]
  0x000001c6e9916ca0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=10420, stack(0x0000007a2bf00000,0x0000007a2c000000)]
  0x000001c6e99171b0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=25992, stack(0x0000007a2c000000,0x0000007a2c100000)]
  0x000001c6e99120b0 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=12340, stack(0x0000007a2c100000,0x0000007a2c200000)]
  0x000001c6e99125c0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=27060, stack(0x0000007a2c200000,0x0000007a2c300000)]
  0x000001c6e9912fe0 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=5828, stack(0x0000007a2c300000,0x0000007a2c400000)]
  0x000001c6e99134f0 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=16128, stack(0x0000007a2c400000,0x0000007a2c500000)]
  0x000001c6e9913a00 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=4552, stack(0x0000007a2c500000,0x0000007a2c600000)]
  0x000001c6eadfc0e0 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=37640, stack(0x0000007a2c600000,0x0000007a2c700000)]
  0x000001c6eadfaca0 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=37076, stack(0x0000007a2c700000,0x0000007a2c800000)]
  0x000001c6eadf9350 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=24656, stack(0x0000007a2c800000,0x0000007a2c900000)]
  0x000001c6eadf8420 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=33396, stack(0x0000007a2c900000,0x0000007a2ca00000)]
  0x000001c6eadf9d70 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=31292, stack(0x0000007a2ca00000,0x0000007a2cb00000)]
  0x000001c6eadf6fe0 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=35844, stack(0x0000007a2cb00000,0x0000007a2cc00000)]
  0x000001c6eadf8e40 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=6868, stack(0x0000007a2cc00000,0x0000007a2cd00000)]
  0x000001c6eadf60b0 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=33864, stack(0x0000007a2cd00000,0x0000007a2ce00000)]
  0x000001c6eadf5ba0 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=37212, stack(0x0000007a2ce00000,0x0000007a2cf00000)]
  0x000001c6eadf74f0 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=35284, stack(0x0000007a2cf00000,0x0000007a2d000000)]
  0x000001c6eadf8930 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=37252, stack(0x0000007a2d000000,0x0000007a2d100000)]
  0x000001c6eadfb6c0 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=17416, stack(0x0000007a2d100000,0x0000007a2d200000)]
  0x000001c6eadfbbd0 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=32136, stack(0x0000007a2d200000,0x0000007a2d300000)]
  0x000001c6eadf5180 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=10400, stack(0x0000007a2d300000,0x0000007a2d400000)]
  0x000001c6eadfc5f0 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=30168, stack(0x0000007a2d400000,0x0000007a2d500000)]
  0x000001c6eadf9860 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=25256, stack(0x0000007a2d500000,0x0000007a2d600000)]
  0x000001c6eadfb1b0 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=35872, stack(0x0000007a2d600000,0x0000007a2d700000)]
  0x000001c6eadfcb00 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=37528, stack(0x0000007a2d700000,0x0000007a2d800000)]
  0x000001c6eadf5690 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=24740, stack(0x0000007a2d800000,0x0000007a2d900000)]
  0x000001c6eadfa280 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=26300, stack(0x0000007a2d900000,0x0000007a2da00000)]
  0x000001c6eadf65c0 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=28856, stack(0x0000007a2da00000,0x0000007a2db00000)]
  0x000001c6eadf6ad0 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=34144, stack(0x0000007a2db00000,0x0000007a2dc00000)]
  0x000001c6eadfa790 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=26936, stack(0x0000007a2dc00000,0x0000007a2dd00000)]
  0x000001c6eadf7a00 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=31804, stack(0x0000007a2dd00000,0x0000007a2de00000)]
  0x000001c6eadf7f10 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=34628, stack(0x0000007a2de00000,0x0000007a2df00000)]
  0x000001c6eb700d50 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=34004, stack(0x0000007a2df00000,0x0000007a2e000000)]
  0x000001c6eb701c80 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=34896, stack(0x0000007a2e000000,0x0000007a2e100000)]
  0x000001c6eb701770 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=36784, stack(0x0000007a2e100000,0x0000007a2e200000)]
  0x000001c6eb702bb0 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=15280, stack(0x0000007a2e200000,0x0000007a2e300000)]
  0x000001c6eb703ae0 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=6384, stack(0x0000007a2e300000,0x0000007a2e400000)]
  0x000001c6eb703ff0 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=30044, stack(0x0000007a2e400000,0x0000007a2e500000)]
  0x000001c6eb700840 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=24772, stack(0x0000007a2e500000,0x0000007a2e600000)]
  0x000001c6eb701260 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=34620, stack(0x0000007a2e600000,0x0000007a2e700000)]
  0x000001c6eb702190 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=25984, stack(0x0000007a2e700000,0x0000007a2e800000)]
  0x000001c6eb7026a0 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=36876, stack(0x0000007a2e800000,0x0000007a2e900000)]
  0x000001c6eb7030c0 JavaThread "Unconstrained build operations Thread 46" [_thread_blocked, id=34020, stack(0x0000007a2e900000,0x0000007a2ea00000)]
  0x000001c6eae489b0 JavaThread "Unconstrained build operations Thread 47" [_thread_blocked, id=37620, stack(0x0000007a2ea00000,0x0000007a2eb00000)]
  0x000001c6eae4b740 JavaThread "Unconstrained build operations Thread 48" [_thread_blocked, id=32772, stack(0x0000007a2eb00000,0x0000007a2ec00000)]
  0x000001c6eae48ec0 JavaThread "Unconstrained build operations Thread 49" [_thread_blocked, id=34512, stack(0x0000007a2ec00000,0x0000007a2ed00000)]
  0x000001c6eae4bc50 JavaThread "Unconstrained build operations Thread 50" [_thread_blocked, id=26084, stack(0x0000007a2ed00000,0x0000007a2ee00000)]
  0x000001c6eae493d0 JavaThread "Unconstrained build operations Thread 51" [_thread_blocked, id=9600, stack(0x0000007a2ee00000,0x0000007a2ef00000)]
  0x000001c6eae4a300 JavaThread "Unconstrained build operations Thread 52" [_thread_blocked, id=31348, stack(0x0000007a2ef00000,0x0000007a2f000000)]
  0x000001c6eae498e0 JavaThread "Unconstrained build operations Thread 53" [_thread_blocked, id=4812, stack(0x0000007a2f000000,0x0000007a2f100000)]
  0x000001c6eae49df0 JavaThread "Unconstrained build operations Thread 54" [_thread_blocked, id=37052, stack(0x0000007a2f100000,0x0000007a2f200000)]
  0x000001c6eae4b230 JavaThread "Unconstrained build operations Thread 55" [_thread_blocked, id=14548, stack(0x0000007a2f200000,0x0000007a2f300000)]
  0x000001c6eae484a0 JavaThread "Unconstrained build operations Thread 56" [_thread_blocked, id=7680, stack(0x0000007a2f300000,0x0000007a2f400000)]
  0x000001c6eae4a810 JavaThread "Unconstrained build operations Thread 57" [_thread_blocked, id=23924, stack(0x0000007a2f400000,0x0000007a2f500000)]
  0x000001c6eae4ad20 JavaThread "jar transforms Thread 4" [_thread_blocked, id=4520, stack(0x0000007a2f500000,0x0000007a2f600000)]
  0x000001c6eb27a0a0 JavaThread "jar transforms Thread 5" [_thread_blocked, id=10984, stack(0x0000007a2f600000,0x0000007a2f700000)]
  0x000001c6eb27a5b0 JavaThread "Memory manager" [_thread_blocked, id=36812, stack(0x0000007a2f700000,0x0000007a2f800000)]
  0x000001c6eb27bf00 JavaThread "jar transforms Thread 6" [_thread_blocked, id=37264, stack(0x0000007a2f800000,0x0000007a2f900000)]
  0x000001c6eb27f6b0 JavaThread "included builds" [_thread_blocked, id=30096, stack(0x0000007a2f900000,0x0000007a2fa00000)]
  0x000001c6eb27e270 JavaThread "Execution worker" [_thread_blocked, id=34352, stack(0x0000007a2fa00000,0x0000007a2fb00000)]
  0x000001c6eb279680 JavaThread "Execution worker Thread 2" [_thread_blocked, id=18384, stack(0x0000007a2fb00000,0x0000007a2fc00000)]
  0x000001c6eb27afd0 JavaThread "Execution worker Thread 3" [_thread_blocked, id=36772, stack(0x0000007a2fc00000,0x0000007a2fd00000)]
  0x000001c6eb27ec90 JavaThread "Execution worker Thread 4" [_thread_blocked, id=28592, stack(0x0000007a2fd00000,0x0000007a2fe00000)]
  0x000001c6eb27f1a0 JavaThread "Execution worker Thread 5" [_thread_blocked, id=25400, stack(0x0000007a2fe00000,0x0000007a2ff00000)]
  0x000001c6eb278750 JavaThread "Execution worker Thread 6" [_thread_blocked, id=14024, stack(0x0000007a2ff00000,0x0000007a30000000)]
  0x000001c6eb279b90 JavaThread "Execution worker Thread 7" [_thread_blocked, id=24148, stack(0x0000007a30000000,0x0000007a30100000)]
  0x000001c6eb278240 JavaThread "Execution worker Thread 8" [_thread_blocked, id=27536, stack(0x0000007a30100000,0x0000007a30200000)]
  0x000001c6eb278c60 JavaThread "Execution worker Thread 9" [_thread_blocked, id=31836, stack(0x0000007a30200000,0x0000007a30300000)]
  0x000001c6e707c7f0 JavaThread "Execution worker Thread 10" [_thread_blocked, id=25288, stack(0x0000007a30300000,0x0000007a30400000)]
  0x000001c6e7079550 JavaThread "Execution worker Thread 11" [_thread_blocked, id=36872, stack(0x0000007a30400000,0x0000007a30500000)]
  0x000001c6e7078110 JavaThread "Execution worker Thread 12" [_thread_blocked, id=14452, stack(0x0000007a30500000,0x0000007a30600000)]
  0x000001c6e707b8c0 JavaThread "Execution worker Thread 13" [_thread_blocked, id=34396, stack(0x0000007a30600000,0x0000007a30700000)]
  0x000001c6e7075da0 JavaThread "Execution worker Thread 14" [_thread_blocked, id=33388, stack(0x0000007a30700000,0x0000007a30800000)]
  0x000001c6e70767c0 JavaThread "Execution worker Thread 15" [_thread_blocked, id=8564, stack(0x0000007a30800000,0x0000007a30900000)]
  0x000001c6e7078620 JavaThread "Execution worker Thread 16" [_thread_blocked, id=4764, stack(0x0000007a30900000,0x0000007a30a00000)]
  0x000001c6e7079040 JavaThread "Execution worker Thread 17" [_thread_blocked, id=13192, stack(0x0000007a30a00000,0x0000007a30b00000)]
  0x000001c6e7078b30 JavaThread "Execution worker Thread 18" [_thread_blocked, id=6300, stack(0x0000007a30b00000,0x0000007a30c00000)]
  0x000001c6e707cd00 JavaThread "Execution worker Thread 19" [_thread_blocked, id=36660, stack(0x0000007a30c00000,0x0000007a30d00000)]
  0x000001c6e7079a60 JavaThread "Cache worker for execution history cache (D:\test_project\Glasses\.gradle\8.10.2\executionHistory)" [_thread_blocked, id=11988, stack(0x0000007a30d00000,0x0000007a30e00000)]
  0x000001c6e7079f70 JavaThread "Unconstrained build operations Thread 58" [_thread_blocked, id=31752, stack(0x0000007a30e00000,0x0000007a30f00000)]
  0x000001c6e707aea0 JavaThread "Unconstrained build operations Thread 59" [_thread_blocked, id=30816, stack(0x0000007a30f00000,0x0000007a31000000)]
  0x000001c6e707a480 JavaThread "Unconstrained build operations Thread 60" [_thread_blocked, id=34536, stack(0x0000007a31000000,0x0000007a31100000)]
  0x000001c6e707a990 JavaThread "Unconstrained build operations Thread 61" [_thread_blocked, id=17728, stack(0x0000007a31100000,0x0000007a31200000)]
  0x000001c6e707b3b0 JavaThread "Unconstrained build operations Thread 62" [_thread_blocked, id=14872, stack(0x0000007a31200000,0x0000007a31300000)]
  0x000001c6e707d720 JavaThread "Unconstrained build operations Thread 63" [_thread_blocked, id=33188, stack(0x0000007a31300000,0x0000007a31400000)]
  0x000001c6e707bdd0 JavaThread "Unconstrained build operations Thread 64" [_thread_blocked, id=35596, stack(0x0000007a31400000,0x0000007a31500000)]
  0x000001c6e7077c00 JavaThread "Unconstrained build operations Thread 65" [_thread_blocked, id=2872, stack(0x0000007a31500000,0x0000007a31600000)]
  0x000001c6e707c2e0 JavaThread "Unconstrained build operations Thread 66" [_thread_blocked, id=2708, stack(0x0000007a31600000,0x0000007a31700000)]
  0x000001c6e70776f0 JavaThread "Unconstrained build operations Thread 67" [_thread_blocked, id=31112, stack(0x0000007a31700000,0x0000007a31800000)]
  0x000001c6e7076cd0 JavaThread "Unconstrained build operations Thread 68" [_thread_blocked, id=32696, stack(0x0000007a31800000,0x0000007a31900000)]
  0x000001c6e707d210 JavaThread "Unconstrained build operations Thread 69" [_thread_blocked, id=8104, stack(0x0000007a31900000,0x0000007a31a00000)]
  0x000001c6e70762b0 JavaThread "Unconstrained build operations Thread 70" [_thread_blocked, id=15008, stack(0x0000007a31a00000,0x0000007a31b00000)]
  0x000001c6e70771e0 JavaThread "Unconstrained build operations Thread 71" [_thread_blocked, id=15996, stack(0x0000007a31b00000,0x0000007a31c00000)]
  0x000001c6ea6c0a20 JavaThread "Unconstrained build operations Thread 72" [_thread_blocked, id=37628, stack(0x0000007a31c00000,0x0000007a31d00000)]
  0x000001c6ea6ba4e0 JavaThread "Unconstrained build operations Thread 73" [_thread_blocked, id=37472, stack(0x0000007a31d00000,0x0000007a31e00000)]
  0x000001c6ea6be6b0 JavaThread "Unconstrained build operations Thread 74" [_thread_blocked, id=24684, stack(0x0000007a31e00000,0x0000007a31f00000)]
  0x000001c6ea6b9fd0 JavaThread "Unconstrained build operations Thread 75" [_thread_blocked, id=37288, stack(0x0000007a31f00000,0x0000007a32000000)]
  0x000001c6ea6bc850 JavaThread "Unconstrained build operations Thread 76" [_thread_blocked, id=33608, stack(0x0000007a32000000,0x0000007a32100000)]
  0x000001c6ea6bb920 JavaThread "WorkerExecutor Queue" [_thread_blocked, id=32528, stack(0x0000007a32100000,0x0000007a32200000)]
  0x000001c6ea6bf0d0 JavaThread "Unconstrained build operations Thread 77" [_thread_blocked, id=17948, stack(0x0000007a32200000,0x0000007a32300000)]
  0x000001c6ea6bdc90 JavaThread "Unconstrained build operations Thread 78" [_thread_blocked, id=37692, stack(0x0000007a32300000,0x0000007a32400000)]
  0x000001c6ea6bcd60 JavaThread "Unconstrained build operations Thread 79" [_thread_blocked, id=8672, stack(0x0000007a32400000,0x0000007a32500000)]
  0x000001c6ea6c0f30 JavaThread "Unconstrained build operations Thread 80" [_thread_blocked, id=33988, stack(0x0000007a32500000,0x0000007a32600000)]
  0x000001c6ea6be1a0 JavaThread "Unconstrained build operations Thread 81" [_thread_blocked, id=3796, stack(0x0000007a32600000,0x0000007a32700000)]
  0x000001c6ea6bebc0 JavaThread "Unconstrained build operations Thread 82" [_thread_blocked, id=4864, stack(0x0000007a32700000,0x0000007a32800000)]
  0x000001c6ea6bfaf0 JavaThread "Unconstrained build operations Thread 83" [_thread_blocked, id=34160, stack(0x0000007a32800000,0x0000007a32900000)]
  0x000001c6ea6c0000 JavaThread "Unconstrained build operations Thread 84" [_thread_blocked, id=11104, stack(0x0000007a32900000,0x0000007a32a00000)]
  0x000001c6ea6c0510 JavaThread "Unconstrained build operations Thread 85" [_thread_blocked, id=25584, stack(0x0000007a32a00000,0x0000007a32b00000)]
  0x000001c6ea6b9ac0 JavaThread "Unconstrained build operations Thread 86" [_thread_blocked, id=14492, stack(0x0000007a32b00000,0x0000007a32c00000)]
  0x000001c6ea6c1440 JavaThread "Unconstrained build operations Thread 87" [_thread_blocked, id=37128, stack(0x0000007a32c00000,0x0000007a32d00000)]
  0x000001c6ea6bbe30 JavaThread "Unconstrained build operations Thread 88" [_thread_blocked, id=33084, stack(0x0000007a32d00000,0x0000007a32e00000)]
  0x000001c6ea6bc340 JavaThread "Unconstrained build operations Thread 89" [_thread_blocked, id=16060, stack(0x0000007a32e00000,0x0000007a32f00000)]
  0x000001c6ea6bd780 JavaThread "Unconstrained build operations Thread 90" [_thread_blocked, id=36980, stack(0x0000007a32f00000,0x0000007a33000000)]
  0x000001c6ea6bd270 JavaThread "Unconstrained build operations Thread 91" [_thread_blocked, id=14628, stack(0x0000007a33000000,0x0000007a33100000)]
  0x000001c6ea6baf00 JavaThread "Unconstrained build operations Thread 92" [_thread_blocked, id=29152, stack(0x0000007a33100000,0x0000007a33200000)]
  0x000001c6e829aa60 JavaThread "Unconstrained build operations Thread 93" [_thread_blocked, id=28260, stack(0x0000007a33200000,0x0000007a33300000)]
  0x000001c6e8294520 JavaThread "Unconstrained build operations Thread 94" [_thread_blocked, id=15072, stack(0x0000007a33300000,0x0000007a33400000)]
  0x000001c6e8294a30 JavaThread "Unconstrained build operations Thread 95" [_thread_blocked, id=24620, stack(0x0000007a33400000,0x0000007a33500000)]
  0x000001c6e82986f0 JavaThread "WorkerExecutor Queue Thread 2" [_thread_blocked, id=31672, stack(0x0000007a33500000,0x0000007a33600000)]
  0x000001c6e8295e70 JavaThread "Unconstrained build operations Thread 96" [_thread_blocked, id=25336, stack(0x0000007a33600000,0x0000007a33700000)]
  0x000001c6e829af70 JavaThread "Unconstrained build operations Thread 97" [_thread_blocked, id=27544, stack(0x0000007a33700000,0x0000007a33800000)]
  0x000001c6e8299110 JavaThread "Unconstrained build operations Thread 98" [_thread_blocked, id=37824, stack(0x0000007a33800000,0x0000007a33900000)]
  0x000001c6e8296da0 JavaThread "Unconstrained build operations Thread 99" [_thread_blocked, id=34428, stack(0x0000007a33900000,0x0000007a33a00000)]
  0x000001c6e829a550 JavaThread "Unconstrained build operations Thread 100" [_thread_blocked, id=33168, stack(0x0000007a33a00000,0x0000007a33b00000)]
  0x000001c6e8295450 JavaThread "Unconstrained build operations Thread 101" [_thread_blocked, id=28604, stack(0x0000007a33b00000,0x0000007a33c00000)]
  0x000001c6e829b480 JavaThread "Unconstrained build operations Thread 102" [_thread_blocked, id=37220, stack(0x0000007a33c00000,0x0000007a33d00000)]
  0x000001c6e82977c0 JavaThread "Unconstrained build operations Thread 103" [_thread_blocked, id=26968, stack(0x0000007a33d00000,0x0000007a33e00000)]
  0x000001c6e8298c00 JavaThread "Unconstrained build operations Thread 104" [_thread_blocked, id=14400, stack(0x0000007a33e00000,0x0000007a33f00000)]
  0x000001c6e8295960 JavaThread "Unconstrained build operations Thread 105" [_thread_blocked, id=37496, stack(0x0000007a33f00000,0x0000007a34000000)]
  0x000001c6e8294010 JavaThread "Unconstrained build operations Thread 106" [_thread_blocked, id=36456, stack(0x0000007a34000000,0x0000007a34100000)]
  0x000001c6e829b990 JavaThread "Unconstrained build operations Thread 107" [_thread_blocked, id=37108, stack(0x0000007a34100000,0x0000007a34200000)]
  0x000001c6e8299620 JavaThread "Unconstrained build operations Thread 108" [_thread_blocked, id=3672, stack(0x0000007a34200000,0x0000007a34300000)]
  0x000001c6e829a040 JavaThread "Unconstrained build operations Thread 109" [_thread_blocked, id=34084, stack(0x0000007a34300000,0x0000007a34400000)]
  0x000001c6e8296380 JavaThread "Unconstrained build operations Thread 110" [_thread_blocked, id=26576, stack(0x0000007a34400000,0x0000007a34500000)]
  0x000001c6e8296890 JavaThread "Unconstrained build operations Thread 111" [_thread_blocked, id=5652, stack(0x0000007a34500000,0x0000007a34600000)]
  0x000001c6e82981e0 JavaThread "Unconstrained build operations Thread 112" [_thread_blocked, id=3364, stack(0x0000007a34600000,0x0000007a34700000)]
  0x000001c6ea6ba9f0 JavaThread "Unconstrained build operations Thread 113" [_thread_blocked, id=15632, stack(0x0000007a34700000,0x0000007a34800000)]
  0x000001c6ea6bb410 JavaThread "Unconstrained build operations Thread 114" [_thread_blocked, id=18900, stack(0x0000007a34800000,0x0000007a34900000)]
  0x000001c6ec260c40 JavaThread "WorkerExecutor Queue Thread 3" [_thread_blocked, id=37724, stack(0x0000007a2b200000,0x0000007a2b300000)]
  0x000001c6ec261b70 JavaThread "WorkerExecutor Queue Thread 4" [_thread_blocked, id=37240, stack(0x0000007a35400000,0x0000007a35500000)]
  0x000001c6ecd83ba0 JavaThread "WorkerExecutor Queue Thread 5" [_thread_blocked, id=32944, stack(0x0000007a35700000,0x0000007a35800000)]
  0x000001c6ecd84ad0 JavaThread "WorkerExecutor Queue Thread 6" [_thread_blocked, id=3620, stack(0x0000007a35800000,0x0000007a35900000)]
  0x000001c6ecd83690 JavaThread "WorkerExecutor Queue Thread 7" [_thread_blocked, id=35896, stack(0x0000007a35900000,0x0000007a35a00000)]
  0x000001c6ecd845c0 JavaThread "WorkerExecutor Queue Thread 8" [_thread_blocked, id=33552, stack(0x0000007a35a00000,0x0000007a35b00000)]
  0x000001c6ecd84fe0 JavaThread "WorkerExecutor Queue Thread 9" [_thread_blocked, id=32832, stack(0x0000007a35b00000,0x0000007a35c00000)]
  0x000001c6eb21db80 JavaThread "WorkerExecutor Queue Thread 10" [_thread_blocked, id=37124, stack(0x0000007a35c00000,0x0000007a35d00000)]
  0x000001c6eb21e090 JavaThread "WorkerExecutor Queue Thread 11" [_thread_blocked, id=26204, stack(0x0000007a35d00000,0x0000007a35e00000)]
  0x000001c6eb21e5a0 JavaThread "WorkerExecutor Queue Thread 12" [_thread_blocked, id=4196, stack(0x0000007a35e00000,0x0000007a35f00000)]
  0x000001c6ecd82920 JavaThread "WorkerExecutor Queue Thread 13" [_thread_blocked, id=9300, stack(0x0000007a35f00000,0x0000007a36000000)]
  0x000001c6ecd82e30 JavaThread "WorkerExecutor Queue Thread 14" [_thread_blocked, id=37448, stack(0x0000007a36000000,0x0000007a36100000)]
  0x000001c6ecd7fb90 JavaThread "WorkerExecutor Queue Thread 15" [_thread_blocked, id=37144, stack(0x0000007a36100000,0x0000007a36200000)]
  0x000001c6ecd800a0 JavaThread "WorkerExecutor Queue Thread 16" [_thread_blocked, id=37436, stack(0x0000007a36200000,0x0000007a36300000)]
  0x000001c6ecd80ac0 JavaThread "WorkerExecutor Queue Thread 17" [_thread_blocked, id=1904, stack(0x0000007a36300000,0x0000007a36400000)]
  0x000001c6eb41c860 JavaThread "WorkerExecutor Queue Thread 18" [_thread_blocked, id=7352, stack(0x0000007a36400000,0x0000007a36500000)]
  0x000001c6ec261150 JavaThread "WorkerExecutor Queue Thread 19" [_thread_blocked, id=29100, stack(0x0000007a36500000,0x0000007a36600000)]
  0x000001c6ec261660 JavaThread "WorkerExecutor Queue Thread 20" [_thread_blocked, id=26544, stack(0x0000007a36600000,0x0000007a36700000)]
  0x000001c6ec262080 JavaThread "Unconstrained build operations Thread 115" [_thread_blocked, id=33756, stack(0x0000007a36700000,0x0000007a36800000)]
  0x000001c6ec262590 JavaThread "Unconstrained build operations Thread 116" [_thread_blocked, id=5704, stack(0x0000007a36800000,0x0000007a36900000)]
  0x000001c6a1708e80 JavaThread "Unconstrained build operations Thread 117" [_thread_blocked, id=36892, stack(0x0000007a36900000,0x0000007a36a00000)]
  0x000001c6ecf4b470 JavaThread "Unconstrained build operations Thread 118" [_thread_blocked, id=28848, stack(0x0000007a36a00000,0x0000007a36b00000)]
  0x000001c6ecbacd90 JavaThread "Unconstrained build operations Thread 119" [_thread_blocked, id=16792, stack(0x0000007a36b00000,0x0000007a36c00000)]
  0x000001c6ecbae6e0 JavaThread "Unconstrained build operations Thread 120" [_thread_blocked, id=35672, stack(0x0000007a36c00000,0x0000007a36d00000)]
  0x000001c6ecbad7b0 JavaThread "Unconstrained build operations Thread 121" [_thread_blocked, id=23672, stack(0x0000007a36d00000,0x0000007a36e00000)]
  0x000001c6ecbad2a0 JavaThread "Unconstrained build operations Thread 122" [_thread_blocked, id=36888, stack(0x0000007a36e00000,0x0000007a36f00000)]
  0x000001c6ecbadcc0 JavaThread "Unconstrained build operations Thread 123" [_thread_blocked, id=28968, stack(0x0000007a36f00000,0x0000007a37000000)]
  0x000001c6ecbae1d0 JavaThread "Unconstrained build operations Thread 124" [_thread_blocked, id=6816, stack(0x0000007a37000000,0x0000007a37100000)]
  0x000001c6eca10100 JavaThread "Unconstrained build operations Thread 125" [_thread_blocked, id=20872, stack(0x0000007a37100000,0x0000007a37200000)]
  0x000001c6eca10b20 JavaThread "Unconstrained build operations Thread 126" [_thread_blocked, id=16964, stack(0x0000007a37200000,0x0000007a37300000)]
  0x000001c6eca10610 JavaThread "Unconstrained build operations Thread 127" [_thread_blocked, id=37048, stack(0x0000007a37300000,0x0000007a37400000)]
  0x000001c6eca11030 JavaThread "Unconstrained build operations Thread 128" [_thread_blocked, id=15232, stack(0x0000007a37400000,0x0000007a37500000)]
  0x000001c6eca11540 JavaThread "Unconstrained build operations Thread 129" [_thread_blocked, id=25808, stack(0x0000007a37500000,0x0000007a37600000)]
  0x000001c6eca11a50 JavaThread "Unconstrained build operations Thread 130" [_thread_blocked, id=32652, stack(0x0000007a37600000,0x0000007a37700000)]
  0x000001c6eca11f60 JavaThread "Unconstrained build operations Thread 131" [_thread_blocked, id=24828, stack(0x0000007a37700000,0x0000007a37800000)]
  0x000001c6eca12470 JavaThread "Unconstrained build operations Thread 132" [_thread_blocked, id=10892, stack(0x0000007a37a00000,0x0000007a37b00000)]
  0x000001c6eca0fbf0 JavaThread "Unconstrained build operations Thread 133" [_thread_blocked, id=10808, stack(0x0000007a37b00000,0x0000007a37c00000)]
  0x000001c6eca0f1d0 JavaThread "Unconstrained build operations Thread 134" [_thread_blocked, id=37316, stack(0x0000007a37c00000,0x0000007a37d00000)]
  0x000001c6eca0ecc0 JavaThread "Unconstrained build operations Thread 135" [_thread_blocked, id=4324, stack(0x0000007a37d00000,0x0000007a37e00000)]
  0x000001c6eca0f6e0 JavaThread "Unconstrained build operations Thread 136" [_thread_blocked, id=8936, stack(0x0000007a37e00000,0x0000007a37f00000)]
  0x000001c6eb0be6a0 JavaThread "Unconstrained build operations Thread 137" [_thread_blocked, id=16188, stack(0x0000007a37f00000,0x0000007a38000000)]
  0x000001c6eb0bebb0 JavaThread "Unconstrained build operations Thread 138" [_thread_blocked, id=8004, stack(0x0000007a38000000,0x0000007a38100000)]
  0x000001c6eb0bdc80 JavaThread "Unconstrained build operations Thread 139" [_thread_blocked, id=37668, stack(0x0000007a38100000,0x0000007a38200000)]
  0x000001c6eb0be190 JavaThread "Unconstrained build operations Thread 140" [_thread_blocked, id=36188, stack(0x0000007a38200000,0x0000007a38300000)]
  0x000001c6eb0bc330 JavaThread "Unconstrained build operations Thread 141" [_thread_blocked, id=18576, stack(0x0000007a38300000,0x0000007a38400000)]
  0x000001c6eb0bd260 JavaThread "Unconstrained build operations Thread 142" [_thread_blocked, id=31096, stack(0x0000007a38400000,0x0000007a38500000)]
  0x000001c6eb0bf0c0 JavaThread "Unconstrained build operations Thread 143" [_thread_blocked, id=37216, stack(0x0000007a38500000,0x0000007a38600000)]
  0x000001c6eb0bc840 JavaThread "Unconstrained build operations Thread 144" [_thread_blocked, id=32312, stack(0x0000007a38600000,0x0000007a38700000)]
  0x000001c6eb0bbe20 JavaThread "Unconstrained build operations Thread 145" [_thread_blocked, id=16712, stack(0x0000007a38700000,0x0000007a38800000)]
  0x000001c6eb0bf5d0 JavaThread "Unconstrained build operations Thread 146" [_thread_blocked, id=36572, stack(0x0000007a38800000,0x0000007a38900000)]
  0x000001c6eb0bcd50 JavaThread "Unconstrained build operations Thread 147" [_thread_blocked, id=25420, stack(0x0000007a38900000,0x0000007a38a00000)]
  0x000001c6eb0bd770 JavaThread "Unconstrained build operations Thread 148" [_thread_blocked, id=37396, stack(0x0000007a38a00000,0x0000007a38b00000)]
  0x000001c6ea9d0870 JavaThread "Unconstrained build operations Thread 149" [_thread_blocked, id=33528, stack(0x0000007a38b00000,0x0000007a38c00000)]
  0x000001c6ea9d3b10 JavaThread "Unconstrained build operations Thread 150" [_thread_blocked, id=37632, stack(0x0000007a38c00000,0x0000007a38d00000)]
  0x000001c6ea9d2be0 JavaThread "Unconstrained build operations Thread 151" [_thread_blocked, id=14976, stack(0x0000007a38d00000,0x0000007a38e00000)]
  0x000001c6ea9d21c0 JavaThread "Unconstrained build operations Thread 152" [_thread_blocked, id=33276, stack(0x0000007a38e00000,0x0000007a38f00000)]
  0x000001c6ea9d17a0 JavaThread "Unconstrained build operations Thread 153" [_thread_blocked, id=11368, stack(0x0000007a38f00000,0x0000007a39000000)]
  0x000001c6ea9d26d0 JavaThread "Unconstrained build operations Thread 154" [_thread_blocked, id=29480, stack(0x0000007a39000000,0x0000007a39100000)]
  0x000001c6ea9d1cb0 JavaThread "Unconstrained build operations Thread 155" [_thread_blocked, id=16096, stack(0x0000007a39100000,0x0000007a39200000)]
  0x000001c6eaa17870 JavaThread "Unconstrained build operations Thread 156" [_thread_blocked, id=26932, stack(0x0000007a39200000,0x0000007a39300000)]
  0x000001c6eaa17d80 JavaThread "Unconstrained build operations Thread 157" [_thread_blocked, id=31460, stack(0x0000007a39300000,0x0000007a39400000)]
  0x000001c6eaa191c0 JavaThread "Unconstrained build operations Thread 158" [_thread_blocked, id=26916, stack(0x0000007a39400000,0x0000007a39500000)]
  0x000001c6eaa18290 JavaThread "Unconstrained build operations Thread 159" [_thread_blocked, id=27964, stack(0x0000007a39500000,0x0000007a39600000)]
  0x000001c6eaa187a0 JavaThread "Unconstrained build operations Thread 160" [_thread_blocked, id=36852, stack(0x0000007a39600000,0x0000007a39700000)]
  0x000001c6ea9d30f0 JavaThread "Unconstrained build operations Thread 161" [_thread_blocked, id=14252, stack(0x0000007a39700000,0x0000007a39800000)]
  0x000001c6ea9d0d80 JavaThread "Unconstrained build operations Thread 162" [_thread_blocked, id=6356, stack(0x0000007a39800000,0x0000007a39900000)]
  0x000001c6ea9d3600 JavaThread "Unconstrained build operations Thread 163" [_thread_blocked, id=4140, stack(0x0000007a39900000,0x0000007a39a00000)]
  0x000001c6ea9d1290 JavaThread "Unconstrained build operations Thread 164" [_thread_blocked, id=36304, stack(0x0000007a39a00000,0x0000007a39b00000)]
  0x000001c6ea9d4020 JavaThread "Unconstrained build operations Thread 165" [_thread_blocked, id=36724, stack(0x0000007a39b00000,0x0000007a39c00000)]
  0x000001c6ed516440 JavaThread "Unconstrained build operations Thread 166" [_thread_blocked, id=36260, stack(0x0000007a39c00000,0x0000007a39d00000)]
  0x000001c6ed514af0 JavaThread "Unconstrained build operations Thread 167" [_thread_blocked, id=30024, stack(0x0000007a39d00000,0x0000007a39e00000)]
  0x000001c6ed516e60 JavaThread "Unconstrained build operations Thread 168" [_thread_blocked, id=5808, stack(0x0000007a39e00000,0x0000007a39f00000)]
  0x000001c6ecf4c3a0 JavaThread "Unconstrained build operations Thread 169" [_thread_blocked, id=2760, stack(0x0000007a39f00000,0x0000007a3a000000)]
  0x000001c6ecf4c8b0 JavaThread "Unconstrained build operations Thread 170" [_thread_blocked, id=37772, stack(0x0000007a3a000000,0x0000007a3a100000)]
  0x000001c6ecf4be90 JavaThread "Unconstrained build operations Thread 171" [_thread_blocked, id=36936, stack(0x0000007a3a100000,0x0000007a3a200000)]
  0x000001c6ed51cf60 JavaThread "Unconstrained build operations Thread 172" [_thread_blocked, id=36240, stack(0x0000007a3a200000,0x0000007a3a300000)]
  0x000001c6ed51ca50 JavaThread "Unconstrained build operations Thread 173" [_thread_blocked, id=33392, stack(0x0000007a3a300000,0x0000007a3a400000)]
  0x000001c6ec296460 JavaThread "Unconstrained build operations Thread 174" [_thread_blocked, id=37016, stack(0x0000007a3a400000,0x0000007a3a500000)]
  0x000001c6ebcfd6b0 JavaThread "Unconstrained build operations Thread 175" [_thread_blocked, id=29780, stack(0x0000007a3a500000,0x0000007a3a600000)]
  0x000001c6ecf34620 JavaThread "Unconstrained build operations Thread 176" [_thread_blocked, id=32996, stack(0x0000007a3a600000,0x0000007a3a700000)]
  0x000001c6ecf34b30 JavaThread "Unconstrained build operations Thread 177" [_thread_blocked, id=3536, stack(0x0000007a3a700000,0x0000007a3a800000)]
  0x000001c6ecf35040 JavaThread "Unconstrained build operations Thread 178" [_thread_blocked, id=12772, stack(0x0000007a3a800000,0x0000007a3a900000)]
  0x000001c6ecf336f0 JavaThread "Unconstrained build operations Thread 179" [_thread_blocked, id=23620, stack(0x0000007a3a900000,0x0000007a3aa00000)]
  0x000001c6ecf34110 JavaThread "Unconstrained build operations Thread 180" [_thread_blocked, id=25668, stack(0x0000007a3aa00000,0x0000007a3ab00000)]
  0x000001c6ed113260 JavaThread "Unconstrained build operations Thread 181" [_thread_blocked, id=36512, stack(0x0000007a3ab00000,0x0000007a3ac00000)]
  0x000001c6ed113770 JavaThread "Unconstrained build operations Thread 182" [_thread_blocked, id=33532, stack(0x0000007a3ac00000,0x0000007a3ad00000)]
  0x000001c6ebcfbd60 JavaThread "Unconstrained build operations Thread 183" [_thread_blocked, id=37328, stack(0x0000007a3ad00000,0x0000007a3ae00000)]
  0x000001c6ebcfc270 JavaThread "Unconstrained build operations Thread 184" [_thread_blocked, id=25888, stack(0x0000007a3ae00000,0x0000007a3af00000)]
  0x000001c6ebcfc780 JavaThread "Unconstrained build operations Thread 185" [_thread_blocked, id=11072, stack(0x0000007a3af00000,0x0000007a3b000000)]
  0x000001c6ebcfd1a0 JavaThread "Unconstrained build operations Thread 186" [_thread_blocked, id=9988, stack(0x0000007a3b000000,0x0000007a3b100000)]
  0x000001c6ebcfcc90 JavaThread "Unconstrained build operations Thread 187" [_thread_blocked, id=25664, stack(0x0000007a3b100000,0x0000007a3b200000)]
  0x000001c6ec96eb70 JavaThread "Unconstrained build operations Thread 188" [_thread_blocked, id=33240, stack(0x0000007a3b200000,0x0000007a3b300000)]
  0x000001c6ec970ee0 JavaThread "Unconstrained build operations Thread 189" [_thread_blocked, id=37296, stack(0x0000007a3b300000,0x0000007a3b400000)]
  0x000001c6ec96e150 JavaThread "Unconstrained build operations Thread 190" [_thread_blocked, id=24524, stack(0x0000007a3b400000,0x0000007a3b500000)]
  0x000001c6ec96ffb0 JavaThread "Unconstrained build operations Thread 191" [_thread_blocked, id=23644, stack(0x0000007a3b500000,0x0000007a3b600000)]
  0x000001c6ec96faa0 JavaThread "Unconstrained build operations Thread 192" [_thread_blocked, id=29012, stack(0x0000007a3b600000,0x0000007a3b700000)]
  0x000001c6ec96f080 JavaThread "Unconstrained build operations Thread 193" [_thread_blocked, id=35712, stack(0x0000007a3b700000,0x0000007a3b800000)]
  0x000001c6ec96f590 JavaThread "Unconstrained build operations Thread 194" [_thread_blocked, id=35072, stack(0x0000007a3b800000,0x0000007a3b900000)]
  0x000001c6ec9704c0 JavaThread "Unconstrained build operations Thread 195" [_thread_blocked, id=8740, stack(0x0000007a3b900000,0x0000007a3ba00000)]
  0x000001c6ec96dc40 JavaThread "Unconstrained build operations Thread 196" [_thread_blocked, id=3304, stack(0x0000007a3ba00000,0x0000007a3bb00000)]
  0x000001c6ec96d730 JavaThread "Unconstrained build operations Thread 197" [_thread_blocked, id=37740, stack(0x0000007a3bb00000,0x0000007a3bc00000)]
  0x000001c6ec96e660 JavaThread "Unconstrained build operations Thread 198" [_thread_blocked, id=17236, stack(0x0000007a3bc00000,0x0000007a3bd00000)]
  0x000001c6ec9709d0 JavaThread "Unconstrained build operations Thread 199" [_thread_blocked, id=35856, stack(0x0000007a3bd00000,0x0000007a3be00000)]
  0x000001c6e71765e0 JavaThread "Unconstrained build operations Thread 200" [_thread_blocked, id=34328, stack(0x0000007a3be00000,0x0000007a3bf00000)]
  0x000001c6ed112d50 JavaThread "WorkerExecutor Queue Thread 21" [_thread_blocked, id=36996, stack(0x0000007a3bf00000,0x0000007a3c000000)]
  0x000001c6e7ee7cf0 JavaThread "pool-3-thread-1" [_thread_blocked, id=9184, stack(0x0000007a3c000000,0x0000007a3c100000)]
  0x000001c6e7ee5980 JavaThread "stderr" [_thread_in_native, id=7700, stack(0x0000007a3c100000,0x0000007a3c200000)]
  0x000001c6e7ee4a50 JavaThread "stdout" [_thread_in_native, id=29892, stack(0x0000007a3c200000,0x0000007a3c300000)]
  0x000001c6e82b9f70 JavaThread "Exec process" [_thread_blocked, id=16392, stack(0x0000007a3c300000,0x0000007a3c400000)]
  0x000001c6ecd82410 JavaThread "Exec process Thread 2" [_thread_blocked, id=37868, stack(0x0000007a3c400000,0x0000007a3c500000)]
  0x000001c6ed51d470 JavaThread "Exec process Thread 3" [_thread_blocked, id=37352, stack(0x0000007a3c500000,0x0000007a3c600000)]
  0x000001c6ed51de90 JavaThread "Exec process Thread 4" [_thread_blocked, id=16232, stack(0x0000007a3c600000,0x0000007a3c700000)]

Other Threads:
=>0x000001c6a13bb860 VMThread "VM Thread" [stack: 0x0000007a28200000,0x0000007a28300000] [id=30664]
  0x000001c6a1752920 WatcherThread [stack: 0x0000007a29200000,0x0000007a29300000] [id=36868]
  0x000001c6821d7910 GCTaskThread "GC Thread#0" [stack: 0x0000007a27d00000,0x0000007a27e00000] [id=37856]
  0x000001c6e7262010 GCTaskThread "GC Thread#1" [stack: 0x0000007a29300000,0x0000007a29400000] [id=37532]
  0x000001c6e755ffb0 GCTaskThread "GC Thread#2" [stack: 0x0000007a29400000,0x0000007a29500000] [id=30284]
  0x000001c6e75b7840 GCTaskThread "GC Thread#3" [stack: 0x0000007a29500000,0x0000007a29600000] [id=10856]
  0x000001c6e763cd80 GCTaskThread "GC Thread#4" [stack: 0x0000007a29600000,0x0000007a29700000] [id=29240]
  0x000001c6e7706150 GCTaskThread "GC Thread#5" [stack: 0x0000007a29700000,0x0000007a29800000] [id=5616]
  0x000001c6e7704e10 GCTaskThread "GC Thread#6" [stack: 0x0000007a29900000,0x0000007a29a00000] [id=4312]
  0x000001c6e77050d0 GCTaskThread "GC Thread#7" [stack: 0x0000007a29a00000,0x0000007a29b00000] [id=37080]
  0x000001c6e7705bd0 GCTaskThread "GC Thread#8" [stack: 0x0000007a29b00000,0x0000007a29c00000] [id=25572]
  0x000001c6e7705650 GCTaskThread "GC Thread#9" [stack: 0x0000007a29c00000,0x0000007a29d00000] [id=35740]
  0x000001c6e7705390 GCTaskThread "GC Thread#10" [stack: 0x0000007a29d00000,0x0000007a29e00000] [id=17836]
  0x000001c6e7705e90 GCTaskThread "GC Thread#11" [stack: 0x0000007a29e00000,0x0000007a29f00000] [id=11864]
  0x000001c6e92f61d0 GCTaskThread "GC Thread#12" [stack: 0x0000007a2ac00000,0x0000007a2ad00000] [id=36880]
  0x000001c6e92f6490 GCTaskThread "GC Thread#13" [stack: 0x0000007a2ad00000,0x0000007a2ae00000] [id=18000]
  0x000001c6e92f5c50 GCTaskThread "GC Thread#14" [stack: 0x0000007a2ae00000,0x0000007a2af00000] [id=19364]
  0x000001c6821e9780 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000007a27e00000,0x0000007a27f00000] [id=3908]
  0x000001c6821ea840 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000007a27f00000,0x0000007a28000000] [id=14204]
  0x000001c6e92f82d0 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000007a2af00000,0x0000007a2b000000] [id=36924]
  0x000001c6e92f6f90 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000007a2b000000,0x0000007a2b100000] [id=36896]
  0x000001c6e92f8590 ConcurrentGCThread "G1 Conc#3" [stack: 0x0000007a2b100000,0x0000007a2b200000] [id=23136]
  0x000001c6a12f4dc0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000007a28000000,0x0000007a28100000] [id=27704]
  0x000001c6ed490ad0 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000007a34900000,0x0000007a34a00000] [id=23424]
  0x000001c6ed48d010 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000007a34a00000,0x0000007a34b00000] [id=11444]
  0x000001c6ed48d300 ConcurrentGCThread "G1 Refine#3" [stack: 0x0000007a34b00000,0x0000007a34c00000] [id=34744]
  0x000001c6ed48ff10 ConcurrentGCThread "G1 Refine#4" [stack: 0x0000007a34c00000,0x0000007a34d00000] [id=30512]
  0x000001c6ed490200 ConcurrentGCThread "G1 Refine#5" [stack: 0x0000007a34d00000,0x0000007a34e00000] [id=37732]
  0x000001c6ed48d5f0 ConcurrentGCThread "G1 Refine#6" [stack: 0x0000007a34e00000,0x0000007a34f00000] [id=26460]
  0x000001c6ed48f930 ConcurrentGCThread "G1 Refine#7" [stack: 0x0000007a34f00000,0x0000007a35000000] [id=37536]
  0x000001c6ed48dec0 ConcurrentGCThread "G1 Refine#8" [stack: 0x0000007a35000000,0x0000007a35100000] [id=34576]
  0x000001c6ed48e1b0 ConcurrentGCThread "G1 Refine#9" [stack: 0x0000007a35100000,0x0000007a35200000] [id=35268]
  0x000001c6ed48f060 ConcurrentGCThread "G1 Refine#10" [stack: 0x0000007a35200000,0x0000007a35300000] [id=26260]
  0x000001c6ed48e4a0 ConcurrentGCThread "G1 Refine#11" [stack: 0x0000007a35300000,0x0000007a35400000] [id=31708]
  0x000001c6ed48f350 ConcurrentGCThread "G1 Refine#12" [stack: 0x0000007a35500000,0x0000007a35600000] [id=35572]
  0x000001c6ed48f640 ConcurrentGCThread "G1 Refine#13" [stack: 0x0000007a35600000,0x0000007a35700000] [id=35368]
  0x000001c6a12f5700 ConcurrentGCThread "G1 Service" [stack: 0x0000007a28100000,0x0000007a28200000] [id=14848]

Threads with active compile tasks:
C2 CompilerThread0    20587 18665       4       java.lang.PublicMethods::merge (81 bytes)
C1 CompilerThread2    20587 18742       2       java.util.Optional::flatMap (38 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001c6821783f0] Threads_lock - owner thread: 0x000001c6a13bb860
[0x000001c682177550] Heap_lock - owner thread: 0x000001c6e7078b30

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001c6a2000000-0x000001c6a2bd0000-0x000001c6a2bd0000), size 12386304, SharedBaseAddress: 0x000001c6a2000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001c6a3000000-0x000001c6e3000000, reserved size: 1073741824
Narrow klass base: 0x000001c6a2000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 20 total, 20 available
 Memory: 16108M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 15
 Concurrent Workers: 4
 Concurrent Refinement Workers: 15
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 362496K, used 180611K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 109963K, committed 110912K, reserved 1179648K
  class space    used 14862K, committed 15360K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000, 0x0000000080100000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HS|  |TAMS 0x0000000080200000, 0x0000000080200000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000, 0x0000000080300000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080400000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%|HC|  |TAMS 0x0000000080500000, 0x0000000080500000| Complete 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000, 0x0000000080600000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080700000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000, 0x0000000080800000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%|HS|  |TAMS 0x0000000080900000, 0x0000000080900000| Complete 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080a00000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000, 0x0000000080b00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080c00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000, 0x0000000080d00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080e00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000, 0x0000000080f00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000, 0x0000000081000000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081100000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081200000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000, 0x0000000081300000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000, 0x0000000081400000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000, 0x0000000081500000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000, 0x0000000081600000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000, 0x0000000081700000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000, 0x0000000081800000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000, 0x0000000081900000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000, 0x0000000081a00000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000, 0x0000000081b00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000, 0x0000000081c00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000, 0x0000000081d00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000, 0x0000000081e00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081f00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000, 0x0000000082000000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000, 0x0000000082100000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000, 0x0000000082200000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000, 0x0000000082300000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000, 0x0000000082400000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000, 0x0000000082500000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000, 0x0000000082600000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000, 0x0000000082700000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000, 0x0000000082800000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000, 0x0000000082900000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000, 0x0000000082a00000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000, 0x0000000082b00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000, 0x0000000082c00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000, 0x0000000082d00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000, 0x0000000082e00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082f00000, 0x0000000082f00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000083000000, 0x0000000083000000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083100000, 0x0000000083100000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HS|  |TAMS 0x0000000083200000, 0x0000000083200000| Complete 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HS|  |TAMS 0x0000000083300000, 0x0000000083300000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%|HS|  |TAMS 0x0000000083400000, 0x0000000083400000| Complete 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%|HS|  |TAMS 0x0000000083500000, 0x0000000083500000| Complete 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083600000, 0x0000000083600000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HS|  |TAMS 0x0000000083700000, 0x0000000083700000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%|HS|  |TAMS 0x0000000083800000, 0x0000000083800000| Complete 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000, 0x0000000083900000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000, 0x0000000083a00000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083b00000, 0x0000000083b00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000, 0x0000000083c00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000, 0x0000000083d00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000, 0x0000000083e00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083f00000, 0x0000000083f00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000, 0x0000000084000000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000, 0x0000000084100000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000, 0x0000000084200000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000, 0x0000000084300000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%|HS|  |TAMS 0x0000000084400000, 0x0000000084400000| Complete 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000, 0x0000000084500000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000, 0x0000000084600000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000, 0x0000000084700000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000, 0x0000000084800000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000, 0x0000000084900000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000, 0x0000000084a00000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000, 0x0000000084b00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000, 0x0000000084c00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000, 0x0000000084d00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000, 0x0000000084e00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000, 0x0000000084f00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000, 0x0000000085000000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000, 0x0000000085100000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000, 0x0000000085200000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000, 0x0000000085300000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000, 0x0000000085400000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000, 0x0000000085500000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000, 0x0000000085600000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000, 0x0000000085700000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000, 0x0000000085800000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000, 0x0000000085900000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000, 0x0000000085a00000| Untracked 
|  90|0x0000000085a00000, 0x0000000085a60c00, 0x0000000085b00000| 37%| O|  |TAMS 0x0000000085a00000, 0x0000000085a60c00| Untracked 
|  91|0x0000000085b00000, 0x0000000085b00000, 0x0000000085c00000|  0%| F|  |TAMS 0x0000000085b00000, 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085c00000, 0x0000000085d00000|  0%| F|  |TAMS 0x0000000085c00000, 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085d00000, 0x0000000085e00000|  0%| F|  |TAMS 0x0000000085d00000, 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085e00000, 0x0000000085f00000|  0%| F|  |TAMS 0x0000000085e00000, 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000085f00000, 0x0000000086000000|  0%| F|  |TAMS 0x0000000085f00000, 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086000000, 0x0000000086100000|  0%| F|  |TAMS 0x0000000086000000, 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086100000, 0x0000000086200000|  0%| F|  |TAMS 0x0000000086100000, 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086200000, 0x0000000086300000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086300000, 0x0000000086400000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000, 0x0000000086500000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000, 0x0000000086600000| Untracked 
| 102|0x0000000086600000, 0x0000000086600000, 0x0000000086700000|  0%| F|  |TAMS 0x0000000086600000, 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086700000, 0x0000000086800000|  0%| F|  |TAMS 0x0000000086700000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000, 0x0000000086900000| Untracked 
| 105|0x0000000086900000, 0x0000000086900000, 0x0000000086a00000|  0%| F|  |TAMS 0x0000000086900000, 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086a00000, 0x0000000086b00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086b00000, 0x0000000086c00000|  0%| F|  |TAMS 0x0000000086b00000, 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086c00000, 0x0000000086d00000|  0%| F|  |TAMS 0x0000000086c00000, 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000, 0x0000000086e00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086e00000, 0x0000000086f00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000, 0x0000000087000000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087000000, 0x0000000087100000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087100000, 0x0000000087200000| Untracked 
| 114|0x0000000087200000, 0x0000000087200000, 0x0000000087300000|  0%| F|  |TAMS 0x0000000087200000, 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087300000, 0x0000000087400000|  0%| F|  |TAMS 0x0000000087300000, 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087400000, 0x0000000087500000|  0%| F|  |TAMS 0x0000000087400000, 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087500000, 0x0000000087600000|  0%| F|  |TAMS 0x0000000087500000, 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087600000, 0x0000000087700000|  0%| F|  |TAMS 0x0000000087600000, 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087700000, 0x0000000087800000|  0%| F|  |TAMS 0x0000000087700000, 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087800000, 0x0000000087900000|  0%| F|  |TAMS 0x0000000087800000, 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087900000, 0x0000000087a00000|  0%| F|  |TAMS 0x0000000087900000, 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087a00000, 0x0000000087b00000|  0%| F|  |TAMS 0x0000000087a00000, 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087c00000, 0x0000000087c00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087c00000, 0x0000000087d00000|  0%| F|  |TAMS 0x0000000087c00000, 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087d00000, 0x0000000087e00000|  0%| F|  |TAMS 0x0000000087d00000, 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087f00000, 0x0000000087f00000| Untracked 
| 127|0x0000000087f00000, 0x0000000087f00000, 0x0000000088000000|  0%| F|  |TAMS 0x0000000087f00000, 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088000000, 0x0000000088100000|  0%| F|  |TAMS 0x0000000088000000, 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088100000, 0x0000000088200000|  0%| F|  |TAMS 0x0000000088100000, 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088200000, 0x0000000088300000|  0%| F|  |TAMS 0x0000000088200000, 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088300000, 0x0000000088400000|  0%| F|  |TAMS 0x0000000088300000, 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088400000, 0x0000000088500000|  0%| F|  |TAMS 0x0000000088400000, 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088500000, 0x0000000088600000|  0%| F|  |TAMS 0x0000000088500000, 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088600000, 0x0000000088700000|  0%| F|  |TAMS 0x0000000088600000, 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088700000, 0x0000000088800000|  0%| F|  |TAMS 0x0000000088700000, 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088800000, 0x0000000088900000|  0%| F|  |TAMS 0x0000000088800000, 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088900000, 0x0000000088a00000|  0%| F|  |TAMS 0x0000000088900000, 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088a00000, 0x0000000088b00000|  0%| F|  |TAMS 0x0000000088a00000, 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088c00000, 0x0000000088c00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088c00000, 0x0000000088d00000|  0%| F|  |TAMS 0x0000000088c00000, 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088d00000, 0x0000000088e00000|  0%| F|  |TAMS 0x0000000088d00000, 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088e00000, 0x0000000088f00000|  0%| F|  |TAMS 0x0000000088e00000, 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000088f00000, 0x0000000089000000|  0%| F|  |TAMS 0x0000000088f00000, 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089100000, 0x0000000089100000| Untracked 
| 145|0x0000000089100000, 0x0000000089100000, 0x0000000089200000|  0%| F|  |TAMS 0x0000000089100000, 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089200000, 0x0000000089300000|  0%| F|  |TAMS 0x0000000089200000, 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%|HS|  |TAMS 0x0000000089400000, 0x0000000089400000| Complete 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%|HC|  |TAMS 0x0000000089500000, 0x0000000089500000| Complete 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%|HC|  |TAMS 0x0000000089600000, 0x0000000089600000| Complete 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%|HC|  |TAMS 0x0000000089700000, 0x0000000089700000| Complete 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%|HC|  |TAMS 0x0000000089800000, 0x0000000089800000| Complete 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%|HC|  |TAMS 0x0000000089900000, 0x0000000089900000| Complete 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%|HC|  |TAMS 0x0000000089a00000, 0x0000000089a00000| Complete 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%|HC|  |TAMS 0x0000000089b00000, 0x0000000089b00000| Complete 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%|HC|  |TAMS 0x0000000089c00000, 0x0000000089c00000| Complete 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%|HC|  |TAMS 0x0000000089d00000, 0x0000000089d00000| Complete 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%|HC|  |TAMS 0x0000000089e00000, 0x0000000089e00000| Complete 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%|HC|  |TAMS 0x0000000089f00000, 0x0000000089f00000| Complete 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%|HC|  |TAMS 0x000000008a000000, 0x000000008a000000| Complete 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%|HC|  |TAMS 0x000000008a100000, 0x000000008a100000| Complete 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%|HC|  |TAMS 0x000000008a200000, 0x000000008a200000| Complete 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%|HC|  |TAMS 0x000000008a300000, 0x000000008a300000| Complete 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| O|  |TAMS 0x000000008a400000, 0x000000008a400000| Untracked 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a500000, 0x000000008a500000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%|HS|  |TAMS 0x000000008a600000, 0x000000008a600000| Complete 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%|HC|  |TAMS 0x000000008a700000, 0x000000008a700000| Complete 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%|HC|  |TAMS 0x000000008a800000, 0x000000008a800000| Complete 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%|HC|  |TAMS 0x000000008a900000, 0x000000008a900000| Complete 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%|HC|  |TAMS 0x000000008aa00000, 0x000000008aa00000| Complete 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008ab00000, 0x000000008ab00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ac00000, 0x000000008ac00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000, 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000, 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000, 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000, 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b100000, 0x000000008b100000| Untracked 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| O|  |TAMS 0x000000008b200000, 0x000000008b200000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000, 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| O|  |TAMS 0x000000008b400000, 0x000000008b400000| Untracked 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| O|  |TAMS 0x000000008b500000, 0x000000008b500000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000, 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000, 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| O|  |TAMS 0x000000008b800000, 0x000000008b800000| Untracked 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| O|  |TAMS 0x000000008b900000, 0x000000008b900000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000, 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000, 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| O|  |TAMS 0x000000008bc00000, 0x000000008bc00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| O|  |TAMS 0x000000008bd00000, 0x000000008bd00000| Untracked 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008be00000, 0x000000008be00000| Untracked 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008bf00000, 0x000000008bf00000| Untracked 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| O|  |TAMS 0x000000008bf0f000, 0x000000008c000000| Untracked 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%|HS|  |TAMS 0x000000008c100000, 0x000000008c100000| Complete 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%|HC|  |TAMS 0x000000008c200000, 0x000000008c200000| Complete 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%|HC|  |TAMS 0x000000008c300000, 0x000000008c300000| Complete 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%|HC|  |TAMS 0x000000008c400000, 0x000000008c400000| Complete 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%|HC|  |TAMS 0x000000008c500000, 0x000000008c500000| Complete 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%|HC|  |TAMS 0x000000008c600000, 0x000000008c600000| Complete 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%|HC|  |TAMS 0x000000008c700000, 0x000000008c700000| Complete 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%|HC|  |TAMS 0x000000008c800000, 0x000000008c800000| Complete 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%|HC|  |TAMS 0x000000008c900000, 0x000000008c900000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%|HC|  |TAMS 0x000000008ca00000, 0x000000008ca00000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%|HC|  |TAMS 0x000000008cb00000, 0x000000008cb00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%|HC|  |TAMS 0x000000008cc00000, 0x000000008cc00000| Complete 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%|HC|  |TAMS 0x000000008cd00000, 0x000000008cd00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%|HC|  |TAMS 0x000000008ce00000, 0x000000008ce00000| Complete 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%|HC|  |TAMS 0x000000008cf00000, 0x000000008cf00000| Complete 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%|HC|  |TAMS 0x000000008d000000, 0x000000008d000000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%|HC|  |TAMS 0x000000008d100000, 0x000000008d100000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%|HC|  |TAMS 0x000000008d200000, 0x000000008d200000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%|HC|  |TAMS 0x000000008d300000, 0x000000008d300000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%|HC|  |TAMS 0x000000008d400000, 0x000000008d400000| Complete 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%|HC|  |TAMS 0x000000008d500000, 0x000000008d500000| Complete 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%|HC|  |TAMS 0x000000008d600000, 0x000000008d600000| Complete 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| O|  |TAMS 0x000000008d600000, 0x000000008d700000| Untracked 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| O|  |TAMS 0x000000008d700000, 0x000000008d800000| Untracked 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| O|  |TAMS 0x000000008d800000, 0x000000008d900000| Untracked 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| S|CS|TAMS 0x000000008d900000, 0x000000008d900000| Complete 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| S|CS|TAMS 0x000000008da00000, 0x000000008da00000| Complete 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| S|CS|TAMS 0x000000008db00000, 0x000000008db00000| Complete 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| S|CS|TAMS 0x000000008dc00000, 0x000000008dc00000| Complete 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| S|CS|TAMS 0x000000008dd00000, 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| S|CS|TAMS 0x000000008de00000, 0x000000008de00000| Complete 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| S|CS|TAMS 0x000000008df00000, 0x000000008df00000| Complete 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| S|CS|TAMS 0x000000008e000000, 0x000000008e000000| Complete 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| S|CS|TAMS 0x000000008e100000, 0x000000008e100000| Complete 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| S|CS|TAMS 0x000000008e200000, 0x000000008e200000| Complete 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000, 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e400000, 0x000000008e500000|  0%| F|  |TAMS 0x000000008e400000, 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e500000, 0x000000008e600000|  0%| F|  |TAMS 0x000000008e500000, 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e600000, 0x000000008e700000|  0%| F|  |TAMS 0x000000008e600000, 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e700000, 0x000000008e800000|  0%| F|  |TAMS 0x000000008e700000, 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e800000, 0x000000008e900000|  0%| F|  |TAMS 0x000000008e800000, 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000, 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008ea00000, 0x000000008eb00000|  0%| F|  |TAMS 0x000000008ea00000, 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000, 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ec00000, 0x000000008ed00000|  0%| F|  |TAMS 0x000000008ec00000, 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000, 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ee00000, 0x000000008ef00000|  0%| F|  |TAMS 0x000000008ee00000, 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008ef00000, 0x000000008f000000|  0%| F|  |TAMS 0x000000008ef00000, 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000, 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000, 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000, 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f300000, 0x000000008f400000|  0%| F|  |TAMS 0x000000008f300000, 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f400000, 0x000000008f500000|  0%| F|  |TAMS 0x000000008f400000, 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f500000, 0x000000008f600000|  0%| F|  |TAMS 0x000000008f500000, 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f600000, 0x000000008f700000|  0%| F|  |TAMS 0x000000008f600000, 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f700000, 0x000000008f800000|  0%| F|  |TAMS 0x000000008f700000, 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f800000, 0x000000008f900000|  0%| F|  |TAMS 0x000000008f800000, 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008f900000, 0x000000008fa00000|  0%| F|  |TAMS 0x000000008f900000, 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fa00000, 0x000000008fb00000|  0%| F|  |TAMS 0x000000008fa00000, 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fb00000, 0x000000008fc00000|  0%| F|  |TAMS 0x000000008fb00000, 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fc00000, 0x000000008fd00000|  0%| F|  |TAMS 0x000000008fc00000, 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fd00000, 0x000000008fe00000|  0%| F|  |TAMS 0x000000008fd00000, 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000, 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000, 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090000000, 0x0000000090100000|  0%| F|  |TAMS 0x0000000090000000, 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090100000, 0x0000000090200000|  0%| F|  |TAMS 0x0000000090100000, 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090200000, 0x0000000090300000|  0%| F|  |TAMS 0x0000000090200000, 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090300000, 0x0000000090400000|  0%| F|  |TAMS 0x0000000090300000, 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x0000000090400000, 0x0000000090500000|  0%| F|  |TAMS 0x0000000090400000, 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090500000, 0x0000000090600000|  0%| F|  |TAMS 0x0000000090500000, 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090600000, 0x0000000090700000|  0%| F|  |TAMS 0x0000000090600000, 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090700000, 0x0000000090800000|  0%| F|  |TAMS 0x0000000090700000, 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090800000, 0x0000000090900000|  0%| F|  |TAMS 0x0000000090800000, 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090900000, 0x0000000090a00000|  0%| F|  |TAMS 0x0000000090900000, 0x0000000090900000| Untracked 
| 266|0x0000000090a00000, 0x0000000090a00000, 0x0000000090b00000|  0%| F|  |TAMS 0x0000000090a00000, 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090b00000, 0x0000000090c00000|  0%| F|  |TAMS 0x0000000090b00000, 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090c00000, 0x0000000090d00000|  0%| F|  |TAMS 0x0000000090c00000, 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090d00000, 0x0000000090e00000|  0%| F|  |TAMS 0x0000000090d00000, 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090e00000, 0x0000000090f00000|  0%| F|  |TAMS 0x0000000090e00000, 0x0000000090e00000| Untracked 
| 271|0x0000000090f00000, 0x0000000090f00000, 0x0000000091000000|  0%| F|  |TAMS 0x0000000090f00000, 0x0000000090f00000| Untracked 
| 272|0x0000000091000000, 0x0000000091000000, 0x0000000091100000|  0%| F|  |TAMS 0x0000000091000000, 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091100000, 0x0000000091200000|  0%| F|  |TAMS 0x0000000091100000, 0x0000000091100000| Untracked 
| 274|0x0000000091200000, 0x0000000091200000, 0x0000000091300000|  0%| F|  |TAMS 0x0000000091200000, 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091300000, 0x0000000091400000|  0%| F|  |TAMS 0x0000000091300000, 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091400000, 0x0000000091500000|  0%| F|  |TAMS 0x0000000091400000, 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091500000, 0x0000000091600000|  0%| F|  |TAMS 0x0000000091500000, 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091600000, 0x0000000091700000|  0%| F|  |TAMS 0x0000000091600000, 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091700000, 0x0000000091800000|  0%| F|  |TAMS 0x0000000091700000, 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091800000, 0x0000000091900000|  0%| F|  |TAMS 0x0000000091800000, 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091900000, 0x0000000091a00000|  0%| F|  |TAMS 0x0000000091900000, 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091a00000, 0x0000000091b00000|  0%| F|  |TAMS 0x0000000091a00000, 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091b00000, 0x0000000091c00000|  0%| F|  |TAMS 0x0000000091b00000, 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091c00000, 0x0000000091d00000|  0%| F|  |TAMS 0x0000000091c00000, 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091d00000, 0x0000000091e00000|  0%| F|  |TAMS 0x0000000091d00000, 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091e00000, 0x0000000091f00000|  0%| F|  |TAMS 0x0000000091e00000, 0x0000000091e00000| Untracked 
| 287|0x0000000091f00000, 0x0000000091f00000, 0x0000000092000000|  0%| F|  |TAMS 0x0000000091f00000, 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092000000, 0x0000000092100000|  0%| F|  |TAMS 0x0000000092000000, 0x0000000092000000| Untracked 
| 289|0x0000000092100000, 0x0000000092100000, 0x0000000092200000|  0%| F|  |TAMS 0x0000000092100000, 0x0000000092100000| Untracked 
| 290|0x0000000092200000, 0x0000000092200000, 0x0000000092300000|  0%| F|  |TAMS 0x0000000092200000, 0x0000000092200000| Untracked 
| 291|0x0000000092300000, 0x0000000092300000, 0x0000000092400000|  0%| F|  |TAMS 0x0000000092300000, 0x0000000092300000| Untracked 
| 292|0x0000000092400000, 0x0000000092400000, 0x0000000092500000|  0%| F|  |TAMS 0x0000000092400000, 0x0000000092400000| Untracked 
| 293|0x0000000092500000, 0x0000000092500000, 0x0000000092600000|  0%| F|  |TAMS 0x0000000092500000, 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092600000, 0x0000000092700000|  0%| F|  |TAMS 0x0000000092600000, 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092700000, 0x0000000092800000|  0%| F|  |TAMS 0x0000000092700000, 0x0000000092700000| Untracked 
| 296|0x0000000092800000, 0x0000000092800000, 0x0000000092900000|  0%| F|  |TAMS 0x0000000092800000, 0x0000000092800000| Untracked 
| 297|0x0000000092900000, 0x0000000092900000, 0x0000000092a00000|  0%| F|  |TAMS 0x0000000092900000, 0x0000000092900000| Untracked 
| 298|0x0000000092a00000, 0x0000000092a00000, 0x0000000092b00000|  0%| F|  |TAMS 0x0000000092a00000, 0x0000000092a00000| Untracked 
| 299|0x0000000092b00000, 0x0000000092b00000, 0x0000000092c00000|  0%| F|  |TAMS 0x0000000092b00000, 0x0000000092b00000| Untracked 
| 300|0x0000000092c00000, 0x0000000092c00000, 0x0000000092d00000|  0%| F|  |TAMS 0x0000000092c00000, 0x0000000092c00000| Untracked 
| 301|0x0000000092d00000, 0x0000000092d00000, 0x0000000092e00000|  0%| F|  |TAMS 0x0000000092d00000, 0x0000000092d00000| Untracked 
| 302|0x0000000092e00000, 0x0000000092e00000, 0x0000000092f00000|  0%| F|  |TAMS 0x0000000092e00000, 0x0000000092e00000| Untracked 
| 303|0x0000000092f00000, 0x0000000092f00000, 0x0000000093000000|  0%| F|  |TAMS 0x0000000092f00000, 0x0000000092f00000| Untracked 
| 304|0x0000000093000000, 0x0000000093000000, 0x0000000093100000|  0%| F|  |TAMS 0x0000000093000000, 0x0000000093000000| Untracked 
| 305|0x0000000093100000, 0x0000000093100000, 0x0000000093200000|  0%| F|  |TAMS 0x0000000093100000, 0x0000000093100000| Untracked 
| 306|0x0000000093200000, 0x0000000093200000, 0x0000000093300000|  0%| F|  |TAMS 0x0000000093200000, 0x0000000093200000| Untracked 
| 307|0x0000000093300000, 0x0000000093300000, 0x0000000093400000|  0%| F|  |TAMS 0x0000000093300000, 0x0000000093300000| Untracked 
| 308|0x0000000093400000, 0x0000000093400000, 0x0000000093500000|  0%| F|  |TAMS 0x0000000093400000, 0x0000000093400000| Untracked 
| 309|0x0000000093500000, 0x0000000093500000, 0x0000000093600000|  0%| F|  |TAMS 0x0000000093500000, 0x0000000093500000| Untracked 
| 310|0x0000000093600000, 0x0000000093600000, 0x0000000093700000|  0%| F|  |TAMS 0x0000000093600000, 0x0000000093600000| Untracked 
| 311|0x0000000093700000, 0x0000000093700000, 0x0000000093800000|  0%| F|  |TAMS 0x0000000093700000, 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093800000, 0x0000000093900000|  0%| F|  |TAMS 0x0000000093800000, 0x0000000093800000| Untracked 
| 313|0x0000000093900000, 0x0000000093900000, 0x0000000093a00000|  0%| F|  |TAMS 0x0000000093900000, 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093a00000, 0x0000000093b00000|  0%| F|  |TAMS 0x0000000093a00000, 0x0000000093a00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093b00000, 0x0000000093c00000|  0%| F|  |TAMS 0x0000000093b00000, 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093c00000, 0x0000000093d00000|  0%| F|  |TAMS 0x0000000093c00000, 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093d00000, 0x0000000093e00000|  0%| F|  |TAMS 0x0000000093d00000, 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093e00000, 0x0000000093f00000|  0%| F|  |TAMS 0x0000000093e00000, 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000093f00000, 0x0000000094000000|  0%| F|  |TAMS 0x0000000093f00000, 0x0000000093f00000| Untracked 
| 320|0x0000000094000000, 0x0000000094000000, 0x0000000094100000|  0%| F|  |TAMS 0x0000000094000000, 0x0000000094000000| Untracked 
| 321|0x0000000094100000, 0x0000000094100000, 0x0000000094200000|  0%| F|  |TAMS 0x0000000094100000, 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094200000, 0x0000000094300000|  0%| F|  |TAMS 0x0000000094200000, 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094300000, 0x0000000094400000|  0%| F|  |TAMS 0x0000000094300000, 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094400000, 0x0000000094500000|  0%| F|  |TAMS 0x0000000094400000, 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094500000, 0x0000000094600000|  0%| F|  |TAMS 0x0000000094500000, 0x0000000094500000| Untracked 
| 326|0x0000000094600000, 0x0000000094600000, 0x0000000094700000|  0%| F|  |TAMS 0x0000000094600000, 0x0000000094600000| Untracked 
| 327|0x0000000094700000, 0x0000000094700000, 0x0000000094800000|  0%| F|  |TAMS 0x0000000094700000, 0x0000000094700000| Untracked 
| 328|0x0000000094800000, 0x0000000094800000, 0x0000000094900000|  0%| F|  |TAMS 0x0000000094800000, 0x0000000094800000| Untracked 
| 329|0x0000000094900000, 0x0000000094900000, 0x0000000094a00000|  0%| F|  |TAMS 0x0000000094900000, 0x0000000094900000| Untracked 
| 330|0x0000000094a00000, 0x0000000094a00000, 0x0000000094b00000|  0%| F|  |TAMS 0x0000000094a00000, 0x0000000094a00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094b00000, 0x0000000094c00000|  0%| F|  |TAMS 0x0000000094b00000, 0x0000000094b00000| Untracked 
| 332|0x0000000094c00000, 0x0000000094c00000, 0x0000000094d00000|  0%| F|  |TAMS 0x0000000094c00000, 0x0000000094c00000| Untracked 
| 333|0x0000000094d00000, 0x0000000094d00000, 0x0000000094e00000|  0%| F|  |TAMS 0x0000000094d00000, 0x0000000094d00000| Untracked 
| 334|0x0000000094e00000, 0x0000000094e00000, 0x0000000094f00000|  0%| F|  |TAMS 0x0000000094e00000, 0x0000000094e00000| Untracked 
| 335|0x0000000094f00000, 0x0000000094f00000, 0x0000000095000000|  0%| F|  |TAMS 0x0000000094f00000, 0x0000000094f00000| Untracked 
| 336|0x0000000095000000, 0x0000000095000000, 0x0000000095100000|  0%| F|  |TAMS 0x0000000095000000, 0x0000000095000000| Untracked 
| 337|0x0000000095100000, 0x0000000095100000, 0x0000000095200000|  0%| F|  |TAMS 0x0000000095100000, 0x0000000095100000| Untracked 
| 338|0x0000000095200000, 0x0000000095200000, 0x0000000095300000|  0%| F|  |TAMS 0x0000000095200000, 0x0000000095200000| Untracked 
| 339|0x0000000095300000, 0x0000000095300000, 0x0000000095400000|  0%| F|  |TAMS 0x0000000095300000, 0x0000000095300000| Untracked 
| 340|0x0000000095400000, 0x0000000095400000, 0x0000000095500000|  0%| F|  |TAMS 0x0000000095400000, 0x0000000095400000| Untracked 
| 341|0x0000000095500000, 0x0000000095500000, 0x0000000095600000|  0%| F|  |TAMS 0x0000000095500000, 0x0000000095500000| Untracked 
| 342|0x0000000095600000, 0x0000000095600000, 0x0000000095700000|  0%| F|  |TAMS 0x0000000095600000, 0x0000000095600000| Untracked 
| 343|0x0000000095700000, 0x0000000095700000, 0x0000000095800000|  0%| F|  |TAMS 0x0000000095700000, 0x0000000095700000| Untracked 
| 344|0x0000000095800000, 0x0000000095800000, 0x0000000095900000|  0%| F|  |TAMS 0x0000000095800000, 0x0000000095800000| Untracked 
| 345|0x0000000095900000, 0x0000000095900000, 0x0000000095a00000|  0%| F|  |TAMS 0x0000000095900000, 0x0000000095900000| Untracked 
| 346|0x0000000095a00000, 0x0000000095a00000, 0x0000000095b00000|  0%| F|  |TAMS 0x0000000095a00000, 0x0000000095a00000| Untracked 
| 347|0x0000000095b00000, 0x0000000095b00000, 0x0000000095c00000|  0%| F|  |TAMS 0x0000000095b00000, 0x0000000095b00000| Untracked 
| 348|0x0000000095c00000, 0x0000000095c00000, 0x0000000095d00000|  0%| F|  |TAMS 0x0000000095c00000, 0x0000000095c00000| Untracked 
| 349|0x0000000095d00000, 0x0000000095d00000, 0x0000000095e00000|  0%| F|  |TAMS 0x0000000095d00000, 0x0000000095d00000| Untracked 
| 350|0x0000000095e00000, 0x0000000095e00000, 0x0000000095f00000|  0%| F|  |TAMS 0x0000000095e00000, 0x0000000095e00000| Untracked 
| 351|0x0000000095f00000, 0x0000000095f00000, 0x0000000096000000|  0%| F|  |TAMS 0x0000000095f00000, 0x0000000095f00000| Untracked 
| 352|0x0000000096000000, 0x0000000096000000, 0x0000000096100000|  0%| F|  |TAMS 0x0000000096000000, 0x0000000096000000| Untracked 
| 353|0x0000000096100000, 0x0000000096100000, 0x0000000096200000|  0%| F|  |TAMS 0x0000000096100000, 0x0000000096100000| Untracked 

Card table byte_map: [0x000001c699910000,0x000001c699d10000] _byte_map_base: 0x000001c699510000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001c6821d7e40, (CMBitMap*) 0x000001c6821d7e80
 Prev Bits: [0x000001c69a110000, 0x000001c69c110000)
 Next Bits: [0x000001c69c110000, 0x000001c69e110000)

Polling page: 0x000001c680100000

Metaspace:

Usage:
  Non-class:     92.87 MB used.
      Class:     14.51 MB used.
       Both:    107.39 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      93.31 MB ( 73%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      15.00 MB (  1%) committed,  1 nodes.
             Both:        1.12 GB reserved,     108.31 MB (  9%) committed. 

Chunk freelists:
   Non-Class:  2.14 MB
       Class:  1008.00 KB
        Both:  3.12 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 160.75 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 1316.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1731.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 5969.
num_chunk_merges: 6.
num_chunk_splits: 3908.
num_chunks_enlarged: 2591.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=7912Kb max_used=8918Kb free=111255Kb
 bounds [0x000001c6915f0000, 0x000001c691eb0000, 0x000001c698a50000]
CodeHeap 'profiled nmethods': size=119104Kb used=30954Kb max_used=31293Kb free=88149Kb
 bounds [0x000001c689a50000, 0x000001c68b8e0000, 0x000001c690ea0000]
CodeHeap 'non-nmethods': size=7488Kb used=4127Kb max_used=4161Kb free=3360Kb
 bounds [0x000001c690ea0000, 0x000001c6912d0000, 0x000001c6915f0000]
 total_blobs=16396 nmethods=15418 adapters=886
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 20.488 Thread 0x000001c6a15906a0 nmethod 18729 0x000001c68b3f7010 code [0x000001c68b3f71c0, 0x000001c68b3f7528]
Event: 20.488 Thread 0x000001c6a15a0fb0 nmethod 18732 0x000001c68a22e490 code [0x000001c68a22e720, 0x000001c68a22f668]
Event: 20.489 Thread 0x000001c6a13e0df0 nmethod 18720 0x000001c689c08390 code [0x000001c689c08a40, 0x000001c689c0d1c8]
Event: 20.489 Thread 0x000001c6a15a0fb0 18733       1       org.gradle.internal.execution.steps.IdentityContext::getInputFileProperties (5 bytes)
Event: 20.489 Thread 0x000001c6a15a0fb0 nmethod 18733 0x000001c691a4af90 code [0x000001c691a4b120, 0x000001c691a4b1f8]
Event: 20.490 Thread 0x000001c6a15906a0 18734       2       org.gradle.internal.event.DefaultListenerManager$ListenerDetails::dispatch (9 bytes)
Event: 20.490 Thread 0x000001c6a15a0fb0 18735   !   2       org.gradle.internal.event.DefaultListenerManager$ListenerDetails::dispatch (67 bytes)
Event: 20.491 Thread 0x000001c6a15906a0 nmethod 18734 0x000001c68b3f6c10 code [0x000001c68b3f6da0, 0x000001c68b3f6f08]
Event: 20.491 Thread 0x000001c6a15906a0 18737 %     3       com.android.tools.r8.internal.Xa0::a @ 23 (167 bytes)
Event: 20.491 Thread 0x000001c6a15a0fb0 nmethod 18735 0x000001c68b12e490 code [0x000001c68b12e6a0, 0x000001c68b12ea78]
Event: 20.492 Thread 0x000001c6a15906a0 nmethod 18737% 0x000001c68b3d6a90 code [0x000001c68b3d6ce0, 0x000001c68b3d79f8]
Event: 20.493 Thread 0x000001c6a13aa4b0 18738       3       com.android.tools.r8.graph.p4::<init> (75 bytes)
Event: 20.493 Thread 0x000001c6a13e0df0 18739       3       com.android.tools.r8.graph.n4::apply (40 bytes)
Event: 20.493 Thread 0x000001c6a13aa4b0 nmethod 18738 0x000001c68a2a4910 code [0x000001c68a2a4b00, 0x000001c68a2a5148]
Event: 20.493 Thread 0x000001c6a13e0df0 nmethod 18739 0x000001c68a2a3c10 code [0x000001c68a2a3e20, 0x000001c68a2a4678]
Event: 20.497 Thread 0x000001c6a13e0df0 18740       2       org.gradle.internal.service.DefaultServiceRegistry$ParentServices::getFactory (12 bytes)
Event: 20.498 Thread 0x000001c6a13e0df0 nmethod 18740 0x000001c689c08010 code [0x000001c689c081a0, 0x000001c689c082c8]
Event: 20.508 Thread 0x000001c6a13aa4b0 18741       1       org.gradle.internal.MutableReference::get (5 bytes)
Event: 20.508 Thread 0x000001c6a13aa4b0 nmethod 18741 0x000001c691a4ac10 code [0x000001c691a4ada0, 0x000001c691a4ae78]
Event: 20.509 Thread 0x000001c6a15906a0 18742       2       java.util.Optional::flatMap (38 bytes)

GC Heap History (20 events):
Event: 17.628 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 276480K, used 171218K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 91416K, committed 92224K, reserved 1179648K
  class space    used 12197K, committed 12544K, reserved 1048576K
}
Event: 18.281 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 276480K, used 226514K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 60 young (61440K), 5 survivors (5120K)
 Metaspace       used 95391K, committed 96256K, reserved 1179648K
  class space    used 12697K, committed 13120K, reserved 1048576K
}
Event: 18.283 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 276480K, used 171596K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 95391K, committed 96256K, reserved 1179648K
  class space    used 12697K, committed 13120K, reserved 1048576K
}
Event: 18.826 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 276480K, used 227916K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 45 young (46080K), 5 survivors (5120K)
 Metaspace       used 96772K, committed 97664K, reserved 1179648K
  class space    used 12869K, committed 13312K, reserved 1048576K
}
Event: 18.829 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 276480K, used 190903K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 96772K, committed 97664K, reserved 1179648K
  class space    used 12869K, committed 13312K, reserved 1048576K
}
Event: 18.831 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 276480K, used 197047K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 7 survivors (7168K)
 Metaspace       used 96772K, committed 97664K, reserved 1179648K
  class space    used 12869K, committed 13312K, reserved 1048576K
}
Event: 18.835 GC heap after
{Heap after GC invocations=47 (full 0):
 garbage-first heap   total 276480K, used 196474K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 1 young (1024K), 1 survivors (1024K)
 Metaspace       used 96772K, committed 97664K, reserved 1179648K
  class space    used 12869K, committed 13312K, reserved 1048576K
}
Event: 19.002 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 276480K, used 257914K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 40 young (40960K), 1 survivors (1024K)
 Metaspace       used 97571K, committed 98432K, reserved 1179648K
  class space    used 12989K, committed 13440K, reserved 1048576K
}
Event: 19.004 GC heap after
{Heap after GC invocations=48 (full 0):
 garbage-first heap   total 276480K, used 222268K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 97571K, committed 98432K, reserved 1179648K
  class space    used 12989K, committed 13440K, reserved 1048576K
}
Event: 19.040 GC heap before
{Heap before GC invocations=48 (full 0):
 garbage-first heap   total 276480K, used 239676K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 22 young (22528K), 4 survivors (4096K)
 Metaspace       used 97721K, committed 98560K, reserved 1179648K
  class space    used 13007K, committed 13440K, reserved 1048576K
}
Event: 19.043 GC heap after
{Heap after GC invocations=49 (full 0):
 garbage-first heap   total 276480K, used 224303K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 97721K, committed 98560K, reserved 1179648K
  class space    used 13007K, committed 13440K, reserved 1048576K
}
Event: 19.105 GC heap before
{Heap before GC invocations=49 (full 0):
 garbage-first heap   total 362496K, used 216111K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 18 young (18432K), 3 survivors (3072K)
 Metaspace       used 97913K, committed 98752K, reserved 1179648K
  class space    used 13034K, committed 13440K, reserved 1048576K
}
Event: 19.108 GC heap after
{Heap after GC invocations=50 (full 0):
 garbage-first heap   total 362496K, used 203472K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 97913K, committed 98752K, reserved 1179648K
  class space    used 13034K, committed 13440K, reserved 1048576K
}
Event: 19.705 GC heap before
{Heap before GC invocations=51 (full 0):
 garbage-first heap   total 362496K, used 303824K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 102 young (104448K), 3 survivors (3072K)
 Metaspace       used 102160K, committed 103040K, reserved 1179648K
  class space    used 13653K, committed 14080K, reserved 1048576K
}
Event: 19.710 GC heap after
{Heap after GC invocations=52 (full 0):
 garbage-first heap   total 362496K, used 210667K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 102160K, committed 103040K, reserved 1179648K
  class space    used 13653K, committed 14080K, reserved 1048576K
}
Event: 19.782 GC heap before
{Heap before GC invocations=52 (full 0):
 garbage-first heap   total 362496K, used 216811K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 10 survivors (10240K)
 Metaspace       used 102457K, committed 103360K, reserved 1179648K
  class space    used 13705K, committed 14144K, reserved 1048576K
}
Event: 19.786 GC heap after
{Heap after GC invocations=53 (full 0):
 garbage-first heap   total 362496K, used 175383K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 102457K, committed 103360K, reserved 1179648K
  class space    used 13705K, committed 14144K, reserved 1048576K
}
Event: 19.864 GC heap before
{Heap before GC invocations=53 (full 0):
 garbage-first heap   total 362496K, used 188695K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 3 survivors (3072K)
 Metaspace       used 102916K, committed 103744K, reserved 1179648K
  class space    used 13799K, committed 14208K, reserved 1048576K
}
Event: 19.866 GC heap after
{Heap after GC invocations=54 (full 0):
 garbage-first heap   total 362496K, used 173202K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 102916K, committed 103744K, reserved 1179648K
  class space    used 13799K, committed 14208K, reserved 1048576K
}
Event: 20.509 GC heap before
{Heap before GC invocations=54 (full 0):
 garbage-first heap   total 362496K, used 299154K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 127 young (130048K), 3 survivors (3072K)
 Metaspace       used 109963K, committed 110912K, reserved 1179648K
  class space    used 14862K, committed 15360K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 20.478 Thread 0x000001c6ecd7fb90 DEOPT PACKING pc=0x000001c68b677c78 sp=0x0000007a361fb970
Event: 20.478 Thread 0x000001c6ecd7fb90 DEOPT UNPACKING pc=0x000001c690ef2b43 sp=0x0000007a361fae98 mode 0
Event: 20.480 Thread 0x000001c6ecd800a0 DEOPT PACKING pc=0x000001c68b677c78 sp=0x0000007a362fb670
Event: 20.480 Thread 0x000001c6ecd800a0 DEOPT UNPACKING pc=0x000001c690ef2b43 sp=0x0000007a362fab98 mode 0
Event: 20.484 Thread 0x000001c6ecd800a0 DEOPT PACKING pc=0x000001c68b677c78 sp=0x0000007a362fb4d0
Event: 20.484 Thread 0x000001c6ecd800a0 DEOPT UNPACKING pc=0x000001c690ef2b43 sp=0x0000007a362fa9f8 mode 0
Event: 20.486 Thread 0x000001c6ecd800a0 DEOPT PACKING pc=0x000001c68b677c78 sp=0x0000007a362fb7a0
Event: 20.486 Thread 0x000001c6ecd800a0 DEOPT UNPACKING pc=0x000001c690ef2b43 sp=0x0000007a362facc8 mode 0
Event: 20.490 Thread 0x000001c6ecd800a0 DEOPT PACKING pc=0x000001c68b677c78 sp=0x0000007a362fb730
Event: 20.490 Thread 0x000001c6ecd800a0 DEOPT UNPACKING pc=0x000001c690ef2b43 sp=0x0000007a362fac58 mode 0
Event: 20.491 Thread 0x000001c6e82986f0 DEOPT PACKING pc=0x000001c68b677c78 sp=0x0000007a335fb6d0
Event: 20.491 Thread 0x000001c6e82986f0 DEOPT UNPACKING pc=0x000001c690ef2b43 sp=0x0000007a335fabf8 mode 0
Event: 20.493 Thread 0x000001c6ecd800a0 DEOPT PACKING pc=0x000001c68b677c78 sp=0x0000007a362fb5a0
Event: 20.493 Thread 0x000001c6ecd800a0 DEOPT UNPACKING pc=0x000001c690ef2b43 sp=0x0000007a362faac8 mode 0
Event: 20.497 Thread 0x000001c6e82986f0 DEOPT PACKING pc=0x000001c68b677c78 sp=0x0000007a335fb6d0
Event: 20.497 Thread 0x000001c6e82986f0 DEOPT UNPACKING pc=0x000001c690ef2b43 sp=0x0000007a335fabf8 mode 0
Event: 20.500 Thread 0x000001c6e82986f0 DEOPT PACKING pc=0x000001c68b677c78 sp=0x0000007a335fb790
Event: 20.500 Thread 0x000001c6e82986f0 DEOPT UNPACKING pc=0x000001c690ef2b43 sp=0x0000007a335facb8 mode 0
Event: 20.503 Thread 0x000001c6ecd800a0 DEOPT PACKING pc=0x000001c68b677c78 sp=0x0000007a362fb6e0
Event: 20.503 Thread 0x000001c6ecd800a0 DEOPT UNPACKING pc=0x000001c690ef2b43 sp=0x0000007a362fac08 mode 0

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 20.243 Thread 0x000001c6eb278750 Exception <a 'java/lang/NoSuchMethodError'{0x0000000090834920}: static Lcom/android/build/api/variant/impl/VariantOutputConfigurationImpl;.<clinit>()V> (0x0000000090834920) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 20.317 Thread 0x000001c6e82986f0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008ffff688}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008ffff688) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.317 Thread 0x000001c6ecd800a0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008fec0fd8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008fec0fd8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.317 Thread 0x000001c6ecd84fe0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008fefa9d8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008fefa9d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.317 Thread 0x000001c6ecd7fb90 Exception <a 'java/lang/NoSuchMethodError'{0x000000008fe59c78}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008fe59c78) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.320 Thread 0x000001c6ecd7fb90 Exception <a 'java/lang/NoSuchMethodError'{0x000000008fc12e40}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008fc12e40) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.320 Thread 0x000001c6e82986f0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008fde27c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008fde27c0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.320 Thread 0x000001c6ecd800a0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008fed44c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008fed44c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.320 Thread 0x000001c6ecd84fe0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008fc41118}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008fc41118) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.350 Thread 0x000001c6e70767c0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000008f6002e0}: Found class java.lang.Object, but interface was expected> (0x000000008f6002e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 20.392 Thread 0x000001c6ecd845c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008f16b420}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x000000008f16b420) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.394 Thread 0x000001c6ecd845c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008f1b8960}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x000000008f1b8960) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.394 Thread 0x000001c6ecd845c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008f1c5600}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x000000008f1c5600) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.438 Thread 0x000001c6e70767c0 Implicit null exception at 0x000001c691689f59 to 0x000001c69168a0f8
Event: 20.438 Thread 0x000001c6e70767c0 Implicit null exception at 0x000001c691b1e85f to 0x000001c691b20e9c
Event: 20.499 Thread 0x000001c6e70767c0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e5af058}> (0x000000008e5af058) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 20.499 Thread 0x000001c6e70767c0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e5ee0f0}> (0x000000008e5ee0f0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 20.499 Thread 0x000001c6e70767c0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e5ee180}> (0x000000008e5ee180) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 20.499 Thread 0x000001c6e70767c0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e5ee558}> (0x000000008e5ee558) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 20.499 Thread 0x000001c6e70767c0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e5f0a98}> (0x000000008e5f0a98) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]

VM Operations (20 events):
Event: 20.180 Executing VM operation: HandshakeAllThreads done
Event: 20.180 Executing VM operation: HandshakeAllThreads
Event: 20.181 Executing VM operation: HandshakeAllThreads done
Event: 20.200 Executing VM operation: ICBufferFull
Event: 20.201 Executing VM operation: ICBufferFull done
Event: 20.240 Executing VM operation: HandshakeAllThreads
Event: 20.240 Executing VM operation: HandshakeAllThreads done
Event: 20.286 Executing VM operation: HandshakeAllThreads
Event: 20.287 Executing VM operation: HandshakeAllThreads done
Event: 20.330 Executing VM operation: HandshakeAllThreads
Event: 20.331 Executing VM operation: HandshakeAllThreads done
Event: 20.398 Executing VM operation: HandshakeAllThreads
Event: 20.399 Executing VM operation: HandshakeAllThreads done
Event: 20.413 Executing VM operation: HandshakeAllThreads
Event: 20.414 Executing VM operation: HandshakeAllThreads done
Event: 20.432 Executing VM operation: HandshakeAllThreads
Event: 20.433 Executing VM operation: HandshakeAllThreads done
Event: 20.452 Executing VM operation: HandshakeAllThreads
Event: 20.453 Executing VM operation: HandshakeAllThreads done
Event: 20.509 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 20.424 loading class javax/management/ListenerNotFoundException
Event: 20.424 loading class javax/management/ListenerNotFoundException done
Event: 20.424 loading class jdk/jfr/Recording
Event: 20.426 loading class jdk/jfr/Recording done
Event: 20.426 loading class jdk/jfr/FlightRecorder
Event: 20.426 loading class jdk/jfr/FlightRecorder done
Event: 20.426 loading class jdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener
Event: 20.427 loading class jdk/jfr/FlightRecorderListener
Event: 20.427 loading class jdk/jfr/FlightRecorderListener done
Event: 20.427 loading class jdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener done
Event: 20.435 loading class com/sun/jmx/mbeanserver/StandardMBeanSupport
Event: 20.435 loading class com/sun/jmx/mbeanserver/StandardMBeanSupport done
Event: 20.435 loading class com/sun/jmx/mbeanserver/StandardMBeanIntrospector
Event: 20.436 loading class com/sun/jmx/mbeanserver/StandardMBeanIntrospector done
Event: 20.437 loading class sun/reflect/misc/MethodUtil
Event: 20.437 loading class sun/reflect/misc/MethodUtil done
Event: 20.437 loading class sun/reflect/misc/MethodUtil$1
Event: 20.438 loading class sun/reflect/misc/MethodUtil$1 done
Event: 20.483 Thread 0x000001c6ed51d470 Thread added: 0x000001c6ed51d470
Event: 20.483 Thread 0x000001c6ed51de90 Thread added: 0x000001c6ed51de90


Dynamic libraries:
0x00007ff661980000 - 0x00007ff661990000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ffacd910000 - 0x00007ffacdb27000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffacb640000 - 0x00007ffacb704000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffacb020000 - 0x00007ffacb3d7000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffaccf40000 - 0x00007ffaccff2000 	C:\Windows\System32\ADVAPI32.DLL
0x00007ffacc150000 - 0x00007ffacc1f7000 	C:\Windows\System32\msvcrt.dll
0x00007ffacd1b0000 - 0x00007ffacd258000 	C:\Windows\System32\sechost.dll
0x00007ffacb450000 - 0x00007ffacb478000 	C:\Windows\System32\bcrypt.dll
0x00007ffacd730000 - 0x00007ffacd844000 	C:\Windows\System32\RPCRT4.dll
0x00007ffacaed0000 - 0x00007ffacafe1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffab0740000 - 0x00007ffab075b000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ffaa2fd0000 - 0x00007ffaa2fe9000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ffacbcd0000 - 0x00007ffacbe7f000 	C:\Windows\System32\USER32.dll
0x00007ffacaff0000 - 0x00007ffacb016000 	C:\Windows\System32\win32u.dll
0x00007ffaa23f0000 - 0x00007ffaa2683000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3672_none_2713b9d173822955\COMCTL32.dll
0x00007ffacd850000 - 0x00007ffacd879000 	C:\Windows\System32\GDI32.dll
0x00007ffacb480000 - 0x00007ffacb598000 	C:\Windows\System32\gdi32full.dll
0x00007ffacb5a0000 - 0x00007ffacb63a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffabe2b0000 - 0x00007ffabe2ba000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffacd000000 - 0x00007ffacd031000 	C:\Windows\System32\IMM32.DLL
0x00007ffac7280000 - 0x00007ffac728c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ffa82ab0000 - 0x00007ffa82b3e000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ff9e8e50000 - 0x00007ff9e9a31000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ffaccf10000 - 0x00007ffaccf18000 	C:\Windows\System32\PSAPI.DLL
0x00007ffac0600000 - 0x00007ffac0634000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffaa60a0000 - 0x00007ffaa60a9000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffacc070000 - 0x00007ffacc0e1000 	C:\Windows\System32\WS2_32.dll
0x00007ffac9cc0000 - 0x00007ffac9cd8000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffac0560000 - 0x00007ffac056a000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ffac8370000 - 0x00007ffac85a2000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffacb890000 - 0x00007ffacbc1e000 	C:\Windows\System32\combase.dll
0x00007ffacd650000 - 0x00007ffacd727000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffab6120000 - 0x00007ffab6152000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffacae50000 - 0x00007ffacaecb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa9e6d0000 - 0x00007ffa9e6de000 	C:\Program Files\Java\jdk-17\bin\instrument.dll
0x00007ffa94460000 - 0x00007ffa94485000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ffa72010000 - 0x00007ffa720e7000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007ffacc200000 - 0x00007ffacca69000 	C:\Windows\System32\SHELL32.dll
0x00007ffac8960000 - 0x00007ffac925f000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffac8820000 - 0x00007ffac895f000 	C:\Windows\SYSTEM32\wintypes.dll
0x00007ffacd050000 - 0x00007ffacd149000 	C:\Windows\System32\SHCORE.dll
0x00007ffacc0f0000 - 0x00007ffacc14e000 	C:\Windows\System32\shlwapi.dll
0x00007ffacab50000 - 0x00007ffacab77000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa94140000 - 0x00007ffa94158000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x00007ffa97d20000 - 0x00007ffa97d39000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007ffac5e90000 - 0x00007ffac5fc6000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffaca180000 - 0x00007ffaca1e9000 	C:\Windows\system32\mswsock.dll
0x00007ffa970d0000 - 0x00007ffa970e6000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007ffab0730000 - 0x00007ffab0740000 	C:\Program Files\Java\jdk-17\bin\verify.dll
0x00007ffa82c00000 - 0x00007ffa82c27000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffa62c50000 - 0x00007ffa62d94000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffaad350000 - 0x00007ffaad35a000 	C:\Program Files\Java\jdk-17\bin\management.dll
0x00007ffaa2f10000 - 0x00007ffaa2f1b000 	C:\Program Files\Java\jdk-17\bin\management_ext.dll
0x00007ffaca4d0000 - 0x00007ffaca4eb000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffac9c80000 - 0x00007ffac9cb5000 	C:\Windows\system32\rsaenh.dll
0x00007ffaca220000 - 0x00007ffaca248000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffaca4b0000 - 0x00007ffaca4bc000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffac94b0000 - 0x00007ffac94dd000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffacd040000 - 0x00007ffacd049000 	C:\Windows\System32\NSI.dll
0x00007ffac6190000 - 0x00007ffac61a9000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffac5e70000 - 0x00007ffac5e8f000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffac94e0000 - 0x00007ffac95e2000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffa9c590000 - 0x00007ffa9c59e000 	C:\Program Files\Java\jdk-17\bin\sunmscapi.dll
0x00007ffacac20000 - 0x00007ffacad86000 	C:\Windows\System32\CRYPT32.dll
0x00007ffaca5e0000 - 0x00007ffaca60d000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffaca5a0000 - 0x00007ffaca5d7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffaab510000 - 0x00007ffaab518000 	C:\Windows\system32\wshunix.dll
0x00007ffac9e30000 - 0x00007ffac9e64000 	C:\Windows\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3672_none_2713b9d173822955;C:\Program Files\Java\jdk-17\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.10.2-bin\a04bxjujx95o3nb99gddekhwo\gradle-8.10.2\lib\agents\gradle-instrumentation-agent-8.10.2.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10.2
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.10.2-bin\a04bxjujx95o3nb99gddekhwo\gradle-8.10.2\lib\gradle-daemon-main-8.10.2.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 4                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 15                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
CLASSPATH=D:\test_project\Glasses\\gradle\wrapper\gradle-wrapper.jar
PATH=E:\Program Files (x86)\VMware\VMware Workstation\bin\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Java\jdk-1.8\bin;E:\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-1.8\bin\server;C:\Program Files\Pandoc\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;E:\Program Files\JetBrains\IntelliJ IDEA 2024.3.4\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\nodejs\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;E:\00Tools\apktool;E:\Harmony_Project\Huawei\commandline-tools-windows-x64-5.0.11.100\command-line-tools\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;E:\Program Files\JetBrains\IntelliJ IDEA 2024.3.4\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Roaming\npm;E:\Program Files\Huawei\DevEco Studio\bin;
USERNAME=498475
LANG=zh_CN.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 186 Stepping 2, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3958)
OS uptime: 0 days 8:54 hours
Hyper-V role detected

CPU: total 20 (initial active 20) (10 cores per cpu, 2 threads per core) family 6 model 186 stepping 2 microcode 0x4114, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv

Memory: 4k page, system-wide physical 16108M (45M free)
TotalPageFile size 41706M (AvailPageFile size 119M)
current process WorkingSet (physical memory assigned to process): 714M, peak: 721M
current process commit charge ("private bytes"): 779M, peak: 1254M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.14+8-LTS-191) for windows-amd64 JRE (17.0.14+8-LTS-191), built on Dec  3 2024 11:07:59 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
