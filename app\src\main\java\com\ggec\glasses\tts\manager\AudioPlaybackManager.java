package com.ggec.glasses.tts.manager;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.MediaPlayer;
import android.net.Uri;
import android.util.Log;

import java.io.File;
import java.io.IOException;

/**
 * 管理音频文件的播放和播放完成后的清理。
 */
public class AudioPlaybackManager {

    private static final String TAG = "AudioPlaybackManager";
    private final Context context;
    private MediaPlayer mediaPlayer;

    public AudioPlaybackManager(Context context) {
        this.context = context.getApplicationContext();
    }

    /**
     * 播放指定的音频文件。
     * 如果当前有正在播放的音频，会先停止。
     * @param filePath 音频文件的绝对路径。
     */
    public void playAudioFile(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            Log.e(TAG, "Audio file path is null or empty.");
            return;
        }

        File audioFile = new File(filePath);
        if (!audioFile.exists()) {
            Log.e(TAG, "Audio file does not exist: " + filePath);
            return;
        }

        Log.d(TAG, "Attempting to play audio file: " + filePath);
        stopPlayback(); // 停止之前的播放

        mediaPlayer = new MediaPlayer();
        mediaPlayer.setAudioAttributes(
                new AudioAttributes.Builder()
                        .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH) // 指示内容类型为语音
                        .setUsage(AudioAttributes.USAGE_MEDIA) // 指示用途为媒体播放
                        .build()
        );

        mediaPlayer.setOnCompletionListener(mp -> {
            Log.d(TAG, "Playback completed for: " + filePath);
            cleanupPlayback(filePath); // 播放完成，清理
        });

        mediaPlayer.setOnErrorListener((mp, what, extra) -> {
            Log.e(TAG, "MediaPlayer error: what=" + what + ", extra=" + extra + " for file: " + filePath);
            cleanupPlayback(filePath); // 发生错误，清理
            return true; // 返回 true 表示错误已被处理
        });

        try {
            mediaPlayer.setDataSource(context, Uri.fromFile(audioFile));
            Log.d(TAG, "Preparing MediaPlayer...");
            mediaPlayer.prepareAsync(); // 异步准备

            mediaPlayer.setOnPreparedListener(mp -> {
                Log.d(TAG, "MediaPlayer prepared. Starting playback...");
                try {
                    mp.start();
                } catch (IllegalStateException e) {
                    Log.e(TAG, "Error starting playback after prepare", e);
                    cleanupPlayback(filePath);
                }
            });

        } catch (IOException | IllegalStateException | SecurityException e) {
            Log.e(TAG, "Error setting data source or preparing MediaPlayer for file: " + filePath, e);
            cleanupPlayback(filePath); // 准备阶段出错，清理
        }
    }

    /**
     * 停止当前播放（如果正在播放）。
     */
    public void stopPlayback() {
        if (mediaPlayer != null) {
            Log.d(TAG, "Stopping previous playback.");
            try {
                if (mediaPlayer.isPlaying()) {
                    mediaPlayer.stop();
                }
            } catch (IllegalStateException e) {
                Log.w(TAG, "Error stopping MediaPlayer, possibly already released.", e);
            }
            // 不在这里删除文件，因为可能只是暂停或切换
            // release 在 cleanupPlayback 中处理
        }
    }

    /**
     * 清理 MediaPlayer 资源并删除关联的音频文件。
     * @param filePath 要删除的音频文件的路径。
     */
    private void cleanupPlayback(String filePath) {
        Log.d(TAG, "Cleaning up playback resources for: " + filePath);
        if (mediaPlayer != null) {
            try {
                mediaPlayer.release(); // 释放 MediaPlayer 资源
            } catch (Exception e) {
                Log.e(TAG, "Error releasing MediaPlayer", e);
            }
            mediaPlayer = null;
        }
        deleteAudioFile(filePath); // 删除临时文件
    }

    /**
     * 删除指定的音频文件。
     * @param filePath 文件路径。
     */
    private void deleteAudioFile(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return;
        }
        File fileToDelete = new File(filePath);
        if (fileToDelete.exists()) {
            if (fileToDelete.delete()) {
                Log.d(TAG, "Successfully deleted temporary audio file: " + filePath);
            } else {
                Log.w(TAG, "Failed to delete temporary audio file: " + filePath);
            }
        } else {
            Log.w(TAG, "Attempted to delete non-existent file: " + filePath);
        }
    }

    /**
     * 在不再需要时释放资源。
     */
    public void release() {
        Log.d(TAG, "Releasing AudioPlaybackManager resources.");
        stopPlayback();
        if (mediaPlayer != null) {
            try {
                mediaPlayer.release();
            } catch (Exception e) {
                Log.e(TAG, "Error releasing MediaPlayer during final release", e);
            }
            mediaPlayer = null;
        }
    }
} 