package com.ggec.glasses.ai.manager;

import android.app.Application;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.ggec.glasses.voice.manager.VoiceManager;
import com.ggec.glasses.voice.manager.VoiceRecognitionCallback;

/**
 * 语音处理程序
 * 负责管理语音识别的逻辑和状态
 */
public class ChatVoiceHandler {
    private static final String TAG = "ChatVoiceHandler";

    private final VoiceManager voiceManager;
    private final MutableLiveData<Boolean> isRecording = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> isRecognizing = new MutableLiveData<>(false);
    private final MutableLiveData<String> partialRecognitionResult = new MutableLiveData<>("");
    private final MutableLiveData<String> finalRecognitionResult = new MutableLiveData<>(); // 用于传递最终结果
    private final MutableLiveData<String> recognitionError = new MutableLiveData<>(); // 用于传递错误

    /**
     * 构造函数
     * @param application 应用程序上下文
     */
    public ChatVoiceHandler(Application application) {
        voiceManager = new VoiceManager(application);
    }

    /**
     * 开始语音识别
     */
    public void startVoiceRecognition() {
        Log.d(TAG, "开始语音识别");
        // 重置状态
        isRecording.postValue(true);
        isRecognizing.postValue(true);
        partialRecognitionResult.postValue("");
        finalRecognitionResult.postValue(null); // 清除上次结果
        recognitionError.postValue(null); // 清除上次错误

        voiceManager.startVoiceRecognition(new VoiceRecognitionCallback() {
            @Override
            public void onRecognitionStarted() {
                Log.d(TAG, "语音识别已开始");
                isRecording.postValue(true);
                isRecognizing.postValue(true);
            }

            @Override
            public void onPartialResult(String text) {
                Log.d(TAG, "部分识别结果: " + text);
                partialRecognitionResult.postValue(text);
            }

            @Override
            public void onFinalResult(String text) {
                Log.d(TAG, "最终识别结果: " + text);
                isRecording.postValue(false);
                isRecognizing.postValue(false); // 识别结束
                partialRecognitionResult.postValue(""); // 清空部分结果
                if (text != null && !text.trim().isEmpty()) {
                    finalRecognitionResult.postValue(text);
                } else {
                    Log.w(TAG, "最终识别结果为空");
                    // 可以选择传递一个空字符串或特定的错误消息
                    // recognitionError.postValue("识别结果为空");
                }
            }

            @Override
            public void onRecognitionError(String error) {
                Log.e(TAG, "语音识别错误: " + error);
                isRecording.postValue(false);
                isRecognizing.postValue(false);
                partialRecognitionResult.postValue(""); // 清空部分结果
                recognitionError.postValue(error);
            }

            @Override
            public void onRecognitionComplete() {
                Log.d(TAG, "语音识别流程完成");
                // 确保状态最终为false，即使没有收到FinalResult或Error
                isRecording.postValue(false);
                isRecognizing.postValue(false);
                // 如果partialResult还有值，可能需要根据业务逻辑决定是否清空或使用
                 if (partialRecognitionResult.getValue() != null && !partialRecognitionResult.getValue().isEmpty()) {
                     Log.d(TAG, "识别完成但仍有部分结果: " + partialRecognitionResult.getValue());
                     // 可能需要将部分结果作为最终结果处理，或者忽略
                     // finalRecognitionResult.postValue(partialRecognitionResult.getValue());
                 }
                 partialRecognitionResult.postValue(""); // 最终清空
            }
        });
    }

    /**
     * 停止语音识别
     */
    public void stopVoiceRecognition() {
        Log.d(TAG, "停止语音识别");
        voiceManager.stopVoiceRecognition();
        // 主动更新状态，避免回调可能的延迟
        isRecording.postValue(false);
        // isRecognizing 的状态由回调处理
    }

    /**
     * 释放资源
     */
    public void release() {
        Log.d(TAG, "释放 ChatVoiceHandler 资源");
        voiceManager.release();
    }

    // --- LiveData Getters ---

    /**
     * 获取是否正在录音的LiveData
     * @return 录音状态LiveData
     */
    public LiveData<Boolean> getIsRecording() {
        return isRecording;
    }

    /**
     * 获取是否正在识别的LiveData
     * @return 识别状态LiveData
     */
    public LiveData<Boolean> getIsRecognizing() {
        return isRecognizing;
    }

    /**
     * 获取部分识别结果的LiveData
     * @return 部分识别结果LiveData
     */
    public LiveData<String> getPartialRecognitionResult() {
        return partialRecognitionResult;
    }

    /**
     * 获取最终识别结果的LiveData (一次性事件)
     * @return 最终识别结果LiveData
     */
    public LiveData<String> getFinalRecognitionResult() {
        return finalRecognitionResult;
    }

     /**
     * 获取识别错误的LiveData (一次性事件)
     * @return 识别错误LiveData
     */
    public LiveData<String> getRecognitionError() {
        return recognitionError;
    }
} 