package com.ggec.glasses.asr.service;

import com.ggec.glasses.asr.model.AsrRequest;
import com.ggec.glasses.asr.model.AsrResult;

import io.reactivex.Flowable;

/**
 * 语音识别服务接口
 * 定义与语音识别相关的核心功能
 */
public interface AsrService {

    /**
     * 初始化语音识别服务
     * 在使用其他方法前必须先调用此方法
     * 
     * @return 如果初始化成功返回true，否则返回false
     */
    boolean initialize();
    
    /**
     * 检查服务是否已初始化
     * 
     * @return 如果服务已初始化返回true，否则返回false
     */
    boolean isInitialized();
    
    /**
     * 开始一次新的语音识别会话
     * 
     * @return 识别结果的Flowable流
     */
    Flowable<AsrResult> startRecognition();
    
    /**
     * 发送音频数据进行识别
     * 在startRecognition()之后调用
     * 
     * @param request 包含音频数据的请求
     * @return 操作是否成功
     */
    boolean processAudioData(AsrRequest request);
    
    /**
     * 停止当前识别会话
     * 
     * @return 操作是否成功
     */
    boolean stopRecognition();
    
    /**
     * 释放资源
     * 当不再需要语音识别服务时调用
     */
    void release();
} 