<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:background="@drawable/bg_profile_setting_item_ripple"
    android:clickable="true"
    android:focusable="true">

    <!-- 设置项图标 -->
    <ImageView
        android:id="@+id/iv_setting_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_public_security" />

    <!-- 设置项标题 -->
    <TextView
        android:id="@+id/tv_setting_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:textColor="@color/font_primary"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_setting_arrow"
        app:layout_constraintStart_toEndOf="@id/iv_setting_icon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="账号与安全" />

    <!-- 右箭头图标 -->
    <ImageView
        android:id="@+id/iv_setting_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_public_arrow_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 