package com.ggec.glasses.ai.data.db.migration;

import androidx.annotation.NonNull;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

/**
 * 数据库版本迁移类，用于从版本1升级到版本2
 * 作为未来数据库版本迁移的模板
 */
public class Migration_1_2 extends Migration {
    
    /**
     * 构造函数
     */
    public Migration_1_2() {
        super(1, 2);
    }
    
    /**
     * 实现迁移逻辑
     * @param database 数据库实例
     */
    @Override
    public void migrate(@NonNull SupportSQLiteDatabase database) {
        // 这里是从版本1到版本2的迁移示例代码
        // 例如：添加新列
        // database.execSQL("ALTER TABLE chat_message ADD COLUMN new_column TEXT");
        
        // 或者创建新表
        // database.execSQL("CREATE TABLE IF NOT EXISTS new_table (id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT)");
        
        // 目前仅作为模板，实际迁移逻辑将在未来版本中实现
    }
} 