package com.ggec.glasses.ai.strategy;

import android.content.Context;
import android.util.Log;

import com.ggec.glasses.ai.constants.ChatConstants;
import com.ggec.glasses.ai.manager.ChatMessageManager;
import com.ggec.glasses.aimodelchat.aimodel.deepseek.DeepSeekChatService;
import com.ggec.glasses.aimodelchat.manager.AIResponseHandler;
import com.ggec.glasses.asr.model.AsrResult;

import io.reactivex.Flowable;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * DeepSeek模型策略实现
 * 使用DeepSeek AI模型处理语音输入和文本输入
 */
public class DeepSeekStrategy extends AbstractAIModelStrategy {

    private static final String TAG = "DeepSeekStrategy";
    
    // 使用统一的会话ID常量
    private static final long DEFAULT_CONVERSATION_ID = ChatConstants.DEFAULT_CONVERSATION_ID;
    
    // DeepSeek服务
    private DeepSeekChatService deepSeekChatService;
    
    // 会话管理
    private ChatMessageManager chatMessageManager;
    
    // RxJava资源管理
    private final CompositeDisposable compositeDisposable = new CompositeDisposable();
    
    // 当前会话ID
    private long currentConversationId = -1;
    
    /**
     * 初始化DeepSeek特定组件
     * @return 初始化是否成功
     */
    @Override
    protected boolean initializeModelSpecificComponents() {
        try {
            // 创建DeepSeek服务
            deepSeekChatService = new DeepSeekChatService(context);
            
            // 验证DeepSeek服务是否可用
            if (!deepSeekChatService.isAvailable()) {
                Log.e(TAG, "DeepSeek服务初始化失败：API Key无效或服务不可用");
                return false;
            }
            
            // 创建ChatMessageManager实例，使用统一的会话ID
            chatMessageManager = new ChatMessageManager(application, DEFAULT_CONVERSATION_ID);
            
            // 创建AI响应处理器
            aiResponseHandler = new AIResponseHandler(chatMessageManager);
            
            return true;
        } catch (Exception e) {
            Log.e(TAG, "初始化DeepSeek组件时出错", e);
            return false;
        }
    }
    
    /**
     * 开始音频处理会话
     * @param conversationId 会话ID
     */
    @Override
    protected void beginAudioSession(long conversationId) {
        if (audioSessionActive) {
            Log.w(TAG, "音频会话已经在进行中");
            return;
        }
        
        this.currentConversationId = conversationId;
        
        // 开始ASR识别
        Flowable<AsrResult> resultFlowable = asrService.startRecognition();
        
        // 订阅ASR结果
        Disposable disposable = resultFlowable
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io())
            .subscribe(
                // onNext: 处理ASR结果
                result -> {
                    if (result.isFinal() && result.isSuccess()) {
                        String recognizedText = result.getText();
                        if (recognizedText != null && !recognizedText.trim().isEmpty()) {
                            Log.d(TAG, "ASR识别完成: " + recognizedText);
                            
                            // 将识别文本发送到DeepSeek进行处理
                            processTextInput(recognizedText, currentConversationId);
                        }
                    }
                },
                // onError: 处理错误
                error -> {
                    Log.e(TAG, "ASR识别出错", error);
                    audioSessionActive = false;
                },
                // onComplete: 会话结束
                () -> {
                    Log.d(TAG, "ASR识别会话结束");
                    audioSessionActive = false;
                }
            );
        
        // 添加到待处理列表
        compositeDisposable.add(disposable);
        
        // 标记会话已开始
        audioSessionActive = true;
        Log.d(TAG, "开始音频处理会话，会话ID: " + conversationId);
    }
    
    /**
     * 处理文本输入并生成回复
     * @param textInput 文本输入
     * @param conversationId 会话ID
     * @return 操作是否成功开始
     */
    @Override
    public boolean processTextInput(String textInput, long conversationId) {
        if (!isAvailable()) {
            Log.e(TAG, "DeepSeek策略不可用");
            return false;
        }
        
        if (textInput == null || textInput.trim().isEmpty()) {
            Log.e(TAG, "文本输入为空");
            return false;
        }
        
        Log.d(TAG, "处理文本输入: " + textInput + ", 会话ID: " + conversationId +
              ", 默认会话ID: " + DEFAULT_CONVERSATION_ID);
        
        // 更新当前会话ID
        this.currentConversationId = conversationId;
        
        // 调用DeepSeek API
        deepSeekChatService.getChatCompletion(textInput, new DeepSeekChatService.CompletionCallback() {
            @Override
            public void onSuccess(String aiResponseText) {
                Log.d(TAG, "DeepSeek响应成功: " + aiResponseText);
                
                // 1. 处理AI响应 (保存到数据库 -> 通过LiveData更新UI)
                executorService.submit(() -> {
                    try {
                        Log.d(TAG, "准备处理AI响应，会话ID: " + conversationId);
                        aiResponseHandler.handleAIResponse(aiResponseText, conversationId);
                    } catch (Exception e) {
                        Log.e(TAG, "处理AI响应时出错", e);
                    }
                });
                
                // 2. 并行触发TTS合成和播放
                executorService.submit(() -> {
                    try {
                        ttsCoordinator.streamAndPlay(aiResponseText);
                    } catch (Exception e) {
                        Log.e(TAG, "触发TTS时出错", e);
                    }
                });
            }
            
            @Override
            public void onError(String error) {
                Log.e(TAG, "DeepSeek响应错误: " + error);
                // 可以在这里添加错误处理逻辑，例如通过TTS播放错误消息
            }
        });
        
        return true;
    }
    
    /**
     * 释放DeepSeek特定资源
     */
    @Override
    protected void releaseModelSpecificResources() {
        // 清理RxJava订阅
        if (compositeDisposable != null && !compositeDisposable.isDisposed()) {
            compositeDisposable.dispose();
        }
        
        // DeepSeek服务不需要显式释放，清除引用即可
        deepSeekChatService = null;
        aiResponseHandler = null;
        chatMessageManager = null;
    }
    
    /**
     * 获取模型类型
     * @return DEEPSEEK枚举值
     */
    @Override
    public AIModelType getModelType() {
        return AIModelType.DEEPSEEK;
    }
} 