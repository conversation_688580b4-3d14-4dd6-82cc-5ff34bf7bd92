/*
 * Copyright (c) 2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.sample.audioconnect.smartwear.gallery

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Build
import androidx.core.content.FileProvider
import androidx.core.net.toUri
import com.realsil.sdk.core.logger.ZLogger
import java.io.File

/**
 * <AUTHOR>
 * @date 2025/03/26
 */
object GalleryUtils {
    fun loadFileList(dirPath:String = "", fileType:Int = FileType.IMAGE):ArrayList<LocaleMediaFile> {
        val castFileList:ArrayList<LocaleMediaFile> = ArrayList()

        val dir = File(dirPath)
        if (dir.isDirectory) {
            val files = dir.listFiles()
            if (files != null && files.isNotEmpty()) {
                var index = 0
                for (file in files) {
                    if (fileType == FileType.VIDEO) {
                        if ("mp4".contains(file.extension, true)) {
                            val thumbnail = getVideoThumbnail(file.path)
                            castFileList.add(LocaleMediaFile(index=index++, type=fileType, filePath = "$dirPath/",
                                name = file.name, originFile = file,
                                uriString = file.toUri().toString(),
                                thumbnail=thumbnail))
                        }
                    } else {
                        if ("jpg".contains(file.extension, true)) {
                            castFileList.add(LocaleMediaFile(index=index++, type=fileType, filePath = "$dirPath/",
                                name = file.name, originFile = file,
                                        uriString = file.toUri().toString(),))
                        }
                    }
                }
            } else {
                ZLogger.w( String.format("%s contains no files", dirPath))
            }
        } else {
            ZLogger.w( String.format("%s is not a directory", dirPath))
        }
        return castFileList
    }

    /**
     * retrieve thumbnail of video file
     * */
    fun getVideoThumbnail(videoPath: String): Bitmap? {
        val retriever = MediaMetadataRetriever()
        try {
            // 设置数据源
            retriever.setDataSource(videoPath)
            // 获取视频的缩略图
            return retriever.frameAtTime
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            retriever.release()
        }
        return null
    }


    /**
     * view media file
     * */
    fun viewFile(context: Context, file: LocaleMediaFile) {
        val intent = Intent(Intent.ACTION_VIEW)
        val videoUri: Uri

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            videoUri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file.originFile
            )
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        } else {
            videoUri = Uri.fromFile(file.originFile)
        }

        if (file.type == FileType.VIDEO) {
            intent.setDataAndType(videoUri, "video/*")
        } else {
            intent.setDataAndType(videoUri, "image/*")
        }
        if (intent.resolveActivity(context.packageManager) != null) {
            context.startActivity(intent)
        }
    }
}