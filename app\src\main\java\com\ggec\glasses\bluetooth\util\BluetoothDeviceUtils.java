package com.ggec.glasses.bluetooth.util;

import android.Manifest;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import com.ggec.glasses.bluetooth.manager.BluetoothProfileManager;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 蓝牙设备工具类
 * 提供设备查询和状态检查的静态方法
 */
public class BluetoothDeviceUtils {

    private static final String TAG = "BluetoothDeviceUtils";

    /**
     * 获取设备当前连接状态
     * @param context 上下文
     * @param device 蓝牙设备
     * @param profileManager 蓝牙配置文件管理器
     * @return 如果任何Profile已连接则返回true，否则返回false
     */
    public static boolean isDeviceConnected(Context context, BluetoothDevice device, BluetoothProfileManager profileManager) {
        if (device == null || profileManager == null) {
            return false;
        }
        
        // 检查蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Log.w(TAG, "检查设备连接状态失败：缺少BLUETOOTH_CONNECT权限");
                return false;
            }
        }
        
        try {
            Map<Integer, BluetoothProfile> profiles = profileManager.getProfiles();
            for (BluetoothProfile profile : profiles.values()) {
                List<BluetoothDevice> connectedDevices = profile.getConnectedDevices();
                if (connectedDevices.contains(device)) {
                    return true;
                }
            }
        } catch (SecurityException e) {
            Log.e(TAG, "获取连接设备列表时出现安全异常", e);
        }
        
        return false;
    }
    
    /**
     * 检查设备是否正在连接
     * @param context 上下文
     * @param device 蓝牙设备
     * @param profileManager 蓝牙配置文件管理器
     * @return 如果任何Profile正在连接则返回true，否则返回false
     */
    public static boolean isDeviceConnecting(Context context, BluetoothDevice device, BluetoothProfileManager profileManager) {
        if (device == null || profileManager == null) {
            return false;
        }
        
        // 检查蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Log.w(TAG, "检查设备连接状态失败：缺少BLUETOOTH_CONNECT权限");
                return false;
            }
        }
        
        try {
            Map<Integer, BluetoothProfile> profiles = profileManager.getProfiles();
            for (BluetoothProfile profile : profiles.values()) {
                List<BluetoothDevice> connectingDevices = profile.getDevicesMatchingConnectionStates(
                        new int[]{BluetoothProfile.STATE_CONNECTING});
                
                if (connectingDevices.contains(device)) {
                    return true;
                }
            }
        } catch (SecurityException e) {
            Log.e(TAG, "获取连接中设备列表时出现安全异常", e);
        }
        
        return false;
    }
    
    /**
     * 获取所有已连接的设备
     * @param context 上下文
     * @param profileManager 蓝牙配置文件管理器
     * @return 已连接的设备列表
     */
    public static List<BluetoothDevice> getConnectedDevices(Context context, BluetoothProfileManager profileManager) {
        List<BluetoothDevice> connectedDevices = new ArrayList<>();
        
        if (profileManager == null) {
            return connectedDevices;
        }
        
        // 检查蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Log.w(TAG, "获取已连接设备列表失败：缺少BLUETOOTH_CONNECT权限");
                return connectedDevices;
            }
        }
        
        try {
            Map<Integer, BluetoothProfile> profiles = profileManager.getProfiles();
            for (BluetoothProfile profile : profiles.values()) {
                List<BluetoothDevice> devices = profile.getConnectedDevices();
                for (BluetoothDevice device : devices) {
                    if (!connectedDevices.contains(device)) {
                        connectedDevices.add(device);
                    }
                }
            }
        } catch (SecurityException e) {
            Log.e(TAG, "获取连接设备列表时出现安全异常", e);
        }
        
        return connectedDevices;
    }
    
    /**
     * 获取所有正在连接的设备
     * @param context 上下文
     * @param profileManager 蓝牙配置文件管理器
     * @return 正在连接的设备列表
     */
    public static List<BluetoothDevice> getConnectingDevices(Context context, BluetoothProfileManager profileManager) {
        List<BluetoothDevice> connectingDevices = new ArrayList<>();
        
        if (profileManager == null) {
            return connectingDevices;
        }
        
        // 检查蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Log.w(TAG, "获取已连接设备列表失败：缺少BLUETOOTH_CONNECT权限");
                return connectingDevices;
            }
        }
        
        try {
            Map<Integer, BluetoothProfile> profiles = profileManager.getProfiles();
            for (BluetoothProfile profile : profiles.values()) {
                List<BluetoothDevice> devices = profile.getDevicesMatchingConnectionStates(
                        new int[]{BluetoothProfile.STATE_CONNECTING});
                
                for (BluetoothDevice device : devices) {
                    if (!connectingDevices.contains(device)) {
                        connectingDevices.add(device);
                    }
                }
            }
        } catch (SecurityException e) {
            Log.e(TAG, "获取连接设备列表时出现安全异常", e);
        }
        
        return connectingDevices;
    }
    
    /**
     * 根据MAC地址获取蓝牙设备
     * @param context 上下文
     * @param adapter 蓝牙适配器
     * @param address MAC地址
     * @return 蓝牙设备，如果未找到则返回null
     */
    public static BluetoothDevice getDeviceByAddress(Context context, android.bluetooth.BluetoothAdapter adapter, String address) {
        if (adapter == null || address == null || address.isEmpty()) {
            return null;
        }
        
        // 检查蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "获取设备失败：缺少BLUETOOTH_CONNECT权限");
                return null;
            }
        }
        
        try {
            // 先从已配对设备中查找
            Set<BluetoothDevice> bondedDevices = adapter.getBondedDevices();
            if (bondedDevices != null) {
                for (BluetoothDevice device : bondedDevices) {
                    if (device.getAddress().equals(address)) {
                        return device;
                    }
                }
            }
            
            // 如果未在已配对设备中找到，尝试通过MAC地址创建设备
            return adapter.getRemoteDevice(address);
        } catch (SecurityException e) {
            Log.e(TAG, "获取设备时出现安全异常", e);
            return null;
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "通过MAC地址获取设备失败：无效的地址格式", e);
            return null;
        }
    }
    
    /**
     * 获取已配对设备列表
     * @param context 上下文
     * @param adapter 蓝牙适配器
     * @return 已配对设备集合
     */
    public static Set<BluetoothDevice> getBondedDevices(Context context, android.bluetooth.BluetoothAdapter adapter) {
        if (adapter == null) {
            return new HashSet<>();
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    Log.e(TAG, "获取已配对设备列表失败：缺少BLUETOOTH_CONNECT权限");
                    return new HashSet<>();
                }
            }
            
            return adapter.getBondedDevices();
        } catch (SecurityException e) {
            Log.e(TAG, "获取已配对设备时出现安全异常", e);
            return new HashSet<>();
        }
    }
} 