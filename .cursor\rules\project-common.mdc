---
description: 
globs: 
alwaysApply: false
---
Before diving into the best practices, please note that you may need to adapt the globs depending on your specific project structure.

---
name: android-studio-best-practices.mdc
description: Best practices for Android Studio development
globs: **/*.{java,gradle}
---

- Regularly update Android Studio to the latest version for improved performance and features
- Use version control integration for better collaboration and code management
- Leverage the built-in emulator for efficient testing across different device configurations

---
name: java-best-practices.mdc
description: Best practices for Java development in Android
globs: **/*.java
---

- Use Java 17 language features like switch expressions and text blocks
- Implement proper exception handling and resource management with try-with-resources
- Utilize Java streams for efficient data processing and manipulation

---
name: gradle-best-practices.mdc
description: Best practices for Gradle build management in Android
globs: **/*.gradle
---

- Use the Gradle version catalog for centralized dependency management
- Implement build caching to speed up build times
- Utilize Gradle's incremental builds for faster development cycles

---
name: android-gradle-plugin-best-practices.mdc
description: Best practices for using the Android Gradle Plugin
globs: **/*.gradle
---

- Keep the Android Gradle Plugin up-to-date for the latest features and optimizations
- Use the plugin's buildConfigField to manage build-time constants
- Leverage the plugin's signing configuration for secure app distribution

---
name: material-design-best-practices.mdc
description: Best practices for using Material Design components
globs: **/*.java
---

- Follow Material Design guidelines for consistent UI across Android devices
- Use Material Theming to customize components while maintaining design consistency
- Implement Material Motion for smooth transitions and animations

---
name: appcompat-best-practices.mdc
description: Best practices for using AppCompat for backward compatibility
globs: **/*.java
---

- Use AppCompat themes and styles for consistent look across different Android versions
- Implement AppCompat's ActionBar for a unified app navigation experience
- Leverage AppCompat's vector drawable support for scalable graphics

---
name: constraintlayout-best-practices.mdc
description: Best practices for using ConstraintLayout in Android
globs: **/*.xml
---

- Use ConstraintLayout as the root layout for complex UI designs
- Implement constraint chains for flexible and responsive layouts
- Utilize ConstraintLayout's guidelines and barriers for dynamic positioning

---
name: activity-best-practices.mdc
description: Best practices for using Activity components in Android
globs: **/*.java
---

- Use ViewModel with Activities for better state management and separation of concerns
- Implement proper lifecycle-aware components to handle configuration changes
- Utilize ActivityResult API for managing results from other activities or intents