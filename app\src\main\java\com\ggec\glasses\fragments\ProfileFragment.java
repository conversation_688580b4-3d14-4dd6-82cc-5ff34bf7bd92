package com.ggec.glasses.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ggec.glasses.R;
import com.ggec.glasses.profile.ProxySettingsActivity;

public class ProfileFragment extends Fragment {

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_profile, container, false);
        initAccountSettingCard(view);
        initSystemSettingCard(view);
        initAppSettingCard(view);
        return view;
    }

    /**
     * 初始化账号设置卡片
     */
    private void initAccountSettingCard(View view) {
        // 获取账号与安全设置项
        View securityItem = view.findViewById(R.id.setting_account_security);
        
        if (securityItem != null) {
            // 设置图标
            ImageView iconView = securityItem.findViewById(R.id.iv_setting_icon);
            if (iconView != null) {
                iconView.setImageResource(R.drawable.ic_public_security);
            }
            
            // 设置文本
            TextView titleView = securityItem.findViewById(R.id.tv_setting_title);
            if (titleView != null) {
                titleView.setText("账号与安全");
            }
        }
    }
    
    /**
     * 初始化系统设置卡片
     */
    private void initSystemSettingCard(View view) {
        // 初始化代理设置项
        View vpnItem = view.findViewById(R.id.setting_vpn);
        if (vpnItem != null) {
            // 设置图标
            ImageView iconView = vpnItem.findViewById(R.id.iv_setting_icon);
            if (iconView != null) {
                iconView.setImageResource(R.drawable.ic_vpn);
            }
            
            // 设置文本
            TextView titleView = vpnItem.findViewById(R.id.tv_setting_title);
            if (titleView != null) {
                titleView.setText("代理设置");
            }
            
            // 添加点击事件
            vpnItem.setOnClickListener(v -> {
                // 跳转到代理设置页面
                Intent intent = new Intent(getActivity(), ProxySettingsActivity.class);
                startActivity(intent);
            });
        }
        
        // 初始化后台权限设置项
        View permissionItem = view.findViewById(R.id.setting_background_permission);
        if (permissionItem != null) {
            // 设置图标
            ImageView iconView = permissionItem.findViewById(R.id.iv_setting_icon);
            if (iconView != null) {
                iconView.setImageResource(R.drawable.ic_public_lock);
            }
            
            // 设置文本
            TextView titleView = permissionItem.findViewById(R.id.tv_setting_title);
            if (titleView != null) {
                titleView.setText("后台权限设置");
            }
        }
    }

    /**
     * 初始化APP设置卡片
     */
    private void initAppSettingCard(View view) {
        // 初始化意见反馈设置项
        View feedbackItem = view.findViewById(R.id.setting_feedback);
        if (feedbackItem != null) {
            // 设置图标
            ImageView iconView = feedbackItem.findViewById(R.id.iv_setting_icon);
            if (iconView != null) {
                iconView.setImageResource(R.drawable.ic_public_feedback);
            }
            
            // 设置文本
            TextView titleView = feedbackItem.findViewById(R.id.tv_setting_title);
            if (titleView != null) {
                titleView.setText("意见反馈");
            }
        }
        
        // 初始化关于设置项
        View aboutItem = view.findViewById(R.id.setting_about);
        if (aboutItem != null) {
            // 设置图标
            ImageView iconView = aboutItem.findViewById(R.id.iv_setting_icon);
            if (iconView != null) {
                iconView.setImageResource(R.drawable.ic_public_detail);
            }
            
            // 设置文本
            TextView titleView = aboutItem.findViewById(R.id.tv_setting_title);
            if (titleView != null) {
                titleView.setText("关于");
            }
        }
    }
} 