package com.ggec.glasses.album.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 视频帧提取工具，用于获取视频的首帧作为缩略图
 */
public class VideoFrameExtractor {
    private static final String TAG = "VideoFrameExtractor";
    private static final String CACHE_DIR = "video_frames";

    /**
     * 从视频提取首帧作为缩略图
     * 
     * @param context 上下文
     * @param videoPath 视频文件路径
     * @return 首帧缩略图
     */
    public static Bitmap extractVideoFrameFromVideo(Context context, String videoPath) {
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        Bitmap bitmap = null;
        
        try {
            retriever.setDataSource(videoPath);
            // 获取视频的首帧
            bitmap = retriever.getFrameAtTime(0, MediaMetadataRetriever.OPTION_CLOSEST_SYNC);
        } catch (Exception e) {
            Log.e(TAG, "Failed to extract video frame: " + e.getMessage());
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                Log.e(TAG, "Error releasing retriever: " + e.getMessage());
            }
        }
        
        return bitmap;
    }
    
    /**
     * 从视频提取首帧并保存为缩略图文件
     * 
     * @param context 上下文
     * @param videoPath 视频文件路径
     * @param thumbnailPath 缩略图保存路径
     * @return 是否成功保存缩略图
     */
    public static boolean saveVideoThumbnail(Context context, String videoPath, String thumbnailPath) {
        Bitmap bitmap = extractVideoFrameFromVideo(context, videoPath);
        if (bitmap == null) {
            return false;
        }
        
        // 确保缓存目录存在
        File cacheDir = new File(context.getExternalCacheDir(), CACHE_DIR);
        if (!cacheDir.exists()) {
            cacheDir.mkdirs();
        }
        
        File thumbnailFile = new File(thumbnailPath);
        // 确保父目录存在
        if (!thumbnailFile.getParentFile().exists()) {
            thumbnailFile.getParentFile().mkdirs();
        }
        
        FileOutputStream outStream = null;
        try {
            outStream = new FileOutputStream(thumbnailFile);
            // 压缩为JPEG格式，质量80%
            bitmap.compress(Bitmap.CompressFormat.JPEG, 80, outStream);
            outStream.flush();
            return true;
        } catch (IOException e) {
            Log.e(TAG, "Failed to save thumbnail: " + e.getMessage());
            return false;
        } finally {
            if (outStream != null) {
                try {
                    outStream.close();
                } catch (IOException e) {
                    Log.e(TAG, "Error closing file stream: " + e.getMessage());
                }
            }
            bitmap.recycle();
        }
    }
    
    /**
     * 清理视频帧缓存目录
     * 
     * @param context 上下文
     * @return 是否成功清理
     */
    public static boolean clearCache(Context context) {
        File cacheDir = new File(context.getExternalCacheDir(), CACHE_DIR);
        if (!cacheDir.exists()) {
            return true;
        }
        
        boolean success = true;
        File[] files = cacheDir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (!file.delete()) {
                    success = false;
                }
            }
        }
        
        return success;
    }
} 