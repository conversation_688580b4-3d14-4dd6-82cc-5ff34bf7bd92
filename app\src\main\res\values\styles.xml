<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 自定义底部导航栏样式 -->
    <style name="CustomBottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemRippleColor">@android:color/transparent</item>
        <item name="backgroundTint">@color/comp_background_gray</item>
    </style>

    <!-- 自定义底部导航栏胶囊样式 -->
    <style name="Widget.App.BottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="materialThemeOverlay">@style/ThemeOverlay.App.BottomNavigationView</item>
    </style>

    <style name="ThemeOverlay.App.BottomNavigationView" parent="">
        <item name="colorPrimary">@color/brand</item>
        <item name="colorSurface">@color/nav_pill_background</item>
    </style>

    <!-- 胶囊样式标签文本 -->
    <style name="PillTabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAlignment">center</item>
    </style>

    <!-- 胶囊样式的标签页 -->
    <style name="PillTabLayoutStyle" parent="Widget.Design.TabLayout">
        <item name="tabBackground">@drawable/tab_selector</item>
        <item name="tabIndicator">@android:color/transparent</item>
        <item name="tabGravity">start</item>
        <item name="tabMode">scrollable</item>
        <item name="tabInlineLabel">true</item>
        <item name="tabRippleColor">@android:color/transparent</item>
        <item name="tabSelectedTextColor">@color/font_on_primary</item>
        <item name="tabTextColor">@color/font_secondary</item>
        <item name="tabTextAppearance">@style/PillTabTextAppearance</item>
    </style>

    <!-- 自定义对话框样式 -->
    <style name="AppAlertDialogStyle" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="android:windowBackground">@drawable/bg_dialog</item>
        <item name="colorAccent">@color/brand</item>
        <item name="android:textColorPrimary">@color/font_primary</item>
        <item name="android:textColorSecondary">@color/font_secondary</item>
        <item name="buttonBarNegativeButtonStyle">@style/NegativeButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">@style/PositiveButtonStyle</item>
        <item name="buttonBarStyle">@style/CustomButtonBarStyle</item>
    </style>
    
    <style name="NegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/brand</item>
        <item name="android:textSize">16sp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center</item>
    </style>
    
    <style name="PositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/warning</item>
        <item name="android:textSize">16sp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center</item>
    </style>
    
    <style name="CustomButtonBarStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:gravity">center</item>
    </style>

    <!-- 自定义开关按钮样式 -->
    <style name="CustomSwitchStyle" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="track">@drawable/switch_track</item>
        <item name="android:background">@null</item>
        <item name="android:switchMinWidth">40dp</item>
        <item name="switchMinWidth">40dp</item>
        <item name="android:switchPadding">6dp</item>
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
    </style>
    
    <!-- 鸿蒙风格开关按钮样式 -->
    <style name="HarmonySwitchStyle" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="track">@drawable/hm_switch_track</item>
        <item name="android:thumb">@drawable/hm_switch_thumb</item>
        <item name="android:background">@null</item>
        <item name="android:switchMinWidth">50dp</item>
        <item name="switchMinWidth">50dp</item>
        <item name="android:switchPadding">8dp</item>
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
    </style>
</resources> 