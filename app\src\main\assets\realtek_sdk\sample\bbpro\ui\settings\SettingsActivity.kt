/*
 * Copyright (c) 2017-2024. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.bbpro.ui.settings

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentTransaction
import com.realsil.bbpro.R
import com.realsil.bbpro.databinding.ActivitySettingsBinding
import com.realsil.sample.audioconnect.durian.deviceinfo.DurianSettingsFragment
import com.realsil.sample.audioconnect.eq.spk.SpkEqActivity
import com.realsil.sample.audioconnect.eq.spk.SpkVoiceEqActivity
import com.realsil.sample.audioconnect.hearing.gamingmode.GamingModeActivity
import com.realsil.sample.audioconnect.ota.BaseOtaActivity
import com.realsil.sample.audioconnect.ota.OtaActivity
import com.realsil.sample.audioconnect.ota.OtaOverGattActivity
import com.realsil.sample.audioconnect.ota.OtaOverSppActivity
import com.realsil.sample.audioconnect.tts.TtsActivity
import com.realsil.sample.audioconnect.tts.TtsHelper
import com.realsil.sdk.audioconnect.repository.RepositoryConstants
import com.realsil.sdk.audioconnect.repository.RepositoryViewModel
import com.realsil.sdk.audioconnect.support.AudioConnectActivity
import com.realsil.sdk.audioconnect.support.AudioConnectHelper
import com.realsil.sdk.audioconnect.support.CapabilityCacheInfo
import com.realsil.sdk.audioconnect.support.ui.ChannelRwsChannelDialogFragment
import com.realsil.sdk.audioconnect.support.ui.SetNameDialogFragment
import com.realsil.sdk.audioconnect.tts.TtsConstants
import com.realsil.sdk.audioconnect.tts.TtsInfo
import com.realsil.sdk.audioconnect.tts.TtsModelCallback
import com.realsil.sdk.audioconnect.tts.TtsModelClient
import com.realsil.sdk.audioconnect.tts.TtsModelProxy
import com.realsil.sdk.audioconnect.tts.utils.TtsUtils
import com.realsil.sdk.bbpro.PeripheralConnectionManager
import com.realsil.sdk.bbpro.core.BeeError
import com.realsil.sdk.bbpro.core.peripheral.ConnectionParameters
import com.realsil.sdk.bbpro.core.protocol.params.ConfigType
import com.realsil.sdk.bbpro.core.transportlayer.AckPacket
import com.realsil.sdk.bbpro.model.DeviceInfo
import com.realsil.sdk.bbpro.params.StatusIndex
import com.realsil.sdk.bbpro.vendor.VendorConstants
import com.realsil.sdk.bbpro.vendor.VendorModelCallback
import com.realsil.sdk.core.bluetooth.compat.BluetoothDeviceCompat
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.support.ui.ChoiceEntity
import com.realsil.sdk.support.ui.SingleChoiceDialogFragment
import com.realsil.sdk.support.view.NoDoubleClickListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Settings
 * <AUTHOR>
 * @date 13/08/2017
 */

class SettingsActivity :
    AudioConnectActivity<ActivitySettingsBinding>(ActivitySettingsBinding::inflate) {
    private var loadFlag = 0x00

    private var mSettingsFragment: SettingsFragment? = null
    private var mDurianSettingsFragment: DurianSettingsFragment? = null

    private val settingsViewModel: SettingsViewModel by viewModels()

    private var mHandler: Handler? = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_STATUS_CHANGED -> {
                    ZLogger.v("MSG_STATUS_CHANGED")
                    if (!settingsViewModel.isBusy()) {
                        cancelProgressBar()
                    }
                    refresh()
                }

                MSG_LOAD_DATA -> {
                    ZLogger.v("MSG_LOAD_DATA")
                    val needLoadData = msg.obj as Boolean
                    if (needLoadData) {
                        reload()
                    } else {
                        if (!settingsViewModel.isBusy()) {
                            cancelProgressBar()
                        }
                        refresh()
                    }
                }

                MSG_START_SYNC -> {
                    settingsViewModel.startSyncData(true)
                }

                else -> {
                }
            }
        }
    }

    private var mTtsClient: TtsModelClient? = null
    private val ttsClient: TtsModelClient
        get() {
            if (mTtsClient == null) {
                mTtsClient = TtsModelProxy.getInstance().getModelClient(mDeviceAddress)
                mTtsClient!!.registerCallback(mTtsModelCallback)
            }
            return mTtsClient as TtsModelClient
        }

    private val mVendorModelCallback = object : VendorModelCallback() {
        override fun onStateChanged(state: Int) {
            super.onStateChanged(state)
            ZLogger.d("SettingsActivity onStateChanged state = $state")
            if (state == PeripheralConnectionManager.STATE_DATA_PREPARED) {
                CoroutineScope(Dispatchers.IO).launch {
                    settingsViewModel.reloadData()
                    settingsViewModel.startSyncData()
                }
            } else if (state == PeripheralConnectionManager.STATE_DEVICE_DISCONNECTED) {
                cancelProgressBar()
            }
        }

        override fun onOperationComplete(operation: Int, status: Byte) {
            super.onOperationComplete(operation, status)
            when (operation) {
                VendorConstants.Operation.GET_CFG_SETTINGS,
                VendorConstants.Operation.SET_MMI,
                VendorConstants.Operation.SET_CONFIGURATION,
//                VendorConstants.Operation.SET_FIND_ME_STATE,
                    -> {
                    if (mHandler != null) {
                        mHandler!!.sendMessage(
                            mHandler!!.obtainMessage(
                                MSG_LOAD_DATA,
                                status == AckPacket.ACK_STATUS_COMPLETE
                            )
                        )
                    }
                }
            }
        }

        override fun onDeviceInfoChanged(indicator: Int, deviceInfo: DeviceInfo) {
            super.onDeviceInfoChanged(indicator, deviceInfo)
            when (indicator) {
                DeviceInfo.INDICATOR_NAME_BREDR -> {
                    //update database
                    settingsViewModel.updateBrEdrName(
                        mDeviceAddress,
                        deviceInfo.brEdrName
                    )
                }

                DeviceInfo.INDICATOR_APT_STATUS,
                DeviceInfo.INDICATOR_NAME_LE,
                DeviceInfo.INDICATOR_BUD_INFO,
                DeviceInfo.INDICATOR_STATUS_RWS_STATE,
                DeviceInfo.INDICATOR_RWS_CHANNEL,
                DeviceInfo.INDICATOR_BATTERY_STATUS,
                DeviceInfo.INDICATOR_RWS_DEFAULT_CHANNEL,
                DeviceInfo.INDICATOR_RWS_BUD_SIDE -> {
                    if (mHandler != null) {
                        mHandler!!.sendMessage(mHandler!!.obtainMessage(MSG_STATUS_CHANGED))
                    }
                }

                else -> {
                }
            }
        }
    }

    private val mTtsModelCallback = object : TtsModelCallback() {
        override fun onOperationComplete(operation: Int, status: Byte) {
            super.onOperationComplete(operation, status)
            when (operation) {
                TtsConstants.OPERATION_GET_LANGUAGE,
                TtsConstants.OPERATION_SET_LANGUAGE
                    -> {
                    if (mHandler != null) {
                        mHandler!!.sendMessage(mHandler!!.obtainMessage(MSG_STATUS_CHANGED))
                    }
                }
            }
        }

        override fun onDeviceInfoChanged(indicator: Int, ttsInfo: TtsInfo) {
            super.onDeviceInfoChanged(indicator, ttsInfo)
            when (indicator) {
                TtsInfo.INDICATOR_LANGUAGE -> {
                    if (!settingsViewModel.isBusy()) {
                        cancelProgressBar()
                    }
                }

                else -> {
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val toolbar = findViewById<Toolbar>(R.id.mToolbar)
        toolbar.setTitle(R.string.title_settings)
        setSupportActionBar(toolbar)
        if (supportActionBar != null) {
            supportActionBar!!.setDisplayHomeAsUpEnabled(true)
        }
        toolbar.setNavigationOnClickListener { onBackPressedDispatcher.onBackPressed() }

        if (!TextUtils.isEmpty(mDeviceAddress)) {
            binding.llDeviceInfo.visibility = View.VISIBLE
            binding.btnRemove.visibility = View.VISIBLE
            binding.fragmentChargingBox.visibility = View.VISIBLE
            setupDeviceInfo()

            if (!settingsViewModel.isConnected()) {
                showProgressBar(getString(R.string.connect_device, mDeviceAddress))
                val beeError = settingsViewModel.connectDevice()
                if (beeError != BeeError.SUCCESS) {
                    cancelProgressBar()
                    showShortToast("connect failed")
                }
            } else {
                ZLogger.v("device already connected, auto start sync data")
                settingsViewModel.startSyncData()
            }
        } else {
            binding.llDeviceInfo.visibility = View.GONE
            binding.btnRemove.visibility = View.GONE
            binding.basicInfo.visibility = View.GONE
            binding.fragmentChargingBox.visibility = View.GONE
        }

    }

    private fun setupDeviceInfo() {
        binding.btnDisconnect.setOnClickListener(object : NoDoubleClickListener() {
            override fun onNoDoubleClick(v: View) {
                super.onNoDoubleClick(v)
                disconnectDevice()

                finish()
            }
        })
        binding.btnRemove.setOnClickListener(object : NoDoubleClickListener() {
            override fun onNoDoubleClick(v: View) {
                super.onNoDoubleClick(v)
                CoroutineScope(Dispatchers.IO).launch {
                    getRemoteDevice(mDeviceAddress)?.let { settingsViewModel.removeDevice(it, true) }
                    launch(Dispatchers.Main) {
                        finish()
                    }
                }
//                showAlertDialog(getString(R.string.toast_ota_music_is_active),
//                    positiveText = getString(R.string.dialog_button_continue),
//                    positiveBlock= {
//                        CoroutineScope(Dispatchers.IO).launch {
//                            getRemoteDevice(mDeviceAddress)?.let { settingsViewModel.removeDevice(it, true) }
//                            launch(Dispatchers.Main) {
//                                finish()
//                            }
//                        }
//                    }, negativeText = getString(R.string.dialog_button_continue),
//                    negativeBlock = {
//                        CoroutineScope(Dispatchers.IO).launch {
//                            getRemoteDevice(mDeviceAddress)?.let { settingsViewModel.removeDevice(it, false) }
//                            launch(Dispatchers.Main) {
//                                finish()
//                            }
//                        }
//                    }
//                )
            }
        })

        initFragments()

        binding.basicInfo.visibility = View.VISIBLE
        binding.itemOta.setOnClickListener(object : NoDoubleClickListener() {
            override fun onNoDoubleClick(v: View) {
                super.onNoDoubleClick(v)
                if (settingsViewModel.isMusicActive()) {
                    showAlertDialog(getString(R.string.toast_ota_music_is_active),
                        positiveText = getString(R.string.dialog_button_continue),
                        positiveBlock= {
                            processOta()
                        }
                    )
                } else {
                    processOta()
                }
            }
        })
        binding.itemEq.setOnClickListener(object : NoDoubleClickListener() {
            override fun onNoDoubleClick(v: View) {
                super.onNoDoubleClick(v)
                val deviceInfo = settingsViewModel.getDeviceInfo()
                if (deviceInfo.isEqInCompatible) {
                    showAlertMessage(getString(R.string.toast_eq_version_incompatible))
                    return
                }
                if (settingsViewModel.isMusicActive()) {
                    val intent = Intent(this@SettingsActivity, SpkEqActivity::class.java)
                    intent.putExtra(SpkEqActivity.EXTRA_KEY_BT_ADDR, settingsViewModel.mDeviceAddress)
                    settingsActivityResult.launch(intent)
                } else {
                    showAlertDialog(getString(R.string.toast_eq_music_is_not_active),
                        positiveText = getString(R.string.dialog_button_continue),
                        positiveBlock=  {
                            val intent = Intent(this@SettingsActivity, SpkEqActivity::class.java)
                            intent.putExtra(
                                SpkEqActivity.EXTRA_KEY_BT_ADDR,
                                settingsViewModel.mDeviceAddress
                            )
                            settingsActivityResult.launch(intent)
                        }
                    )
                }
            }
        })
        binding.itemVoiceEq.setOnClickListener(object : NoDoubleClickListener() {
            override fun onNoDoubleClick(v: View) {
                super.onNoDoubleClick(v)
                if (settingsViewModel.isInCallMode()) {
                    val intent = Intent(this@SettingsActivity, SpkVoiceEqActivity::class.java)
                    intent.putExtra(
                        SpkVoiceEqActivity.EXTRA_KEY_BT_ADDR,
                        settingsViewModel.mDeviceAddress
                    )
                    settingsActivityResult.launch(intent)
                } else {
                    showAlertDialog(getString(R.string.toast_voice_eq_music_is_not_active),
                        positiveText = getString(R.string.dialog_button_continue),
                        positiveBlock = {
                            val intent = Intent(this@SettingsActivity, SpkVoiceEqActivity::class.java)
                            intent.putExtra(
                                SpkVoiceEqActivity.EXTRA_KEY_BT_ADDR,
                                settingsViewModel.mDeviceAddress
                            )
                            settingsActivityResult.launch(intent)
                        }
                    )
                }
            }
        })
        binding.itemTts.setOnClickListener(object : NoDoubleClickListener() {
            override fun onNoDoubleClick(v: View) {
                super.onNoDoubleClick(v)
                processTts()
            }
        })
        binding.itemName.setOnClickListener(object : NoDoubleClickListener() {
            override fun onNoDoubleClick(v: View) {
                super.onNoDoubleClick(v)
                changeDeviceName()
            }
        })
        binding.itemLanguage.setOnClickListener(object : NoDoubleClickListener() {
            override fun onNoDoubleClick(v: View) {
                super.onNoDoubleClick(v)
                setLanguage()
            }
        })
        binding.itemRwsChannel.setOnClickListener(object : NoDoubleClickListener() {
            override fun onNoDoubleClick(v: View) {
                super.onNoDoubleClick(v)
                val deviceInfo = settingsViewModel.getDeviceInfo()
                if (!deviceInfo.isRwsEngaged) {
                    showAlertMessage(getString(R.string.toast_rws_disconnected))
                    return
                }

                changeRwsChannel()
            }
        })

        binding.itemGamingMode.setOnClickListener(object : NoDoubleClickListener() {
            override fun onNoDoubleClick(v: View) {
                super.onNoDoubleClick(v)
                val intent = Intent(this@SettingsActivity, GamingModeActivity::class.java)
                intent.putExtra(AudioConnectActivity.EXTRA_KEY_BT_ADDR, mDeviceAddress)
                settingsActivityResult.launch(intent)
            }
        })

        settingsViewModel.capabilityCacheInfoLiveData.observe(this) {
            refreshCapabilityInfo(it)
        }
        refreshCapabilityInfo(settingsViewModel.capabilityCacheInfo)

        settingsViewModel.registerVendorModelCallback(mVendorModelCallback)
        settingsViewModel.syncStateLiveData().observe(this) {
            invalidateOptionsMenu()
//            if (settingsViewModel.isBusy()) {
//                showProgressBar(getString(R.string.toast_sync_data_processing))
//            } else {
//                cancelProgressBar()
//            }
        }
        val deviceViewModel = RepositoryViewModel.instance!!.getDevice(mDeviceAddress)
        deviceViewModel.deviceInfoEntityLiveData.observe(this) {
            binding.tvDeviceName.text = it.deviceName
            binding.itemName.setSubTitle(it.deviceName)

            val curAppLanguage =
                TtsUtils.convertSocLan2App(it.specVersion, it.activeLanguage.toByte()) //app language
            ZLogger.v("update cmdSetVersion: ${it.specVersion} curAppLanguage: $curAppLanguage")
            binding.itemLanguage.setSubTitle(
                TtsHelper.parseLanguage(
                    baseContext,
                    curAppLanguage.toInt()
                )
            )
            if (mHandler != null) {
                mHandler!!.sendMessage(mHandler!!.obtainMessage(MSG_STATUS_CHANGED))
            }
        }
        deviceViewModel.activeDeviceInfoLiveData.observe(this) {
            refreshDeviceInfo(it)
        }
        deviceViewModel.multiLinkConflict.observe(this) {
            if (it) {
                showMultiLinkDialog(getString(R.string.toast_app_multi_link_conflict))
            } else {
                hideMultiLinkDialog()
            }
        }
        deviceViewModel.chargingCaseEntityLiveData
            .observe(this) {
                ZLogger.v("headset, address=$mDeviceAddress, chargingCase, address=${it.deviceAddress}, supported=${it.supported}")
                if (it.supported || settingsViewModel.capabilityCacheInfo.chargingCaseFeatureSupported) {
                    binding.fragmentChargingBox.visibility = View.VISIBLE
                }else {
                    binding.fragmentChargingBox.visibility = View.GONE
                }
            }
        settingsViewModel.changePeripheral(mDeviceAddress)
    }

    override fun disconnectDevice() {
        super.disconnectDevice()
        showProgressBar(getString(R.string.toast_disconnect_device, mDeviceAddress))
        val beeError = settingsViewModel.disconnect()
        if (beeError.code != BeeError.SUCCESS) {
            cancelProgressBar()
            showShortToast(beeError.message)
        }
    }

    private fun initFragments() {
        if (mSettingsFragment == null) {
            mSettingsFragment = SettingsFragment.newInstance(null)
        }
        if (mDurianSettingsFragment == null) {
            mDurianSettingsFragment = DurianSettingsFragment.newInstance(null)
        }

        supportFragmentManager.beginTransaction()
            .add(R.id.fragment_content, mSettingsFragment!!)
            .add(R.id.fragment_content, mDurianSettingsFragment!!)
            .hide(mSettingsFragment!!).hide(mDurianSettingsFragment!!)
            .commit()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.menu_settings, menu)
//        menu.findItem(R.id.menu_scan_filter).isVisible = isScanFilterEnabled
        if (settingsViewModel.isBusy()) {
            menu.findItem(R.id.action_sync)?.isVisible = false
            menu.findItem(R.id.action_sync_view)?.setActionView(
                R.layout.actionbar_indeterminate_progress
            )
        } else {
            menu.findItem(R.id.action_sync)?.isVisible = true
            menu.findItem(R.id.action_sync_view)?.actionView = null
        }

        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        val i = item.itemId
        // stop the le scan when close the activity
        if (i == R.id.action_sync) {
            if (mHandler != null) {
                mHandler!!.sendMessage(mHandler!!.obtainMessage(MSG_START_SYNC, -1))
            }
//            invalidateOptionsMenu()
        }
        return true
    }

    private val settingsActivityResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { it ->
            if (it.resultCode == Activity.RESULT_CANCELED) {
                if (!settingsViewModel.isConnected()) {
                    ZLogger.d(" connection is lost, close the window")
                    finish()
                }
            }
        }

    override fun onDestroy() {
        super.onDestroy()

        mTtsClient?.unregisterCallback(mTtsModelCallback)

        if (mHandler != null) {
            mHandler!!.removeCallbacksAndMessages(null)
            mHandler = null
        }
    }

    private fun processOta() {
        val intent: Intent
        val otaChanel = settingsViewModel.getOtaChannel()
        if (settingsViewModel.transportChannel() == ConnectionParameters.CHANNEL_TYPE_GATT) {
            intent = Intent(this, OtaOverGattActivity::class.java)
            intent.putExtra(BaseOtaActivity.EXTRA_KEY_BT_ADDR, mDeviceAddress)
            intent.putExtra(BaseOtaActivity.EXTRA_KEY_BT_DEVICE_NAME, settingsViewModel.deviceInfoEntity.deviceName)
            settingsActivityResult.launch(intent)
        } else if (settingsViewModel.transportChannel() == ConnectionParameters.CHANNEL_TYPE_SPP
            || otaChanel == RepositoryConstants.OTA_CHANNEL_SPP) {
            intent = Intent(this, OtaOverSppActivity::class.java)
            intent.putExtra(BaseOtaActivity.EXTRA_KEY_BT_ADDR, mDeviceAddress)
            intent.putExtra(BaseOtaActivity.EXTRA_KEY_BT_DEVICE_NAME, settingsViewModel.deviceInfoEntity.deviceName)
            settingsActivityResult.launch(intent)
        } else {
            intent = Intent(this, OtaActivity::class.java)
            intent.putExtra(
                BaseOtaActivity.EXTRA_KEY_BT_ADDR,
                settingsViewModel.getDeviceInfo().leAddr
            )
            intent.putExtra(BaseOtaActivity.EXTRA_KEY_BT_DEVICE_NAME, settingsViewModel.deviceInfoEntity.deviceName)
            settingsActivityResult.launch(intent)
        }
    }

    private fun processTts() {
        val intent = Intent(this, TtsActivity::class.java)
        intent.putExtra(EXTRA_KEY_BT_ADDR, settingsViewModel.mDeviceAddress)
        startActivity(intent)
    }


    private fun refreshDeviceInfo(deviceInfo: DeviceInfo) {
        ZLogger.v("refreshDeviceInfo:$deviceInfo")
        val rwsInfo = deviceInfo.wrapperRwsInfo()

        if (rwsInfo.isRws) {
            if (rwsInfo.leftConnected) {
                binding.ivLeftBtStatus.setImageLevel(AudioConnectHelper.getBatteryLevel(rwsInfo.leftBatteryValue))
                binding.tvLeftBtStatus.text =
                    getString(R.string.text_primary_bat_status, rwsInfo.leftBatteryValue)
                binding.llLeft.visibility = View.VISIBLE
            } else {
                binding.llLeft.visibility = View.GONE
            }

            if (rwsInfo.rightConnected) {
                binding.ivRightBtStatus.setImageLevel(AudioConnectHelper.getBatteryLevel(rwsInfo.rightBatteryValue))
                binding.tvRightBtStatus.text =
                    getString(R.string.text_secondary_bat_status, rwsInfo.rightBatteryValue)
                binding.llRight.visibility = View.VISIBLE
            } else {
                binding.llRight.visibility = View.GONE
            }
        } else {
            binding.ivLeftBtStatus.setImageLevel(AudioConnectHelper.getBatteryLevel(rwsInfo.activeBatteryValue))
            binding.tvLeftBtStatus.text =
                getString(R.string.text_single_bat_status, rwsInfo.activeBatteryValue)
            binding.llLeft.visibility = View.VISIBLE
            binding.llRight.visibility = View.GONE
        }

        binding.itemRwsChannel.setSubTitle(
            "${
                AudioConnectHelper.parseRwsChannel(
                    this,
                    rwsInfo.leftActiveChannel
                )
            } -- ${AudioConnectHelper.parseRwsChannel(this, rwsInfo.rightActiveChannel)}"
        )

    }

    private fun refreshCapabilityInfo(capabilityCacheInfo: CapabilityCacheInfo) {
        ZLogger.v("refreshCapabilityInfo:$capabilityCacheInfo")
        if (capabilityCacheInfo.durianFeatureSupported) {
            supportFragmentManager.beginTransaction()
                .hide(mSettingsFragment!!).show(mDurianSettingsFragment!!)
                .commitAllowingStateLoss()
        } else {
            supportFragmentManager.beginTransaction()
                .hide(mDurianSettingsFragment!!).show(mSettingsFragment!!)
                .commitAllowingStateLoss()
        }

        if (capabilityCacheInfo.otaFeatureSupported) {
            binding.itemOta.visibility = View.VISIBLE
        } else {
            binding.itemOta.visibility = View.GONE
        }

        if (capabilityCacheInfo.deviceNameFeatureSupported) {
            binding.itemName.visibility = View.VISIBLE
        } else {
            binding.itemName.visibility = View.GONE
        }

        if (capabilityCacheInfo.gamingModeFeatureSupported) {
            binding.itemGamingMode.visibility = View.VISIBLE
        } else {
            binding.itemGamingMode.visibility = View.GONE
        }
        if (capabilityCacheInfo.ttsFeatureSupported) {
//            binding.itemTts.visibility = View.VISIBLE
            binding.itemTts.visibility = View.GONE
        } else {
            binding.itemTts.visibility = View.GONE
        }

        if (capabilityCacheInfo.eqModeFeatureSupported) {
            binding.itemEq.visibility = View.VISIBLE
        } else {
            binding.itemEq.visibility = View.GONE
        }

        if (capabilityCacheInfo.spkVoiceEqFeatureSupported) {
            binding.itemVoiceEq.visibility = View.VISIBLE
        } else {
            binding.itemVoiceEq.visibility = View.GONE
        }
        if (capabilityCacheInfo.languageFeatureSupported) {
            binding.itemLanguage.visibility = View.VISIBLE
        } else {
            binding.itemLanguage.visibility = View.GONE
        }
        if (capabilityCacheInfo.rwsChannelFeatureSupported) {
            binding.itemRwsChannel.visibility = View.VISIBLE
        } else {
            binding.itemRwsChannel.visibility = View.GONE
        }
    }

    private fun refresh() {
        if (settingsViewModel.isConnected()) {
            ZLogger.v("device connected")
            binding.tvConnectionState.setText(R.string.text_state_connected)
            binding.tvConnectionState.setTextColor(ContextCompat.getColor(this, R.color.material_green_500))
            binding.btnDisconnect.visibility = View.VISIBLE

            val deviceInfo = settingsViewModel.getDeviceInfo()
            refreshDeviceInfo(deviceInfo)

            CoroutineScope(Dispatchers.IO).launch {
                settingsViewModel.reloadCacheDeviceInfo()
            }
        } else {
            ZLogger.v("device disconnected")
            hideMultiLinkDialog()
            binding.tvConnectionState.setText(R.string.text_state_disconnected)
            binding.tvConnectionState.setTextColor(ContextCompat.getColor(this, R.color.material_red_500))
            binding.btnDisconnect.visibility = View.GONE
        }
    }

    private fun reload() {
//        ZLogger.v("reload:loadlag=$loadFlag")
        if (loadFlag and LOAD_BREDR_NAME == LOAD_BREDR_NAME) {
            loadFlag = loadFlag xor LOAD_BREDR_NAME
            ZLogger.v("loadlag=$loadFlag")
            showProgressBar(R.string.toast_processing)
            val ret = settingsViewModel.getPeripheralConnectionManager().reqDeviceName(ConfigType.LEGACY_NAME)
            if (ret.code != BeeError.SUCCESS) {
                cancelProgressBar()
                showShortToast(ret.message)
            }
            return
        }

        if (loadFlag and LOAD_CHANNEL == LOAD_CHANNEL) {
            loadFlag = loadFlag xor LOAD_CHANNEL
            ZLogger.v("LOAD_CHANNEL=$loadFlag")
            showProgressBar(R.string.toast_processing)
            val ret =
                settingsViewModel.getPeripheralConnectionManager().getStatus(StatusIndex.STATUS_INDEX_RWS_CHANNEL)
            if (ret.code != BeeError.SUCCESS) {
                cancelProgressBar()
                showShortToast(ret.message)
            }
            return
        }

        cancelProgressBar()
    }

    private fun changeDeviceName() {
        val fragment = SetNameDialogFragment.getInstance(
            settingsViewModel.deviceInfoEntity.deviceName, object :SetNameDialogFragment.OnDialogListener{

                override fun onSubmit(name: String) {
                    loadFlag = loadFlag or LOAD_BREDR_NAME
                    ZLogger.v("loadlag=$loadFlag")
                    showProgressBar(R.string.toast_processing)
                    val ret = settingsViewModel.getPeripheralConnectionManager().changeDeviceName(ConfigType.LEGACY_NAME, name)
                    if (ret.code != BeeError.SUCCESS) {
                        loadFlag = loadFlag xor LOAD_BREDR_NAME
                        ZLogger.v("loadlag=$loadFlag")
                        cancelProgressBar()
                        showShortToast(ret.message)
                    }
                }

            }
        )
        val ft = supportFragmentManager.beginTransaction()
        ft.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE)
        fragment.show(ft, SetNameDialogFragment.TAG)
    }

    private fun setLanguage() {
        val mListener =
            SingleChoiceDialogFragment.OnDialogListener { item ->
                val entity = item as ChoiceEntity
                showProgressBar(R.string.toast_processing)
                val ret = ttsClient.setLanguage(entity.value)
                if (ret.code != BeeError.SUCCESS) {
                    cancelProgressBar()
                    showShortToast(ret.message)
                }
            }

        val deviceInfo = settingsViewModel.getDeviceInfo()

        val fragment = SingleChoiceDialogFragment.getInstance(
            null,
            "Set Language", TtsHelper.wrapperSupportedLanguages(
                deviceInfo.cmdSetVersion,
                settingsViewModel.getSupportedLanguages()
            ), mListener
        )
        val ft = supportFragmentManager.beginTransaction()
        ft.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE)
        fragment.show(ft, SingleChoiceDialogFragment.TAG)
    }

    /**
     * 主: 左聲道 副: 右聲道 -> 主: 右聲道 副: 左聲道 -> 主: 左右混音 副: 左右混音
     */
    private fun changeRwsChannel() {
        val mListener = object: ChannelRwsChannelDialogFragment.OnDialogListener {
            override fun onListViewItemClick(channel: Int) {
                loadFlag = loadFlag or LOAD_CHANNEL
                ZLogger.v("loadlag=$loadFlag")
                showProgressBar(R.string.toast_processing)
                val ret = settingsViewModel.getPeripheralConnectionManager().changeRwsChannelSetting()
                if (ret.code != BeeError.SUCCESS) {
                    loadFlag = loadFlag xor LOAD_CHANNEL
                    ZLogger.v("loadlag=$loadFlag")
                    cancelProgressBar()
                    showShortToast(ret.message)
                }
            }

        }

        val deviceInfo = settingsViewModel.getDeviceInfo()
        val rwsInfo = deviceInfo.wrapperRwsInfo()
        val fragment = ChannelRwsChannelDialogFragment.getInstance(null, rwsInfo, mListener)
        val ft = supportFragmentManager.beginTransaction()
        ft.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE)
        fragment.show(ft, ChannelRwsChannelDialogFragment.TAG)
    }

    companion object {
        private const val D = true

        private const val LOAD_BREDR_NAME = 0x0002
        private const val LOAD_CHANNEL = 0x0004

        const val MSG_START_SYNC = 0x00
        const val MSG_STATUS_CHANGED = 0x02
        const val MSG_LOAD_DATA = 0x03
    }
}
