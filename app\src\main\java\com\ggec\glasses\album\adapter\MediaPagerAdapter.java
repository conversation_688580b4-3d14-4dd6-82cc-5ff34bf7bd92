package com.ggec.glasses.album.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.ggec.glasses.album.fragments.MediaListFragment;

public class MediaPagerAdapter extends FragmentStateAdapter {

    public MediaPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    public MediaPagerAdapter(@NonNull Fragment fragment) {
        super(fragment);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return MediaListFragment.newInstance(MediaListFragment.TYPE_ALL);
            case 1:
                return MediaListFragment.newInstance(MediaListFragment.TYPE_PHOTO);
            case 2:
                return MediaListFragment.newInstance(MediaListFragment.TYPE_VIDEO);
            default:
                return MediaListFragment.newInstance(MediaListFragment.TYPE_ALL);
        }
    }

    @Override
    public int getItemCount() {
        return 3; // 全部、图片、视频
    }
} 