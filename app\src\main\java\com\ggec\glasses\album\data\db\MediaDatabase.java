package com.ggec.glasses.album.data.db;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;

import com.ggec.glasses.album.data.dao.AlbumDao;
import com.ggec.glasses.album.data.dao.AlbumMediaDao;
import com.ggec.glasses.album.data.dao.MediaDao;
import com.ggec.glasses.album.data.dao.MediaTagDao;
import com.ggec.glasses.album.data.dao.TagDao;
import com.ggec.glasses.album.data.entity.Album;
import com.ggec.glasses.album.data.entity.AlbumMedia;
import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.data.entity.MediaTag;
import com.ggec.glasses.album.data.entity.Tag;

import java.io.File;

/**
 * Room数据库抽象类，用于应用内相册功能
 */
@Database(
    entities = {
        Media.class,
        Album.class,
        AlbumMedia.class,
        Tag.class,
        MediaTag.class
    },
    version = 1,
    exportSchema = true
)
@TypeConverters({Converters.class})
public abstract class MediaDatabase extends RoomDatabase {
    
    // 单例模式
    private static volatile MediaDatabase INSTANCE;
    
    // 数据库名称
    private static final String DATABASE_NAME = "glasses_media.db";
    
    // DAO访问方法
    public abstract MediaDao mediaDao();
    public abstract AlbumDao albumDao();
    public abstract AlbumMediaDao albumMediaDao();
    public abstract TagDao tagDao();
    public abstract MediaTagDao mediaTagDao();
    
    /**
     * 获取数据库实例
     * @param context 应用上下文
     * @return 媒体数据库实例
     */
    public static synchronized MediaDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            // 获取外部存储目录
            File externalFilesDir = context.getExternalFilesDir(null);
            File dbDir = new File(externalFilesDir, "database");
            
            // 确保数据库目录存在
            if (!dbDir.exists()) {
                dbDir.mkdirs();
            }
            
            // 指定数据库文件路径
            File dbFile = new File(dbDir, DATABASE_NAME);
            
            INSTANCE = Room.databaseBuilder(
                    context.getApplicationContext(),
                    MediaDatabase.class,
                    dbFile.getAbsolutePath())
                    .fallbackToDestructiveMigration() // 在升级失败时重建数据库
                    .build();
        }
        return INSTANCE;
    }
    
    /**
     * 销毁数据库实例，通常在应用退出时调用
     */
    public static void destroyInstance() {
        INSTANCE = null;
    }
} 