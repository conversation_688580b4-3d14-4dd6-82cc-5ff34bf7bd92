package com.ggec.glasses.utils;

/**
 * 字符串处理工具类
 */
public class StringUtils {

    /**
     * 将文件名中间部分替换为省略号，保留头部和尾部
     * 
     * @param filename 文件名
     * @param maxLength 最大长度
     * @param prefixLength 保留的前缀长度
     * @param suffixLength 保留的后缀长度
     * @return 处理后的文件名
     */
    public static String getMiddleEllipsisFilename(String filename, int maxLength, int prefixLength, int suffixLength) {
        if (filename == null) {
            return "";
        }
        
        // 如果文件名长度小于等于最大长度，直接返回原文件名
        if (filename.length() <= maxLength) {
            return filename;
        }
        
        // 确保前缀和后缀长度不超过最大长度减3（省略号的长度）
        int totalFixLength = prefixLength + suffixLength;
        if (totalFixLength + 3 > maxLength) {
            // 如果前后缀总长度过大，减少前后缀长度
            int availableLength = maxLength - 3;
            float ratio = (float) prefixLength / totalFixLength;
            prefixLength = Math.max(1, Math.round(availableLength * ratio));
            suffixLength = Math.max(1, availableLength - prefixLength);
        }
        
        // 获取前缀和后缀
        String prefix = filename.substring(0, prefixLength);
        String suffix = filename.substring(filename.length() - suffixLength);
        
        // 拼接结果
        return prefix + "..." + suffix;
    }
    
    /**
     * 将文件名中间部分替换为省略号，使用默认参数
     * 
     * @param filename 文件名
     * @return 处理后的文件名
     */
    public static String getMiddleEllipsisFilename(String filename) {
        return getMiddleEllipsisFilename(filename, 20, 8, 8);
    }
    
    /**
     * 从完整文件路径中提取文件名
     * 
     * @param filePath 文件路径
     * @return 文件名
     */
    public static String getFileNameFromPath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "";
        }
        
        int lastSeparatorIndex = filePath.lastIndexOf('/');
        if (lastSeparatorIndex == -1) {
            // 尝试Windows路径分隔符
            lastSeparatorIndex = filePath.lastIndexOf('\\');
        }
        
        if (lastSeparatorIndex != -1 && lastSeparatorIndex < filePath.length() - 1) {
            return filePath.substring(lastSeparatorIndex + 1);
        }
        
        return filePath;
    }
} 