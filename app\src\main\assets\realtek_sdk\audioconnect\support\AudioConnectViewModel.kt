/*
 * Copyright (c) 2023-2024. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.sdk.audioconnect.support

import android.app.Application
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.media.AudioManager
import android.os.Build
import android.telecom.TelecomManager
import android.telephony.TelephonyManager
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.realsil.sdk.audioconnect.repository.RepositoryViewModel
import com.realsil.sdk.audioconnect.repository.database.DeviceInfoRepository
import com.realsil.sdk.audioconnect.repository.database.general.DeviceInfoEntity
import com.realsil.sdk.bbpro.BumblebeeCallback
import com.realsil.sdk.bbpro.MultiPeripheralConnectionManager
import com.realsil.sdk.bbpro.PeripheralConnectionManager
import com.realsil.sdk.bbpro.authentication.AuthenticationConfigure
import com.realsil.sdk.bbpro.authentication.AuthenticationState
import com.realsil.sdk.bbpro.core.BeeError
import com.realsil.sdk.bbpro.core.gatt.GattTransportConnParams
import com.realsil.sdk.bbpro.core.peripheral.ConnectionParameters
import com.realsil.sdk.bbpro.core.peripheral.PeripheralParameters
import com.realsil.sdk.bbpro.core.spp.SppTransportConnParams
import com.realsil.sdk.bbpro.model.DeviceInfo
import com.realsil.sdk.bbpro.multilink.MultiLinkInfo
import com.realsil.sdk.bbpro.profile.AppReq
import com.realsil.sdk.bbpro.vendor.VendorModelCallback
import com.realsil.sdk.bbpro.vendor.VendorModelClient
import com.realsil.sdk.core.bluetooth.compat.BluetoothDeviceCompat
import com.realsil.sdk.core.bluetooth.connection.le.GattConnParams
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.core.utility.StringUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * The ViewModel for Audio Connect, the upper layers can implement this to realize it's own business.
 * <AUTHOR>
 * @date 2023/11/10
 */
open class AudioConnectViewModel(application: Application) : AndroidViewModel(application) {
    var mContext: Context
    var mBeeProManager: PeripheralConnectionManager? = null
    var mDeviceAddress: String = ""

    private var mAudioManager: AudioManager
    private var telephonyManager: TelephonyManager
    private lateinit var telecomManager: TelecomManager

    var deviceInfoChanged = MutableLiveData<Boolean>()
    val iosConnected: MutableLiveData<Boolean> by lazy {
        MutableLiveData<Boolean>()
    }

    var deviceInfoRepository: DeviceInfoRepository
    var deviceInfoEntity: DeviceInfoEntity

    fun deviceInfoEntityLiveData(): MutableLiveData<DeviceInfoEntity>  {
        return RepositoryViewModel.instance!!.getDevice(mDeviceAddress).deviceInfoEntityLiveData
    }

    var capabilityCacheInfo: CapabilityCacheInfo
    val capabilityCacheInfoLiveData: MutableLiveData<CapabilityCacheInfo> by lazy {
        MutableLiveData<CapabilityCacheInfo>()
    }

    val cacheDeviceInfo: DeviceInfo
        get() {
            return RepositoryViewModel.instance!!.getDevice(mDeviceAddress).activeDeviceInfo
        }
    val cacheDeviceInfoLiveData: MutableLiveData<DeviceInfo>
        get() {
            return RepositoryViewModel.instance!!.getDevice(mDeviceAddress).activeDeviceInfoLiveData
        }

    fun getPeripheralConnectionManager(): PeripheralConnectionManager {
        if (mBeeProManager == null) {
            mBeeProManager = MultiPeripheralConnectionManager.getInstance(getApplication())
                .getPeripheralConnectionManager(mDeviceAddress)
            mBeeProManager?.registerVendorModelCallback(innerVendorModelCallback)
        }
        return mBeeProManager!!
    }

    fun getVendorClient():VendorModelClient {
        return mBeeProManager!!.vendorClient
    }

    open fun changePeripheral(address: String) {
        ZLogger.v("changePeripheral to $address")
        clearBeeProManager()
        mDeviceAddress = address
        getPeripheralConnectionManager()

        CoroutineScope(Dispatchers.IO).launch {
            reloadData()
        }
    }

    open suspend fun reloadData() {
        if (StringUtils.isEmpty(mDeviceAddress)) {
            return
        }
        ZLogger.v("reloadData:$mDeviceAddress")
        reloadCacheDeviceInfo()
        deviceInfoEntity = deviceInfoRepository.getDevice(address = mDeviceAddress, false)
        RepositoryViewModel.instance!!.getDevice(mDeviceAddress).reload()
    }

    fun clearBeeProManager() {
        mBeeProManager?.unregisterVendorModelCallback(innerVendorModelCallback)
        mBeeProManager = null
    }

    fun unregisterVendorModelCallback(callback: VendorModelCallback) {
        mBeeProManager?.unregisterVendorModelCallback(callback)
    }

    fun registerVendorModelCallbackImpl(callback: VendorModelCallback) {
        mBeeProManager?.registerVendorModelCallback(callback)
    }
    fun unregisterVendorModelCallbackImpl(callback: VendorModelCallback) {
        mBeeProManager?.unregisterVendorModelCallback(callback)
    }
    fun getState(): Int {
        if (mBeeProManager == null) {
            return PeripheralConnectionManager.STATE_INIT
        }
        return mBeeProManager!!.state
    }

    fun getDeviceInfo(): DeviceInfo {
        return getPeripheralConnectionManager().deviceInfo
    }


    fun isDeviceAuthenticationVerified(): Boolean {
        return getVendorClient().isDeviceAuthenticationVerified
    }


    fun isChargingCaseFeatureSupported(): Boolean {
        val chargingCaseEntity = deviceInfoRepository.getChargingCaseEntity(mDeviceAddress)

        return chargingCaseEntity.supported
    }

    open suspend fun reloadCacheDeviceInfo() {
        withContext(Dispatchers.IO) {
            val deviceViewModel = RepositoryViewModel.instance!!.getDevice(mDeviceAddress)
            deviceViewModel.reloadCacheDeviceInfo(getPeripheralConnectionManager().deviceInfo)

            capabilityCacheInfo.update(deviceViewModel.getCapabilityEntity())
            capabilityCacheInfoLiveData.postValue(capabilityCacheInfo)
        }
    }

    /**
     * connect remote device
     * */
    fun connectDevice(): Int {
        val params = ConnectionParameters.Builder(mDeviceAddress)
            .channelType(deviceInfoEntity.transportChannel)
            .gattTransportConnParams(
                GattTransportConnParams.Builder(mDeviceAddress)
                    .connParams(
                        GattConnParams.Builder().address(mDeviceAddress)
                        .createBond(true)
                        .transport(GattConnParams.TRANSPORT_LE)
                        .ignoreEnableNotificationResponse(true).build())
//                .serviceUuid(UUID.fromString("000000DA-3C17-D293-8E48-14FE2E4DA212"))
//                .serviceUuid(UUID.fromString("000002fd-3c17-d293-8e48-14fe2e4da212"))
                    // 0000-0000-1000-8000-00805F9B34FB
//                .txUuid(BluetoothUuidCompat.fromShortValue(0xFD03))
//                .rxUuid(BluetoothUuidCompat.fromShortValue(0xFD04))
                    .build()
            )
            .sppTransportConnParams(
                SppTransportConnParams.Builder(mDeviceAddress)
                    .createBond(true, 1)
                    // optional, configure customized SPP UUID
//            .uuid(UUID.fromString("00001101-0000-1000-8000-00805F9B34FB"))
//            .freshUuid(false/*bluetoothDevice.bondState != BluetoothDevice.BOND_BONDED*/)
                    .build()
            )
            .peripheralParameters(
                PeripheralParameters.Builder()
                    .syncDataWhenConnected(true)
                    .connectA2dp(true)
                    .listenHfp(true).build()
            )
            .build()

        return getPeripheralConnectionManager().startConnect(params)
    }


    open fun disconnect(): BeeError {
        val ret =  getPeripheralConnectionManager().disconnect()
//        if (ret.code == BeeError.SUCCESS) {
//            MultiPeripheralConnectionManager.getInstance(getApplication()).removePeripheralConnectionManager(mDeviceAddress)
//        }
        return ret
    }

    /**
     * remove device
     * */
    fun removeDevice(device: BluetoothDevice, removeBond: Boolean = false) {
        ZLogger.v("pending to remove device")
        getPeripheralConnectionManager().disconnect()
        if (removeBond) {
            BluetoothDeviceCompat.removeBond(device)
        }
//        MultiPeripheralConnectionManager.getInstance(getApplication()).removePeripheralConnectionManager(mDeviceAddress)
        deviceInfoRepository.removeDevice(mDeviceAddress)
        mDeviceAddress = ""
    }

    /**
     * Checks whether the device is connected.
     * */
    fun isConnected(): Boolean {
        if (mBeeProManager == null) {
            ZLogger.v("mBeeProManager is null")
            return false
        }
        return mBeeProManager!!.isConnected
    }

    fun authenticateDevice(): BeeError {
        return getVendorClient().authentication(
            AuthenticationConfigure.Builder().build())
    }

    fun reAuthenticateDevice(): BeeError {
        return getVendorClient().authentication(
            AuthenticationConfigure.Builder().forceExchange(true).build()
        )
    }

    fun setDeviceInfoChanged(changed: Boolean) {
        deviceInfoChanged.postValue(changed)
    }

    fun updateMultiLinkInfo(multiLinkInfo: MultiLinkInfo) {
        if (!isConnected()) {
            RepositoryViewModel.instance!!.setMultiLinkConflict(mDeviceAddress, false)
            return
        }
        val deviceInfo = getPeripheralConnectionManager().deviceInfo
        if (!deviceInfo.isMultiLinkSupported) {
            RepositoryViewModel.instance!!.setMultiLinkConflict(mDeviceAddress, false)
            return
        }
        RepositoryViewModel.instance!!.setMultiLinkConflict(
            mDeviceAddress,
            multiLinkInfo.connNum > 1
        )
    }

    fun updateAuthenticationState(state:AuthenticationState) {
        if (state.deviceVerified) {
            deviceInfoRepository.updateAuthenticationKey(mDeviceAddress, state.authenticationKey)
        } else {
            deviceInfoRepository.updateAuthenticationKey(mDeviceAddress, ByteArray(0))
        }
        RepositoryViewModel.instance!!.getDevice(mDeviceAddress).deviceAuthenticationVerifiedLiveData.postValue(state.deviceVerified)
    }


    fun sendAppReq(req: AppReq):BeeError {
        return getPeripheralConnectionManager().sendAppReq(req)
    }

    /**
     * Checks whether In call audio mode. A telephony call is established.
     * for some phone device, SCO may be established when ringtone mode.
     * */
    fun isInCallMode(): Boolean {
        val callState = telephonyManager.callState
        val isInCall = telecomManager.isInCall
        val audioMode = mAudioManager.mode
        ZLogger.v("callState:$callState， isInCall:$isInCall, audioMode:$audioMode")
        if (callState == TelephonyManager.CALL_STATE_RINGING ||
            callState == TelephonyManager.CALL_STATE_OFFHOOK ||
            isInCall || audioMode == AudioManager.MODE_RINGTONE ||
            audioMode == AudioManager.MODE_IN_CALL
        ) {

            return true
        }
        return false
    }

    /**
     * Checks whether any music is active.
     * */
    fun isMusicActive(): Boolean {
        if (mAudioManager.isMusicActive) {
            return true
        }
        return false
    }

    fun registerManagerCallback(callback: BumblebeeCallback) {
        mBeeProManager?.addManagerCallback(callback)
    }

    fun unregisterManagerCallback(callback: BumblebeeCallback) {
        mBeeProManager?.removeManagerCallback(callback)
    }

    private var uiVendorModelCallback: VendorModelCallback? = null
    fun registerVendorModelCallback(callback: VendorModelCallback) {
        this.uiVendorModelCallback = callback
    }

    fun unregisterVendorModelCallback() {
        this.uiVendorModelCallback = null
    }

    private val innerVendorModelCallback = object : VendorModelCallback() {
        override fun onMultiLinkInfoChanged(multilinkInfo: MultiLinkInfo) {
            super.onMultiLinkInfoChanged(multilinkInfo)
            updateMultiLinkInfo(multilinkInfo)
        }

        override fun onDeviceInfoChanged(indicator: Int, deviceInfo: DeviceInfo) {
            super.onDeviceInfoChanged(indicator, deviceInfo)
            CoroutineScope(Dispatchers.IO).launch {
                reloadCacheDeviceInfo()
            }

            uiVendorModelCallback?.onDeviceInfoChanged(indicator, deviceInfo)
        }

        override fun onOperationComplete(operation: Int, status: Byte) {
            super.onOperationComplete(operation, status)
            uiVendorModelCallback?.onOperationComplete(operation, status)
        }

        override fun onStateChanged(state: Int) {
            super.onStateChanged(state)
            uiVendorModelCallback?.onStateChanged(state)
        }

        override fun onAuthenticationStateChanged(state: AuthenticationState) {
            super.onAuthenticationStateChanged(state)
            updateAuthenticationState(state)
        }
    }

    init {
        mContext = application
        mAudioManager = application.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        telephonyManager =
            application.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            telecomManager = application.getSystemService(Context.TELECOM_SERVICE) as TelecomManager
        }

        deviceInfoRepository = DeviceInfoRepository(application)

        deviceInfoEntity = DeviceInfoEntity()

        capabilityCacheInfo = CapabilityCacheInfo()
        capabilityCacheInfoLiveData.postValue(capabilityCacheInfo)
    }

    override fun onCleared() {
        super.onCleared()
        unregisterVendorModelCallback()
        mBeeProManager?.unregisterVendorModelCallback(innerVendorModelCallback)
        mBeeProManager = null
    }

    companion object
}
