package com.ggec.glasses.utils;

import androidx.annotation.AnimRes;
import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.ggec.glasses.R;

/**
 * 页面切换动画工具类
 * 用于管理Fragment之间的页面转场动画效果
 */
public class PageAnimUtils {

    // 默认动画资源ID
    private static final int DEFAULT_ENTER_ANIM = R.anim.slide_in_right;
    private static final int DEFAULT_EXIT_ANIM = R.anim.slide_out_left;
    private static final int DEFAULT_POP_ENTER_ANIM = R.anim.slide_in_left;
    private static final int DEFAULT_POP_EXIT_ANIM = R.anim.slide_out_right;

    /**
     * 为Fragment事务添加默认的前进动画效果（右侧滑入，左侧滑出）
     * @param transaction Fragment事务
     * @return 添加了动画效果的Fragment事务
     */
    public static FragmentTransaction addForwardAnimation(@NonNull FragmentTransaction transaction) {
        return transaction.setCustomAnimations(
                DEFAULT_ENTER_ANIM,
                DEFAULT_EXIT_ANIM,
                DEFAULT_POP_ENTER_ANIM,
                DEFAULT_POP_EXIT_ANIM
        );
    }

    /**
     * 为Fragment事务添加定制的动画效果
     * @param transaction Fragment事务
     * @param enterAnim 进入动画资源ID
     * @param exitAnim 退出动画资源ID
     * @param popEnterAnim 返回时进入动画资源ID
     * @param popExitAnim 返回时退出动画资源ID
     * @return 添加了动画效果的Fragment事务
     */
    public static FragmentTransaction addCustomAnimation(
            @NonNull FragmentTransaction transaction,
            @AnimRes int enterAnim,
            @AnimRes int exitAnim,
            @AnimRes int popEnterAnim,
            @AnimRes int popExitAnim
    ) {
        return transaction.setCustomAnimations(
                enterAnim,
                exitAnim,
                popEnterAnim,
                popExitAnim
        );
    }
    
    /**
     * 使用默认向前导航动画（右侧滑入）安全地执行Fragment事务
     * @param fragmentManager Fragment管理器
     * @param transaction Fragment事务
     */
    public static void commitTransactionWithForwardAnimation(
            @NonNull FragmentManager fragmentManager,
            @NonNull FragmentTransaction transaction
    ) {
        if (!fragmentManager.isStateSaved()) {
            addForwardAnimation(transaction).commit();
        }
    }
    
    /**
     * 使用自定义动画安全地执行Fragment事务
     * @param fragmentManager Fragment管理器
     * @param transaction Fragment事务
     * @param enterAnim 进入动画资源ID
     * @param exitAnim 退出动画资源ID
     * @param popEnterAnim 返回时进入动画资源ID
     * @param popExitAnim 返回时退出动画资源ID
     */
    public static void commitTransactionWithCustomAnimation(
            @NonNull FragmentManager fragmentManager,
            @NonNull FragmentTransaction transaction,
            @AnimRes int enterAnim,
            @AnimRes int exitAnim,
            @AnimRes int popEnterAnim,
            @AnimRes int popExitAnim
    ) {
        if (!fragmentManager.isStateSaved()) {
            addCustomAnimation(transaction, enterAnim, exitAnim, popEnterAnim, popExitAnim).commit();
        }
    }
} 