package com.ggec.glasses.asr.model;

import java.nio.ByteBuffer;

/**
 * 语音识别请求模型
 * 封装发送到语音识别服务的请求参数
 */
public class AsrRequest {
    
    private final ByteBuffer audioData;  // 音频数据
    private final boolean isEnd;         // 是否为最后一块数据
    
    /**
     * 创建语音识别请求
     * @param audioData 音频数据
     * @param isEnd 是否为最后一块数据
     */
    public AsrRequest(ByteBuffer audioData, boolean isEnd) {
        this.audioData = audioData;
        this.isEnd = isEnd;
    }
    
    /**
     * 创建包含音频数据的请求（非结束块）
     * @param audioData 音频数据
     * @return AsrRequest实例
     */
    public static AsrRequest data(ByteBuffer audioData) {
        return new AsrRequest(audioData, false);
    }
    
    /**
     * 创建最终的空数据块，表示音频结束
     * @return AsrRequest实例
     */
    public static AsrRequest end() {
        return new AsrRequest(ByteBuffer.allocate(0), true);
    }
    
    /**
     * 获取音频数据
     * @return 音频数据ByteBuffer
     */
    public ByteBuffer getAudioData() {
        return audioData;
    }
    
    /**
     * 检查是否为最后一块数据
     * @return 如果是最后一块数据返回true，否则返回false
     */
    public boolean isEnd() {
        return isEnd;
    }
} 