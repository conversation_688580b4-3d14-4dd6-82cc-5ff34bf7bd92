package com.ggec.glasses.ai.util;

import android.content.Context;
import android.util.Log;

import com.ggec.glasses.ai.data.db.AIChatMessageHistory;
import com.ggec.glasses.ai.data.db.entity.ChatMessage;
import com.ggec.glasses.ai.data.db.entity.MessageStatus;
import com.ggec.glasses.ai.data.db.repository.RepositoryCallback;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 示例消息提供者
 * 提供数据库层 (ChatMessage) 的示例消息生成功能
 */
public class SampleMessageProvider {
    private static final String TAG = "SampleMessageProvider";

    // 保留使用的常量
    private static final String USER_AI_INQUIRY = "我想了解一下人工智能的最新发展。";
    private static final String AI_INITIAL_RESPONSE = "你好，我是AI助手，能帮你做什么？";
    private static final String AI_AI_OVERVIEW =
            "人工智能近期有很多突破，包括大型语言模型(LLM)如GPT-4的发布，多模态模型如DALL-E的广泛应用，以及自动驾驶技术的进步。你对哪方面更感兴趣？";
    private static final String USER_LLM_INTEREST = "我对大型语言模型比较感兴趣，能详细介绍一下吗？";
    private static final String AI_LLM_EXPLANATION =
            "大型语言模型(LLM)是一类能够理解和生成人类语言的AI系统。最新的LLM如GPT-4拥有上千亿参数，通过海量文本数据训练，能够执行翻译、写作、编程等多种任务。它们的工作原理基于Transformer架构，使用自注意力机制来处理序列数据。不过，这些模型也面临幻觉(生成虚假信息)、偏见等挑战。";
    private static final String USER_APPLICATION_INQUIRY = "这些模型能应用在哪些领域？";
    private static final String AI_APPLICATION_DESCRIPTION =
            "大型语言模型在多个领域有广泛应用：\n1. 教育：个性化学习助手和内容生成\n2. 医疗：辅助诊断和医学文献分析\n3. 客户服务：智能客服和问答系统\n4. 内容创作：写作辅助和创意建议\n5. 编程：代码生成和debug辅助\n6. 法律：文档分析和案例研究\n\n这些技术正在改变许多行业的工作方式。";
    private static final String USER_THANKS = "谢谢解答，非常有帮助！";
    private static final String AI_CLOSING = "不客气！如果你还有其他问题，随时可以向我咨询。";

    /**
     * 获取数据库层示例消息列表
     * @param conversationId 会话ID
     * @return 数据库层示例消息列表
     */
    public static List<ChatMessage> getDBMessages(long conversationId) {
        List<ChatMessage> messages = new ArrayList<>();
        long currentTime = System.currentTimeMillis();

        // 创建示例消息对
        addMessagePair(messages,
                AI_INITIAL_RESPONSE,
                USER_AI_INQUIRY,
                currentTime - 60000 * 10, // 10分钟前
                conversationId);

        addMessagePair(messages,
                AI_AI_OVERVIEW,
                USER_LLM_INTEREST,
                currentTime - 60000 * 8, // 8分钟前
                conversationId);

        addMessagePair(messages,
                AI_LLM_EXPLANATION,
                USER_APPLICATION_INQUIRY,
                currentTime - 60000 * 6, // 6分钟前
                conversationId);

        addMessagePair(messages,
                AI_APPLICATION_DESCRIPTION,
                USER_THANKS,
                currentTime - 60000 * 4, // 4分钟前
                conversationId);

        // 添加最后一条AI消息
        messages.add(ChatMessage.createAIMessage(
                AI_CLOSING,
                conversationId,
                new Date(currentTime - 60000 * 2) // 2分钟前
        ));

        return messages;
    }

    /**
     * 添加一对AI和用户消息
     */
    private static void addMessagePair(List<ChatMessage> messages, String aiContent, String userContent,
                                      long timestamp, long conversationId) {
        // 添加AI消息
        messages.add(ChatMessage.createAIMessage(
                aiContent,
                conversationId,
                new Date(timestamp)
        ));

        // 添加用户回复消息
        messages.add(ChatMessage.createUserMessage(
                userContent,
                conversationId,
                new Date(timestamp + 30000) // 假设用户30秒后回复
        ));
    }

    /**
     * 通过回调提供数据库层示例消息列表
     * @param conversationId 会话ID
     * @param callback 回调，用于返回生成的 ChatMessage 列表或错误
     */
    public static void importWithCallback(long conversationId, RepositoryCallback<List<ChatMessage>> callback) {
        try {
            // 直接生成数据库层示例消息
            List<ChatMessage> sampleMessages = getDBMessages(conversationId);
            Log.d(TAG, "成功生成 " + sampleMessages.size() + " 条数据库层示例消息");
            callback.onSuccess(sampleMessages);
        } catch (Exception e) {
            Log.e(TAG, "生成示例消息时发生异常", e);
            callback.onError("创建示例消息异常: " + e.getMessage());
        }
    }
} 