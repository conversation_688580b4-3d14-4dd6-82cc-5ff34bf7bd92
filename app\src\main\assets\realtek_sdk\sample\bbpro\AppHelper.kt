/*
 * Copyright (c) 2017-2022. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.bbpro

import android.content.Context

/**
 * <AUTHOR>
 */
object AppHelper {
    fun wapperFindmeStatus(context: Context, leftbud: Boolean, rightbud: Boolean): String {
        val sb = StringBuilder()
        if (leftbud) {
            sb.append("left:${leftbud} -${context.getString(R.string.rtk_switchTextOn)}")
        } else {
            sb.append("left:${leftbud} -${context.getString(R.string.rtk_switchTextOff)}")
        }
        if (rightbud) {
            sb.append(", right:${rightbud} -${context.getString(R.string.rtk_switchTextOn)}")
        } else {
            sb.append(", right:${rightbud} -${context.getString(R.string.rtk_switchTextOff)}")
        }
        return sb.toString()
    }

}