package com.ggec.glasses.bluetooth.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.glasses.R;
import com.ggec.glasses.bluetooth.model.BluetoothDeviceModel;

import java.util.List;

/**
 * 蓝牙设备适配器
 * 用于展示蓝牙设备列表
 */
public class BluetoothDeviceAdapter extends RecyclerView.Adapter<BluetoothDeviceAdapter.DeviceViewHolder> {

    private final Context context;
    private final List<BluetoothDeviceModel> deviceList;
    private OnDeviceClickListener listener;
    private RecyclerView recyclerView;

    /**
     * 构造方法
     * @param context 上下文
     * @param deviceList 设备列表
     */
    public BluetoothDeviceAdapter(Context context, List<BluetoothDeviceModel> deviceList) {
        this.context = context;
        this.deviceList = deviceList;
    }

    @NonNull
    @Override
    public DeviceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_bluetooth_device, parent, false);
        return new DeviceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DeviceViewHolder holder, int position) {
        BluetoothDeviceModel device = deviceList.get(position);
        
        // 设置设备图标
        holder.ivDeviceIcon.setImageResource(device.getIconResId());
        
        // 设置设备名称
        holder.tvDeviceName.setText(device.getName());
        
        // 设置设备地址
        holder.tvDeviceAddress.setText(device.getAddress());
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onDeviceClick(device);
            }
        });
        
        // 处理分隔线显示逻辑
        if (position < deviceList.size() - 1) {
            holder.divider.setVisibility(View.VISIBLE);
        } else {
            holder.divider.setVisibility(View.GONE);
        }
    }

    @Override
    public int getItemCount() {
        return deviceList.size();
    }

    /**
     * 设置设备点击监听器
     * @param listener 监听器
     */
    public void setOnDeviceClickListener(OnDeviceClickListener listener) {
        this.listener = listener;
    }

    /**
     * 更新设备列表数据
     * @param newDeviceList 新的设备列表
     */
    public void updateDeviceList(List<BluetoothDeviceModel> newDeviceList) {
        // 保存原始数据大小
        int oldSize = deviceList.size();
        // 清空数据
        deviceList.clear();
        
        // 如果原来有数据，通知已移除
        if (oldSize > 0) {
            notifyItemRangeRemoved(0, oldSize);
        }
        
        // 添加新数据
        deviceList.addAll(newDeviceList);
        
        // 通知数据集完全改变，强制重新布局
        notifyDataSetChanged();
        
        // 请求重新测量布局
        if (recyclerView != null) {
            recyclerView.post(() -> recyclerView.requestLayout());
        }
    }

    // 在onAttachedToRecyclerView中保存RecyclerView引用
    @Override
    public void onAttachedToRecyclerView(@NonNull RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        this.recyclerView = recyclerView;
    }
    
    @Override
    public void onDetachedFromRecyclerView(@NonNull RecyclerView recyclerView) {
        super.onDetachedFromRecyclerView(recyclerView);
        this.recyclerView = null;
    }

    /**
     * 设备视图持有者
     */
    static class DeviceViewHolder extends RecyclerView.ViewHolder {
        ImageView ivDeviceIcon;
        TextView tvDeviceName;
        TextView tvDeviceAddress;
        View divider;

        DeviceViewHolder(@NonNull View itemView) {
            super(itemView);
            ivDeviceIcon = itemView.findViewById(R.id.iv_device_icon);
            tvDeviceName = itemView.findViewById(R.id.tv_device_name);
            tvDeviceAddress = itemView.findViewById(R.id.tv_device_address);
            divider = itemView.findViewById(R.id.divider);
        }
    }

    /**
     * 设备点击监听器接口
     */
    public interface OnDeviceClickListener {
        /**
         * 设备点击回调
         * @param device 被点击的设备
         */
        void onDeviceClick(BluetoothDeviceModel device);
    }
} 