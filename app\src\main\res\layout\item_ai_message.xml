<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:paddingTop="8dp"
    android:paddingEnd="60dp"
    android:paddingBottom="8dp">

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_ai_message"
        android:maxWidth="280dp"
        android:minWidth="48dp"
        android:paddingStart="16dp"
        android:paddingTop="12dp"
        android:paddingEnd="16dp"
        android:paddingBottom="12dp"
        android:textColor="@color/font_primary"
        android:textSize="16sp"
        android:textIsSelectable="true"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="这是AI助手的回复消息" />

</androidx.constraintlayout.widget.ConstraintLayout> 