<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="true">

    <!-- 下层背景层 - 用于图片展示 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/background_layer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/comp_background_gray">

        <!-- 媒体容器，用于加载子Fragment -->
        <FrameLayout
            android:id="@+id/media_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 使用PhotoView支持缩放 -->
        <com.github.chrisbanes.photoview.PhotoView
            android:id="@+id/img_display"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/image_content"
            android:scaleType="fitCenter"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 视频播放容器 -->
        <FrameLayout
            android:id="@+id/video_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:background="@android:color/transparent"
            android:hardwareAccelerated="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- 这里将动态加载视频播放器视图 -->

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 上层操作层 - 包含顶部导航栏和底部工具栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/operation_layer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 顶部组合容器（状态栏+导航栏） -->
        <LinearLayout
            android:id="@+id/top_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">

            <!-- 状态栏背景 -->
            <View
                android:id="@+id/status_bar_background"
                android:layout_width="match_parent"
                android:layout_height="36dp"
                android:background="@color/comp_background_gray" />

            <!-- 顶部导航栏 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/header_layout"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:background="@color/comp_background_gray">

                <!-- 返回按钮 -->
                <ImageView
                    android:id="@+id/btn_back"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/btn_ripple_small_corner"
                    android:clickable="true"
                    android:contentDescription="@string/btn_back"
                    android:focusable="true"
                    android:padding="12dp"
                    android:src="@drawable/ic_public_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/icon_primary" />

                <!-- 标题 -->
                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="56dp"
                    android:layout_marginEnd="56dp"
                    android:background="@drawable/btn_ripple_small_corner"
                    android:clickable="true"
                    android:ellipsize="middle"
                    android:focusable="true"
                    android:gravity="center"
                    android:maxLines="1"
                    android:padding="8dp"
                    android:text="@string/image_detail"
                    android:textColor="@color/font_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <!-- 底部工具栏 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/footer_layout"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@color/comp_background_gray"
            app:layout_constraintBottom_toBottomOf="parent">

            <!-- 下载按钮组 -->
            <LinearLayout
                android:id="@+id/btn_download_group"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:background="@drawable/btn_ripple_small_corner"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="3dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btn_share_group"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/btn_download"
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:contentDescription="@string/btn_download"
                    android:padding="5dp"
                    android:src="@drawable/ic_public_download"
                    app:tint="@color/icon_primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-2dp"
                    android:text="@string/btn_download"
                    android:textColor="@color/font_primary"
                    android:textSize="12sp" />
            </LinearLayout>

            <!-- 分享按钮组 -->
            <LinearLayout
                android:id="@+id/btn_share_group"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:background="@drawable/btn_ripple_small_corner"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="3dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btn_delete_group"
                app:layout_constraintStart_toEndOf="@+id/btn_download_group"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/btn_share"
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:contentDescription="@string/btn_share"
                    android:padding="5dp"
                    android:src="@drawable/ic_public_share"
                    app:tint="@color/icon_primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-2dp"
                    android:text="@string/btn_share"
                    android:textColor="@color/font_primary"
                    android:textSize="12sp" />
            </LinearLayout>

            <!-- 删除按钮组 -->
            <LinearLayout
                android:id="@+id/btn_delete_group"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:background="@drawable/btn_ripple_small_corner"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="3dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btn_more_group"
                app:layout_constraintStart_toEndOf="@+id/btn_share_group"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/btn_delete"
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:contentDescription="@string/btn_delete"
                    android:padding="5dp"
                    android:src="@drawable/ic_public_delete"
                    app:tint="@color/icon_primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-2dp"
                    android:text="@string/btn_delete"
                    android:textColor="@color/font_primary"
                    android:textSize="12sp" />
            </LinearLayout>

            <!-- 更多按钮组 -->
            <LinearLayout
                android:id="@+id/btn_more_group"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:background="@drawable/btn_ripple_small_corner"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="3dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/btn_delete_group"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/btn_more"
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:contentDescription="@string/btn_more"
                    android:padding="5dp"
                    android:src="@drawable/ic_public_more"
                    app:tint="@color/icon_primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="-2dp"
                    android:text="@string/btn_more"
                    android:textColor="@color/font_primary"
                    android:textSize="12sp" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout> 