package com.ggec.glasses.album.data.db;

import androidx.room.TypeConverter;

import java.util.Date;

/**
 * Room数据库的类型转换器
 * 用于转换Java中的特殊类型与数据库中支持的类型之间的映射
 */
public class Converters {
    
    /**
     * 将Date对象转换为Long类型，存储到数据库
     * @param date 需要转换的日期
     * @return 转换后的时间戳（毫秒）
     */
    @TypeConverter
    public static Long dateToTimestamp(Date date) {
        return date == null ? null : date.getTime();
    }
    
    /**
     * 将Long类型的时间戳转换为Date对象
     * @param timestamp 时间戳（毫秒）
     * @return 转换后的Date对象
     */
    @TypeConverter
    public static Date timestampToDate(Long timestamp) {
        return timestamp == null ? null : new Date(timestamp);
    }
} 