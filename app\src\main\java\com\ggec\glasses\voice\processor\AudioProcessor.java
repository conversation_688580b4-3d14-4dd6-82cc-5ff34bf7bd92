package com.ggec.glasses.voice.processor;

import android.util.Log;

import com.ggec.glasses.asr.model.AsrRequest;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * 音频处理器
 * 负责处理原始音频数据并转换为ASR服务所需的格式
 */
public class AudioProcessor {
    private static final String TAG = "AudioProcessor";
    
    // 默认缓冲区大小
    private static final int DEFAULT_BUFFER_SIZE = 3200; // 100ms@16kHz,16bit,mono
    
    // 音频格式参数
    private final int sampleRate;
    private final int channelCount;
    private final int bitsPerSample;
    
    /**
     * 构造函数
     * 
     * @param sampleRate 采样率
     * @param channelCount 通道数
     * @param bitsPerSample 每个样本的位数
     */
    public AudioProcessor(int sampleRate, int channelCount, int bitsPerSample) {
        this.sampleRate = sampleRate;
        this.channelCount = channelCount;
        this.bitsPerSample = bitsPerSample;
    }
    
    /**
     * 处理音频数据，转换为AsrRequest对象
     * 
     * @param audioData 原始音频数据
     * @param sizeInBytes 有效数据大小（字节）
     * @return 处理后的AsrRequest对象
     */
    public AsrRequest process(byte[] audioData, int sizeInBytes) {
        if (audioData == null || sizeInBytes <= 0) {
            Log.w(TAG, "无效的音频数据");
            return null;
        }
        
        try {
            // 创建足够大的ByteBuffer
            ByteBuffer buffer = ByteBuffer.allocate(sizeInBytes);
            
            // 设置字节序为小端序（PCM数据通常是小端序）
            buffer.order(ByteOrder.LITTLE_ENDIAN);
            
            // 复制音频数据
            buffer.put(audioData, 0, sizeInBytes);
            
            // 准备读取（将位置重置到开始）
            buffer.flip();
            
            // 返回非结束数据块
            return AsrRequest.data(buffer);
        } catch (Exception e) {
            Log.e(TAG, "处理音频数据时出错", e);
            return null;
        }
    }
    
    /**
     * 创建结束标记请求
     * 
     * @return 表示数据结束的AsrRequest对象
     */
    public AsrRequest createEndRequest() {
        return AsrRequest.end();
    }
    
    /**
     * 获取当前采样率
     * 
     * @return 采样率
     */
    public int getSampleRate() {
        return sampleRate;
    }
    
    /**
     * 获取通道数
     * 
     * @return 通道数
     */
    public int getChannelCount() {
        return channelCount;
    }
    
    /**
     * 获取每个样本的位数
     * 
     * @return 每个样本的位数
     */
    public int getBitsPerSample() {
        return bitsPerSample;
    }
} 