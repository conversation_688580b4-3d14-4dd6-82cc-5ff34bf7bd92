package com.ggec.glasses.ai.data.db.converters;

import androidx.room.TypeConverter;

import com.ggec.glasses.ai.data.db.entity.MessageStatus;

import java.util.Date;

/**
 * Room数据库的统一类型转换器
 * 集中处理所有类型转换，便于管理和维护
 */
public class RoomConverters {
    
    /**
     * 将Date对象转换为Long类型，存储到数据库
     * @param date 需要转换的日期
     * @return 转换后的时间戳（毫秒）
     */
    @TypeConverter
    public static Long dateToTimestamp(Date date) {
        return DateConverter.dateToTimestamp(date);
    }
    
    /**
     * 将Long类型的时间戳转换为Date对象
     * @param timestamp 时间戳（毫秒）
     * @return 转换后的Date对象
     */
    @TypeConverter
    public static Date timestampToDate(Long timestamp) {
        return DateConverter.timestampToDate(timestamp);
    }
    
    /**
     * 将MessageStatus枚举转换为Integer类型，存储到数据库
     * @param status 消息状态枚举
     * @return 对应的整型值
     */
    @TypeConverter
    public static Integer messageStatusToInt(MessageStatus status) {
        return MessageStatusConverter.messageStatusToInt(status);
    }
    
    /**
     * 将Integer类型转换为MessageStatus枚举
     * @param value 整型值
     * @return 对应的消息状态枚举
     */
    @TypeConverter
    public static MessageStatus intToMessageStatus(Integer value) {
        return MessageStatusConverter.intToMessageStatus(value);
    }
} 