package com.ggec.glasses.ai.data.db.converters;

import androidx.room.TypeConverter;

import com.ggec.glasses.ai.data.db.entity.MessageStatus;

/**
 * 消息状态转换器，用于在Room数据库中存储和检索MessageStatus枚举
 */
public class MessageStatusConverter {
    
    /**
     * 将MessageStatus枚举转换为Integer类型，存储到数据库
     * @param status 消息状态枚举
     * @return 对应的整型值，如果status为null则返回null
     */
    @TypeConverter
    public static Integer messageStatusToInt(MessageStatus status) {
        return status == null ? null : status.getValue();
    }
    
    /**
     * 将Integer类型转换为MessageStatus枚举
     * @param value 整型值
     * @return 对应的消息状态枚举，如果value为null则返回null
     */
    @TypeConverter
    public static MessageStatus intToMessageStatus(Integer value) {
        return value == null ? null : MessageStatus.fromInt(value);
    }
} 