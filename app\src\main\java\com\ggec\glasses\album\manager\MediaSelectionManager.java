package com.ggec.glasses.album.manager;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.fragments.MediaListFragment;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 媒体选择管理器
 * 用于在相册模块内的不同Fragment之间共享媒体选中状态
 */
public class MediaSelectionManager {
    
    // 单例模式
    private static MediaSelectionManager INSTANCE;
    
    // 选中的媒体ID集合
    private final Set<Long> selectedMediaIds = new HashSet<>();
    
    // 选中项数量LiveData
    private final MutableLiveData<Integer> selectedCountLiveData = new MutableLiveData<>(0);
    
    // 全选状态LiveData（按标签页类型）
    private final Map<String, MutableLiveData<Boolean>> allSelectedLiveDataMap = new HashMap<>();
    
    // 总全选状态LiveData
    private final MutableLiveData<Boolean> totalAllSelectedLiveData = new MutableLiveData<>(false);
    
    // 当前活动标签页类型
    private String currentTabType = MediaListFragment.TYPE_ALL;
    
    // 媒体列表缓存（按标签页类型）
    private final Map<String, List<Media>> mediaListMap = new HashMap<>();
    
    // 私有构造函数
    private MediaSelectionManager() {
        // 初始化每个标签页类型的全选状态LiveData
        allSelectedLiveDataMap.put(MediaListFragment.TYPE_ALL, new MutableLiveData<>(false));
        allSelectedLiveDataMap.put(MediaListFragment.TYPE_PHOTO, new MutableLiveData<>(false));
        allSelectedLiveDataMap.put(MediaListFragment.TYPE_VIDEO, new MutableLiveData<>(false));
    }
    
    /**
     * 获取MediaSelectionManager实例
     * @return MediaSelectionManager实例
     */
    public static synchronized MediaSelectionManager getInstance() {
        if (INSTANCE == null) {
            INSTANCE = new MediaSelectionManager();
        }
        return INSTANCE;
    }
    
    /**
     * 切换媒体选中状态
     * @param media 媒体对象
     * @return 是否选中
     */
    public boolean toggleMediaSelection(Media media) {
        if (media == null) {
            return false;
        }
        
        boolean isSelected;
        if (selectedMediaIds.contains(media.getId())) {
            selectedMediaIds.remove(media.getId());
            isSelected = false;
        } else {
            selectedMediaIds.add(media.getId());
            isSelected = true;
        }
        
        // 更新选中数量
        updateSelectedCount();
        
        // 更新所有标签页的全选状态
        updateAllSelectedStates();
        
        return isSelected;
    }
    
    /**
     * 检查媒体是否被选中
     * @param mediaId 媒体ID
     * @return 是否选中
     */
    public boolean isMediaSelected(long mediaId) {
        return selectedMediaIds.contains(mediaId);
    }
    
    /**
     * 清空所有选中状态
     */
    public void clearSelection() {
        selectedMediaIds.clear();
        updateSelectedCount();
        
        // 更新所有标签页的全选状态
        updateAllSelectedStates();
    }
    
    /**
     * 获取选中的媒体ID集合
     * @return 选中的媒体ID集合
     */
    public Set<Long> getSelectedMediaIds() {
        return new HashSet<>(selectedMediaIds);
    }
    
    /**
     * 获取选中项数量LiveData
     * @return 选中项数量LiveData
     */
    public LiveData<Integer> getSelectedCountLiveData() {
        return selectedCountLiveData;
    }
    
    /**
     * 获取特定标签页的全选状态LiveData
     * @param tabType 标签页类型
     * @return 对应标签页的全选状态LiveData
     */
    public LiveData<Boolean> getAllSelectedLiveData(String tabType) {
        MutableLiveData<Boolean> liveData = allSelectedLiveDataMap.get(tabType);
        return liveData != null ? liveData : new MutableLiveData<>(false);
    }
    
    /**
     * 获取总全选状态LiveData
     * @return 总全选状态LiveData
     */
    public LiveData<Boolean> getTotalAllSelectedLiveData() {
        return totalAllSelectedLiveData;
    }
    
    /**
     * 更新选中数量
     */
    private void updateSelectedCount() {
        selectedCountLiveData.setValue(selectedMediaIds.size());
    }
    
    /**
     * 设置当前活动标签页类型
     * @param tabType 标签页类型
     */
    public void setCurrentTabType(String tabType) {
        this.currentTabType = tabType;
    }
    
    /**
     * 获取当前活动标签页类型
     * @return 当前活动标签页类型
     */
    public String getCurrentTabType() {
        return currentTabType;
    }
    
    /**
     * 更新所有标签页的全选状态
     */
    private void updateAllSelectedStates() {
        // 更新每个标签页的全选状态
        for (Map.Entry<String, List<Media>> entry : mediaListMap.entrySet()) {
            String tabType = entry.getKey();
            List<Media> mediaList = entry.getValue();
            updateTabAllSelectedState(tabType, mediaList);
        }
        
        // 更新总全选状态
        updateTotalAllSelectedState();
    }
    
    /**
     * 更新特定标签页的全选状态
     * @param tabType 标签页类型
     * @param mediaList 媒体列表
     */
    private void updateTabAllSelectedState(String tabType, List<Media> mediaList) {
        MutableLiveData<Boolean> liveData = allSelectedLiveDataMap.get(tabType);
        if (liveData == null || mediaList == null || mediaList.isEmpty()) {
            if (liveData != null) {
                liveData.setValue(false);
            }
            return;
        }
        
        // 计算该标签页的所有媒体是否都被选中
        boolean allSelected = true;
        for (Media media : mediaList) {
            if (!selectedMediaIds.contains(media.getId())) {
                allSelected = false;
                break;
            }
        }
        
        liveData.setValue(allSelected);
    }
    
    /**
     * 更新总全选状态
     */
    private void updateTotalAllSelectedState() {
        // 获取所有标签页的媒体总数
        int totalMediaCount = 0;
        for (List<Media> mediaList : mediaListMap.values()) {
            totalMediaCount += mediaList.size();
        }
        
        if (totalMediaCount == 0) {
            totalAllSelectedLiveData.setValue(false);
            return;
        }
        
        // 计算所有媒体是否都被选中
        boolean allSelected = totalMediaCount > 0 && selectedMediaIds.size() == totalMediaCount;
        totalAllSelectedLiveData.setValue(allSelected);
    }
    
    /**
     * 设置特定标签页的媒体列表
     * @param tabType 标签页类型
     * @param mediaList 媒体列表
     */
    public void setTabMediaList(String tabType, List<Media> mediaList) {
        mediaListMap.put(tabType, mediaList);
        updateTabAllSelectedState(tabType, mediaList);
        updateTotalAllSelectedState();
    }
    
    /**
     * 切换当前标签页的全选/取消全选状态
     * @return 是否全选
     */
    public boolean toggleCurrentTabSelectAll() {
        List<Media> mediaList = mediaListMap.get(currentTabType);
        if (mediaList == null || mediaList.isEmpty()) {
            return false;
        }
        
        // 获取当前标签页的全选状态
        MutableLiveData<Boolean> allSelectedLiveData = allSelectedLiveDataMap.get(currentTabType);
        boolean isAllSelected = allSelectedLiveData != null && Boolean.TRUE.equals(allSelectedLiveData.getValue());
        
        if (isAllSelected) {
            // 如果当前是全选状态，则取消选中当前标签页的所有媒体
            for (Media media : mediaList) {
                selectedMediaIds.remove(media.getId());
            }
        } else {
            // 如果当前不是全选状态，则选中当前标签页的所有媒体
            for (Media media : mediaList) {
                selectedMediaIds.add(media.getId());
            }
        }
        
        // 更新选中数量
        updateSelectedCount();
        
        // 更新所有标签页的全选状态
        updateAllSelectedStates();
        
        return !isAllSelected; // 返回切换后的状态
    }
} 