package com.ggec.glasses.ai.strategy;

import android.app.Application;
import android.content.Context;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.ggec.glasses.aimodelchat.manager.AIResponseHandler;
import com.ggec.glasses.ai.data.ChatUIMessage;
import com.ggec.glasses.asr.model.AsrRequest;
import com.ggec.glasses.asr.service.AsrService;
import com.ggec.glasses.asr.service.AsrServiceFactory;
import com.ggec.glasses.tts.manager.TtsCoordinator;
import com.ggec.glasses.voice.processor.AudioProcessor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * AI模型策略抽象基类
 * 提供通用功能实现和状态管理
 */
public abstract class AbstractAIModelStrategy implements AIModelStrategy {

    private static final String TAG = "AbstractAIModelStrategy";
    
    // 标准音频格式参数
    protected static final int AUDIO_SAMPLE_RATE = 16000;
    protected static final int AUDIO_CHANNELS = 1;
    protected static final int AUDIO_BITS_PER_SAMPLE = 16;

    // 服务组件
    protected Context context;
    protected Application application;
    protected AsrService asrService;
    protected AIResponseHandler aiResponseHandler;
    protected TtsCoordinator ttsCoordinator;
    protected AudioProcessor audioProcessor;
    protected ExecutorService executorService;
    
    // 状态标志
    protected boolean initialized = false;
    protected boolean audioSessionActive = false;
    
    /**
     * 初始化策略
     * @param context 应用上下文
     * @return 初始化是否成功
     */
    @Override
    public boolean initialize(Context context) {
        if (initialized) {
            Log.w(TAG, getModelType() + " 策略已经初始化");
            return true;
        }
        
        this.context = context.getApplicationContext();
        
        // 检查上下文是否为Application类型
        if (this.context instanceof Application) {
            this.application = (Application) this.context;
        } else {
            Log.e(TAG, "上下文不是Application类型，无法初始化TTS服务");
            return false;
        }
        
        this.executorService = Executors.newCachedThreadPool();
        
        // 初始化音频处理器
        this.audioProcessor = new AudioProcessor(
                AUDIO_SAMPLE_RATE,
                AUDIO_CHANNELS, 
                AUDIO_BITS_PER_SAMPLE
        );
        
        // 初始化TTS协调器 - 现在使用Application实例
        this.ttsCoordinator = new TtsCoordinator(this.application);
        
        // 创建ASR服务实例 - 使用正确的getService方法
        this.asrService = AsrServiceFactory.getService(this.context);
        boolean asrInitialized = this.asrService.initialize();
        
        if (!asrInitialized) {
            Log.e(TAG, "ASR服务初始化失败");
            release();
            return false;
        }
        
        // 初始化模型特定组件
        boolean modelSpecificInit = initializeModelSpecificComponents();
        
        if (!modelSpecificInit) {
            Log.e(TAG, getModelType() + " 模型特定组件初始化失败");
            release();
            return false;
        }
        
        this.initialized = true;
        Log.d(TAG, getModelType() + " 策略初始化成功");
        return true;
    }
    
    /**
     * 初始化特定AI模型相关的组件
     * 由子类实现
     * @return 初始化是否成功
     */
    protected abstract boolean initializeModelSpecificComponents();
    
    /**
     * 检查策略是否已初始化并可用
     * @return 可用状态
     */
    @Override
    public boolean isAvailable() {
        return initialized && asrService != null && asrService.isInitialized();
    }
    
    /**
     * 处理语音输入并生成回复
     * @param audioData 音频数据字节数组
     * @param sizeInBytes 有效数据大小
     * @param conversationId 会话ID
     * @return 操作是否成功开始
     */
    @Override
    public boolean processAudioInput(byte[] audioData, int sizeInBytes, long conversationId) {
        if (!isAvailable()) {
            Log.e(TAG, getModelType() + " 策略不可用或未初始化");
            return false;
        }
        
        // 如果未开始音频会话，则启动
        if (!audioSessionActive) {
            beginAudioSession(conversationId);
        }
        
        // 处理音频数据
        AsrRequest asrRequest = audioProcessor.process(audioData, sizeInBytes);
        if (asrRequest != null) {
            return asrService.processAudioData(asrRequest);
        }
        
        return false;
    }
    
    /**
     * 开始音频处理会话
     * @param conversationId 会话ID
     */
    protected abstract void beginAudioSession(long conversationId);
    
    /**
     * 结束当前音频处理会话
     * @return 操作是否成功
     */
    @Override
    public boolean endAudioProcessing() {
        if (!audioSessionActive) {
            return true; // 已经结束或未开始
        }
        
        // 发送结束标记
        AsrRequest endRequest = audioProcessor.createEndRequest();
        boolean success = asrService.processAudioData(endRequest);
        
        // 标记会话结束
        audioSessionActive = false;
        
        return success;
    }
    
    /**
     * 释放资源
     */
    @Override
    public void release() {
        Log.d(TAG, "释放 " + getModelType() + " 策略资源");
        
        // 结束任何活动的音频会话
        if (audioSessionActive) {
            endAudioProcessing();
        }
        
        // 释放ASR服务
        if (asrService != null) {
            asrService.release();
            asrService = null;
        }
        
        // 释放TTS协调器
        if (ttsCoordinator != null) {
            ttsCoordinator.release();
            ttsCoordinator = null;
        }
        
        // 关闭线程池
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            executorService = null;
        }
        
        // 释放模型特定资源
        releaseModelSpecificResources();
        
        audioProcessor = null;
        initialized = false;
        context = null;
        application = null;
    }
    
    /**
     * 释放模型特定资源
     * 由子类实现
     */
    protected abstract void releaseModelSpecificResources();
} 