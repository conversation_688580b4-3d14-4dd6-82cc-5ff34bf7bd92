package com.ggec.glasses.ai.data.cache.impl;

import com.ggec.glasses.ai.data.cache.api.CacheCallback;
import com.ggec.glasses.ai.data.cache.api.CacheEntry;
import com.ggec.glasses.ai.data.cache.api.CachePolicy;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * LRU(最近最少使用)缓存实现
 * 当缓存达到容量上限时，自动移除最久未使用的条目
 *
 * @param <K> 缓存键类型
 * @param <V> 缓存值类型
 */
public class LruCache<K, V> extends AbstractCache<K, V> {
    
    /** 用于跟踪访问顺序的LinkedHashMap */
    private final LinkedHashMap<K, Long> accessOrderMap;
    
    /**
     * 创建LRU缓存
     *
     * @param maxSize 最大容量
     */
    public LruCache(int maxSize) {
        this(new CachePolicy.LruPolicy(maxSize));
    }
    
    /**
     * 创建LRU缓存
     *
     * @param policy 缓存策略
     */
    public LruCache(CachePolicy policy) {
        super(policy);
        this.accessOrderMap = new LinkedHashMap<>(16, 0.75f, true);
    }
    
    @Override
    public boolean put(K key, V value, long expireTime) {
        boolean result = super.put(key, value, expireTime);
        
        if (result) {
            // 更新访问顺序
            lock.writeLock().lock();
            try {
                accessOrderMap.put(key, System.currentTimeMillis());
            } finally {
                lock.writeLock().unlock();
            }
        }
        
        return result;
    }
    
    @Override
    public V get(K key) {
        V value = super.get(key);
        
        if (value != null) {
            // 更新访问顺序
            lock.writeLock().lock();
            try {
                accessOrderMap.put(key, System.currentTimeMillis());
            } finally {
                lock.writeLock().unlock();
            }
        }
        
        return value;
    }
    
    @Override
    public V remove(K key) {
        V value = super.remove(key);
        
        if (value != null) {
            // 从访问顺序中移除
            lock.writeLock().lock();
            try {
                accessOrderMap.remove(key);
            } finally {
                lock.writeLock().unlock();
            }
        }
        
        return value;
    }
    
    @Override
    public void clear() {
        super.clear();
        
        // 清空访问顺序
        lock.writeLock().lock();
        try {
            accessOrderMap.clear();
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    @Override
    protected boolean removeEldestEntry() {
        if (!cachePolicy.isFull(cacheMap.size())) {
            return false;
        }
        
        lock.writeLock().lock();
        try {
            // 找到最早访问的键
            K eldestKey = null;
            long eldestTime = Long.MAX_VALUE;
            
            for (Map.Entry<K, Long> entry : accessOrderMap.entrySet()) {
                if (entry.getValue() < eldestTime) {
                    eldestKey = entry.getKey();
                    eldestTime = entry.getValue();
                }
            }
            
            if (eldestKey != null) {
                CacheEntry<V> removed = cacheMap.remove(eldestKey);
                accessOrderMap.remove(eldestKey);
                
                if (removed != null) {
                    V value = removed.getValue();
                    notifyEntryRemoved(eldestKey, value, CacheCallback.RemovalReason.EVICTED);
                    return true;
                }
            }
            
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 移除最久未使用的N个条目
     *
     * @param count 要移除的条目数量
     * @return 实际移除的条目数量
     */
    public int trimToSize(int count) {
        if (count <= 0) {
            clear();
            return cacheMap.size();
        }
        
        int removed = 0;
        lock.writeLock().lock();
        try {
            int targetSize = cacheMap.size() - count;
            if (targetSize <= 0) {
                return 0;
            }
            
            // 按访问时间排序
            Iterator<Map.Entry<K, Long>> iterator = accessOrderMap.entrySet().iterator();
            while (iterator.hasNext() && cacheMap.size() > targetSize) {
                Map.Entry<K, Long> entry = iterator.next();
                K key = entry.getKey();
                
                CacheEntry<V> cacheEntry = cacheMap.remove(key);
                if (cacheEntry != null) {
                    V value = cacheEntry.getValue();
                    notifyEntryRemoved(key, value, CacheCallback.RemovalReason.EVICTED);
                    removed++;
                }
                
                iterator.remove();
            }
        } finally {
            lock.writeLock().unlock();
        }
        
        return removed;
    }
} 