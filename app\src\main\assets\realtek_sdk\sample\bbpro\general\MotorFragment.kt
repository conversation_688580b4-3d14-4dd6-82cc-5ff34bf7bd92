/*
 * Copyright (c) 2017-2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.bbpro.general

import android.os.Bundle
import android.view.View
import androidx.fragment.app.FragmentTransaction
import com.realsil.bbpro.R
import com.realsil.bbpro.databinding.FragmentMotorBinding
import com.realsil.sdk.audioconnect.support.AudioConnectFragment
import com.realsil.sdk.audioconnect.support.AudioConnectHelper
import com.realsil.sdk.audioconnect.support.ui.SetVibratorModeFragment
import com.realsil.sdk.bbpro.BumblebeeCallback
import com.realsil.sdk.bbpro.MultiPeripheralConnectionManager
import com.realsil.sdk.bbpro.PeripheralConnectionManager
import com.realsil.sdk.bbpro.core.BeeError
import com.realsil.sdk.bbpro.core.peripheral.Peripheral
import com.realsil.sdk.bbpro.vendor.VendorConstants
import com.realsil.sdk.bbpro.vendor.VendorModelCallback
import com.realsil.sdk.bbpro.vendor.VendorModelClient
import com.realsil.sdk.core.bluetooth.connection.legacy.BluetoothSpp
import com.realsil.sdk.support.ui.ChoiceEntity
import com.realsil.sdk.support.ui.SingleChoiceDialogFragment

/**
 * <AUTHOR>
 * @date 2018/3/13
 */
class MotorFragment : AudioConnectFragment<FragmentMotorBinding>(FragmentMotorBinding::inflate) {
    private var mBeeProManager: PeripheralConnectionManager? = null
    private var mVendorModelClient: VendorModelClient? = null

    private val beeProManager: PeripheralConnectionManager
        get() {
            if (mBeeProManager == null) {
                mBeeProManager = MultiPeripheralConnectionManager.getInstance(context).getPeripheralConnectionManager(mDeviceAddress)
            }
            return mBeeProManager as PeripheralConnectionManager
        }

    private val vendorModelClient: VendorModelClient
        get() {
            if (mVendorModelClient == null) {
                mVendorModelClient = VendorModelClient.getInstance()
            }
            return mVendorModelClient as VendorModelClient
        }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.motorVibratorView.setOnClickListener {
            loadVibrator()
        }
        binding.motorVibratorView.setOnLongClickListener(View.OnLongClickListener {
            toggleVibrator()
            return@OnLongClickListener true
        })
        binding.motorVibratorModeView.setOnClickListener {
            loadVibratorModeParameters()
        }
        binding.motorVibratorModeView.setOnLongClickListener(View.OnLongClickListener {
            setVibratorMode()
            return@OnLongClickListener true
        })


        beeProManager
        mBeeProManager!!.addManagerCallback(mBeeProManagerCallback)

        vendorModelClient
        vendorModelClient.registerCallback(mVendorModelCallback)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (mBeeProManager != null) {
            mBeeProManager!!.removeManagerCallback(mBeeProManagerCallback)
        }

        if (mVendorModelClient != null) {
            mVendorModelClient!!.unregisterCallback(mVendorModelCallback)
        }
    }

    private fun loadVibrator() {
        showProgressBar(R.string.toast_processing)
        val ret: BeeError = vendorModelClient.vibratorStatus

        if (ret.code != BeeError.SUCCESS) {
            cancelProgressBar()
            showShortToast(ret.message)
        }
    }

    private fun toggleVibrator() {
        val mListener =
            SingleChoiceDialogFragment.OnDialogListener { item ->
                val entity = item as ChoiceEntity
                showProgressBar(R.string.toast_processing)
                val ret: BeeError
                if (entity.value == 0x00.toByte()) {
                    ret = vendorModelClient.setVibratorDisable()
                } else {
                    ret = vendorModelClient.setVibratorEnable()
                }
                if (ret.code != BeeError.SUCCESS) {
                    cancelProgressBar()
                    showShortToast(ret.message)
                }
            }
        val fragment = SingleChoiceDialogFragment.getInstance(
            null,
            "Vibrator", AudioConnectHelper.getToggleStatus(), mListener
        )
        val ft = childFragmentManager.beginTransaction()
        ft.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE)
        fragment.show(ft, SingleChoiceDialogFragment.TAG)
    }

    private fun loadVibratorModeParameters() {
        val ret = vendorModelClient.motorModeParameters
        if (ret.code != BeeError.SUCCESS) {
            cancelProgressBar()
            showShortToast(ret.message)
        }
    }

    private fun setVibratorMode() {
        val args = Bundle()
        val fragment = SetVibratorModeFragment.getInstance(
            args, object : SetVibratorModeFragment.OnDialogListener{
                override fun onSubmit(onPeriodTime: Int, offPeriodTime: Int, vibratorCount: Int) {
                    showProgressBar(R.string.toast_processing)
                    val ret = vendorModelClient.setVibratorModeParameters(
                        onPeriodTime,
                        offPeriodTime, vibratorCount
                    )
                    if (ret.code != BeeError.SUCCESS) {
                        cancelProgressBar()
                        showShortToast(ret.message)
                    }
                }
            }
        )
        val ft = childFragmentManager.beginTransaction()
        ft.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE)
        fragment.show(ft, SetVibratorModeFragment.TAG)
    }


    fun refresh() {
        activity?.runOnUiThread {
            cancelProgressBar()
        }
    }

    private val mBeeProManagerCallback = object : BumblebeeCallback() {
        override fun onConnectionStateChanged(peripheral: Peripheral?, state: Int) {
//            super.onConnectionStateChanged(peripheral, state)
            if (state == BluetoothSpp.STATE_DISCONNECTED) {
                cancelProgressBar()
            }
        }

        override fun onStateChanged(state: Int) {
            super.onStateChanged(state)

            if (state == PeripheralConnectionManager.STATE_DATA_PREPARED) {
                refresh()
            }
        }
    }

    private val mVendorModelCallback = object : VendorModelCallback() {
        override fun onOperationComplete(operation: Int, status: Byte) {
            super.onOperationComplete(operation, status)
            when (operation) {
                VendorConstants.Operation.GET_VIBRATOR_STATUS,
                VendorConstants.Operation.SET_VIBRATOR_MODE_PARAMETERS,
                VendorConstants.Operation.GET_VIBRATOR_MODE_PARAMETERS -> {
                    activity?.runOnUiThread {
                        cancelProgressBar()
                    }
                }
            }
        }

    }

    companion object {

        const val TAG = "MotorFragment"
        private const val D = true

        fun newInstance(args: Bundle?): MotorFragment {

            val fragment = MotorFragment()
            if (args != null) {
                fragment.arguments = args
            }
            return fragment
        }
    }


}
