package com.ggec.glasses.album.util;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.BitmapFactory;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.util.Log;

import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.data.manager.MediaFileManager;
import com.ggec.glasses.album.data.repository.MediaRepository;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 测试数据加载器，用于从assets中导入测试媒体文件
 */
public class TestDataLoader {
    
    private static final String TAG = "TestDataLoader";
    
    private static final String TEST_MEDIA_DIR = "test_media";
    private static final String PHOTO_DIR = "photo";
    private static final String VIDEO_DIR = "video";
    private static final String TEMP_IMPORT_DIR = "import_temp";
    
    private final Context context;
    private final MediaRepository mediaRepository;
    private final MediaFileManager mediaFileManager;
    
    /**
     * 构造函数
     * @param context 应用上下文
     * @param mediaRepository 媒体仓库
     * @param mediaFileManager 媒体文件管理器
     */
    public TestDataLoader(Context context, MediaRepository mediaRepository, MediaFileManager mediaFileManager) {
        this.context = context;
        this.mediaRepository = mediaRepository;
        this.mediaFileManager = mediaFileManager;
    }
    
    /**
     * 加载所有测试数据
     * @param callback 回调接口，通知加载结果
     */
    public void loadAllTestData(OnTestDataLoadedCallback callback) {
        new Thread(() -> {
            try {
                AssetManager assetManager = context.getAssets();
                
                // 确保临时目录存在
                File tempDir = new File(context.getExternalCacheDir(), TEMP_IMPORT_DIR);
                if (!tempDir.exists()) {
                    tempDir.mkdirs();
                }
                
                // 统计导入的文件数
                AtomicInteger successCount = new AtomicInteger(0);
                AtomicInteger errorCount = new AtomicInteger(0);
                
                // 导入测试图片
                String[] photoFiles = null;
                try {
                    photoFiles = assetManager.list(TEST_MEDIA_DIR + "/" + PHOTO_DIR);
                    Log.d(TAG, "Found " + (photoFiles != null ? photoFiles.length : 0) + " photo files");
                } catch (IOException e) {
                    Log.e(TAG, "Error listing photo files", e);
                    photoFiles = new String[0];
                }
                
                if (photoFiles != null && photoFiles.length > 0) {
                    CountDownLatch photoLatch = new CountDownLatch(photoFiles.length);
                    
                    for (String fileName : photoFiles) {
                        try {
                            importPhotoFromAssets(assetManager, TEST_MEDIA_DIR + "/" + PHOTO_DIR + "/" + fileName, 
                                new ImportCallback() {
                                    @Override
                                    public void onSuccess() {
                                        successCount.incrementAndGet();
                                        photoLatch.countDown();
                                    }
                                    
                                    @Override
                                    public void onError(String error) {
                                        Log.e(TAG, "Error importing photo: " + error);
                                        errorCount.incrementAndGet();
                                        photoLatch.countDown();
                                    }
                                });
                        } catch (Exception e) {
                            Log.e(TAG, "Exception importing photo: " + fileName, e);
                            errorCount.incrementAndGet();
                            photoLatch.countDown();
                        }
                    }
                    
                    // 等待所有图片导入完成
                    try {
                        photoLatch.await(60, java.util.concurrent.TimeUnit.SECONDS);
                    } catch (InterruptedException e) {
                        Log.e(TAG, "Photo import interrupted", e);
                    }
                }
                
                // 导入测试视频
                String[] videoFiles = null;
                try {
                    videoFiles = assetManager.list(TEST_MEDIA_DIR + "/" + VIDEO_DIR);
                    Log.d(TAG, "Found " + (videoFiles != null ? videoFiles.length : 0) + " video files");
                } catch (IOException e) {
                    Log.e(TAG, "Error listing video files", e);
                    videoFiles = new String[0];
                }
                
                if (videoFiles != null && videoFiles.length > 0) {
                    CountDownLatch videoLatch = new CountDownLatch(videoFiles.length);
                    
                    for (String fileName : videoFiles) {
                        try {
                            importVideoFromAssets(assetManager, TEST_MEDIA_DIR + "/" + VIDEO_DIR + "/" + fileName, 
                                new ImportCallback() {
                                    @Override
                                    public void onSuccess() {
                                        successCount.incrementAndGet();
                                        videoLatch.countDown();
                                    }
                                    
                                    @Override
                                    public void onError(String error) {
                                        Log.e(TAG, "Error importing video: " + error);
                                        errorCount.incrementAndGet();
                                        videoLatch.countDown();
                                    }
                                });
                        } catch (Exception e) {
                            Log.e(TAG, "Exception importing video: " + fileName, e);
                            errorCount.incrementAndGet();
                            videoLatch.countDown();
                        }
                    }
                    
                    // 等待所有视频导入完成
                    try {
                        videoLatch.await(120, java.util.concurrent.TimeUnit.SECONDS);
                    } catch (InterruptedException e) {
                        Log.e(TAG, "Video import interrupted", e);
                    }
                }
                
                // 清理临时目录
                cleanTempDirectory(tempDir);
                
                // 记录导入结果
                int totalFiles = (photoFiles != null ? photoFiles.length : 0) + (videoFiles != null ? videoFiles.length : 0);
                Log.d(TAG, "Import completed: " + successCount.get() + " succeeded, " + 
                      errorCount.get() + " failed, out of " + totalFiles + " total files");
                
                // 通知加载结果
                if (callback != null) {
                    if (successCount.get() > 0) {
                        callback.onSuccess();
                    } else {
                        callback.onError("没有成功导入任何文件");
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error loading test data", e);
                if (callback != null) {
                    callback.onError("导入测试数据时发生错误: " + e.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * 从assets导入图片
     * @param assetManager 资源管理器
     * @param assetPath 资源路径
     * @param callback 导入回调
     * @throws IOException IO异常
     */
    private void importPhotoFromAssets(AssetManager assetManager, String assetPath, ImportCallback callback) throws IOException {
        String fileName = assetPath.substring(assetPath.lastIndexOf("/") + 1);
        Log.d(TAG, "Importing photo: " + fileName);
        
        // 创建临时文件，使用外部缓存目录
        File tempDir = new File(context.getExternalCacheDir(), TEMP_IMPORT_DIR);
        File tempFile = new File(tempDir, "temp_" + System.currentTimeMillis() + "_" + fileName);
        
        try {
            // 将assets中的文件复制到临时文件
            copyAssetToFile(assetManager, assetPath, tempFile);
            
            // 验证文件是否成功复制
            if (!tempFile.exists() || tempFile.length() == 0) {
                String error = "Failed to copy asset to temp file";
                Log.e(TAG, error);
                if (callback != null) callback.onError(error);
                return;
            }
            
            // 获取图片尺寸
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(tempFile.getAbsolutePath(), options);
            int width = options.outWidth;
            int height = options.outHeight;
            
            // 检查图片尺寸是否有效
            if (width <= 0 || height <= 0) {
                Log.w(TAG, "Invalid image dimensions: " + width + "x" + height + " for " + fileName);
                // 设置默认尺寸以避免错误
                width = width <= 0 ? 1024 : width;
                height = height <= 0 ? 768 : height;
            }
            
            // 通过仓库保存媒体文件
            Uri fileUri = Uri.fromFile(tempFile);
            mediaRepository.saveMedia(fileUri, MediaFileManager.TYPE_IMAGE, width, height, 0, "image/jpeg", new MediaRepository.OnMediaSavedCallback() {
                @Override
                public void onSuccess(Media media) {
                    Log.d(TAG, "Successfully imported photo: " + fileName);
                    // 删除临时文件
                    tempFile.delete();
                    if (callback != null) callback.onSuccess();
                }
                
                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "Error importing photo: " + fileName + ", error: " + errorMessage);
                    // 删除临时文件
                    tempFile.delete();
                    if (callback != null) callback.onError(errorMessage);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception in importPhotoFromAssets: " + fileName, e);
            if (tempFile.exists()) {
                tempFile.delete();
            }
            if (callback != null) callback.onError(e.getMessage());
        }
    }
    
    /**
     * 从assets导入视频
     * @param assetManager 资源管理器
     * @param assetPath 资源路径
     * @param callback 导入回调
     * @throws IOException IO异常
     */
    private void importVideoFromAssets(AssetManager assetManager, String assetPath, ImportCallback callback) throws IOException {
        String fileName = assetPath.substring(assetPath.lastIndexOf("/") + 1);
        Log.d(TAG, "Importing video: " + fileName);
        
        // 创建临时文件，使用外部缓存目录
        File tempDir = new File(context.getExternalCacheDir(), TEMP_IMPORT_DIR);
        File tempFile = new File(tempDir, "temp_" + System.currentTimeMillis() + "_" + fileName);
        
        try {
            // 将assets中的文件复制到临时文件
            copyAssetToFile(assetManager, assetPath, tempFile);
            
            // 验证文件是否成功复制
            if (!tempFile.exists() || tempFile.length() == 0) {
                String error = "Failed to copy asset to temp file";
                Log.e(TAG, error);
                if (callback != null) callback.onError(error);
                return;
            }
            
            // 获取视频信息（宽度、高度、时长）
            int width = 0;
            int height = 0;
            long duration = 0;
            
            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
            try {
                retriever.setDataSource(tempFile.getAbsolutePath());
                
                String widthStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
                String heightStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
                String durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
                
                if (widthStr != null) width = Integer.parseInt(widthStr);
                if (heightStr != null) height = Integer.parseInt(heightStr);
                if (durationStr != null) duration = Long.parseLong(durationStr);
                
                // 检查视频尺寸和时长是否有效
                if (width <= 0 || height <= 0) {
                    Log.w(TAG, "Invalid video dimensions: " + width + "x" + height + " for " + fileName);
                    // 设置默认尺寸以避免错误
                    width = width <= 0 ? 1280 : width;
                    height = height <= 0 ? 720 : height;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error extracting video metadata: " + fileName, e);
                // 设置默认值
                width = 1280;
                height = 720;
                duration = 5000; // 5秒
            } finally {
                try {
                    retriever.release();
                } catch (Exception e) {
                    // Ignore
                }
            }
            
            // 通过仓库保存媒体文件
            Uri fileUri = Uri.fromFile(tempFile);
            long finalDuration = duration;
            int finalWidth = width;
            int finalHeight = height;
            
            mediaRepository.saveMedia(fileUri, MediaFileManager.TYPE_VIDEO, finalWidth, finalHeight, finalDuration, "video/mp4", new MediaRepository.OnMediaSavedCallback() {
                @Override
                public void onSuccess(Media media) {
                    Log.d(TAG, "Successfully imported video: " + fileName);
                    // 删除临时文件
                    tempFile.delete();
                    if (callback != null) callback.onSuccess();
                }
                
                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "Error importing video: " + fileName + ", error: " + errorMessage);
                    // 删除临时文件
                    tempFile.delete();
                    if (callback != null) callback.onError(errorMessage);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception in importVideoFromAssets: " + fileName, e);
            if (tempFile.exists()) {
                tempFile.delete();
            }
            if (callback != null) callback.onError(e.getMessage());
        }
    }
    
    /**
     * 将assets中的文件复制到指定文件
     * @param assetManager 资源管理器
     * @param assetPath 资源路径
     * @param outFile 输出文件
     * @throws IOException IO异常
     */
    private void copyAssetToFile(AssetManager assetManager, String assetPath, File outFile) throws IOException {
        Log.d(TAG, "Copying asset: " + assetPath);
        try (InputStream in = assetManager.open(assetPath);
             OutputStream out = new FileOutputStream(outFile)) {
            byte[] buffer = new byte[8192];
            int read;
            long totalBytes = 0;
            while ((read = in.read(buffer)) != -1) {
                out.write(buffer, 0, read);
                totalBytes += read;
            }
            out.flush();
            Log.d(TAG, "Successfully copied " + totalBytes + " bytes from " + assetPath);
        } catch (IOException e) {
            Log.e(TAG, "Error copying asset: " + assetPath, e);
            throw e;
        }
    }
    
    /**
     * 清理临时目录中的所有文件
     * @param directory 要清理的目录
     */
    private void cleanTempDirectory(File directory) {
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        file.delete();
                    }
                }
            }
        }
    }
    
    /**
     * 导入回调接口
     */
    private interface ImportCallback {
        void onSuccess();
        void onError(String error);
    }
    
    /**
     * 测试数据加载回调接口
     */
    public interface OnTestDataLoadedCallback {
        void onSuccess();
        void onError(String errorMessage);
    }
} 