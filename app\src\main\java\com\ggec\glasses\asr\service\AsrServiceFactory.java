package com.ggec.glasses.asr.service;

import android.content.Context;

import com.ggec.glasses.asr.service.impl.DashScopeAsrService;

/**
 * 语音识别服务工厂类
 * 用于创建和获取语音识别服务实例
 */
public class AsrServiceFactory {
    
    private static AsrService instance;
    
    private AsrServiceFactory() {
        // 私有构造函数，防止实例化
    }
    
    /**
     * 获取语音识别服务实例
     * @param context 应用上下文
     * @return AsrService实例
     */
    public static synchronized AsrService getService(Context context) {
        if (instance == null) {
            instance = new DashScopeAsrService(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 释放语音识别服务实例
     */
    public static synchronized void releaseService() {
        if (instance != null) {
            instance.release();
            instance = null;
        }
    }
} 