package com.ggec.glasses.voice.recorder;

import android.annotation.SuppressLint;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.util.Log;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 语音录制器
 * 负责初始化和管理AudioRecord，捕获麦克风数据
 */
public class VoiceRecorder {
    private static final String TAG = "VoiceRecorder";
    
    // 默认音频配置
    private static final int DEFAULT_SAMPLE_RATE = 16000; // 16kHz
    private static final int DEFAULT_CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int DEFAULT_AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    
    // 缓冲区大小（字节）
    private static final int BUFFER_SIZE_FACTOR = 2;
    
    private AudioRecord audioRecord;
    private int bufferSize;
    private int sampleRate;
    private int channelConfig;
    private int audioFormat;
    
    private final AtomicBoolean isRecording = new AtomicBoolean(false);
    private VoiceRecorderCallback callback;
    private Thread recordingThread;
    
    /**
     * 默认构造函数
     * 使用默认音频配置
     */
    public VoiceRecorder() {
        this(DEFAULT_SAMPLE_RATE, DEFAULT_CHANNEL_CONFIG, DEFAULT_AUDIO_FORMAT);
    }
    
    /**
     * 构造函数
     * 使用自定义音频配置
     * 
     * @param sampleRate 采样率
     * @param channelConfig 通道配置
     * @param audioFormat 音频格式
     */
    public VoiceRecorder(int sampleRate, int channelConfig, int audioFormat) {
        this.sampleRate = sampleRate;
        this.channelConfig = channelConfig;
        this.audioFormat = audioFormat;
        
        // 计算最小缓冲区大小，并增加冗余确保性能
        int minBufferSize = AudioRecord.getMinBufferSize(
                sampleRate, channelConfig, audioFormat);
        this.bufferSize = minBufferSize * BUFFER_SIZE_FACTOR;
        
        Log.d(TAG, "使用缓冲区大小: " + this.bufferSize + " 字节");
    }
    
    /**
     * 初始化录音器
     * 注意：调用此方法前，调用者必须已经请求并获得了RECORD_AUDIO权限
     * @return 如果初始化成功返回true，否则返回false
     */
    @SuppressLint("MissingPermission")
    public boolean initialize() {
        if (audioRecord != null) {
            release();
        }
        
        try {
            audioRecord = new AudioRecord(
                    MediaRecorder.AudioSource.MIC,
                    sampleRate,
                    channelConfig,
                    audioFormat,
                    bufferSize);
            
            return audioRecord.getState() == AudioRecord.STATE_INITIALIZED;
        } catch (Exception e) {
            Log.e(TAG, "初始化AudioRecord失败", e);
            return false;
        }
    }
    
    /**
     * 开始录音
     * 
     * @param callback 录音回调
     * @return 如果成功开始录音返回true，否则返回false
     */
    public boolean startRecording(VoiceRecorderCallback callback) {
        if (callback == null) {
            Log.e(TAG, "回调不能为null");
            return false;
        }
        
        this.callback = callback;
        
        if (audioRecord == null || audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
            Log.e(TAG, "AudioRecord未初始化或初始化失败");
            callback.onRecordingError("录音器未初始化");
            return false;
        }
        
        if (isRecording.get()) {
            Log.w(TAG, "录音已在进行中");
            return false;
        }
        
        isRecording.set(true);
        
        try {
            audioRecord.startRecording();
            
            // 检查录音是否真正开始
            if (audioRecord.getRecordingState() != AudioRecord.RECORDSTATE_RECORDING) {
                Log.e(TAG, "无法开始录音");
                isRecording.set(false);
                callback.onRecordingError("无法开始录音");
                return false;
            }
            
            // 启动录音线程
            recordingThread = new Thread(this::recordingLoop, "AudioRecordingThread");
            recordingThread.start();
            
            callback.onRecordingStarted();
            return true;
        } catch (Exception e) {
            Log.e(TAG, "开始录音时出错", e);
            isRecording.set(false);
            callback.onRecordingError("开始录音时出错: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 录音线程主循环
     */
    private void recordingLoop() {
        byte[] buffer = new byte[bufferSize];
        Log.d(TAG, "录音线程启动");
        
        while (isRecording.get() && !Thread.interrupted()) {
            try {
                int read = audioRecord.read(buffer, 0, bufferSize);
                
                if (read > 0 && callback != null) {
                    callback.onAudioDataReceived(buffer, read);
                } else if (read < 0) {
                    Log.e(TAG, "录音失败，错误码: " + read);
                    if (callback != null) {
                        callback.onRecordingError("录音失败，错误码: " + read);
                    }
                    break;
                }
            } catch (Exception e) {
                if (isRecording.get() && callback != null) {
                    Log.e(TAG, "录音过程中出错", e);
                    callback.onRecordingError("录音过程中出错: " + e.getMessage());
                }
                break;
            }
        }
        
        Log.d(TAG, "录音线程结束");
        
        // 确保录音停止
        stopRecordingInternal();
    }
    
    /**
     * 停止录音
     */
    public void stopRecording() {
        if (isRecording.getAndSet(false)) {
            Log.d(TAG, "停止录音");
            
            // 中断录音线程
            if (recordingThread != null) {
                recordingThread.interrupt();
                try {
                    recordingThread.join(500); // 等待线程结束，最多500ms
                } catch (InterruptedException e) {
                    Log.w(TAG, "等待录音线程结束时被中断", e);
                }
            }
            
            stopRecordingInternal();
        }
    }
    
    /**
     * 内部停止录音方法
     */
    private void stopRecordingInternal() {
        if (audioRecord != null) {
            try {
                if (audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                    audioRecord.stop();
                }
            } catch (Exception e) {
                Log.e(TAG, "停止录音时出错", e);
            }
        }
        
        if (callback != null) {
            callback.onRecordingStopped();
        }
    }
    
    /**
     * 释放资源
     */
    public void release() {
        stopRecording();
        
        if (audioRecord != null) {
            try {
                audioRecord.release();
            } catch (Exception e) {
                Log.e(TAG, "释放AudioRecord资源时出错", e);
            } finally {
                audioRecord = null;
            }
        }
    }
    
    /**
     * 检查是否正在录音
     * 
     * @return 如果正在录音返回true，否则返回false
     */
    public boolean isRecording() {
        return isRecording.get();
    }
    
    /**
     * 获取当前采样率
     * 
     * @return 采样率
     */
    public int getSampleRate() {
        return sampleRate;
    }
    
    /**
     * 获取当前通道配置
     * 
     * @return 通道配置
     */
    public int getChannelConfig() {
        return channelConfig;
    }
    
    /**
     * 获取当前音频格式
     * 
     * @return 音频格式
     */
    public int getAudioFormat() {
        return audioFormat;
    }
} 