/*
 * Copyright (c) 2021-2024. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.bbpro.ui.nearby

import android.app.Activity
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothProfile
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import com.realsil.bbpro.R
import com.realsil.bbpro.databinding.ActivityNearbyBinding
import com.realsil.bbpro.ui.settings.SettingsActivity
import com.realsil.sdk.audioconnect.repository.database.general.DeviceInfoEntity
import com.realsil.sdk.audioconnect.support.AudioConnectActivity
import com.realsil.sdk.audioconnect.support.ConnectionManager
import com.realsil.sdk.audioconnect.support.device.NearbyDeviceAdapter
import com.realsil.sdk.audioconnect.support.device.NearbyViewModel
import com.realsil.sdk.audioconnect.support.sync.DeviceInfoSyncManager
import com.realsil.sdk.bbpro.core.peripheral.ConnectionParameters
import com.realsil.sdk.core.bluetooth.BluetoothProfileCallback
import com.realsil.sdk.core.bluetooth.BluetoothProfileManager
import com.realsil.sdk.core.bluetooth.RtkBluetoothManager
import com.realsil.sdk.core.bluetooth.RtkBluetoothManagerCallback
import com.realsil.sdk.core.bluetooth.scanner.ScannerParams
import com.realsil.sdk.core.bluetooth.scanner.SpecScanRecord
import com.realsil.sdk.core.bluetooth.scanner.compat.CompatScanFilter
import com.realsil.sdk.core.compat.RtkContextCompat
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.fastpair.FastPairManager
import com.realsil.sdk.fastpair.FastPairService
import com.realsil.sdk.support.base.BaseViewBindingActivity
import com.realsil.sdk.support.recyclerview.LineItemDecoration
import com.realsil.sdk.support.recyclerview.makeVertical
import com.realsil.sdk.support.scanner.BluetoothScannerActivity
import com.realsil.sdk.support.scanner.LeScannerActivity
import com.realsil.sdk.support.scanner.LegacyScannerActivity
import com.realtek.sdk.support.debugger.WriteLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 10/05/2024
 */
class NearbyActivity :
    BaseViewBindingActivity<ActivityNearbyBinding>(ActivityNearbyBinding::inflate) {
    val MSG_RELOAD = 0

    private var scanMode = ScannerParams.SCAN_MODE_SPP

    private var mDeviceAdapter: NearbyDeviceAdapter? = null

    private var mInnerBroadcastReceiver: BroadcastReceiver? = null

    private var bluetoothProfileManager: BluetoothProfileManager? = null

    private var deviceInfoSyncManager: DeviceInfoSyncManager? = null

    private val nearbyViewModel: NearbyViewModel by viewModels()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding.mToolbar.setTitle(R.string.title_nearby_device)

        binding.mToolbar.inflateMenu(R.menu.menu_main)
        binding.mToolbar.setOnMenuItemClickListener { item ->
            if (item.itemId == R.id.action_add_device) {
                showAlertDialog(getString(R.string.title_scan_device_options),
                    positiveText = getString(R.string.button_scan_by_ble),
                    positiveBlock = {
                        scanBleDevice()
                    }, negativeText = getString(R.string.button_scan_by_bredr),
                    negativeBlock = {
                        scanBrEdrDevice()
                    }
                )
            } else if (item.itemId == R.id.action_settings) {
                redirect2DeviceInfoActivity()
            }
            false
        }

        deviceInfoSyncManager = DeviceInfoSyncManager.getInstance(this)

        initRecyclerView()

        initData()

        WriteLog.instance!!.restartLog()

        initBluetooth()

        BluetoothProfileManager.getInstance().registerProfiles()

        RtkBluetoothManager.getInstance().addManagerCallback(mBluetoothManagerCallback)

        FastPairManager.startService(this, true)
        nearbyViewModel.deviceListLiveData.observe(this) {
            mDeviceAdapter?.setEntityList(it)
        }
    }

    private fun scanBrEdrDevice() {
        scanMode = ScannerParams.SCAN_MODE_SPP
        val scannerParams = ScannerParams(ScannerParams.SCAN_MODE_SPP)
        scannerParams.isNameNullable = false
        /*scannerParams.dispatchFilters = arrayListOf(
            DispatcherFilter.Builder()
                .setFilterUuids(BluetoothUuidCompat.HEADSET_PROFILE_UUIDS)
                .build())*/
        val intent = Intent(this@NearbyActivity, LegacyScannerActivity::class.java)
        intent.putExtra(BluetoothScannerActivity.EXTRA_KEY_SCAN_PARAMS, scannerParams)
        startBluetoothScanner(intent)
    }

    private fun scanBleDevice() {
        scanMode = ScannerParams.SCAN_MODE_GATT
        val scannerParams = ScannerParams(ScannerParams.SCAN_MODE_GATT)
        scannerParams.isNameNullable = false

        val scanFilters: MutableList<CompatScanFilter> = ArrayList()
        scanFilters.add(
            CompatScanFilter.Builder()
                .setServiceUuid(
                    ConnectionManager.AUDIO_CONNECT_SERVICE_UUID_PRI,
                    ConnectionManager.AUDIO_CONNECT_SERVICE_UUID_MASK
                )
                .build()
        )
        scanFilters.add(
            CompatScanFilter.Builder()
                .setServiceUuid(
                    ConnectionManager.AUDIO_CONNECT_SERVICE_UUID_SEC,
                    ConnectionManager.AUDIO_CONNECT_SERVICE_UUID_MASK
                )
                .build()
        )
        scannerParams.scanFilters = scanFilters

        val intent = Intent(this@NearbyActivity, LeScannerActivity::class.java)
        intent.putExtra(BluetoothScannerActivity.EXTRA_KEY_SCAN_PARAMS, scannerParams)
        startBluetoothScanner(intent)
    }

    override fun onBtScannerCallback(
        device: BluetoothDevice,
        scanRecord: SpecScanRecord?
    ) {
        super.onBtScannerCallback(device, scanRecord)

        ZLogger.d("onBtScannerCallback scanMode = $scanMode")
        CoroutineScope(Dispatchers.IO).launch {
            if (scanMode == ScannerParams.SCAN_MODE_GATT) {
                deviceInfoSyncManager?.createNewDevice(ConnectionParameters.CHANNEL_TYPE_GATT,
                    deviceAddress = device.address, deviceName = device.name)
            } else {
                deviceInfoSyncManager?.createNewDevice(
                    ConnectionParameters.CHANNEL_TYPE_SPP,
                    deviceAddress = device.address, deviceName = device.name)
            }
            launch(Dispatchers.Main) {
                redirect2DeviceInfoActivity(device.address)
            }
        }
    }

    private fun initRecyclerView() {
        binding.deviceRecyclerView.makeVertical(this)
        binding.deviceRecyclerView.addItemDecoration(
            LineItemDecoration(
                this, LineItemDecoration.VERTICAL_LIST, 8
            )
        )
        mDeviceAdapter =
            NearbyDeviceAdapter(this, ArrayList())
        mDeviceAdapter!!.setOnAdapterListener(object : NearbyDeviceAdapter.OnAdapterListener {

            override fun onItemClick(entity: DeviceInfoEntity) {
                redirect2DeviceInfoActivity(entity.deviceAddress)
            }

        })
        binding.deviceRecyclerView.adapter = mDeviceAdapter
    }


    private val deviceInfoActivityResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { it ->
            ZLogger.v("it.resultCode=${it.resultCode}")
            if (it.resultCode == Activity.RESULT_OK) {
//                mHandler.sendMessage(mHandler.obtainMessage(MSG_RELOAD))
                reload(true)
            }
        }

    private fun redirect2DeviceInfoActivity(address: String = "") {
        val intent = Intent(this@NearbyActivity, SettingsActivity::class.java)
        intent.putExtra(AudioConnectActivity.EXTRA_KEY_BT_ADDR, address)
        deviceInfoActivityResult.launch(intent)
    }

    private fun initData() {
        if (bluetoothProfileManager == null) {
            BluetoothProfileManager.initial(this)
            bluetoothProfileManager = BluetoothProfileManager.getInstance()
        }
        if (bluetoothProfileManager != null) {
            bluetoothProfileManager!!.addManagerCallback(mBluetoothProfileCallback)
        }

        val intentFilter = IntentFilter()
        intentFilter.addAction(FastPairService.ACTION_FAST_PAIR_POPUP_CLOSED)
        intentFilter.addAction(FastPairService.ACTION_FAST_PAIR_CONNECTION_ESTABLISHED)
        intentFilter.addAction(FastPairService.ACTION_FAST_PAIR_CONNECTION_DISCONNECTED)
        intentFilter.addAction(FastPairService.ACTION_FAST_PAIR_CONNECTION_STATE_CHANGED)
        mInnerBroadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val action = intent.action

                if (FastPairService.ACTION_FAST_PAIR_CONNECTION_ESTABLISHED == action ||
                    FastPairService.ACTION_FAST_PAIR_CONNECTION_DISCONNECTED == action ||
                    FastPairService.ACTION_FAST_PAIR_CONNECTION_STATE_CHANGED == action
                ) {
                    ZLogger.d("ACTION_FAST_PAIR_CONNECTION action = $action")
                    mHandler.sendMessage(mHandler.obtainMessage(MSG_RELOAD))
                }
            }
        }
        RtkContextCompat.registerReceiver(this, mInnerBroadcastReceiver, intentFilter)

        checkConflictApp(CONFLICT_APP_PACKAGE_NAME)

        //requestPermissions()
        reload(true)
    }

    override fun onStart() {
        ZLogger.v(D, "NearbyActivity onStart")
        super.onStart()
        //checkState()

        if (!isBLEEnabled()) {
            redirect2EnableBT()
        } else {
            //重连操作
            /*val curDevice = PeripheralManager.getInstance().activeDevice
            if (curDevice != null) {
                if (!audioConnectViewModel.isConnected()) {
                    connect(curDevice)
                }
            }*/
        }
    }

    override fun onResume() {
        super.onResume()

        reload()
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            unregisterReceiver(mInnerBroadcastReceiver)
        } catch (_: Exception) {
        }
        if (bluetoothProfileManager != null) {
            bluetoothProfileManager!!.removeManagerCallback(mBluetoothProfileCallback)
        }
        RtkBluetoothManager.getInstance().removeManagerCallback(mBluetoothManagerCallback)

        cancelProgressBar()

        mHandler.removeCallbacksAndMessages(null)

        WriteLog.instance!!.stopLog()
    }

    private val mBluetoothManagerCallback: RtkBluetoothManagerCallback =
        object : RtkBluetoothManagerCallback() {
            override fun onBleAclConnectionStateChanged(
                bluetoothDevice: BluetoothDevice,
                connected: Boolean
            ) {
                super.onBleAclConnectionStateChanged(bluetoothDevice, connected)
                ZLogger.v("onBleAclConnectionStateChanged$connected")
                mHandler.sendMessage(mHandler.obtainMessage(MSG_RELOAD))
            }

            override fun onAclConnectionStateChanged(
                bluetoothDevice: BluetoothDevice,
                connected: Boolean
            ) {
                super.onAclConnectionStateChanged(bluetoothDevice, connected)
                ZLogger.v("onAclConnectionStateChanged:$connected")
                mHandler.sendMessage(mHandler.obtainMessage(MSG_RELOAD))
            }
        }

    private val mBluetoothProfileCallback: BluetoothProfileCallback =
        object : BluetoothProfileCallback() {
            override fun onHfpConnectionStateChanged(bluetoothDevice: BluetoothDevice, state: Int) {
                super.onHfpConnectionStateChanged(bluetoothDevice, state)
                if (state == BluetoothProfile.STATE_DISCONNECTED) {

                } else if (state == BluetoothProfile.STATE_CONNECTED) {
                    ZLogger.v("onHfpConnectionStateChanged:$state")
                    mHandler.sendMessage(mHandler.obtainMessage(MSG_RELOAD))
                }
            }
        }

    private fun reload(removeUnBondedDevice:Boolean = false) {
        CoroutineScope(Dispatchers.IO).launch {
            //remove unbonded devices
            val deviceList =  nearbyViewModel.deviceInfoSyncManager.getPairDeviceInfos()
            if (removeUnBondedDevice) {
                if (deviceList.isNotEmpty()) {
                    for (device in deviceList) {
                        if (!isDeviceBonded(device.deviceAddress)) {
                            nearbyViewModel.deviceInfoRepository.removeDevice(device.deviceAddress)
                        }
                    }
                }
            }

            nearbyViewModel.loadDeviceList()
        }
    }

    private val mHandler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_RELOAD -> {
                    runOnUiThread {
                        reload()
                    }
                }

                else -> {
                }
            }
        }
    }

    companion object {
        private const val D = true
        private const val TAG = "NearbyActivity"
        private const val CONFLICT_APP_PACKAGE_NAME = "com.realsil.bbpro.tts"
    }
}
