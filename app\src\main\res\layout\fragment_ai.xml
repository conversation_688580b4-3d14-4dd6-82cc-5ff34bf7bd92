<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginTop="36dp"
        android:background="@color/comp_background_gray"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="19dp"
            android:text="@string/ai_assistant"
            android:textColor="@color/font_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 更多选项按钮 -->
        <ImageView
            android:id="@+id/btn_more"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/btn_ripple_small_corner"
            android:clickable="true"
            android:contentDescription="@string/more_options"
            android:focusable="true"
            android:padding="12dp"
            android:src="@drawable/ic_public_more"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/icon_primary" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 底部操作栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:elevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 语音交互按钮 -->
        <ImageButton
            android:id="@+id/btn_voice"
            android:layout_width="128dp"
            android:layout_height="48dp"
            android:background="@drawable/bg_voice_button"
            android:contentDescription="@string/voice_interaction"
            android:padding="12dp"
            android:src="@drawable/ic_public_voice"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/white" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 - 使用RecyclerView替代ScrollView -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="6dp"
        app:layout_constraintBottom_toTopOf="@id/bottom_bar"
        app:layout_constraintTop_toBottomOf="@id/header_layout">
        
        <!-- 消息列表容器 - RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview_messages"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:scrollbars="vertical"
            android:fadeScrollbars="false"
            android:overScrollMode="always"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:listitem="@layout/item_ai_message" />
        
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 