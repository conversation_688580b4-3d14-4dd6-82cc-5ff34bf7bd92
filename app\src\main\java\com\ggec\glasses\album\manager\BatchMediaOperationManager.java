package com.ggec.glasses.album.manager;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;

import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.data.repository.MediaRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 批量媒体操作管理器
 * 负责处理多个媒体文件的批量下载、删除等操作
 */
public class BatchMediaOperationManager {
    private static final String TAG = "BatchMediaOperationManager";

    /**
     * 批量操作回调接口
     */
    public interface BatchOperationCallback {
        /**
         * 操作进度更新
         * @param current 当前进度
         * @param total 总数
         */
        void onProgressUpdate(int current, int total);

        /**
         * 操作完成
         * @param successCount 成功数量
         * @param failedCount 失败数量
         */
        void onComplete(int successCount, int failedCount);

        /**
         * 操作失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 批量保存媒体文件到系统相册
     * @param context 上下文
     * @param mediaIds 媒体ID集合
     * @param callback 回调
     */
    public static void batchSaveMediaToGallery(Context context, Set<Long> mediaIds, BatchOperationCallback callback) {
        if (mediaIds == null || mediaIds.isEmpty()) {
            if (callback != null) {
                callback.onError("没有选择任何媒体文件");
            }
            return;
        }

        // 获取MediaRepository实例
        MediaRepository repository = MediaRepository.getInstance(context);
        
        // 在后台线程中执行
        new Thread(() -> {
            try {
                // 获取所有选中的媒体文件
                List<Media> mediaList = new ArrayList<>();
                for (Long mediaId : mediaIds) {
                    Media media = repository.getMediaById(mediaId);
                    if (media != null && !media.isDeleted()) {
                        mediaList.add(media);
                    }
                }
                
                if (mediaList.isEmpty()) {
                    if (callback != null) {
                        callback.onError("所选媒体文件不存在或已被删除");
                    }
                    return;
                }
                
                int totalCount = mediaList.size();
                AtomicInteger successCount = new AtomicInteger(0);
                AtomicInteger failedCount = new AtomicInteger(0);
                AtomicInteger processedCount = new AtomicInteger(0);
                
                // 创建同步计数器
                CountDownLatch latch = new CountDownLatch(totalCount);
                
                // 处理每个媒体文件
                for (Media media : mediaList) {
                    AlbumMediaExportManager.saveMediaToGallery(context, media, new AlbumMediaExportManager.SaveMediaCallback() {
                        @Override
                        public void onSuccess() {
                            successCount.incrementAndGet();
                            int current = processedCount.incrementAndGet();
                            
                            // 更新进度
                            if (callback != null) {
                                callback.onProgressUpdate(current, totalCount);
                            }
                            
                            latch.countDown();
                        }
                        
                        @Override
                        public void onFailed(String errorMessage) {
                            failedCount.incrementAndGet();
                            int current = processedCount.incrementAndGet();
                            
                            Log.e(TAG, "保存媒体失败: " + errorMessage + ", 文件: " + media.getFileName());
                            
                            // 更新进度
                            if (callback != null) {
                                callback.onProgressUpdate(current, totalCount);
                            }
                            
                            latch.countDown();
                        }
                    });
                }
                
                // 等待所有任务完成
                latch.await();
                
                // 回调通知操作完成
                if (callback != null) {
                    callback.onComplete(successCount.get(), failedCount.get());
                }
            } catch (Exception e) {
                Log.e(TAG, "批量保存媒体文件失败", e);
                if (callback != null) {
                    callback.onError("批量保存操作发生错误: " + e.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * 批量删除媒体文件
     * @param context 上下文
     * @param mediaIds 媒体ID集合
     * @param callback 回调
     */
    public static void batchDeleteMedia(Context context, Set<Long> mediaIds, BatchOperationCallback callback) {
        if (mediaIds == null || mediaIds.isEmpty()) {
            if (callback != null) {
                callback.onError("没有选择任何媒体文件");
            }
            return;
        }
        
        // 获取MediaRepository实例
        MediaRepository repository = MediaRepository.getInstance(context);
        
        // 在后台线程中执行
        new Thread(() -> {
            try {
                // 获取所有选中的媒体文件
                List<Media> mediaList = new ArrayList<>();
                for (Long mediaId : mediaIds) {
                    Media media = repository.getMediaById(mediaId);
                    if (media != null && !media.isDeleted()) {
                        mediaList.add(media);
                    }
                }
                
                if (mediaList.isEmpty()) {
                    if (callback != null) {
                        callback.onError("所选媒体文件不存在或已被删除");
                    }
                    return;
                }
                
                int totalCount = mediaList.size();
                AtomicInteger successCount = new AtomicInteger(0);
                AtomicInteger failedCount = new AtomicInteger(0);
                AtomicInteger processedCount = new AtomicInteger(0);
                
                // 创建同步计数器
                CountDownLatch latch = new CountDownLatch(totalCount);
                
                // 处理每个媒体文件
                for (Media media : mediaList) {
                    AlbumMediaExportManager.deleteMedia(context, media, new AlbumMediaExportManager.DeleteMediaCallback() {
                        @Override
                        public void onSuccess() {
                            successCount.incrementAndGet();
                            int current = processedCount.incrementAndGet();
                            
                            // 更新进度
                            if (callback != null) {
                                callback.onProgressUpdate(current, totalCount);
                            }
                            
                            latch.countDown();
                        }
                        
                        @Override
                        public void onFailed(String errorMessage) {
                            failedCount.incrementAndGet();
                            int current = processedCount.incrementAndGet();
                            
                            Log.e(TAG, "删除媒体失败: " + errorMessage + ", 文件: " + media.getFileName());
                            
                            // 更新进度
                            if (callback != null) {
                                callback.onProgressUpdate(current, totalCount);
                            }
                            
                            latch.countDown();
                        }
                    });
                }
                
                // 等待所有任务完成
                latch.await();
                
                // 回调通知操作完成
                if (callback != null) {
                    callback.onComplete(successCount.get(), failedCount.get());
                }
            } catch (Exception e) {
                Log.e(TAG, "批量删除媒体文件失败", e);
                if (callback != null) {
                    callback.onError("批量删除操作发生错误: " + e.getMessage());
                }
            }
        }).start();
    }
} 