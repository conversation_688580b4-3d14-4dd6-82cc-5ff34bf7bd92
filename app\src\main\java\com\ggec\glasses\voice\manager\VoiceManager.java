package com.ggec.glasses.voice.manager;

import android.content.Context;
import android.media.AudioFormat;
import android.util.Log;

import com.ggec.glasses.asr.model.AsrRequest;
import com.ggec.glasses.asr.model.AsrResult;
import com.ggec.glasses.asr.service.AsrService;
import com.ggec.glasses.asr.service.AsrServiceFactory;
import com.ggec.glasses.voice.processor.AudioProcessor;
import com.ggec.glasses.voice.recorder.VoiceRecorder;
import com.ggec.glasses.voice.recorder.VoiceRecorderCallback;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * 语音管理器
 * 协调录音组件和ASR服务，提供完整的语音识别功能
 */
public class VoiceManager {
    private static final String TAG = "VoiceManager";
    
    // 音频参数
    private static final int SAMPLE_RATE = 16000; // 16kHz
    private static final int CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    private static final int BITS_PER_SAMPLE = 16; // 与AUDIO_FORMAT相对应
    private static final int CHANNEL_COUNT = 1; // 单声道
    
    private final Context context;
    private final VoiceRecorder voiceRecorder;
    private final AudioProcessor audioProcessor;
    private AsrService asrService;
    
    private final CompositeDisposable disposables = new CompositeDisposable();
    private VoiceRecognitionCallback callback;
    private boolean isInitialized = false;
    private boolean isRecognizing = false;
    
    private StringBuilder partialResultBuilder = new StringBuilder();
    
    /**
     * 构造函数
     * 
     * @param context 应用上下文
     */
    public VoiceManager(Context context) {
        this.context = context.getApplicationContext();
        
        // 创建录音器和音频处理器
        voiceRecorder = new VoiceRecorder(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);
        audioProcessor = new AudioProcessor(SAMPLE_RATE, CHANNEL_COUNT, BITS_PER_SAMPLE);
    }
    
    /**
     * 初始化语音管理器
     * 
     * @return 如果初始化成功返回true，否则返回false
     */
    public boolean initialize() {
        if (isInitialized) {
            Log.d(TAG, "已经初始化");
            return true;
        }
        
        Log.d(TAG, "初始化VoiceManager");
        
        try {
            // 初始化录音器
            boolean recorderInitialized = voiceRecorder.initialize();
            if (!recorderInitialized) {
                Log.e(TAG, "初始化录音器失败");
                return false;
            }
            
            // 获取ASR服务实例
            asrService = AsrServiceFactory.getService(context);
            
            // 初始化ASR服务
            boolean asrInitialized = asrService.initialize();
            if (!asrInitialized) {
                Log.e(TAG, "初始化ASR服务失败");
                return false;
            }
            
            isInitialized = true;
            Log.d(TAG, "VoiceManager初始化成功");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "初始化VoiceManager时出错", e);
            return false;
        }
    }
    
    /**
     * 开始语音识别
     * 
     * @param callback 识别回调
     * @return 如果成功开始识别返回true，否则返回false
     */
    public boolean startVoiceRecognition(VoiceRecognitionCallback callback) {
        if (callback == null) {
            Log.e(TAG, "回调不能为null");
            return false;
        }
        
        this.callback = callback;
        
        if (!isInitialized) {
            if (!initialize()) {
                callback.onRecognitionError("语音管理器未初始化");
                return false;
            }
        }
        
        if (isRecognizing) {
            Log.w(TAG, "识别已在进行中");
            return false;
        }
        
        Log.d(TAG, "开始语音识别");
        
        try {
            // 重置部分结果构建器
            partialResultBuilder.setLength(0);
            
            // 开始ASR识别
            Disposable disposable = asrService.startRecognition()
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                            this::handleAsrResult,
                            this::handleAsrError,
                            this::handleAsrComplete
                    );
            
            // 添加到CompositeDisposable
            disposables.add(disposable);
            
            // 开始录音
            boolean started = voiceRecorder.startRecording(new VoiceRecorderCallback() {
                @Override
                public void onRecordingStarted() {
                    Log.d(TAG, "录音开始");
                    isRecognizing = true;
                    callback.onRecognitionStarted();
                }
                
                @Override
                public void onAudioDataReceived(byte[] audioData, int sizeInBytes) {
                    processAudioData(audioData, sizeInBytes);
                }
                
                @Override
                public void onRecordingError(String error) {
                    Log.e(TAG, "录音错误: " + error);
                    callback.onRecognitionError("录音错误: " + error);
                    stopVoiceRecognition();
                }
                
                @Override
                public void onRecordingStopped() {
                    Log.d(TAG, "录音停止");
                    // 发送结束标记
                    sendEndOfAudioMarker();
                }
            });
            
            if (!started) {
                Log.e(TAG, "启动录音失败");
                disposables.clear();
                callback.onRecognitionError("启动录音失败");
                return false;
            }
            
            return true;
        } catch (Exception e) {
            Log.e(TAG, "开始语音识别时出错", e);
            callback.onRecognitionError("开始语音识别时出错: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 处理音频数据
     * 
     * @param audioData 录音数据
     * @param sizeInBytes 有效数据大小
     */
    private void processAudioData(byte[] audioData, int sizeInBytes) {
        try {
            // 处理音频数据
            AsrRequest request = audioProcessor.process(audioData, sizeInBytes);
            
            if (request != null && asrService != null) {
                // 发送到ASR服务
                boolean success = asrService.processAudioData(request);
                
                if (!success) {
                    Log.w(TAG, "处理音频数据失败");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "处理音频数据时出错", e);
        }
    }
    
    /**
     * 发送音频结束标记
     */
    private void sendEndOfAudioMarker() {
        try {
            if (asrService != null) {
                AsrRequest endRequest = audioProcessor.createEndRequest();
                asrService.processAudioData(endRequest);
            }
        } catch (Exception e) {
            Log.e(TAG, "发送音频结束标记时出错", e);
        }
    }
    
    /**
     * 处理ASR结果
     * 
     * @param result ASR结果
     */
    private void handleAsrResult(AsrResult result) {
        if (result == null) {
            return;
        }
        
        if (!result.isSuccess()) {
            Log.e(TAG, "ASR识别失败: " + result.getErrorMessage());
            if (callback != null) {
                callback.onRecognitionError("识别失败: " + result.getErrorMessage());
            }
            return;
        }
        
        String text = result.getText();
        
        if (result.isFinal()) {
            // 这是最终结果
            Log.d(TAG, "收到最终识别结果: " + text);
            if (callback != null) {
                callback.onFinalResult(text);
            }
        } else {
            // 这是部分结果
            Log.d(TAG, "收到部分识别结果: " + text);
            if (callback != null) {
                callback.onPartialResult(text);
            }
        }
    }
    
    /**
     * 处理ASR错误
     * 
     * @param error 错误
     */
    private void handleAsrError(Throwable error) {
        Log.e(TAG, "ASR识别异常", error);
        
        if (callback != null) {
            callback.onRecognitionError("识别异常: " + error.getMessage());
        }
        
        cleanupResources();
    }
    
    /**
     * 处理ASR完成
     */
    private void handleAsrComplete() {
        Log.d(TAG, "ASR识别完成");
        
        if (callback != null) {
            callback.onRecognitionComplete();
        }
        
        cleanupResources();
    }
    
    /**
     * 停止语音识别
     */
    public void stopVoiceRecognition() {
        Log.d(TAG, "停止语音识别");
        
        if (voiceRecorder != null && voiceRecorder.isRecording()) {
            voiceRecorder.stopRecording();
        }
        
        isRecognizing = false;
    }
    
    /**
     * 清理资源
     */
    private void cleanupResources() {
        disposables.clear();
        isRecognizing = false;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        Log.d(TAG, "释放VoiceManager资源");
        
        stopVoiceRecognition();
        cleanupResources();
        
        if (voiceRecorder != null) {
            voiceRecorder.release();
        }
        
        // 由于ASR服务是单例，这里不释放它，避免影响其他可能的用户
    }
    
    /**
     * 检查是否在识别
     * 
     * @return 如果正在识别返回true，否则返回false
     */
    public boolean isRecognizing() {
        return isRecognizing;
    }
} 