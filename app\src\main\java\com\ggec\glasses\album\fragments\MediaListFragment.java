package com.ggec.glasses.album.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.glasses.R;
import com.ggec.glasses.album.adapter.MediaAdapter;
import com.ggec.glasses.album.data.entity.Media;
import com.ggec.glasses.album.manager.MediaSelectionManager;
import com.ggec.glasses.album.util.MultiSelectModeHelper;
import com.ggec.glasses.album.viewmodel.MediaViewModel;
import com.ggec.glasses.fragments.AlbumFragment;
import com.ggec.glasses.utils.PageAnimUtils;

import java.util.List;

public class MediaListFragment extends Fragment {

    private static final String ARG_MEDIA_TYPE = "media_type";
    public static final String TYPE_ALL = "ALL";
    public static final String TYPE_PHOTO = "PHOTO";
    public static final String TYPE_VIDEO = "VIDEO";

    private MediaViewModel mediaViewModel;
    private MediaAdapter mediaAdapter;
    private RecyclerView rvMedia;
    private TextView tvEmptyState;
    private MediaSelectionManager selectionManager;
    private String mediaType;

    public static MediaListFragment newInstance(String mediaType) {
        MediaListFragment fragment = new MediaListFragment();
        Bundle args = new Bundle();
        args.putString(ARG_MEDIA_TYPE, mediaType);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mediaType = getArguments().getString(ARG_MEDIA_TYPE, TYPE_ALL);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_media_content, container, false);
        
        // 初始化视图
        rvMedia = view.findViewById(R.id.rv_media);
        tvEmptyState = view.findViewById(R.id.tv_empty_state);
        
        // 设置四列网格布局
        GridLayoutManager layoutManager = new GridLayoutManager(requireContext(), 4);
        rvMedia.setLayoutManager(layoutManager);
        
        // 初始化适配器
        mediaAdapter = new MediaAdapter(requireContext());
        rvMedia.setAdapter(mediaAdapter);
        
        // 初始化MediaSelectionManager
        selectionManager = MediaSelectionManager.getInstance();
        
        // 设置项点击事件
        mediaAdapter.setOnItemClickListener((media, position) -> {
            // 检查是否处于多选模式
            if (MultiSelectModeHelper.isInMultiSelectMode(this)) {
                // 多选模式下点击处理 - 选择/取消选择媒体项
                handleMultiSelectItemClick(media, position);
            } else {
                // 非多选模式下点击处理 - 跳转到媒体展示页面
                if ("IMAGE".equals(media.getType()) || "VIDEO".equals(media.getType())) {
                    navigateToMediaDisplay(media);
                }
            }
        });
        
        return view;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 获取ViewModel
        mediaViewModel = new ViewModelProvider(requireActivity()).get(MediaViewModel.class);
        
        // 根据媒体类型观察相应的数据变化
        switch (mediaType) {
            case TYPE_PHOTO:
                mediaViewModel.getPhotoMedia().observe(getViewLifecycleOwner(), this::updateMediaList);
                break;
            case TYPE_VIDEO:
                mediaViewModel.getVideoMedia().observe(getViewLifecycleOwner(), this::updateMediaList);
                break;
            default:
                mediaViewModel.getAllMedia().observe(getViewLifecycleOwner(), this::updateMediaList);
                break;
        }
        
        // 观察多选模式状态变化
        MultiSelectModeHelper.observeMultiSelectMode(this).observe(getViewLifecycleOwner(), this::updateMultiSelectMode);
        
        // 观察选中项数量变化，更新工具栏和顶部导航栏计数
        selectionManager.getSelectedCountLiveData().observe(getViewLifecycleOwner(), count -> {
            // 更新工具栏状态
            updateMultiSelectToolbar(count);
            
            // 更新顶部导航栏计数
            if (getParentFragment() instanceof AlbumFragment) {
                AlbumFragment albumFragment = (AlbumFragment) getParentFragment();
                albumFragment.updateSelectedCount(count);
            }
        });
        
        // 观察特定标签页的全选状态变化，更新UI
        selectionManager.getAllSelectedLiveData(mediaType).observe(getViewLifecycleOwner(), isTabAllSelected -> {
            // 当该标签页的全选状态改变时，通知适配器刷新UI
            updateMediaAdapter();
        });
    }
    
    @Override
    public void onResume() {
        super.onResume();
        // 当Fragment可见时，更新当前活动标签页类型
        selectionManager.setCurrentTabType(mediaType);
    }
    
    /**
     * 处理多选模式下的项点击事件
     * @param media 媒体文件
     * @param position 位置
     */
    private void handleMultiSelectItemClick(Media media, int position) {
        // 更新选中状态
        selectionManager.toggleMediaSelection(media);
        
        // 更新UI
        mediaAdapter.notifyItemChanged(position);
    }
    
    /**
     * 更新多选模式状态
     * @param isMultiSelectMode 是否处于多选模式
     */
    private void updateMultiSelectMode(boolean isMultiSelectMode) {
        // 更新适配器的多选模式状态
        mediaAdapter.setMultiSelectMode(isMultiSelectMode);
        
        // 如果不是多选模式，清空选中状态
        if (!isMultiSelectMode) {
            selectionManager.clearSelection();
        }
    }
    
    /**
     * 更新媒体适配器
     */
    private void updateMediaAdapter() {
        if (mediaAdapter != null) {
            mediaAdapter.notifySelectionChanged();
        }
    }
    
    /**
     * 更新多选模式工具栏
     * @param selectedCount 选中项数量
     */
    private void updateMultiSelectToolbar(int selectedCount) {
        // 获取多选模式工具栏
        View toolbar = requireActivity().findViewById(R.id.multi_select_toolbar);
        if (toolbar != null) {
            // 更新操作按钮状态
            View btnDelete = toolbar.findViewById(R.id.btn_delete_multi);
            if (btnDelete != null) {
                btnDelete.setEnabled(selectedCount > 0);
            }
            
            View btnShare = toolbar.findViewById(R.id.btn_share_multi);
            if (btnShare != null) {
                btnShare.setEnabled(selectedCount > 0);
            }
        }
    }
    
    /**
     * 跳转到媒体展示页面
     */
    private void navigateToMediaDisplay(Media media) {
        // 创建Intent启动MediaDisplayActivity
        Intent intent = new Intent(requireContext(), com.ggec.glasses.album.MediaDisplayActivity.class);
        intent.putExtra(com.ggec.glasses.album.MediaDisplayActivity.EXTRA_MEDIA, media);
        
        // 启动Activity
        startActivity(intent);
        
        // 移除自定义转场动画，使用系统默认动画
    }
    
    /**
     * 更新媒体列表
     */
    private void updateMediaList(List<Media> mediaList) {
        if (mediaList == null || mediaList.isEmpty()) {
            // 显示空状态
            rvMedia.setVisibility(View.GONE);
            tvEmptyState.setVisibility(View.VISIBLE);
        } else {
            // 显示媒体列表
            rvMedia.setVisibility(View.VISIBLE);
            tvEmptyState.setVisibility(View.GONE);
            mediaAdapter.setMediaList(mediaList);
            
            // 更新选择管理器中的当前标签页媒体列表
            selectionManager.setTabMediaList(mediaType, mediaList);
        }
    }
} 