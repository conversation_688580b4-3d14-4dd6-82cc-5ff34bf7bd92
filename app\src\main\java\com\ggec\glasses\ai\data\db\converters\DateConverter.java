package com.ggec.glasses.ai.data.db.converters;

import androidx.room.TypeConverter;

import java.util.Date;

/**
 * 日期转换器，用于在Room数据库中存储和检索Date类型
 */
public class DateConverter {
    
    /**
     * 将Date对象转换为Long类型，存储到数据库
     * @param date 需要转换的日期
     * @return 转换后的时间戳（毫秒），如果date为null则返回null
     */
    @TypeConverter
    public static Long dateToTimestamp(Date date) {
        return date == null ? null : date.getTime();
    }
    
    /**
     * 将Long类型的时间戳转换为Date对象
     * @param timestamp 时间戳（毫秒）
     * @return 转换后的Date对象，如果timestamp为null则返回null
     */
    @TypeConverter
    public static Date timestampToDate(Long timestamp) {
        return timestamp == null ? null : new Date(timestamp);
    }
} 