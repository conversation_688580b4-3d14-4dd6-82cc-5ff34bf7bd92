package com.ggec.glasses.bluetooth.receiver;

import android.Manifest;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;

import androidx.core.app.ActivityCompat;

/**
 * 蓝牙广播接收器
 * 负责接收并处理蓝牙状态变化广播
 */
public class BluetoothBroadcastReceiver extends BroadcastReceiver {
    
    private static final String TAG = "BluetoothBroadcastReceiver";
    private final Context context;
    private BluetoothStateListener listener;
    private boolean isRegistered = false;
    
    /**
     * 构造函数
     * @param context 上下文
     */
    public BluetoothBroadcastReceiver(Context context) {
        this.context = context.getApplicationContext();
    }
    
    /**
     * 设置蓝牙状态监听器
     * @param listener 监听器
     */
    public void setListener(BluetoothStateListener listener) {
        this.listener = listener;
    }
    
    /**
     * 注册广播接收器
     */
    public void register() {
        if (isRegistered) {
            return;
        }
        
        IntentFilter filter = new IntentFilter();
        filter.addAction(android.bluetooth.BluetoothAdapter.ACTION_STATE_CHANGED);
        filter.addAction(android.bluetooth.BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED);
        filter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
        filter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
        filter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        
        context.registerReceiver(this, filter);
        isRegistered = true;
        Log.d(TAG, "蓝牙广播接收器已注册");
    }
    
    /**
     * 注销广播接收器
     */
    public void unregister() {
        if (!isRegistered) {
            return;
        }
        
        try {
            context.unregisterReceiver(this);
            isRegistered = false;
            Log.d(TAG, "蓝牙广播接收器已注销");
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "注销蓝牙广播接收器失败", e);
        }
    }
    
    @Override
    public void onReceive(Context context, Intent intent) {
        if (listener == null) {
            Log.w(TAG, "接收到蓝牙广播，但未设置监听器");
            return;
        }
        
        final String action = intent.getAction();
        if (action == null) {
            return;
        }
        
        Log.d(TAG, "收到蓝牙广播: " + action);
        
        switch (action) {
            case android.bluetooth.BluetoothAdapter.ACTION_STATE_CHANGED:
                handleBluetoothStateChanged(intent);
                break;
            case android.bluetooth.BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED:
                handleConnectionStateChanged(intent);
                break;
            case BluetoothDevice.ACTION_ACL_CONNECTED:
                handleDeviceConnected(intent);
                break;
            case BluetoothDevice.ACTION_ACL_DISCONNECTED:
                handleDeviceDisconnected(intent);
                break;
            case BluetoothDevice.ACTION_BOND_STATE_CHANGED:
                handleBondStateChanged(intent);
                break;
        }
    }
    
    /**
     * 处理蓝牙适配器状态变化
     */
    private void handleBluetoothStateChanged(Intent intent) {
        final int state = intent.getIntExtra(
                android.bluetooth.BluetoothAdapter.EXTRA_STATE, 
                android.bluetooth.BluetoothAdapter.ERROR);
        
        switch (state) {
            case android.bluetooth.BluetoothAdapter.STATE_OFF:
            case android.bluetooth.BluetoothAdapter.STATE_TURNING_OFF:
                listener.onBluetoothAdapterStateChanged(false);
                break;
            case android.bluetooth.BluetoothAdapter.STATE_ON:
                listener.onBluetoothAdapterStateChanged(true);
                break;
            case android.bluetooth.BluetoothAdapter.STATE_TURNING_ON:
                // 状态转换中，不处理
                break;
        }
    }
    
    /**
     * 处理蓝牙连接状态变化
     */
    private void handleConnectionStateChanged(Intent intent) {
        int connectionState = intent.getIntExtra(
                android.bluetooth.BluetoothAdapter.EXTRA_CONNECTION_STATE, 
                android.bluetooth.BluetoothAdapter.STATE_DISCONNECTED);
        
        boolean isConnecting = connectionState == android.bluetooth.BluetoothAdapter.STATE_CONNECTING;
        boolean isConnected = connectionState == android.bluetooth.BluetoothAdapter.STATE_CONNECTED;
        
        listener.onBluetoothConnectionStateChanged(isConnecting, isConnected);
    }
    
    /**
     * 处理设备连接事件
     */
    private void handleDeviceConnected(Intent intent) {
        BluetoothDevice device = getBluetoothDeviceFromIntent(intent);
        if (device != null) {
            listener.onDeviceConnected(device);
        }
    }
    
    /**
     * 处理设备断开连接事件
     */
    private void handleDeviceDisconnected(Intent intent) {
        BluetoothDevice device = getBluetoothDeviceFromIntent(intent);
        if (device != null) {
            listener.onDeviceDisconnected(device);
        }
    }
    
    /**
     * 处理设备配对状态变化事件
     */
    private void handleBondStateChanged(Intent intent) {
        BluetoothDevice device = getBluetoothDeviceFromIntent(intent);
        int bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.ERROR);
        
        if (device != null) {
            listener.onDeviceBondStateChanged(device, bondState);
        }
    }
    
    /**
     * 从Intent中获取BluetoothDevice对象
     */
    private BluetoothDevice getBluetoothDeviceFromIntent(Intent intent) {
        BluetoothDevice device = null;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {
                device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice.class);
            }
        } else {
            device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
        }
        
        return device;
    }
    
    /**
     * 蓝牙状态监听器接口
     */
    public interface BluetoothStateListener {
        /**
         * 蓝牙适配器状态变化回调
         * @param enabled 是否启用
         */
        void onBluetoothAdapterStateChanged(boolean enabled);
        
        /**
         * 蓝牙连接状态变化回调
         * @param isConnecting 是否正在连接
         * @param isConnected 是否已连接
         */
        void onBluetoothConnectionStateChanged(boolean isConnecting, boolean isConnected);
        
        /**
         * 设备连接回调
         * @param device 已连接的设备
         */
        void onDeviceConnected(BluetoothDevice device);
        
        /**
         * 设备断开连接回调
         * @param device 断开连接的设备
         */
        void onDeviceDisconnected(BluetoothDevice device);
        
        /**
         * 设备配对状态变化回调
         * @param device 设备
         * @param bondState 配对状态
         */
        void onDeviceBondStateChanged(BluetoothDevice device, int bondState);
    }
} 