package com.ggec.glasses.ai.data.cache.api;

import java.util.List;
import java.util.Map;

/**
 * 通用缓存接口
 * 定义缓存操作的基本方法
 *
 * @param <K> 缓存键类型
 * @param <V> 缓存值类型
 */
public interface ICache<K, V> {
    
    /**
     * 将对象存入缓存
     *
     * @param key 缓存键
     * @param value 缓存值
     * @return 是否成功存入
     */
    boolean put(K key, V value);
    
    /**
     * 将对象存入缓存并设置过期时间
     *
     * @param key 缓存键
     * @param value 缓存值
     * @param expireTime 过期时间(毫秒)
     * @return 是否成功存入
     */
    boolean put(K key, V value, long expireTime);
    
    /**
     * 批量存入缓存对象
     *
     * @param map 缓存键值对映射
     * @return 成功存入的键的数量
     */
    int putAll(Map<K, V> map);
    
    /**
     * 从缓存获取对象
     *
     * @param key 缓存键
     * @return 缓存对象，如果不存在或已过期则返回null
     */
    V get(K key);
    
    /**
     * 批量获取缓存对象
     *
     * @param keys 缓存键列表
     * @return 缓存对象映射，仅包含存在且未过期的对象
     */
    Map<K, V> getAll(List<K> keys);
    
    /**
     * 检查键是否存在于缓存中
     *
     * @param key 缓存键
     * @return 是否存在且未过期
     */
    boolean contains(K key);
    
    /**
     * 从缓存移除指定对象
     *
     * @param key 缓存键
     * @return 被移除的对象，如果不存在则返回null
     */
    V remove(K key);
    
    /**
     * 批量移除缓存对象
     *
     * @param keys 缓存键列表
     * @return 成功移除的键的数量
     */
    int removeAll(List<K> keys);
    
    /**
     * 清空缓存
     */
    void clear();
    
    /**
     * 获取缓存大小
     *
     * @return 当前缓存中的对象数量
     */
    int size();
    
    /**
     * 添加缓存回调
     *
     * @param callback 缓存回调
     */
    void addCallback(CacheCallback<K, V> callback);
    
    /**
     * 移除缓存回调
     *
     * @param callback 缓存回调
     */
    void removeCallback(CacheCallback<K, V> callback);
    
    /**
     * 设置缓存策略
     *
     * @param policy 缓存策略
     */
    void setCachePolicy(CachePolicy policy);
    
    /**
     * 获取缓存策略
     *
     * @return 当前缓存策略
     */
    CachePolicy getCachePolicy();
} 