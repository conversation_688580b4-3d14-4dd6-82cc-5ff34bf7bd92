package com.ggec.glasses.ai.strategy;

import android.content.Context;

/**
 * AI模型策略接口
 * 定义不同AI模型处理语音和文本的统一接口
 */
public interface AIModelStrategy {

    /**
     * 初始化策略
     * @param context 应用上下文
     * @return 初始化是否成功
     */
    boolean initialize(Context context);
    
    /**
     * 检查策略是否已初始化并可用
     * @return 可用状态
     */
    boolean isAvailable();
    
    /**
     * 处理语音输入并生成回复
     * @param audioData 音频数据字节数组
     * @param sizeInBytes 有效数据大小
     * @param conversationId 会话ID
     * @return 操作是否成功开始
     */
    boolean processAudioInput(byte[] audioData, int sizeInBytes, long conversationId);
    
    /**
     * 处理文本输入并生成回复
     * @param textInput 文本输入
     * @param conversationId 会话ID
     * @return 操作是否成功开始
     */
    boolean processTextInput(String textInput, long conversationId);
    
    /**
     * 结束当前音频处理会话
     * @return 操作是否成功
     */
    boolean endAudioProcessing();
    
    /**
     * 获取此策略支持的AI模型类型
     * @return AI模型类型
     */
    AIModelType getModelType();
    
    /**
     * 释放资源
     */
    void release();
} 