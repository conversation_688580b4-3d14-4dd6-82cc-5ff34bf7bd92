package com.ggec.glasses.device;

import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;

import com.ggec.glasses.R;

/**
 * Debug活动
 */
public class DebugActivity extends AppCompatActivity {

    private ImageView ivBack;
    private TextView tvTitle;
    private ConstraintLayout headerLayout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 设置为沉浸式状态栏
        setupImmersiveStatusBar();
        
        setContentView(R.layout.activity_debug);

        // 初始化UI组件
        initViews();
        // 设置事件监听器
        setupListeners();
        // 设置窗口插入适配
        setupWindowInsets();
    }

    /**
     * 设置沉浸式状态栏
     */
    private void setupImmersiveStatusBar() {
        // 使内容延伸到状态栏和导航栏后面
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        
        // 设置状态栏颜色（透明）
        Window window = getWindow();
        window.setStatusBarColor(ContextCompat.getColor(this, android.R.color.transparent));
        
        // 设置状态栏图标为深色（因为背景是浅色）
        WindowInsetsControllerCompat windowInsetsController = 
                WindowCompat.getInsetsController(window, window.getDecorView());
        if (windowInsetsController != null) {
            windowInsetsController.setAppearanceLightStatusBars(true);
        }
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvTitle = findViewById(R.id.tv_title);
        headerLayout = findViewById(R.id.header_layout);
        
        // 设置标题文本
        tvTitle.setText(R.string.debug_settings_title);
    }

    /**
     * 设置事件监听器
     */
    private void setupListeners() {
        // 返回按钮点击事件
        ivBack.setOnClickListener(v -> finish());
    }
    
    /**
     * 设置窗口插入适配
     */
    private void setupWindowInsets() {
        final View mainContainer = findViewById(R.id.main_container);
        ViewCompat.setOnApplyWindowInsetsListener(mainContainer, (v, windowInsets) -> {
            Insets insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars());
            
            // 为头部布局添加状态栏高度的上边距，并额外增加12dp的距离
            int extraTopMargin = (int) (12 * getResources().getDisplayMetrics().density); // 12dp转为像素
            ConstraintLayout.LayoutParams params = 
                    (ConstraintLayout.LayoutParams) headerLayout.getLayoutParams();
            params.topMargin = insets.top + extraTopMargin;
            headerLayout.setLayoutParams(params);
            
            // 返回修改后的窗口插入
            return WindowInsetsCompat.CONSUMED;
        });
    }
} 