package com.ggec.glasses.ai.manager;

import android.app.Application;
import android.util.Log;

import com.ggec.glasses.ai.strategy.AIModelStrategy;
import com.ggec.glasses.ai.strategy.AIModelStrategyFactory;
import com.ggec.glasses.ai.strategy.AIModelType;

/**
 * 协调 AI 模型调用和音频处理流程的管理器
 * 使用策略模式支持多种不同的AI模型处理方式
 */
public class AIChatCoordinator {

    private static final String TAG = "AIChatCoordinator";

    private final Application application;
    private final AIModelStrategyFactory strategyFactory;
    private AIModelStrategy currentStrategy;

    /**
     * 构造函数
     * @param application 应用上下文
     * @param chatMessageManager 消息管理器
     */
    public AIChatCoordinator(Application application, ChatMessageManager chatMessageManager) {
        this.application = application;
        
        // 获取策略工厂
        this.strategyFactory = AIModelStrategyFactory.getInstance(application);
        
        // 获取当前激活的策略
        this.currentStrategy = strategyFactory.getCurrentStrategy(application);
        
        if (this.currentStrategy == null || !this.currentStrategy.isAvailable()) {
            Log.e(TAG, "当前策略初始化失败或不可用：" + 
                  (this.currentStrategy != null ? this.currentStrategy.getModelType() : "null"));
        } else {
            Log.d(TAG, "成功初始化 " + this.currentStrategy.getModelType() + " 策略");
        }
    }

    /**
     * 处理音频输入数据
     * @param audioData 音频数据字节数组
     * @param sizeInBytes 有效数据大小
     * @param conversationId 会话ID
     * @return 是否成功开始处理
     */
    public boolean processAudioInput(byte[] audioData, int sizeInBytes, long conversationId) {
        if (currentStrategy == null || !currentStrategy.isAvailable()) {
            Log.e(TAG, "无法处理音频输入：当前策略不可用");
            return false;
        }
        
        return currentStrategy.processAudioInput(audioData, sizeInBytes, conversationId);
    }
    
    /**
     * 结束音频处理
     * @return 操作是否成功
     */
    public boolean endAudioProcessing() {
        if (currentStrategy == null) {
            return false;
        }
        
        return currentStrategy.endAudioProcessing();
    }

    /**
     * 处理文本输入并生成AI回复
     * @param userMessage 用户输入的文本消息
     * @param conversationId 会话ID
     * @return 操作是否成功开始
     */
    public boolean requestAIReply(String userMessage, long conversationId) {
        if (currentStrategy == null || !currentStrategy.isAvailable()) {
            Log.e(TAG, "无法处理文本输入：当前策略不可用");
            return false;
        }
        
        return currentStrategy.processTextInput(userMessage, conversationId);
    }
    
    /**
     * 切换AI模型类型
     * @param modelType 要切换到的新模型类型
     * @return 切换是否成功
     */
    public boolean switchModelType(AIModelType modelType) {
        if (strategyFactory.switchModelType(application, modelType)) {
            // 获取新的当前策略
            this.currentStrategy = strategyFactory.getCurrentStrategy(application);
            Log.d(TAG, "成功切换到 " + modelType + " 模型策略");
            return true;
        }
        
        Log.e(TAG, "切换到 " + modelType + " 模型策略失败");
        return false;
    }
    
    /**
     * 获取当前使用的AI模型类型
     * @return 当前激活的模型类型
     */
    public AIModelType getCurrentModelType() {
        return strategyFactory.getCurrentModelType();
    }

    /**
     * 释放资源
     */
    public void release() {
        Log.d(TAG, "释放 AIChatCoordinator 资源");
        
        // 当前策略具体释放逻辑由策略自己负责
        if (currentStrategy != null) {
            currentStrategy.release();
            currentStrategy = null;
        }
    }
}