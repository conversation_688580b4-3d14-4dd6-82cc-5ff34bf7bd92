/*
 * Copyright (c) 2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.sample.audioconnect.smartwear.gallery

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.google.android.material.tabs.TabLayoutMediator
import com.realsil.sample.audioconnect.smartwear.R
import com.realsil.sample.audioconnect.smartwear.databinding.ActivityGlassGalleryBinding
import com.realsil.sdk.audioconnect.smartwear.SmartWearModelClient
import com.realsil.sdk.audioconnect.smartwear.SmartWearModelProxy
import com.realsil.sdk.audioconnect.support.AudioConnectActivity
import com.realsil.sdk.core.utility.StringUtils
import com.realsil.sdk.support.widget.ViewPager2Adapter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 * The Gallery Ui.
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
class GlassGalleryActivity : AudioConnectActivity<ActivityGlassGalleryBinding>(ActivityGlassGalleryBinding::inflate) {

    private val galleryViewModel: GalleryViewModel by viewModels()

    private var mAdapter: ViewPager2Adapter? = null

    private var mSmartWearModelClient: SmartWearModelClient? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding.toolbar.setTitle(R.string.title_ai_gallery)
        setSupportActionBar(binding.toolbar)
        if (supportActionBar != null) {
            supportActionBar!!.setDisplayHomeAsUpEnabled(true)
        }
        binding.toolbar.setNavigationOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }

        binding.btnConnectWiFi.setOnClickListener {
            showWiFiDialog("WiFi not connected!",
                positiveText = "Reconnect", positiveBlock = {
                    galleryViewModel.connectWiFi("AI_GLASS_AP", "rtkaiglass")
                },
                negativeText = "Setup Network", negativeBlock = {
                    startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
                })
        }
        binding.btnGetGalleryInfo.setOnClickListener { queryGalleryInfo() }

        val mFragmentList = ArrayList<Fragment>()
        val titles = ArrayList<String>()
        mFragmentList.add(GalleryFileListFragment.getInstance(fileType = FileType.IMAGE))
        titles.add("Image")
        mFragmentList.add(GalleryFileListFragment.getInstance(fileType = FileType.VIDEO))
        titles.add("Video")
        mAdapter = ViewPager2Adapter(this, mFragmentList)
        binding.fileViewPager.adapter = mAdapter
        binding.fileViewPager.offscreenPageLimit = mFragmentList.size
        binding.fileViewPager.isSaveEnabled = false

        TabLayoutMediator(binding.fileTabLayout, binding.fileViewPager,
            TabLayoutMediator.TabConfigurationStrategy { tab, position ->
                tab.text = titles[position]
            }).attach()

        mSmartWearModelClient = SmartWearModelProxy.getInstance().getModelClient(mDeviceAddress)

        galleryViewModel.getWifiConnectedLiveData().observe(this){
            if (it) {
                binding.btnConnectWiFi.text = "Connected"
                binding.btnConnectWiFi.setTextColor(ContextCompat.getColor(this, com.realtek.sdk.support.debugger.R.color.material_green_500))
                queryGalleryInfo()
            } else {
                binding.btnConnectWiFi.text = "Disconnected"
                binding.btnConnectWiFi.setTextColor(ContextCompat.getColor(this, com.realtek.sdk.support.debugger.R.color.material_red_500))
            }
        }

        enableWiFi()
    }

    private fun showWiFiDialog(message:String = "",
                               positiveText:String = getString(com.realsil.sdk.support.R.string.rtk_ok),
                               positiveBlock: () -> Unit = {},
                               negativeText:String  = "",
                               negativeBlock: () -> Unit = {}) {
        val dialogBuilder = AlertDialog.Builder(this)
            .setMessage(message)

        if (!StringUtils.isEmpty(positiveText)) {
            dialogBuilder.setPositiveButton(positiveText) { dialogInterface, i ->
                dialogInterface.dismiss()
                positiveBlock()
            }
        }
        if (!StringUtils.isEmpty(negativeText)) {
            dialogBuilder.setNegativeButton(negativeText) { dialogInterface, i ->
                dialogInterface.dismiss()
                negativeBlock()
            }
        }
        dialogBuilder.show()
    }

    private fun enableWiFi() {
        CoroutineScope(Dispatchers.IO).launch {
            mSmartWearModelClient?.openWifiModule()
            galleryViewModel.checkNetwork()
        }
    }

    private fun queryGalleryInfo() {
        CoroutineScope(Dispatchers.IO).launch {
            galleryViewModel.downloadMediaList()
        }
    }

}