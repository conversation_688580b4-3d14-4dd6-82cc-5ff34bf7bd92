<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="false">

    <!-- 顶部标题栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/comp_background_gray"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:padding="12dp"
            android:src="@drawable/ic_public_back"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:contentDescription="返回" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="代理设置"
            android:textColor="@color/font_primary"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- VPN图标 -->
    <ImageView
        android:id="@+id/iv_vpn"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="60dp"
        android:src="@drawable/ic_vpn"
        app:tint="@color/icon_emphasize"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_layout"
        android:contentDescription="VPN图标" />

    <!-- 说明文本 -->
    <TextView
        android:id="@+id/tv_description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginHorizontal="24dp"
        android:gravity="center"
        android:text="此处代理只适用于优化上网体验，并不具备翻墙功能。"
        android:textColor="@color/font_secondary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_vpn" />

    <!-- 代理设置卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/proxy_settings_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="36dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/background_primary"
        app:layout_constraintTop_toBottomOf="@id/tv_description">

        <!-- 开关设置栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="连接眼镜时开启代理模式"
                android:textColor="@color/font_primary"
                android:textSize="16sp" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_proxy"
                style="@style/CustomSwitchStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="48dp"
                android:gravity="center_vertical" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 