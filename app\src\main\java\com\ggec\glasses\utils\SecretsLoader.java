package com.ggec.glasses.utils;

import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 用于加载 secrets.properties 文件中的敏感信息
 */
public class SecretsLoader {

    private static final String TAG = "SecretsLoader";
    private static final String PROPERTIES_FILE = "secrets.properties";
    private static Properties properties;

    /**
     * 加载属性文件（如果尚未加载）
     * @param context 应用上下文
     */
    private static void loadProperties(Context context) {
        if (properties == null) {
            properties = new Properties();
            AssetManager assetManager = context.getAssets();
            try (InputStream inputStream = assetManager.open(PROPERTIES_FILE)) {
                properties.load(inputStream);
            } catch (IOException e) {
                Log.e(TAG, "无法加载 secrets.properties 文件", e);
                // 在无法加载文件时，创建一个空的 Properties 对象，避免 NullPointerException
                properties = new Properties();
            }
        }
    }

    /**
     * 获取指定键的值
     * @param context 应用上下文
     * @param key 属性键
     * @return 属性值，如果键不存在或加载失败则返回 null
     */
    public static String getSecret(Context context, String key) {
        loadProperties(context.getApplicationContext()); // 使用 Application Context 避免内存泄漏
        return properties.getProperty(key);
    }

    /**
     * 获取指定键的值，如果不存在则返回默认值
     * @param context 应用上下文
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 属性值或默认值
     */
    public static String getSecret(Context context, String key, String defaultValue) {
        loadProperties(context.getApplicationContext());
        return properties.getProperty(key, defaultValue);
    }
} 