package com.ggec.glasses.ai.data.db.dao;

import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Update;

import java.util.List;

/**
 * 基础数据访问对象接口，提供通用的CRUD操作
 * @param <T> 实体类型
 */
public interface BaseDao<T> {
    
    /**
     * 插入单个实体
     * @param entity 要插入的实体
     * @return 新插入记录的ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(T entity);
    
    /**
     * 批量插入实体
     * @param entities 要插入的实体列表
     * @return 新插入记录的ID列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insertAll(List<T> entities);
    
    /**
     * 更新实体
     * @param entity 要更新的实体
     */
    @Update
    void update(T entity);
    
    /**
     * 批量更新实体
     * @param entities 要更新的实体列表
     */
    @Update
    void updateAll(List<T> entities);
    
    /**
     * 删除实体
     * @param entity 要删除的实体
     */
    @Delete
    void delete(T entity);
    
    /**
     * 批量删除实体
     * @param entities 要删除的实体列表
     */
    @Delete
    void deleteAll(List<T> entities);
} 