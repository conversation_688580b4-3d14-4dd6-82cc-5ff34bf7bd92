{"formatVersion": 1, "database": {"version": 1, "identityHash": "85f7cecf1970a2191e926d62ce3c7128", "entities": [{"tableName": "media", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `file_name` TEXT, `file_path` TEXT, `thumbnail_path` TEXT, `type` TEXT, `mime_type` TEXT, `size` INTEGER NOT NULL, `width` INTEGER NOT NULL, `height` INTEGER NOT NULL, `duration` INTEGER NOT NULL, `creation_date` INTEGER, `modification_date` INTEGER, `is_deleted` INTEGER NOT NULL, `custom_metadata` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fileName", "columnName": "file_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "filePath", "columnName": "file_path", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thumbnail<PERSON>ath", "columnName": "thumbnail_path", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mimeType", "columnName": "mime_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "size", "columnName": "size", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "width", "columnName": "width", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "height", "columnName": "height", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "creationDate", "columnName": "creation_date", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "modificationDate", "columnName": "modification_date", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isDeleted", "columnName": "is_deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "customMetadata", "columnName": "custom_metadata", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "album", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT, `cover_media_id` INTEGER, `creation_date` INTEGER, `modification_date` INTEGER, `description` TEXT, FOREIGN KEY(`cover_media_id`) REFERENCES `media`(`id`) ON UPDATE NO ACTION ON DELETE SET NULL )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "coverMediaId", "columnName": "cover_media_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "creationDate", "columnName": "creation_date", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "modificationDate", "columnName": "modification_date", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_album_cover_media_id", "unique": false, "columnNames": ["cover_media_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_album_cover_media_id` ON `${TABLE_NAME}` (`cover_media_id`)"}], "foreignKeys": [{"table": "media", "onDelete": "SET NULL", "onUpdate": "NO ACTION", "columns": ["cover_media_id"], "referencedColumns": ["id"]}]}, {"tableName": "album_media", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`album_id` INTEGER NOT NULL, `media_id` INTEGER NOT NULL, `added_date` INTEGER, PRIMARY KEY(`album_id`, `media_id`), FOREI<PERSON><PERSON> KEY(`album_id`) REFERENCES `album`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREI<PERSON><PERSON> KEY(`media_id`) REFERENCES `media`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "albumId", "columnName": "album_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mediaId", "columnName": "media_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "addedDate", "columnName": "added_date", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["album_id", "media_id"]}, "indices": [{"name": "index_album_media_album_id", "unique": false, "columnNames": ["album_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_album_media_album_id` ON `${TABLE_NAME}` (`album_id`)"}, {"name": "index_album_media_media_id", "unique": false, "columnNames": ["media_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_album_media_media_id` ON `${TABLE_NAME}` (`media_id`)"}], "foreignKeys": [{"table": "album", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["album_id"], "referencedColumns": ["id"]}, {"table": "media", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["media_id"], "referencedColumns": ["id"]}]}, {"tableName": "tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT, `color_code` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "colorCode", "columnName": "color_code", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_tag_name", "unique": true, "columnNames": ["name"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_tag_name` ON `${TABLE_NAME}` (`name`)"}], "foreignKeys": []}, {"tableName": "media_tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`media_id` INTEGER NOT NULL, `tag_id` INTEGER NOT NULL, PRIMARY KEY(`media_id`, `tag_id`), FOREI<PERSON>N KEY(`media_id`) REFERENCES `media`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOR<PERSON><PERSON><PERSON> KEY(`tag_id`) REFERENCES `tag`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "mediaId", "columnName": "media_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tagId", "columnName": "tag_id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["media_id", "tag_id"]}, "indices": [{"name": "index_media_tag_media_id", "unique": false, "columnNames": ["media_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_media_tag_media_id` ON `${TABLE_NAME}` (`media_id`)"}, {"name": "index_media_tag_tag_id", "unique": false, "columnNames": ["tag_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_media_tag_tag_id` ON `${TABLE_NAME}` (`tag_id`)"}], "foreignKeys": [{"table": "media", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["media_id"], "referencedColumns": ["id"]}, {"table": "tag", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["tag_id"], "referencedColumns": ["id"]}]}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '85f7cecf1970a2191e926d62ce3c7128')"]}}