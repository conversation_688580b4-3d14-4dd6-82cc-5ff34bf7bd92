package com.ggec.glasses.profile;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;

import com.ggec.glasses.R;

/**
 * 代理设置活动
 */
public class ProxySettingsActivity extends AppCompatActivity {

    private ImageView ivBack;
    private SwitchCompat switchProxy;
    private ConstraintLayout headerLayout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 设置为沉浸式状态栏
        setupImmersiveStatusBar();
        
        setContentView(R.layout.activity_proxy_settings);

        // 初始化UI组件
        initViews();
        // 设置事件监听器
        setupListeners();
        // 设置窗口插入适配
        setupWindowInsets();
    }

    /**
     * 设置沉浸式状态栏
     */
    private void setupImmersiveStatusBar() {
        // 使内容延伸到状态栏和导航栏后面
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        
        // 设置状态栏颜色（透明）
        Window window = getWindow();
        window.setStatusBarColor(ContextCompat.getColor(this, android.R.color.transparent));
        
        // 设置状态栏图标为深色（因为背景是浅色）
        WindowInsetsControllerCompat windowInsetsController = 
                WindowCompat.getInsetsController(window, window.getDecorView());
        if (windowInsetsController != null) {
            windowInsetsController.setAppearanceLightStatusBars(true);
        }
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        headerLayout = findViewById(R.id.header_layout);
        switchProxy = findViewById(R.id.switch_proxy);
        
        // 确保应用自定义样式
        try {
            // 设置轨道颜色状态列表
            setupTrackTint();
            
            // 设置自定义滑块
            setupCustomThumb();
            
            // 默认状态设置为关闭
            switchProxy.setChecked(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 设置轨道颜色
     */
    private void setupTrackTint() {
        // 定义颜色状态列表
        int[][] states = new int[][]{
            new int[]{android.R.attr.state_checked},
            new int[]{-android.R.attr.state_checked}
        };
        
        // 使用品牌色和灰色
        int[] colors = new int[]{
            getResources().getColor(R.color.brand),
            0xFFE0E0E0
        };
        
        android.content.res.ColorStateList colorStateList = 
                new android.content.res.ColorStateList(states, colors);
        
        // 设置轨道颜色
        switchProxy.setTrackTintList(colorStateList);
    }
    
    /**
     * 设置自定义滑块
     */
    private void setupCustomThumb() {
        // 将dp转换为px - 增大滑块尺寸
        final int sizeInDp = 18; // 从12dp增加到18dp
        final int sizeInPx = (int) TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, 
                sizeInDp, 
                getResources().getDisplayMetrics()
        );
        
        // 创建一个自定义Drawable类，确保尺寸设置生效
        Drawable thumbDrawable = new Drawable() {
            private final Paint mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            private final int mSize = sizeInPx;
            private final RectF mRect = new RectF();
            
            {
                // 初始化画笔
                mPaint.setColor(Color.WHITE);
                mPaint.setStyle(Paint.Style.FILL);
            }
            
            @Override
            public void draw(Canvas canvas) {
                // 绘制白色填充圆形
                mPaint.setColor(Color.WHITE);
                mPaint.setStyle(Paint.Style.FILL);
                canvas.drawCircle(mRect.centerX(), mRect.centerY(), mSize / 2f, mPaint);
                
                // 绘制灰色边框
                mPaint.setColor(0xFFE0E0E0);
                mPaint.setStyle(Paint.Style.STROKE);
                mPaint.setStrokeWidth(1);
                canvas.drawCircle(mRect.centerX(), mRect.centerY(), mSize / 2f - 0.5f, mPaint);
            }
            
            @Override
            public void setAlpha(int alpha) {
                mPaint.setAlpha(alpha);
                invalidateSelf();
            }
            
            @Override
            public void setColorFilter(ColorFilter colorFilter) {
                mPaint.setColorFilter(colorFilter);
                invalidateSelf();
            }
            
            @Override
            public int getOpacity() {
                return PixelFormat.TRANSLUCENT;
            }
            
            @Override
            public int getIntrinsicWidth() {
                return mSize;
            }
            
            @Override
            public int getIntrinsicHeight() {
                return mSize;
            }
            
            @Override
            public void setBounds(int left, int top, int right, int bottom) {
                super.setBounds(left, top, right, bottom);
                mRect.set(left, top, right, bottom);
            }
        };
        
        // 创建状态列表Drawable以处理不同状态
        StateListDrawable stateListDrawable = new StateListDrawable();
        stateListDrawable.addState(new int[]{android.R.attr.state_checked}, thumbDrawable);
        stateListDrawable.addState(new int[]{-android.R.attr.state_checked}, thumbDrawable);
        
        // 设置自定义滑块
        switchProxy.setThumbDrawable(stateListDrawable);
        
        // 添加边界距离，确保滑块与轨道边缘保持适当距离
        switchProxy.setThumbTextPadding((int) TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, 
                8, 
                getResources().getDisplayMetrics()
        ));
        
        // 设置track的padding，增加滑块移动的边界距离
        switchProxy.setPadding(
                (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 4, getResources().getDisplayMetrics()),
                switchProxy.getPaddingTop(),
                (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 4, getResources().getDisplayMetrics()),
                switchProxy.getPaddingBottom()
        );
        
        // 设置最小高度和宽度
        switchProxy.setMinimumHeight((int) TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, 
                48, 
                getResources().getDisplayMetrics()
        ));
        
        // 强制布局刷新以应用新的滑块
        switchProxy.requestLayout();
    }

    /**
     * 设置事件监听器
     */
    private void setupListeners() {
        // 返回按钮点击事件
        ivBack.setOnClickListener(v -> finish());
        
        // 代理开关切换事件
        switchProxy.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // 暂时不实现功能，仅UI展示
            // 可以在此处添加相关逻辑，例如保存状态或调用相关服务
        });
    }
    
    /**
     * 设置窗口插入适配
     */
    private void setupWindowInsets() {
        final View mainContainer = findViewById(R.id.main_container);
        ViewCompat.setOnApplyWindowInsetsListener(mainContainer, (v, windowInsets) -> {
            Insets insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars());
            
            // 为头部布局添加状态栏高度的上边距，并额外增加12dp的距离
            int extraTopMargin = (int) (12 * getResources().getDisplayMetrics().density); // 12dp转为像素
            ConstraintLayout.LayoutParams params = 
                    (ConstraintLayout.LayoutParams) headerLayout.getLayoutParams();
            params.topMargin = insets.top + extraTopMargin;
            headerLayout.setLayoutParams(params);
            
            // 返回修改后的窗口插入
            return WindowInsetsCompat.CONSUMED;
        });
    }
} 