/*
 * Copyright (c) 2017-2022. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.sdk.audioconnect.support

import android.content.Context
import com.realsil.sdk.bbpro.core.transportlayer.AckPacket
import com.realsil.sdk.bbpro.vendor.VendorConstants
import com.realsil.sdk.support.ui.ChoiceEntity
import com.realsil.sdk.support.ui.ChoiceItem

/**
 *
 * <AUTHOR>
 */
object AudioConnectHelper {

    /**
     * @param value
     * @return
     */
    fun getBatteryLevel(value: Int): Int {
        val level: Int
        if (value > 80) {
            level = 5
        } else if (value > 60) {
            level = 4
        } else if (value > 40) {
            level = 3
        } else if (value > 20) {
            level = 2
        } else if (value > 1) {
            level = 1
        } else {
            level = 0
        }
        //        ZLogger.v("value=" + value + ", level=" + level);
        return level
    }

    /**
     * @param status
     * @return
     */
    fun parseAckStatus(operation: Int, status: Byte): String {
        val sb = StringBuilder()
        when (operation) {
            VendorConstants.Operation.SET_FIND_ME_STATE -> sb.append("SET_FIND_ME_STATE:")
            VendorConstants.Operation.SET_VOLUME -> sb.append("SET_VOLUME:")
            VendorConstants.Operation.GET_GAMING_MODE_STATE -> sb.append("GET_GAMING_MODE_STATE:")
            VendorConstants.Operation.ENABLE_VIBRATOR -> sb.append("ENABLE_VIBRATOR:")
            VendorConstants.Operation.DISABLE_VIBRATOR -> sb.append("DISABLE_VIBRATOR:")
            VendorConstants.Operation.TOGGLE_VIBRATOR_AND_VP -> sb.append("TOGGLE_VIBRATOR_AND_VP:")
            VendorConstants.Operation.STOP_VIBRATOR -> sb.append("STOP_VIBRATOR:")
            VendorConstants.Operation.GET_VIBRATOR_STATUS -> sb.append("GET_VIBRATOR_STATUS:")
            VendorConstants.Operation.CHECK_VIBRATOR -> sb.append("CHECK_VIBRATOR:")
            VendorConstants.Operation.GET_VIBRATOR_MODE_PARAMETERS -> sb.append("GET_VIBRATOR_MODE_PARAMETERS:")
            VendorConstants.Operation.SET_VIBRATOR_MODE_PARAMETERS -> sb.append("SET_VIBRATOR_MODE_PARAMETERS:")
            VendorConstants.Operation.GET_SUPPORTED_MMI_LIST -> sb.append("GET_SUPPORTED_MMI_LIST:")
            VendorConstants.Operation.GET_SUPPORTED_CLICK_TYPE -> sb.append("GET_SUPPORTED_CLICK_TYPE:")
            VendorConstants.Operation.GET_SUPPORTED_CALL_STATUS -> sb.append("GET_SUPPORTED_CALL_STATUS:")
            VendorConstants.Operation.GET_KEY_MMI_MAP -> sb.append("GET_KEY_MMI_MAP:")
            VendorConstants.Operation.SET_KEY_MMI_MAP -> sb.append("SET_KEY_MMI_MAP:")
            else -> {
            }
        }
        when (status) {
            AckPacket.ACK_STATUS_COMPLETE -> sb.append(String.format("0x%02X, COMPLETE", status))
            AckPacket.ACK_STATUS_DISALLOW -> sb.append(String.format("0x%02X, DISALLOW", status))
            AckPacket.ACK_STATUS_UNKNOWN_COMMAND -> sb.append(String.format("0x%02X, UNKNOWN_COMMAND", status))
            AckPacket.ACK_STATUS_PARAMETERS_ERROR -> sb.append(String.format("0x%02X, PARAMETERS_ERROR", status))
            AckPacket.ACK_STATUS_BUSY -> sb.append(String.format("0x%02X, BUSY", status))
            AckPacket.ACK_STATUS_PROCESS_FAIL -> sb.append(String.format("0x%02X, PROCESS_FAIL", status))
            else -> sb.append(String.format("0x%02X, UNKNOWN", status))
        }
        return sb.toString()
    }

    /**
     * @param status
     * @return
     */
    fun wrapperAckResult(status: Byte, context: Context): String {
        return when (status) {
            AckPacket.ACK_STATUS_COMPLETE -> String.format("0x%02X, %s", status, context.getString(R.string.audioconnect_support_ack_status_complete))
            AckPacket.ACK_STATUS_DISALLOW -> String.format("0x%02X, %s", status, context.getString(R.string.audioconnect_support_ack_status_disallow))
            AckPacket.ACK_STATUS_UNKNOWN_COMMAND -> String.format("0x%02X, %s", status, context.getString(R.string.audioconnect_support_ack_status_unknown_command))
            AckPacket.ACK_STATUS_PARAMETERS_ERROR -> String.format("0x%02X, %s", status, context.getString(R.string.audioconnect_support_ack_status_parameters_error))
            AckPacket.ACK_STATUS_BUSY -> String.format("0x%02X, %s", status, context.getString(R.string.audioconnect_support_ack_status_busy))
            AckPacket.ACK_STATUS_PROCESS_FAIL -> String.format("0x%02X, %s", status, context.getString(R.string.audioconnect_support_ack_process_fail))
            else -> String.format("0x%02X, %s", status, context.getString(R.string.audioconnect_support_ack_status_complete))
        }
    }

    /**
     *
     */
    fun getToggleStatus(): ArrayList<ChoiceItem> {
        val languages = ArrayList<ChoiceItem>()
        languages.add(ChoiceEntity(0x00, R.string.text_status_disabled))
        languages.add(ChoiceEntity(0x01, R.string.text_status_enabled))
        return languages
    }

    fun parseRwsChannel(context: Context?, channel: Byte): String {
        return if (context == null) {
            ""
        } else when (channel) {
            VendorConstants.RWS_CHANNEL_LEFT -> context.getString(R.string.rws_channel_let)
            VendorConstants.RWS_CHANNEL_RIGHT -> context.getString(R.string.rws_channel_right)
            VendorConstants.RWS_CHANNEL_MIX -> context.getString(R.string.rws_channel_mix)
            else -> context.getString(R.string.rws_channel_unknown)
        }
    }

}