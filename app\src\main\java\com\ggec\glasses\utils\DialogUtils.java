package com.ggec.glasses.utils;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;

import com.ggec.glasses.R;

/**
 * 对话框工具类，提供创建各种对话框的方法
 */
public class DialogUtils {

    /**
     * 创建确认对话框（使用自定义布局）
     * 
     * @param context 上下文
     * @param title 标题
     * @param message 消息内容
     * @param positiveText 确认按钮文本
     * @param negativeText 取消按钮文本
     * @param positiveListener 确认按钮点击监听器
     * @param negativeListener 取消按钮点击监听器
     * @return Dialog对象
     */
    public static Dialog createCustomConfirmDialog(
            @NonNull Context context,
            @NonNull String title,
            @NonNull String message,
            @NonNull String positiveText,
            @NonNull String negativeText,
            @Nullable DialogInterface.OnClickListener positiveListener,
            @Nullable DialogInterface.OnClickListener negativeListener) {
        
        // 创建对话框
        Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        
        // 设置自定义布局
        View dialogView = LayoutInflater.from(context).inflate(R.layout.layout_custom_dialog, null);
        dialog.setContentView(dialogView);
        
        // 设置标题和消息
        TextView tvTitle = dialogView.findViewById(R.id.dialog_title);
        TextView tvMessage = dialogView.findViewById(R.id.dialog_message);
        Button btnPositive = dialogView.findViewById(R.id.btn_positive);
        Button btnNegative = dialogView.findViewById(R.id.btn_negative);
        
        tvTitle.setText(title);
        tvMessage.setText(message);
        btnPositive.setText(positiveText);
        btnNegative.setText(negativeText);
        
        // 设置按钮点击事件
        btnPositive.setOnClickListener(v -> {
            if (positiveListener != null) {
                positiveListener.onClick(dialog, DialogInterface.BUTTON_POSITIVE);
            }
            dialog.dismiss();
        });
        
        btnNegative.setOnClickListener(v -> {
            if (negativeListener != null) {
                negativeListener.onClick(dialog, DialogInterface.BUTTON_NEGATIVE);
            }
            dialog.dismiss();
        });
        
        return dialog;
    }
    
    /**
     * 创建简化版自定义确认对话框
     * 
     * @param context 上下文
     * @param title 标题
     * @param message 消息内容
     * @param positiveListener 确认按钮点击监听器
     * @return Dialog对象
     */
    public static Dialog createCustomConfirmDialog(
            @NonNull Context context,
            @NonNull String title,
            @NonNull String message,
            @Nullable DialogInterface.OnClickListener positiveListener) {
        
        return createCustomConfirmDialog(context, title, message, "确定", "取消", positiveListener, null);
    }
    
    /**
     * 显示自定义确认对话框
     * 
     * @param context 上下文
     * @param title 标题
     * @param message 消息内容
     * @param positiveText 确认按钮文本
     * @param negativeText 取消按钮文本
     * @param positiveListener 确认按钮点击监听器
     * @param negativeListener 取消按钮点击监听器
     */
    public static void showConfirmDialog(
            @NonNull Context context,
            @NonNull String title,
            @NonNull String message,
            @NonNull String positiveText,
            @NonNull String negativeText,
            @Nullable DialogInterface.OnClickListener positiveListener,
            @Nullable DialogInterface.OnClickListener negativeListener) {
        
        createCustomConfirmDialog(context, title, message, positiveText, negativeText,
                positiveListener, negativeListener).show();
    }
    
    /**
     * 显示简化版确认对话框
     * 
     * @param context 上下文
     * @param title 标题
     * @param message 消息内容
     * @param positiveListener 确认按钮点击监听器
     */
    public static void showConfirmDialog(
            @NonNull Context context,
            @NonNull String title,
            @NonNull String message,
            @Nullable DialogInterface.OnClickListener positiveListener) {
        
        createCustomConfirmDialog(context, title, message, positiveListener).show();
    }
    
    /**
     * 创建确认对话框（使用系统AlertDialog）
     * 已废弃，建议使用自定义对话框
     * 
     * @param context 上下文
     * @param title 标题
     * @param message 消息内容
     * @param positiveText 确认按钮文本
     * @param negativeText 取消按钮文本
     * @param positiveListener 确认按钮点击监听器
     * @param negativeListener 取消按钮点击监听器
     * @return AlertDialog对象
     */
    @Deprecated
    public static AlertDialog createAlertDialog(
            @NonNull Context context,
            @NonNull String title,
            @NonNull String message,
            @NonNull String positiveText,
            @NonNull String negativeText,
            @Nullable DialogInterface.OnClickListener positiveListener,
            @Nullable DialogInterface.OnClickListener negativeListener) {
        
        return new AlertDialog.Builder(context, R.style.AppAlertDialogStyle)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton(positiveText, positiveListener)
                .setNegativeButton(negativeText, negativeListener)
                .create();
    }
} 