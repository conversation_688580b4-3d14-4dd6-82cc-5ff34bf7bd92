package com.ggec.glasses.album.util;

/**
 * 时间格式化工具类
 * 提供将毫秒转换为友好格式的方法
 */
public class TimeFormatUtil {
    
    /**
     * 将毫秒转换为时:分:秒或分:秒格式的字符串
     * 对于超过1小时的视频，显示格式为HH:MM:SS
     * 对于不到1小时的视频，显示格式为MM:SS
     * 
     * @param milliseconds 毫秒数
     * @return 格式化后的时间字符串
     */
    public static String formatDuration(long milliseconds) {
        if (milliseconds <= 0) {
            return "00:00";
        }
        
        long totalSeconds = milliseconds / 1000;
        long hours = totalSeconds / 3600;
        long minutes = (totalSeconds % 3600) / 60;
        long seconds = totalSeconds % 60;
        
        if (hours > 0) {
            // 超过1小时，格式为HH:MM:SS
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        } else {
            // 不到1小时，格式为MM:SS
            return String.format("%02d:%02d", minutes, seconds);
        }
    }
} 