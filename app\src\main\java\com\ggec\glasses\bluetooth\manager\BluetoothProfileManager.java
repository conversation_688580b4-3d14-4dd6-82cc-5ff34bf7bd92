package com.ggec.glasses.bluetooth.manager;

import android.Manifest;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 蓝牙配置文件管理器
 * 负责管理蓝牙配置文件的连接和状态
 */
public class BluetoothProfileManager {
    
    private static final String TAG = "BluetoothProfileManager";
    
    // 存储不同类型的蓝牙配置文件代理
    private final Map<Integer, BluetoothProfile> profiles = new HashMap<>();
    
    // 支持的蓝牙配置文件类型
    private static final int[] SUPPORTED_PROFILES = {
            BluetoothProfile.HEADSET,  // 耳机配置文件
            BluetoothProfile.A2DP,     // 高级音频分发配置文件
            BluetoothProfile.HID_DEVICE, // HID设备配置文件
    };
    
    private final Context context;
    private final android.bluetooth.BluetoothAdapter nativeAdapter;
    
    // 蓝牙配置文件服务监听器
    private final BluetoothProfile.ServiceListener profileListener = new BluetoothProfile.ServiceListener() {
        @Override
        public void onServiceConnected(int profile, BluetoothProfile proxy) {
            Log.d(TAG, "蓝牙配置文件服务连接成功: " + profileName(profile));
            synchronized (profiles) {
                profiles.put(profile, proxy);
            }
        }

        @Override
        public void onServiceDisconnected(int profile) {
            Log.d(TAG, "蓝牙配置文件服务断开连接: " + profileName(profile));
            synchronized (profiles) {
                profiles.remove(profile);
            }
        }
    };
    
    /**
     * 构造函数
     * @param context 上下文
     * @param nativeAdapter 原生蓝牙适配器
     */
    public BluetoothProfileManager(Context context, android.bluetooth.BluetoothAdapter nativeAdapter) {
        this.context = context.getApplicationContext();
        this.nativeAdapter = nativeAdapter;
        
        // 初始化蓝牙配置文件
        initProfiles();
    }
    
    /**
     * 初始化蓝牙配置文件
     */
    private void initProfiles() {
        if (nativeAdapter == null) {
            return;
        }
        
        for (int profile : SUPPORTED_PROFILES) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                        Log.w(TAG, "初始化蓝牙配置文件失败：缺少BLUETOOTH_CONNECT权限");
                        continue;
                    }
                }
                boolean result = nativeAdapter.getProfileProxy(context, profileListener, profile);
                if (result) {
                    Log.d(TAG, "开始获取蓝牙配置文件代理: " + profileName(profile));
                } else {
                    Log.e(TAG, "获取蓝牙配置文件代理失败: " + profileName(profile));
                }
            } catch (SecurityException e) {
                Log.e(TAG, "获取蓝牙配置文件代理时出现安全异常: " + profileName(profile), e);
            } catch (IllegalArgumentException e) {
                Log.e(TAG, "获取蓝牙配置文件代理时参数异常: " + profileName(profile), e);
            }
        }
    }
    
    /**
     * 释放蓝牙配置文件资源
     */
    public void releaseProfiles() {
        synchronized (profiles) {
            for (Map.Entry<Integer, BluetoothProfile> entry : profiles.entrySet()) {
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                            continue;
                        }
                    }
                    nativeAdapter.closeProfileProxy(entry.getKey(), entry.getValue());
                } catch (SecurityException e) {
                    Log.e(TAG, "关闭蓝牙配置文件代理时出现安全异常: " + profileName(entry.getKey()), e);
                }
            }
            profiles.clear();
        }
    }
    
    /**
     * 获取配置文件名称
     * @param profile 配置文件类型
     * @return 配置文件名称
     */
    public String profileName(int profile) {
        switch (profile) {
            case BluetoothProfile.HEADSET:
                return "HEADSET";
            case BluetoothProfile.A2DP:
                return "A2DP";
            case BluetoothProfile.HID_DEVICE:
                return "HID_DEVICE";
            default:
                return "UNKNOWN(" + profile + ")";
        }
    }
    
    /**
     * 检查是否至少有一个Profile已连接
     * @return 如果任一Profile已连接则返回true，否则返回false
     */
    public boolean isAnyProfileConnected() {
        if (nativeAdapter == null || !nativeAdapter.isEnabled()) {
            return false;
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    Log.e(TAG, "检查Profile连接状态失败：缺少BLUETOOTH_CONNECT权限");
                    return false;
                }
            }
            
            synchronized (profiles) {
                for (BluetoothProfile profile : profiles.values()) {
                    List<BluetoothDevice> connectedDevices = profile.getConnectedDevices();
                    if (!connectedDevices.isEmpty()) {
                        return true;
                    }
                }
            }
        } catch (SecurityException e) {
            Log.e(TAG, "检查Profile连接状态时出现安全异常", e);
        }
        
        return false;
    }
    
    /**
     * 获取所有已连接的设备
     * @return 已连接设备列表
     */
    public Map<Integer, BluetoothProfile> getProfiles() {
        synchronized (profiles) {
            return new HashMap<>(profiles);
        }
    }
    
    /**
     * 获取指定类型的蓝牙配置文件
     * @param profileType 配置文件类型
     * @return 配置文件，如果不存在则返回null
     */
    public BluetoothProfile getProfile(int profileType) {
        synchronized (profiles) {
            return profiles.get(profileType);
        }
    }
} 