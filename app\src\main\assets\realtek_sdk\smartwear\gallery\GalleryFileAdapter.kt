/*
 * Copyright (c) 2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.sample.audioconnect.smartwear.gallery

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.net.toUri
import androidx.recyclerview.widget.RecyclerView
import com.realsil.sample.audioconnect.smartwear.R
import com.realsil.sdk.support.recyclerview.BaseRecyclerViewAdapter

/**
 * <AUTHOR>
 * @date 2025/03/26
 */
class GalleryFileAdapter(context: Context, list: ArrayList<LocaleMediaFile>) :
    BaseRecyclerViewAdapter<LocaleMediaFile, GalleryFileAdapter.SubFileViewHolder>(context, list) {

    private var subFileAdapterListener: SubFileAdapterListener? = null
    fun registerSubFileAdapterListener(listener: SubFileAdapterListener) {
        this.subFileAdapterListener = listener
    }

    var selectedItems: ArrayList<LocaleMediaFile> = ArrayList()

    fun setData(dataList: ArrayList<LocaleMediaFile>) {
        setEntityList(dataList)
        selectedItems.clear()
        subFileAdapterListener?.onDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SubFileViewHolder {
        return SubFileViewHolder(
            layoutInflater.inflate(
                R.layout.item_view_gallery_file,
                parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: SubFileViewHolder, position: Int) {
        val subFileInfo = getEntity(position) ?: return
        holder.subFileInfo = subFileInfo

        if (subFileInfo.type == FileType.IMAGE) {
            holder.ivCover.setImageURI(subFileInfo.originFile.toUri())
        } else {
            if (subFileInfo.thumbnail != null) {
                holder.ivCover.setImageBitmap(subFileInfo.thumbnail)
            } else{
                holder.ivCover.setImageResource(R.mipmap.ic_launcher)
            }
        }
    }

    inner class SubFileViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivCover: AppCompatImageView
        var subFileInfo: LocaleMediaFile? = null

        init {
            ivCover = itemView.findViewById(R.id.ivCover)
            itemView.setOnClickListener {
                subFileInfo?.let { it1 -> subFileAdapterListener?.onItemClick(getMediaListUri(), subFileInfo!!) }
            }
        }
    }

    fun getMediaListUri():ArrayList<String> {
        val uriList = ArrayList<String>()
        for (item in itemEntities) {
            uriList.add(item.uriString)
        }
        return uriList
    }
}

abstract class SubFileAdapterListener {
    open fun onDataSetChanged() {}
    open fun onItemClick(uriList: ArrayList<String>, file: LocaleMediaFile){}
}
