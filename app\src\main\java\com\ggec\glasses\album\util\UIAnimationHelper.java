package com.ggec.glasses.album.util;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.view.View;
import android.view.Window;
import android.view.WindowInsetsController;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.ggec.glasses.R;

/**
 * UI动画帮助类，封装了媒体展示界面中的所有动画逻辑
 */
public class UIAnimationHelper {
    private final Context context;
    private final View rootView;
    private final int animationDuration = 300; // 动画持续时间
    private final ConstraintLayout backgroundLayer;
    private int originalBackgroundColor;
    
    // 跟踪状态栏图标颜色是否已经切换
    private boolean colorSwitched = false;
    
    /**
     * 构造函数
     * @param context 上下文
     * @param rootView 根视图
     */
    public UIAnimationHelper(@NonNull Context context, @NonNull View rootView) {
        this.context = context;
        this.rootView = rootView;
        this.backgroundLayer = rootView.findViewById(R.id.background_layer);
        this.originalBackgroundColor = ContextCompat.getColor(context, R.color.comp_background_gray);
    }
    
    /**
     * 设置原始背景颜色
     * @param color 颜色值
     */
    public void setOriginalBackgroundColor(int color) {
        this.originalBackgroundColor = color;
    }
    
    /**
     * 动画过渡背景颜色
     * @param fromColor 起始颜色
     * @param toColor 目标颜色
     * @param statusBarBackground 状态栏背景视图（可选）
     * @param headerLayout 头部布局（可选）
     */
    public void animateBackgroundColor(int fromColor, int toColor, View statusBarBackground, View headerLayout) {
        if (backgroundLayer == null) return;
        
        ValueAnimator colorAnimation = ValueAnimator.ofObject(new ArgbEvaluator(), fromColor, toColor);
        colorAnimation.setDuration(animationDuration);
        colorAnimation.addUpdateListener(animator -> {
            int animatedColor = (int) animator.getAnimatedValue();
            // 更新主背景层颜色
            backgroundLayer.setBackgroundColor(animatedColor);
            
            // 同步更新状态栏背景颜色和头部布局颜色（如果提供）
            if (statusBarBackground != null && statusBarBackground.getVisibility() == View.VISIBLE) {
                statusBarBackground.setBackgroundColor(animatedColor);
            }
            
            if (headerLayout != null && headerLayout.getVisibility() == View.VISIBLE) {
                headerLayout.setBackgroundColor(animatedColor);
            }
        });
        colorAnimation.start();
    }
    
    /**
     * 动画过渡状态栏颜色
     * @param window 窗口对象
     * @param fromColor 起始颜色
     * @param toColor 目标颜色
     * @param fromLight 开始是否为浅色状态栏
     * @param toLight 结束是否为浅色状态栏
     */
    public void animateStatusBarColor(Window window, int fromColor, int toColor, boolean fromLight, boolean toLight) {
        if (window == null) return;
        
        ValueAnimator colorAnimation = ValueAnimator.ofObject(new ArgbEvaluator(), fromColor, toColor);
        colorAnimation.setDuration(animationDuration);
        
        // 如果开始和结束状态的亮度不同，在中间点切换状态栏图标颜色
        final boolean lightChanged = fromLight != toLight;
        
        colorAnimation.addUpdateListener(animator -> {
            int animatedColor = (int) animator.getAnimatedValue();
            window.setStatusBarColor(animatedColor);
            
            // 当过渡到一半时改变状态栏图标颜色
            if (lightChanged && animator.getAnimatedFraction() > 0.5f && !colorSwitched) {
                setStatusBarIconColor(window, toLight);
                colorSwitched = true;
            }
        });
        
        colorAnimation.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                // 如果不需要改变状态栏图标颜色，则在开始时直接设置
                if (!lightChanged) {
                    setStatusBarIconColor(window, toLight);
                }
                colorSwitched = false;
            }
            
            @Override
            public void onAnimationEnd(Animator animation) {
                // 确保动画结束后状态栏图标颜色正确设置
                setStatusBarIconColor(window, toLight);
                colorSwitched = false;
            }
        });
        
        colorAnimation.start();
    }
    
    /**
     * 设置状态栏图标颜色
     * @param window 窗口对象
     * @param isLightStatusBar 是否使用浅色状态栏（深色图标）
     */
    public void setStatusBarIconColor(Window window, boolean isLightStatusBar) {
        if (window == null) return;
        
        // 根据Android版本设置状态栏图标颜色
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11及以上版本使用WindowInsetsController
            WindowInsetsController insetsController = window.getInsetsController();
            if (insetsController != null) {
                if (isLightStatusBar) {
                    // 浅色状态栏，深色图标
                    insetsController.setSystemBarsAppearance(
                            WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS,
                            WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS);
                } else {
                    // 深色状态栏，浅色图标
                    insetsController.setSystemBarsAppearance(
                            0,
                            WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS);
                }
            }
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0-10.0
            View decorView = window.getDecorView();
            int flags = decorView.getSystemUiVisibility();
            if (isLightStatusBar) {
                // 浅色状态栏，深色图标
                flags |= View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
            } else {
                // 深色状态栏，浅色图标
                flags &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
            }
            decorView.setSystemUiVisibility(flags);
        }
    }
    
    /**
     * 动画显示/隐藏操作层
     * @param show 是否显示
     * @param topContainer 顶部容器
     * @param footerLayout 底部工具栏
     * @param animateBackground 是否执行背景颜色动画 (新加参数)
     */
    public void animateOperationLayer(boolean show, View topContainer, View footerLayout, boolean animateBackground) {
        if (show) {
            showOperationLayer(topContainer, footerLayout, animateBackground); // 传递参数
        } else {
            // 隐藏时总是执行背景动画
            hideOperationLayer(topContainer, footerLayout);
        }
    }

    // 重载一个默认行为的方法，保持向后兼容（可选，但建议）
    public void animateOperationLayer(boolean show, View topContainer, View footerLayout) {
        animateOperationLayer(show, topContainer, footerLayout, true); // 默认执行背景动画
    }
    
    /**
     * 显示操作层动画
     * @param topContainer 顶部容器
     * @param footerLayout 底部工具栏
     * @param animateBackground 是否执行背景颜色动画 (新加参数)
     */
    private void showOperationLayer(View topContainer, View footerLayout, boolean animateBackground) {
        if (topContainer == null || footerLayout == null) return;
        
        // 设置顶部组合容器初始位置和透明度
        topContainer.setAlpha(0f);
        topContainer.setTranslationY(-topContainer.getHeight());
        topContainer.setVisibility(View.VISIBLE);
        
        // 设置底部工具栏初始位置和透明度
        footerLayout.setAlpha(0f);
        footerLayout.setTranslationY(footerLayout.getHeight());
        footerLayout.setVisibility(View.VISIBLE);
        
        // 顶部组合容器向下平移动画
        topContainer.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(animationDuration)
                .setInterpolator(new DecelerateInterpolator())
                .setListener(null)
                .start();
        
        // 底部工具栏向上平移动画
        footerLayout.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(animationDuration)
                .setInterpolator(new DecelerateInterpolator())
                .setListener(null)
                .start();
        
        // 根据参数决定是否执行背景颜色动画
        if (animateBackground) {
            // 背景颜色从黑色渐变回原始颜色
            animateBackgroundColor(Color.BLACK, originalBackgroundColor, null, null);
        } else {
            // 如果不执行动画，直接设置背景色为原始颜色，确保初始状态正确
            if (backgroundLayer != null) {
                 backgroundLayer.setBackgroundColor(originalBackgroundColor);
            }
        }
    }
    
    /**
     * 隐藏操作层动画
     * @param topContainer 顶部容器
     * @param footerLayout 底部工具栏
     */
    private void hideOperationLayer(View topContainer, View footerLayout) {
        if (topContainer == null || footerLayout == null) return;
        
        // 顶部组合容器向上平移动画
        topContainer.animate()
                .alpha(0f)
                .translationY(-topContainer.getHeight())
                .setDuration(animationDuration)
                .setInterpolator(new AccelerateInterpolator())
                .setListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        topContainer.setVisibility(View.GONE);
                        // 动画结束后重置平移位置，为下次显示做准备
                        topContainer.setTranslationY(0f);
                    }
                }).start();
        
        // 底部工具栏向下平移动画
        footerLayout.animate()
                .alpha(0f)
                .translationY(footerLayout.getHeight())
                .setDuration(animationDuration)
                .setInterpolator(new AccelerateInterpolator())
                .setListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        footerLayout.setVisibility(View.GONE);
                        // 动画结束后重置平移位置，为下次显示做准备
                        footerLayout.setTranslationY(0f);
                    }
                }).start();
        
        // 背景颜色从原始颜色渐变为黑色
        animateBackgroundColor(originalBackgroundColor, Color.BLACK, null, null);
    }
} 