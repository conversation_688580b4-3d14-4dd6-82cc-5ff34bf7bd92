/*
 * Copyright (c) 2017-2024. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.sdk.audioconnect.support.sync

import android.content.Context
import com.realsil.sdk.audioconnect.repository.database.AudioConnectRoomDatabase
import com.realsil.sdk.audioconnect.repository.database.DeviceInfoRepository
import com.realsil.sdk.audioconnect.repository.database.general.ChargingCaseEntity
import com.realsil.sdk.audioconnect.repository.database.general.DeviceInfoEntity
import com.realsil.sdk.bbpro.core.peripheral.ConnectionParameters
import com.realsil.sdk.bbpro.model.DeviceInfo
import com.realsil.sdk.core.logger.ZLogger

/**
 * Device Sync Data Manager
 *
 * <AUTHOR>
 */
class DeviceInfoSyncManager private constructor(context: Context) {

    private lateinit var mContext: Context
    private lateinit var deviceInfoRepository: DeviceInfoRepository

    fun getPairDeviceInfos(): List<DeviceInfoEntity> {
        val deviceInfoEntityList: List<DeviceInfoEntity>? =
            AudioConnectRoomDatabase.Companion.getDatabase(mContext).deviceInfoDao().getPairDevices()
        if (deviceInfoEntityList == null) {
            ZLogger.v("no device exist in db, return null ")
            return ArrayList()
        }
        return deviceInfoEntityList
    }
    /**
     * create if not exist
     * */
    fun createNewDevice(transportChannel: Int = ConnectionParameters.CHANNEL_TYPE_SPP,
                        deviceAddress:String = "", deviceName:String = ""): DeviceInfoEntity {
        return deviceInfoRepository.createNewDevice(transportChannel=transportChannel,
            deviceAddress=deviceAddress,
            deviceName = deviceName)
    }

    /**
     * create if not exist
     * */
    fun getAudioConnectDevice(deviceAddress: String, deviceName:String = ""): DeviceInfoEntity {
        return deviceInfoRepository.getAudioConnectDevice(deviceAddress=deviceAddress, deviceName = deviceName)
    }

    fun updateDeviceLanguage(deviceAddress: String, activeLanguage: Int, supportedLanguages: Int) {
        deviceInfoRepository.updateDeviceLanguage(deviceAddress, activeLanguage, supportedLanguages)
    }

    fun updateSyncOtaData(deviceAddress:String, syncData:Boolean) {
        AudioConnectRoomDatabase.getDatabase(mContext).updateSyncOtaData(deviceAddress, syncData)
    }

    fun updateDeviceId(deviceAddress:String, deviceId: String) {
        AudioConnectRoomDatabase.getDatabase(mContext).updateDeviceId(deviceAddress, deviceId)
    }

    fun updateSingleDeviceId(
        deviceAddress: String,
        lchSingleDeviceId: String,
        rchSingleDeviceId: String
    ) {
        AudioConnectRoomDatabase.getDatabase(mContext).updateSingleDeviceId(deviceAddress, lchSingleDeviceId, rchSingleDeviceId)
    }
    fun updateSpeakerMode(
        deviceAddress: String,mode:Int
    ) {
        AudioConnectRoomDatabase.getDatabase(mContext).updateSpeakerMode(deviceAddress, mode)
    }

    fun getChargingCaseEntity(headsetDeviceAddress: String): ChargingCaseEntity {
        return deviceInfoRepository.getChargingCaseEntity(headsetDeviceAddress)
    }

    companion object {
        @Volatile
        private var mInstance: DeviceInfoSyncManager? = null

        /**
         * get instance of [DeviceInfoSyncManager]
         *
         * @param context Application Context
         * @return [DeviceInfoSyncManager]
         */
        fun getInstance(context: Context): DeviceInfoSyncManager {
            if (mInstance == null) {
                synchronized(DeviceInfoSyncManager::class.java) {
                    if (mInstance == null) {
                        mInstance = DeviceInfoSyncManager(context)
                    }
                }
            }
            return mInstance!!
        }
    }

    init {
        mContext = context.applicationContext
        deviceInfoRepository = DeviceInfoRepository(mContext)
    }
}