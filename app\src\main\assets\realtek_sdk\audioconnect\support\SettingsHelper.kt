/*
 * Copyright (c) 2017-2023. Realtek Semiconductor Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.realsil.sdk.audioconnect.support

import android.content.Context
import android.text.TextUtils
import com.realsil.sdk.bbpro.core.peripheral.ConnectionParameters
import com.realsil.sdk.core.bluetooth.scanner.ScannerParams
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.support.preference.BaseSharedPrefes
import com.realsil.sdk.support.scanner.RtkBtSettings.Companion.KEY_BT_SCAN_MECHANISM


/**
 * <AUTHOR>
 * @date 13/08/2017
 */

class SettingsHelper private constructor(context: Context) : BaseSharedPrefes(context) {

    fun getLastActiveDevice(): String {
        return getString(KEY_FASTPAIR_LAST_ACTIVE_DEVICE, "")
    }

    fun setLastActiveDevice(address: String) {
        set(KEY_FASTPAIR_LAST_ACTIVE_DEVICE, address)
    }

    fun getLastActiveDeviceName(): String {
        return getString(KEY_FASTPAIR_LAST_ACTIVE_DEVICE_NAME, "")
    }

    fun setLastActiveDeviceName(name: String) {
        set(KEY_FASTPAIR_LAST_ACTIVE_DEVICE_NAME, name)
    }

    val productType: Int
        get() {
            val value = getString(KEY_FASTPAIR_PRODUCT, "")
            if (TextUtils.isEmpty(value)) {
                set(KEY_FASTPAIR_PRODUCT, java.lang.String.valueOf(1))
                return 3
            } else {
                return Integer.parseInt(value)
            }
        }

    val chipType: Int
        get() {
            val value = getString(KEY_FASTPAIR_CHIP_TYPE, "")
            if (TextUtils.isEmpty(value)) {
                set(KEY_FASTPAIR_CHIP_TYPE, java.lang.String.valueOf(3))
                return 3
            } else {
                return Integer.parseInt(value)
            }
        }

    val isCaseEnabled: Boolean
        get() {
            if (!contains(KEY_CASE)) {
                set(KEY_CASE, true)
                return true
            }

            return getBoolean(KEY_CASE, true)
        }

    val isFastpairEnabled: Boolean
        get() {
            if (!contains(KEY_TOGGLE_FASTPAIR)) {
                set(KEY_TOGGLE_FASTPAIR, false)
                return false
            }

            return getBoolean(KEY_TOGGLE_FASTPAIR, false)
        }

    val isEngagedAdv: Boolean
        get() {
            if (!contains(KEY_ENGAGED_ADV)) {
                set(KEY_ENGAGED_ADV, true)
                return true
            }

            return getBoolean(KEY_ENGAGED_ADV, true)
        }

    val isA2dpEnabled: Boolean
        get() {
            if (!contains(KEY_A2DP)) {
                set(KEY_A2DP, false)
                return false
            }

            return getBoolean(KEY_A2DP, false)
        }

    val isHfpEnabled: Boolean
        get() {
            if (!contains(KEY_HFP)) {
                set(KEY_HFP, true)
                return true
            }

            return getBoolean(KEY_HFP, true)
        }

    val scanRssiFilter: Int
        get() {
            val value = getString(KEY_FASTPAIR_SCAN_FILTER_RSSI, "")
            if (TextUtils.isEmpty(value)) {
                set(KEY_FASTPAIR_SCAN_FILTER_RSSI, java.lang.String.valueOf(-1000))
                return -1000
            } else {
                return Integer.parseInt(value)
            }
        }

    val isDfuDebugEnabled: Boolean
        get() {
            if (!contains(KEY_DEBUG)) {
                set(KEY_DEBUG, false)
                return false
            }

            return getBoolean(KEY_DEBUG, false)
        }

    val dfuDebugLevel: Int
        get() {
            val value = getString(KEY_DEBUG_LEVEL, "")
            if (TextUtils.isEmpty(value)) {
                set(KEY_DEBUG_LEVEL, ZLogger.VERBOSE.toString())
                return ZLogger.VERBOSE
            } else {
                return Integer.parseInt(value)
            }
        }

    var rssiMonitorMode: Int
        get() {
            return getInt(KEY_RSSI_MONITOR_MODE, 0)
        }
        set(value) {
            set(KEY_RSSI_MONITOR_MODE, value)
        }

    var rssiMonitorLinkType: Int
        get() {
            return getInt(KEY_RSSI_MONITOR_TYPE, 1)
        }
        set(value) {
            set(KEY_RSSI_MONITOR_TYPE, value)
        }
    var rssiMonitorPeriod: Int
        get() {
            return getInt(KEY_RSSI_MONITOR_PERIOD, 50)
        }
        set(value) {
            set(KEY_RSSI_MONITOR_PERIOD, value)
        }
    var rssiMonitorInterval: Int
        get() {
            return getInt(KEY_RSSI_MONITOR_INTERVAL, 2)
        }
        set(value) {
            set(KEY_RSSI_MONITOR_INTERVAL, value)
        }

    val connectionPreferredTransport: Int
        get() {
            val value: String = getString(
                KEY_CONNECTION_PREFERRED_TRANSPORT,
                ""
            )
            if (TextUtils.isEmpty(value)) {
                set(
                    KEY_CONNECTION_PREFERRED_TRANSPORT,
                    ConnectionParameters.CHANNEL_TYPE_SPP.toString()
                )
                return  ConnectionParameters.CHANNEL_TYPE_SPP
            } else {
                return value.toInt()
            }
        }

    init {
        ZLogger.v(
            "isDfuDebugEnabled:$isDfuDebugEnabled\n" +
                    "isEngagedAdv:$isEngagedAdv\n" +
                    "isA2dpEnabled:$isA2dpEnabled\n" +
                    "isHfpEnabled:$isHfpEnabled\n" +
                    "getDfuDebugLevel:$dfuDebugLevel\n" +
                    "isCaseEnabled:$isCaseEnabled\n" +
                    "isFastpairEnabled:$isFastpairEnabled\n" +
                    "productType:$productType\n" +
                    "chipType:$chipType,connectionPreferredTransport=$connectionPreferredTransport\n" +
                    "scanRssiFilter:$scanRssiFilter" +
                    "lastActiveDevice:${getLastActiveDevice()}/${getLastActiveDeviceName()}"
        )
    }

    companion object {
        const val KEY_CONNECTION_PREFERRED_TRANSPORT = "KEY_CONNECTION_PREFERRED_TRANSPORT"
        const val KEY_RSSI_MONITOR_MODE = "KEY_RSSI_MONITOR_MODE"
        const val KEY_RSSI_MONITOR_TYPE = "KEY_RSSI_MONITOR_TYPE"
        const val KEY_RSSI_MONITOR_PERIOD = "KEY_RSSI_MONITOR_PERIOD"
        const val KEY_RSSI_MONITOR_INTERVAL = "KEY_RSSI_MONITOR_INTERVAL"

        const val KEY_FASTPAIR_LAST_ACTIVE_DEVICE = "KEY_FASTPAIR_LAST_ACTIVE_DEVICE"
        const val KEY_FASTPAIR_LAST_ACTIVE_DEVICE_NAME = "KEY_FASTPAIR_LAST_ACTIVE_DEVICE_NAME"

        @JvmField
        val KEY_FASTPAIR_PRODUCT = "KEY_FASTPAIR_PRODUCT"

        @JvmField
        val KEY_FASTPAIR_CHIP_TYPE = "KEY_FASTPAIR_CHIP_TYPE"

        @JvmField
        val KEY_CASE = "switch_case"

        @JvmField
        val KEY_ENGAGED_ADV = "switch_engaged_adv"

        @JvmField
        val KEY_A2DP = "switch_a2dp"

        @JvmField
        val KEY_HFP = "switch_hfp"

        @JvmField
        val KEY_TOGGLE_FASTPAIR = "switch_toggle_fastpair"

        @JvmField
        val KEY_FASTPAIR_SCAN_FILTER_RSSI = "KEY_FASTPAIR_SCAN_FILTER_RSSI"

        @JvmField
        val KEY_DEBUG = "switch_debug"

        @JvmField
        val KEY_DEBUG_LEVEL = "rtk_debug_level"


        @Volatile
        private var instance: SettingsHelper? = null

        fun initialize(context: Context) {
            if (instance == null) {
                synchronized(SettingsHelper::class.java) {
                    if (instance == null) {
                        instance = SettingsHelper(context.applicationContext)
                    }
                }
            }
        }

        fun getInstance(): SettingsHelper? {
            if (instance == null) {
                ZLogger.w("not initialized, please call initialize(Context context) first")
            }
            return instance
        }

    }

}
