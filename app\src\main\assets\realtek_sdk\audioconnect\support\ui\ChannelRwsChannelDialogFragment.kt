/*
 * Copyright (c) 2017-2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */
package com.realsil.sdk.audioconnect.support.ui

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.realsil.sdk.audioconnect.support.AudioConnectHelper.parseRwsChannel
import com.realsil.sdk.audioconnect.support.R
import com.realsil.sdk.bbpro.model.RwsInfo

/**
 * <AUTHOR>
 * @date 2018/ic_eq_bass_booster/ic_eq_piano
 */
class ChannelRwsChannelDialogFragment : DialogFragment() {
    private var mListener: OnDialogListener? = null
    private var tvPrimaryChannel: TextView? = null
    private var tvSecondaryChannel: TextView? = null
    private var rwsInfo: RwsInfo? = null

    interface OnDialogListener {
        /**
         * @param channel
         */
        fun onListViewItemClick(channel: Int)
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }


    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialogView =
            layoutInflater.inflate(R.layout.rtk_audioconnect_dialogview_change_rws_channel, null)
        tvPrimaryChannel = dialogView.findViewById(R.id.tvPrimaryChannel)
        tvSecondaryChannel = dialogView.findViewById(R.id.tvSecondaryChannel)

        tvPrimaryChannel!!.setText(parseRwsChannel(context, rwsInfo!!.leftActiveChannel))
        tvSecondaryChannel!!.setText(parseRwsChannel(context, rwsInfo!!.rightActiveChannel))

        val builder = MaterialAlertDialogBuilder(requireActivity(), R.style.RtkAlertDialogTheme)

        return builder.setView(dialogView).setTitle(R.string.title_change_rws_channel)
            .setPositiveButton(
                R.string.button_change_rws_channel
            ) { dialog1: DialogInterface?, which: Int ->
                if (mListener != null) {
                    mListener!!.onListViewItemClick(which)
                }
            }
            .setNegativeButton(
                R.string.rtk_cancel
            ) { dialog1: DialogInterface?, which: Int -> }.create()
    }

    companion object {
        const val TAG: String = "ChannelRwsChannelDialogFragment"
        fun getInstance(
            args: Bundle?,
            rwsInfo: RwsInfo?,
            listener: OnDialogListener?
        ): ChannelRwsChannelDialogFragment {
            val fragment = ChannelRwsChannelDialogFragment()

            if (args != null) {
                fragment.arguments = args
            }
            fragment.rwsInfo = rwsInfo
            fragment.mListener = listener
            return fragment
        }
    }
}
