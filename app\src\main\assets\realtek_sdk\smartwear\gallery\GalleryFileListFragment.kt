/*
 * Copyright (c) 2025 Realsil.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Authors: <AUTHORS>
 */

package com.realsil.sample.audioconnect.smartwear.gallery

import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.realsil.sample.audioconnect.smartwear.databinding.FragmentGalleryBinding
import com.realsil.sample.audioconnect.smartwear.view.GridSpaceItemDecoration
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.support.base.BaseViewBindingFragment
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 * <AUTHOR>
 * @date 2025/03/26
 */
class GalleryFileListFragment : BaseViewBindingFragment<FragmentGalleryBinding>(FragmentGalleryBinding::inflate) {

    private val galleryViewModel: GalleryViewModel by activityViewModels()

    private var mRecyclerViewAdapter: GalleryFileAdapter? = null
    private var fileType: Int = FileType.IMAGE


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initRecyclerView()
        galleryViewModel.galleryFileListLiveData.observe(viewLifecycleOwner){
            if (galleryViewModel.fileType == fileType) {
                ZLogger.v("reload files,fileType=$fileType")
                mRecyclerViewAdapter?.setData(galleryViewModel.getFileListByType(fileType))
            }
        }
    }

    private fun initRecyclerView() {
        val gridLayoutManager = GridLayoutManager(requireContext(), 2)
        gridLayoutManager.orientation = LinearLayoutManager.VERTICAL
        binding.rvGallery.layoutManager = gridLayoutManager
        binding.rvGallery.addItemDecoration(GridSpaceItemDecoration(requireContext(), 2, 8, 8))
        mRecyclerViewAdapter = GalleryFileAdapter(requireContext(), ArrayList())
        mRecyclerViewAdapter!!.registerSubFileAdapterListener(object: SubFileAdapterListener() {
            override fun onItemClick(uriList: ArrayList<String>, file: LocaleMediaFile) {
                super.onItemClick(uriList, file)
                GalleryUtils.viewFile(requireContext(), file)
            }
        })

        binding.rvGallery.setEmptyView(binding.tvDownloadMedia)
        binding.rvGallery.adapter = mRecyclerViewAdapter
    }

    override fun onResume() {
        super.onResume()
        ZLogger.v("onResume, fileType=$fileType")
        showProgressBar("loading...")
        CoroutineScope(Dispatchers.IO).launch {
            galleryViewModel.changeFileType(fileType)
            launch(Dispatchers.Main) {
                cancelProgressBar()
            }
        }
    }

    companion object {
        const val TAG = "GalleryFileListFragment"

        @JvmStatic
        fun getInstance(args: Bundle = Bundle(), fileType: Int): GalleryFileListFragment {
            ZLogger.v("GalleryFileListFragment.getInstance(fileType=$fileType)")
            val fragment = GalleryFileListFragment()
            fragment.arguments = args
            fragment.fileType = fileType
            return fragment
        }
    }
}