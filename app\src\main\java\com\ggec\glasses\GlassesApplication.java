package com.ggec.glasses;

import android.app.Application;
import android.os.Build;
import android.util.Log;

import com.ggec.glasses.album.data.db.MediaDatabase;
import com.ggec.glasses.album.data.manager.MediaFileManager;
import com.ggec.glasses.album.util.VideoFrameExtractor;
import com.ggec.glasses.bluetooth.adapter.GlassesBluetoothAdapter;
import com.ggec.glasses.bluetooth.manager.BluetoothManager;
import com.ggec.glasses.device.manager.DeviceManager;

import java.io.File;
import java.util.concurrent.Executors;

/**
 * 应用程序类，用于初始化全局组件
 */
public class GlassesApplication extends Application {
    
    private static final String TAG = "GlassesApplication";
    
    // 媒体文件管理器实例
    private static MediaFileManager mediaFileManager;
    
    // 设备管理器实例
    private static DeviceManager deviceManager;
    
    // 蓝牙适配器实例
    private static GlassesBluetoothAdapter bluetoothAdapter;
    
    // 蓝牙管理器实例
    private static BluetoothManager bluetoothManager;
    
    /**
     * 获取全局媒体文件管理器实例
     * @return 媒体文件管理器
     */
    public static MediaFileManager getMediaFileManager() {
        return mediaFileManager;
    }
    
    /**
     * 获取全局设备管理器实例
     * @return 设备管理器
     */
    public static DeviceManager getDeviceManager() {
        return deviceManager;
    }
    
    /**
     * 获取全局蓝牙适配器实例
     * @return 蓝牙适配器
     */
    public static GlassesBluetoothAdapter getBluetoothAdapter() {
        return bluetoothAdapter;
    }
    
    /**
     * 获取全局蓝牙管理器实例
     * @return 蓝牙管理器
     */
    public static BluetoothManager getBluetoothManager() {
        return bluetoothManager;
    }
    
    @Override
    public void onCreate() {
        super.onCreate();
        
        // 初始化媒体文件管理器
        mediaFileManager = new MediaFileManager(this);
        
        // 初始化媒体数据库
        MediaDatabase.getInstance(this);
        
        // 初始化蓝牙适配器
        bluetoothAdapter = GlassesBluetoothAdapter.getInstance(this);
        
        // 初始化蓝牙管理器
        bluetoothManager = BluetoothManager.getInstance(this);
        bluetoothManager.registerBluetoothStateReceiver();
        
        // 初始化设备管理器
        deviceManager = DeviceManager.getInstance(this);
    }
    
    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        
        // 在内存不足时清理缓存
        if (level >= TRIM_MEMORY_RUNNING_LOW) {
            clearCache(false); // 仅清理临时文件，保留必要缓存
        }
    }
    
    @Override
    public void onLowMemory() {
        super.onLowMemory();
        
        // 在极低内存时清理缓存
        clearCache(true); // 清理所有可清理的缓存
    }
    
    @Override
    public void onTerminate() {
        // 应用终止时清理资源
        clearCache(false);
        
        // 销毁数据库实例
        MediaDatabase.destroyInstance();
        
        // 释放蓝牙资源
        if (bluetoothAdapter != null) {
            bluetoothAdapter.releaseProfiles();
        }
        
        // 注销蓝牙监听器
        if (bluetoothManager != null) {
            bluetoothManager.unregisterBluetoothStateReceiver();
        }
        
        super.onTerminate();
    }
    
    /**
     * 清理应用缓存
     * @param clearAll 是否清理所有缓存（包括Glide缓存）
     */
    public void clearCache(boolean clearAll) {
        Executors.newSingleThreadExecutor().execute(() -> {
            try {
                // 清理媒体文件临时目录
                if (mediaFileManager != null) {
                    mediaFileManager.cleanTempFiles();
                }
                
                // 清理视频帧提取缓存
                VideoFrameExtractor.clearCache(this);
                
                // 如果需要清理所有缓存，包括Glide缓存
                if (clearAll) {
                    File externalCacheDir = getExternalCacheDir();
                    if (externalCacheDir != null && externalCacheDir.exists()) {
                        deleteDirectory(externalCacheDir);
                    }
                }
                
                Log.d(TAG, "Cache cleaned successfully");
            } catch (Exception e) {
                Log.e(TAG, "Error cleaning cache", e);
            }
        });
    }
    
    /**
     * 递归删除目录中的所有文件
     * @param directory 要删除的目录
     * @return 是否删除成功
     */
    private boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
        }
        // 不删除目录本身，只清空内容
        return true;
    }
}
