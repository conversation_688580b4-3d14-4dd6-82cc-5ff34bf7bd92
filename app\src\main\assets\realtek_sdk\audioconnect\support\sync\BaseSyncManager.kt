/*
 * Copyright (c) 2023-2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */
package com.realsil.sdk.audioconnect.support.sync

import android.bluetooth.BluetoothProfile
import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.realsil.sdk.audioconnect.repository.RepositoryViewModel
import com.realsil.sdk.audioconnect.repository.database.AudioConnectRoomDatabase
import com.realsil.sdk.audioconnect.repository.database.DeviceInfoRepository
import com.realsil.sdk.audioconnect.repository.database.general.DeviceInfoEntity
import com.realsil.sdk.audioconnect.repository.database.keymap.KeyMapRepository
import com.realsil.sdk.bbpro.BumblebeeCallback
import com.realsil.sdk.bbpro.MultiPeripheralConnectionManager
import com.realsil.sdk.bbpro.PeripheralConnectionManager
import com.realsil.sdk.bbpro.core.BeeError
import com.realsil.sdk.bbpro.core.peripheral.Peripheral
import com.realsil.sdk.bbpro.model.DeviceInfo
import com.realsil.sdk.bbpro.model.KeyMmiSettings
import com.realsil.sdk.bbpro.multilink.MultiLinkInfo
import com.realsil.sdk.bbpro.vendor.VendorModelCallback
import com.realsil.sdk.core.bluetooth.BluetoothProfileManager
import com.realsil.sdk.core.bluetooth.connection.legacy.BluetoothSpp
import com.realsil.sdk.core.logger.ZLogger
import java.util.Locale
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 * @date 2023/04/21
 */
abstract class BaseSyncManager {
    protected var VDBG = false

    protected lateinit var mContext: Context

    @JvmField
    protected var deviceAddress = ""

    @JvmField
    protected var mBeeProManager: PeripheralConnectionManager? = null

    @JvmField
    var cacheMultilinkInfo: MultiLinkInfo? = null
    protected var mSyncCallback: SyncManagerCallback? = null

    @JvmField
    protected var mState = STATE_INIT

    val syncStateLiveData: MutableLiveData<Int> by lazy {
        MutableLiveData<Int>()
    }

    @JvmField
    protected val mStateLock = Any()
    private val syncExecutor: ThreadPoolExecutor?

    protected var manualSyncEnabled = false
    @JvmField
    protected var isSyncAborted = false
    open fun getTag(): String {
        return ""
    }

    var keyMapRepository: KeyMapRepository? = null
    lateinit var deviceInfoRepository: DeviceInfoRepository
    var audioConnectDeviceEntity: DeviceInfoEntity? = null

    var autoStartSyncEnabled = false

    val beeProManager: PeripheralConnectionManager
        get() {
            if (mBeeProManager == null) {
                ZLogger.v(getTag() + ": create PeripheralConnectionManager:$deviceAddress")
                mBeeProManager = MultiPeripheralConnectionManager.getInstance(mContext)
                    .getPeripheralConnectionManager(deviceAddress)
                mBeeProManager!!.addManagerCallback(mBeeProManagerCallback)
//                mBeeProManager!!.vendorClient.registerCallback(mVendorModelCallback)
                mBeeProManager!!.vendorClient!!.registerCallback(createVendorModelCallback())
            }
            return mBeeProManager!!
        }

    val isBusy: Boolean
        get() = mState and STATE_BUSY_MASK == STATE_BUSY_MASK

    fun isBusy(state: Int): Boolean {
        return if (this.mState and STATE_BUSY_MASK != STATE_BUSY_MASK) {
            false
        } else this.mState <= state
    }

    fun disconnect(): BeeError {
        return beeProManager.disconnect()
    }

    fun isDeviceConnected():Boolean {
        if (mBeeProManager == null) {
            ZLogger.v(getTag() + ", mBeeProManager not initialized")
            return false
        }
        if (!mBeeProManager!!.isConnected) {
            ZLogger.v(getTag() + ", connection(${mBeeProManager!!.deviceAddress}) disconnected")
            return false
        }
        return true
    }

    protected open fun innerCheck(): Boolean {
        if (!isDeviceConnected()) {
            isSyncAborted = true
            notifyStateChanged(STATE_DISCONNECTED, true)
            ZLogger.v(getTag() + "sync interrupted, because of (${deviceAddress}) not connected")
            return false
        }
        if (isSyncAborted) {
            ZLogger.v(getTag() + "sync aborted")
            return false
        }
        return true
    }

    fun syncDeviceInfo(message: String = "", state: Int, block: () -> BeeError): Boolean {
        if (!innerCheck()) {
            return false
        }
        ZLogger.i("sync $message start")
        notifyStateChanged(state, true)
        isNeedSyncLock = true
        val ret: BeeError = block()
        if (ret.code == BeeError.SUCCESS) {
            waitSyncAck()
        } else {
            isSyncAborted = true
            ZLogger.w("sync $message failed: " + ret.message)
            notifyStateChanged(STATE_DATA_SYNC_FAILED, true)
            return false
        }

        ZLogger.d("sync $message complete")
        return true
    }


    /**
     * @return
     */
    @Synchronized
    fun startSync(manualSync:Boolean = false): Boolean {
        if (syncExecutor == null) {
            ZLogger.d("syncExecutor is null")
            return false
        }

        synchronized(mStateLock) {
            if (mState and STATE_BUSY_MASK == STATE_BUSY_MASK) {
                ZLogger.v(String.format("already STATE_BUSY_MASK, state=0x%04X", mState))
                return true
            }
        }

        manualSyncEnabled = manualSync

        try {
            syncExecutor.execute(Runnable {
                sync()
            })
        } catch (e: Exception) {
            ZLogger.w(e.toString())
        }
        return true
    }

    fun stopSync() {
        ZLogger.d("stop Sync ...")
        isSyncAborted = true
        notifyStateChanged(STATE_DATA_SYNC_ABORTED, true)
    }

    open fun changePeripheral(address: String) {
        ZLogger.v(getTag() + " changePeripheral:$address")
        if (deviceAddress != address) {
            stopSync()

            if (mBeeProManager != null) {
                mBeeProManager!!.removeManagerCallback(mBeeProManagerCallback)
                mBeeProManager!!.unregisterVendorModelCallback(createVendorModelCallback())
                mBeeProManager = null
            }

            deviceAddress = address
        }
    }

    open fun destroy() {
        ZLogger.v(getTag() + " destroy")
        syncExecutor?.shutdown()
        stopSync()
        unregisterCallback()

        if (mBeeProManager != null) {
            mBeeProManager!!.removeManagerCallback(mBeeProManagerCallback)
            mBeeProManager!!.unregisterVendorModelCallback(createVendorModelCallback())
            mBeeProManager = null
        }
    }

    private var mSyncLock = Object()

    @JvmField
    protected var isNeedSyncLock = false
    protected fun waitSyncAck() {
        synchronized(mSyncLock) {
            if (isNeedSyncLock) {
                try {
                    mSyncLock.wait(SYNC_LOCK_WAIT_TIME.toLong())
                } catch (e: InterruptedException) {
                    ZLogger.w(e.message)
                }
            }
        }
    }

    protected fun notifySyncAck() {
        synchronized(mSyncLock) {
            isNeedSyncLock = false
            mSyncLock.notifyAll()
        }
    }

    fun registerCallback(callback: SyncManagerCallback?) {
        mSyncCallback = callback
    }

    fun unregisterCallback() {
        mSyncCallback = null
    }

    fun checkSyncState(state: Int): Boolean {
        return (mState and state) == state
    }

    protected fun notifyStateChanged(state: Int, notifyEnabled: Boolean) {
        synchronized(mStateLock) {
            if (state != mState) {
                ZLogger.v(String.format("%s-syncState 0x%04X > 0x%04X", getTag(), mState, state))
                mState = state
            }
        }
        if (notifyEnabled) {
            syncStateLiveData.postValue(mState)
            if (mSyncCallback != null) {
                mSyncCallback!!.onStateChanged(mState)
            }
        }
    }

    protected fun notifyDataChanged() {
        if (mSyncCallback != null) {
            mSyncCallback!!.onDataChanged()
        }
    }

    val deviceInfo: DeviceInfo
        get() = beeProManager.deviceInfo

    fun dumpDeviceInfo() {
        val deviceInfo = beeProManager.deviceInfo
        ZLogger.v(deviceInfo.toString())
    }

    fun sync() {
        notifyStateChanged(STATE_DATA_SYNC_PROCESSING, true)
        isSyncAborted = false

        dumpDeviceInfo()
        processSyncProcedure()

        RepositoryViewModel.instance!!.getDevice(deviceAddress).reload()

        if (!isSyncAborted) {
            isSyncAborted = true
            notifyStateChanged(STATE_SYNC_COMPLETED, true)
        } else {
            notifyStateChanged(STATE_DATA_SYNC_ABORTED, true)
        }
        manualSyncEnabled = false
    }

    open fun processSyncProcedure(): Boolean {
        return true
    }


    fun isProfileConnected(deviceAddress: String): Boolean {
        val hfpState = BluetoothProfileManager.getInstance()
            .getConnectionState(BluetoothProfile.HEADSET, deviceAddress)
        val a2dpState =
            BluetoothProfileManager.getInstance()
                .getConnectionState(BluetoothProfile.A2DP, deviceAddress)
        ZLogger.v(
            String.format(
                Locale.US, "%s, hfpState= %d,a2dpState= %d",
                deviceAddress, hfpState, a2dpState
            )
        )
        return BluetoothProfile.STATE_CONNECTED == hfpState || BluetoothProfile.STATE_CONNECTED == a2dpState
    }

    fun reloadDeviceInfo() {
        audioConnectDeviceEntity = getAudioConnectDevice(deviceAddress)
    }

    /**
     * create if not exist
     * */
    fun getAudioConnectDevice(deviceAddress: String): DeviceInfoEntity {
        return deviceInfoRepository.getAudioConnectDevice(deviceAddress=deviceAddress)
    }

    fun isMultiLinkConflict(): Boolean {
        if (!beeProManager.isConnected) {
            return false
        }
        val deviceInfo = beeProManager.deviceInfo
        ZLogger.v("isMultiLinkSupported=" + deviceInfo.isMultiLinkSupported)

        if (!deviceInfo.isMultiLinkSupported) {
            return false
        }

        if (cacheMultilinkInfo == null) {
            return false
        }
        ZLogger.v("cacheMultilinkInfo.getConnNum()=" + cacheMultilinkInfo!!.connNum)
        return cacheMultilinkInfo!!.connNum > 1
    }

    open fun processDeviceInfoChanged(deviceInfo: DeviceInfo, indicator: Int) {
        ZLogger.v(
            String.format(
                getTag() + ".processDeviceInfoChanged: mState=0x%04X, indicator=0x%02X",
                mState,
                indicator
            )
        )
    }

    private val mBeeProManagerCallback: BumblebeeCallback = object : BumblebeeCallback() {
        override fun onStateChanged(state: Int) {
            super.onStateChanged(state)
            when (state) {
                PeripheralConnectionManager.STATE_INIT,
                PeripheralConnectionManager.STATE_DEVICE_DISCONNECTED -> {
                    notifyStateChanged(STATE_DISCONNECTED, true)
                    ZLogger.v(getTag() + "auto stop sync ...")
                    stopSync()
                }

                PeripheralConnectionManager.STATE_DATA_PREPARED -> {
                    notifyStateChanged(STATE_CONNECTED, true)
                    if (autoStartSyncEnabled) {
                        ZLogger.v(getTag() + ", auto start sync app data ...")

//                        updateAudioConnectDevice(
//                            deviceAddress, beeProManager.deviceInfo)

                        startSync()
                    }
                }

                else -> {

                }
            }
        }

        override fun onConnectionStateChanged(peripheral: Peripheral, state: Int) {
//            super.onConnectionStateChanged(peripheral, state)
            if (state == BluetoothSpp.STATE_DISCONNECTED) {
                cacheMultilinkInfo = null
                if (!isSyncAborted) {
                    stopSync()
                }
                notifyStateChanged(STATE_DISCONNECTED, true)
            }
        }

        override fun onDeviceInfoChanged(deviceInfo: DeviceInfo, indicator: Int) {
            super.onDeviceInfoChanged(deviceInfo, indicator)
//            ZLogger.v(getTag() + ": 22 onDeviceInfoChanged:" + indicator)
//            processDeviceInfoChanged(deviceInfo, indicator)
        }
    }

    open fun processOperationComplete(operation: Int, status: Byte) {}
    open fun processMultiLinkInfoChanged(multilinkInfo: MultiLinkInfo) {}

    open fun createVendorModelCallback(): VendorModelCallback {
        return mVendorModelCallback
    }

    val mVendorModelCallback: VendorModelCallback = object : VendorModelCallback() {
        override fun onOperationComplete(operation: Int, status: Byte) {
            super.onOperationComplete(operation, status)
            processOperationComplete(operation, status)
        }

        override fun onMultiLinkInfoChanged(multilinkInfo: MultiLinkInfo) {
            super.onMultiLinkInfoChanged(multilinkInfo)
            cacheMultilinkInfo = multilinkInfo
            processMultiLinkInfoChanged(multilinkInfo)
        }

        override fun onDeviceInfoChanged(indicator: Int, deviceInfo: DeviceInfo) {
            super.onDeviceInfoChanged(indicator, deviceInfo)
            processDeviceInfoChanged(deviceInfo, indicator)
        }

        override fun onKeyMapSettingsReported(settings: List<KeyMmiSettings>) {
            super.onKeyMapSettingsReported(settings)
            saveKeyMaps()
            if (mState == STATE_DATA_SYNC_KEY_MMI_MAP) {
                notifySyncAck()
            } else {
                notifyDataChanged()
            }
        }

        override fun onRwsKeyMapSettingsReported(settings: MutableList<KeyMmiSettings>) {
            super.onRwsKeyMapSettingsReported(settings)
            saveRwsKeyMaps()

            if (mState == STATE_DATA_SYNC_RWS_KEY_MMI_MAP) {
                notifySyncAck()
            } else {
                notifyDataChanged()
            }
        }
    }

    fun saveSupportedMmis() {
        keyMapRepository?.saveSupportedMmis(deviceAddress, beeProManager.vendorClient.deviceInfo)
    }

    fun saveSupportedCallStatus() {
        keyMapRepository?.saveSupportedCallStatus(deviceAddress, beeProManager.vendorClient.deviceInfo)
    }

    fun saveSupportedClickType() {
        keyMapRepository?.saveSupportedClickType(deviceAddress, beeProManager.vendorClient.deviceInfo)
    }

    fun saveKeyMaps() {
        keyMapRepository?.saveKeyMaps(deviceAddress, beeProManager.vendorClient.deviceInfo)
    }

    fun saveRwsKeyMaps() {
        keyMapRepository?.saveRwsKeyMaps(deviceAddress, beeProManager.vendorClient.deviceInfo)
    }

    init {
        val workQueue = LinkedBlockingQueue<Runnable>()
        syncExecutor = ThreadPoolExecutor(
            CORE_THREAD_NUM,
            MAX_THREAD_NUM,
            KEEP_ALIVE_TIME,
            TimeUnit.MILLISECONDS,
            workQueue,
            ThreadPoolExecutor.AbortPolicy()
        )
    }

    companion object {
        const val STATE_IDLE_MASK = 0x0100
        const val STATE_INIT = STATE_IDLE_MASK or 0x01
        const val STATE_DISCONNECTED = STATE_IDLE_MASK or 0x02
        const val STATE_CONNECTED = STATE_IDLE_MASK or 0x03
        const val STATE_SYNC_COMPLETED = STATE_IDLE_MASK or 0x04
        const val STATE_DATA_SYNC_FAILED = STATE_IDLE_MASK or 0x05
        const val STATE_DATA_SYNC_ABORTED = STATE_IDLE_MASK or 0x06
        const val STATE_BUSY_MASK = 0x0200

        const val STATE_DATA_SYNC_PROCESSING = STATE_BUSY_MASK or 0x01
        const val STATE_DATA_SYNC_OTA_DEVICE_INFO: Int = STATE_BUSY_MASK or 0x02

        /**
         * used to support LE OTA
         * */
        const val STATE_DATA_SYNC_LE_ADDR: Int = STATE_BUSY_MASK or 0x03
        const val STATE_DATA_SYNC_BREDR_NAME: Int = STATE_BUSY_MASK or 0x04
        const val STATE_DATA_SYNC_LE_NAME: Int = STATE_BUSY_MASK or 0x05
        const val STATE_DATA_SYNC_RWS_INFO: Int = STATE_BUSY_MASK or 0x06

        /**
         * option for TTS
         */
        const val STATE_DATA_SYNC_LANGUAGE: Int = STATE_BUSY_MASK or 0x07

        /**
         * option for Multi-link
         */
        const val STATE_DATA_SYNC_MULTI_LINK: Int = STATE_BUSY_MASK or 0x08
        const val STATE_DATA_SYNC_RWS_CHANNEL: Int = STATE_BUSY_MASK or 0x09
        const val STATE_DATA_SYNC_GAMING_MODE: Int = STATE_BUSY_MASK or 0x0A
        const val STATE_DATA_SYNC_EAR_DETECTION: Int = STATE_BUSY_MASK or 0x0B
        const val STATE_DATA_SYNC_QUERY_VP_TONE_STATUS: Int = STATE_BUSY_MASK or 0x0C
        const val STATE_DATA_SYNC_SUPPORTED_MMI_LIST = STATE_BUSY_MASK or 0x0D
        const val STATE_DATA_SYNC_SUPPORTED_CALL_STATUS = STATE_BUSY_MASK or 0x0E
        const val STATE_DATA_SYNC_SUPPORTED_CLICK_TYPE = STATE_BUSY_MASK or 0x0F
        const val STATE_DATA_SYNC_KEY_MMI_MAP = STATE_BUSY_MASK or 0x10
        const val STATE_DATA_SYNC_RWS_KEY_MMI_MAP = STATE_BUSY_MASK or 0x11

        /**
         * option for listening mode
         */
        const val STATE_DATA_SYNC_QUERY_LISTENING_MODE_CYCLE = STATE_BUSY_MASK or 0x20
        const val STATE_DATA_SYNC_QUERY_LISTENING_MODE_INFO = STATE_BUSY_MASK or 0x21
        const val STATE_DATA_SYNC_GET_APT_VOLUME_INFO = STATE_BUSY_MASK or 0x22
        const val STATE_DATA_SYNC_GET_APT_VOLUME_SYNC_STATE = STATE_BUSY_MASK or 0x23
        const val STATE_DATA_SYNC_GET_APT_POWER_ON_DELAY_TIME = STATE_BUSY_MASK or 0x24
        const val STATE_DATA_SYNC_GET_APT_NR_STATE = STATE_BUSY_MASK or 0x25
        const val STATE_DATA_SYNC_GET_LLAPT_BRIGHTNESS_INFO = STATE_BUSY_MASK or 0x26
        const val STATE_DATA_SYNC_GET_LLAPT_SCENARIO_CHOOSE_INFO = STATE_BUSY_MASK or 0x27
        const val STATE_DATA_SYNC_GET_ANC_SCENARIO_CHOOSE_INFO = STATE_BUSY_MASK or 0x28

        const val STATE_DATA_SYNC_CHARGING_CASE_GET_BT_ADDR = STATE_BUSY_MASK or 0x29
        protected const val SYNC_LOCK_WAIT_TIME = 3 * 1000

        /**
         * Core thread num of send write command thread pool
         */
        private const val CORE_THREAD_NUM = 10

        /**
         * Max thread num of send write command thread pool
         */
        private const val MAX_THREAD_NUM = 10

        /**
         * Keep alive time of idle thread in send write command thread pool
         */
        private const val KEEP_ALIVE_TIME = 1000.toLong()
    }
}