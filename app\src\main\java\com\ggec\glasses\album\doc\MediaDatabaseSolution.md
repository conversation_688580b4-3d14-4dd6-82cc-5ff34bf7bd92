# Android APP媒体数据库解决方案

## 目录
1. [概述](#概述)
2. [文件存储规划](#文件存储规划)
3. [数据库设计](#数据库设计)
4. [数据访问层设计](#数据访问层设计)
5. [缓存管理策略](#缓存管理策略)
6. [性能优化](#性能优化)
7. [兼容性处理](#兼容性处理)

## 概述

本文档详细描述了Android应用中独立媒体数据库的完整解决方案。该媒体数据库将用于应用内的相册功能，支持图片和视频的存储、检索和展示。该相册与系统相册完全独立，其中存储的媒体文件不会在系统相册中显示。

技术规格：
- 开发语言：Java 17
- 界面开发：XML
- Gradle版本：8.10.2

## 文件存储规划

### 目录结构

```
/data/data/com.example.glasses/files/
    ├── media/                 # 媒体文件主目录
    │   ├── images/            # 图片存储目录
    │   │   ├── thumbnails/    # 图片缩略图目录
    │   │   └── originals/     # 原始图片目录
    │   ├── videos/            # 视频存储目录
    │   │   ├── thumbnails/    # 视频缩略图目录
    │   │   └── originals/     # 原始视频目录
    │   └── temp/              # 临时文件目录（用于处理中的文件）
    └── cache/                 # 缓存目录
```

### 文件命名规则

- 所有媒体文件将使用唯一标识符进行命名，避免文件名冲突
- 命名格式：`{类型}_{时间戳}_{随机UUID}.{扩展名}`
  - 例如：`img_1649123456789_a1b2c3d4.jpg`
- 缩略图命名将基于原始文件名，添加后缀
  - 例如：`img_1649123456789_a1b2c3d4_thumb.jpg`

### 存储策略

1. **内部存储**：
   - 默认情况下，所有媒体文件存储在应用的内部存储空间中
   - 优点：安全性高，应用卸载时文件自动清理
   - 缺点：受设备存储空间限制

2. **外部文件管理**：
   - 提供导入/导出功能，允许用户将媒体文件转移到外部存储
   - 支持批量导入/导出操作
   - 在导入时进行文件验证和格式检查

## 数据库设计

### 数据库架构

使用Room持久性库实现SQLite数据库，建立以下表结构：

1. **媒体文件表（Media）**

```
Media
├── id (主键, 自增长)
├── fileName (文件名)
├── filePath (文件路径)
├── thumbnailPath (缩略图路径)
├── type (类型：IMAGE/VIDEO)
├── mimeType (MIME类型)
├── size (文件大小，字节)
├── width (宽度，像素)
├── height (高度，像素)
├── duration (视频时长，毫秒，图片为0)
├── creationDate (创建日期)
├── modificationDate (修改日期)
├── isDeleted (是否已删除，用于回收站功能)
└── customMetadata (自定义元数据，JSON格式)
```

2. **相册表（Album）**

```
Album
├── id (主键, 自增长)
├── name (相册名称)
├── coverMediaId (封面媒体ID，外键)
├── creationDate (创建日期)
├── modificationDate (修改日期)
└── description (相册描述)
```

3. **相册媒体关联表（AlbumMedia）**

```
AlbumMedia
├── albumId (外键，关联Album表)
├── mediaId (外键，关联Media表)
└── addedDate (添加日期)
```

4. **标签表（Tag）**

```
Tag
├── id (主键, 自增长)
├── name (标签名称)
└── colorCode (标签颜色代码)
```

5. **媒体标签关联表（MediaTag）**

```
MediaTag
├── mediaId (外键，关联Media表)
└── tagId (外键，关联Tag表)
```

### 索引设计

- 为`Media`表的`type`、`creationDate`和`isDeleted`字段创建索引
- 为`AlbumMedia`表的`albumId`和`mediaId`字段创建索引
- 为`MediaTag`表的`mediaId`和`tagId`字段创建索引

### 数据迁移策略

- 使用Room的Migration类处理数据库版本升级
- 为每次数据库结构变更编写明确的迁移路径
- 保留数据库备份和恢复机制

## 数据访问层设计

### 架构模式

采用Repository模式结合MVVM架构：

```
UI <--> ViewModel <--> Repository <--> DataSource(本地/远程)
```

### 组件设计

1. **数据访问对象（DAO）**

```java
// 主要DAO接口
public interface MediaDao {
    // 基本CRUD操作
    @Insert
    long insertMedia(Media media);
    
    @Update
    void updateMedia(Media media);
    
    @Delete
    void deleteMedia(Media media);
    
    @Query("SELECT * FROM Media WHERE id = :mediaId")
    Media getMediaById(long mediaId);
    
    // 查询操作
    @Query("SELECT * FROM Media WHERE type = :type AND isDeleted = 0 ORDER BY creationDate DESC")
    List<Media> getMediaByType(String type);
    
    @Query("SELECT * FROM Media WHERE isDeleted = 0 ORDER BY creationDate DESC")
    LiveData<List<Media>> getAllMedia();
    
    // 更多特定查询...
}
```

2. **存储库（Repository）**

```java
// 媒体存储库
public class MediaRepository {
    private MediaDao mediaDao;
    private AlbumDao albumDao;
    // 其他DAO...
    
    // 同步和异步数据操作方法
    public LiveData<List<Media>> getAllMedia() {...}
    public LiveData<List<Media>> getMediaByAlbum(long albumId) {...}
    public void insertMedia(MediaFile file, OnMediaSavedCallback callback) {...}
    // ...
}
```

3. **文件管理器**

```java
// 文件操作管理
public class MediaFileManager {
    // 文件存储和检索方法
    public File saveMediaFile(Uri sourceUri, String mediaType) {...}
    public File generateThumbnail(File originalFile, String mediaType) {...}
    public boolean deleteMediaFile(String filePath) {...}
    // ...
}
```

### 异步处理

- 使用协程或RxJava处理所有数据库操作，避免主线程阻塞
- 实现分页加载媒体文件，使用`Paging`库支持高效滚动
- 为长时间操作提供进度反馈机制

## 缓存管理策略

### 内存缓存

1. **图片缓存**
   - 使用LruCache缓存缩略图，设定合理的内存使用上限
   - 缓存策略基于最近使用频率和屏幕可见性

```java
// 示例缓存配置
private void setupImageCache() {
    // 分配可用内存的1/8作为缓存
    final int maxMemory = (int) (Runtime.getRuntime().maxMemory() / 1024);
    final int cacheSize = maxMemory / 8;
    
    imageCache = new LruCache<String, Bitmap>(cacheSize) {
        @Override
        protected int sizeOf(String key, Bitmap bitmap) {
            return bitmap.getByteCount() / 1024;
        }
    };
}
```

2. **元数据缓存**
   - 缓存最近访问的媒体元数据，减少数据库查询
   - 优先缓存活跃相册内容

### 磁盘缓存

1. **缩略图持久化**
   - 缩略图预先生成并持久化到磁盘
   - 多种尺寸缩略图支持不同显示场景

2. **临时文件管理**
   - 定期清理处理中间文件
   - 设置缓存清理策略和阈值

### 预加载策略

1. **预测性加载**
   - 基于用户浏览模式预测并预加载相邻媒体内容
   - 滚动方向感知预加载

2. **后台索引**
   - 新媒体添加时在后台处理缩略图生成和元数据提取
   - 使用WorkManager调度非紧急任务

## 性能优化

### 数据加载优化

1. **懒加载**
   - 仅在需要时加载完整媒体数据
   - 使用占位符显示未完全加载的内容

2. **分页加载**
   - 实现RecyclerView分页加载
   - 配置适当的页面大小和预取距离

```java
// 分页配置示例
PagedList.Config config = new PagedList.Config.Builder()
    .setPageSize(20)               // 每页加载20项
    .setPrefetchDistance(40)       // 预取距离为当前位置前后40项
    .setInitialLoadSizeHint(60)    // 初始加载60项
    .setEnablePlaceholders(true)   // 启用占位符
    .build();
```

### 图片处理优化

1. **尺寸采样**
   - 根据显示需求调整加载图片的采样率
   - 避免加载超过必要分辨率的图片

2. **并行处理**
   - 多线程处理批量媒体操作
   - 合理配置线程池大小，避免资源竞争

### 查询优化

1. **索引利用**
   - 确保查询使用了适当的索引
   - 监控和优化慢查询

2. **批处理操作**
   - 批量执行插入和更新操作
   - 使用事务包装批量操作

## 兼容性处理

### 存储访问框架兼容

- 支持Android不同版本的存储访问机制
- 针对Android 10+的分区存储进行适配

### 媒体格式兼容

1. **格式支持**
   - 支持常见图片格式：JPEG, PNG, GIF, WebP, HEIF
   - 支持常见视频格式：MP4, 3GP, WebM, MKV

2. **格式检测与转换**
   - 实现格式识别和元数据提取
   - 必要时进行格式转换，确保兼容性

### 屏幕适配

- 针对不同尺寸和分辨率的设备优化缩略图尺寸
- 自适应网格布局，根据屏幕大小调整列数

### 权限处理

- 适应不同Android版本的权限请求模式
- 处理运行时权限请求和回调

```java
// 权限检查示例
private boolean checkAndRequestPermissions() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        ArrayList<String> permissionsNeeded = new ArrayList<>();
        
        // 检查存储权限
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) 
                != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.READ_EXTERNAL_STORAGE);
        }
        
        // 检查相机权限
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA) 
                != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.CAMERA);
        }
        
        if (!permissionsNeeded.isEmpty()) {
            ActivityCompat.requestPermissions(activity, 
                    permissionsNeeded.toArray(new String[0]), 
                    PERMISSION_REQUEST_CODE);
            return false;
        }
    }
    return true;
}
``` 