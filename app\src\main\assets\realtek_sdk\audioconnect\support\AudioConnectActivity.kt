/*
 * Copyright (c) 2024-2025. Realtek Semiconductor Corporation.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.realsil.sdk.audioconnect.support

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import androidx.appcompat.app.AlertDialog
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewbinding.ViewBinding
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.realsil.sdk.bbpro.BumblebeeCallback
import com.realsil.sdk.bbpro.PeripheralConnectionManager
import com.realsil.sdk.core.logger.ZLogger
import com.realsil.sdk.support.base.BaseViewBindingActivity

/**
 * <AUTHOR>
 * @date 2024/06/21
 */
abstract class AudioConnectActivity<D : ViewBinding>(inflate: (LayoutInflater) -> D) :
    BaseViewBindingActivity<D>(inflate) {
    @JvmField
    var mDeviceAddress: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val intent = intent
        if (intent != null) {
            mDeviceAddress = intent.getStringExtra(EXTRA_KEY_BT_ADDR)!!
        }
    }

    open fun disconnectDevice() {

    }

    private val mManagerCallback = object : BumblebeeCallback() {
        override fun onStateChanged(state: Int) {
            super.onStateChanged(state)
            ZLogger.d("mManagerCallback onStateChanged state = $state")
            if (state == PeripheralConnectionManager.STATE_DATA_PREPARED) {
                val intent = Intent(MessageAction.ACTION_BLUETOOTH_DEVICE_CONNECTED)
                intent.putExtra(MessageAction.EXTRA_DEVICE_ADDRESS, mDeviceAddress)
                LocalBroadcastManager.getInstance(applicationContext).sendBroadcast(intent)
            } else if (state == PeripheralConnectionManager.STATE_DEVICE_DISCONNECTED) {
                val intent = Intent(MessageAction.ACTION_BLUETOOTH_DEVICE_DISCONNECTED)
                intent.putExtra(MessageAction.EXTRA_DEVICE_ADDRESS, mDeviceAddress)
                LocalBroadcastManager.getInstance(applicationContext).sendBroadcast(intent)
            }
        }
    }

    private var multiLinkDialog: AlertDialog? = null
    fun showMultiLinkDialog(message: String) {
        if (multiLinkDialog == null) {
            multiLinkDialog = MaterialAlertDialogBuilder(this, R.style.RtkAlertDialogTheme)
                .setPositiveButton(R.string.button_multi_link_continue) { dialog, which ->
                    dialog.dismiss()
                }
                .setNegativeButton(R.string.button_multi_link_disconnect) { dialog, which ->
                    dialog.dismiss()
                    disconnectDevice()
                }
                .create()
        }
        multiLinkDialog!!.setMessage(message)

        if (!multiLinkDialog!!.isShowing) {
            multiLinkDialog!!.show()
        }
    }

    fun hideMultiLinkDialog() {
        multiLinkDialog?.dismiss()
    }

    companion object {
        const val EXTRA_KEY_BT_ADDR = "bt_addr"
    }
}