package com.ggec.glasses;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.ViewModelProvider;

import com.ggec.glasses.device.viewmodel.DeviceViewModel;
import com.ggec.glasses.fragments.AIFragment;
import com.ggec.glasses.fragments.AlbumFragment;
import com.ggec.glasses.fragments.HomeFragment;
import com.ggec.glasses.fragments.ProfileFragment;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.snackbar.Snackbar;

public class MainActivity extends AppCompatActivity {

    private BottomNavigationView bottomNavigationView;
    private View divider;
    private DeviceViewModel deviceViewModel;
    
    // 二次返回退出相关变量
    private boolean doubleBackToExitPressedOnce = false;
    private static final int EXIT_DELAY = 2000; // 两次返回键间隔时间（毫秒）

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 使内容延伸到状态栏和导航栏后面
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        
        // 设置状态栏颜色（主题中已设置，这里可以省略，但为了明确，可以保留）
        Window window = getWindow();
        window.setStatusBarColor(ContextCompat.getColor(this, R.color.comp_background_gray));
        
        // 设置状态栏图标为深色（因为背景是浅色）
        WindowInsetsControllerCompat windowInsetsController = WindowCompat.getInsetsController(window, window.getDecorView());
        if (windowInsetsController != null) {
            windowInsetsController.setAppearanceLightStatusBars(true);
        }

        // 原有的 EdgeToEdge.enable(this); 包含了部分上述功能，可以移除或注释掉
        // EdgeToEdge.enable(this); 

        setContentView(R.layout.activity_main);
        
        // 初始化DeviceViewModel
        deviceViewModel = new ViewModelProvider(this).get(DeviceViewModel.class);
        
        // 设置窗口插入适配 - 保持对系统栏的处理
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            // 只应用左右内边距，顶部状态栏和底部导航栏由 WindowCompat 处理
            v.setPadding(systemBars.left, 0, systemBars.right, 0);
             // 返回消费掉的 Insets，防止子视图重复处理
             return insets.inset(0, systemBars.top, 0, systemBars.bottom);
        });

        // 初始化控件
        bottomNavigationView = findViewById(R.id.bottom_navigation);
        divider = findViewById(R.id.divider);
        
        // 设置底部导航栏点击事件
        bottomNavigationView.setOnItemSelectedListener(item -> {
            Fragment selectedFragment = null;
            int itemId = item.getItemId();

            if (itemId == R.id.navigation_home) {
                selectedFragment = new HomeFragment();
            } else if (itemId == R.id.navigation_album) {
                selectedFragment = new AlbumFragment();
            } else if (itemId == R.id.navigation_ai) {
                selectedFragment = new AIFragment();
            } else if (itemId == R.id.navigation_profile) {
                selectedFragment = new ProfileFragment();
            }

            if (selectedFragment != null) {
                getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_container, selectedFragment)
                        .commit();
                return true;
            }
            return false;
        });

        // 默认选择首页
        if (savedInstanceState == null) {
            bottomNavigationView.setSelectedItemId(R.id.navigation_home);
        }
        
        // 设置Fragment回调监听，处理底部导航栏的显示和隐藏
        getSupportFragmentManager().registerFragmentLifecycleCallbacks(
                new FragmentManager.FragmentLifecycleCallbacks() {
                    @Override
                    public void onFragmentResumed(@NonNull FragmentManager fm, @NonNull Fragment f) {
                        super.onFragmentResumed(fm, f);
                        
                        // 检查是否是独立的媒体展示页面，如果是则隐藏底部导航栏
                        if (f.getClass().getSimpleName().equals("MediaDisplayFragment")) {
                            setBottomNavigationVisibility(false);
                        } else if (f instanceof AlbumFragment) {
                            // 检查相册Fragment是否处于多选模式
                            AlbumFragment albumFragment = (AlbumFragment) f;
                            if (albumFragment.isInMultiSelectMode()) {
                                setBottomNavigationVisibility(false);
                            } else {
                                setBottomNavigationVisibility(true);
                            }
                        } else {
                            setBottomNavigationVisibility(true);
                        }
                    }
                }, false);
                
        // 设置返回键处理
        setupBackPressedHandler();
    }
    
    /**
     * 设置返回键处理，实现二次返回退出功能
     */
    private void setupBackPressedHandler() {
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                // 如果当前位于非主页面，则先切换到主页
                if (bottomNavigationView.getSelectedItemId() != R.id.navigation_home) {
                    bottomNavigationView.setSelectedItemId(R.id.navigation_home);
                    return;
                }
                
                // 处理二次返回退出逻辑
                if (doubleBackToExitPressedOnce) {
                    // 第二次按返回键，退出应用
                    finish();
                    return;
                }

                // 第一次按返回键，设置标志并显示提示
                doubleBackToExitPressedOnce = true;
                Toast.makeText(MainActivity.this, "再按一次返回键退出应用", Toast.LENGTH_SHORT).show();

                // 延迟重置标志
                new Handler(Looper.getMainLooper()).postDelayed(() -> 
                    doubleBackToExitPressedOnce = false, EXIT_DELAY);
            }
        });
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        // 应用从后台恢复时，检查当前fragment的状态
        Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.fragment_container);
        if (currentFragment instanceof AlbumFragment) {
            AlbumFragment albumFragment = (AlbumFragment) currentFragment;
            if (albumFragment.isInMultiSelectMode()) {
                setBottomNavigationVisibility(false);
            }
        } else if (currentFragment != null && currentFragment.getClass().getSimpleName().equals("MediaDisplayFragment")) {
            setBottomNavigationVisibility(false);
        }
    }
    
    /**
     * 设置底部导航栏的可见性
     * @param isVisible 是否可见
     */
    public void setBottomNavigationVisibility(boolean isVisible) {
        if (bottomNavigationView != null && divider != null) {
            int visibility = isVisible ? View.VISIBLE : View.GONE;
            bottomNavigationView.setVisibility(visibility);
            divider.setVisibility(visibility);
        }
    }
}