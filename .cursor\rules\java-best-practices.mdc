---
description: 
globs: **/*.java
alwaysApply: false
---
- Adhere to the Java naming conventions (e.g., PascalCase for classes, camelCase for methods and variables)
- Use final for variables and methods where possible to improve code safety
- Implement proper exception handling with try-catch blocks
- Utilize Java 8+ features like lambda expressions and streams for concise code
- Follow the Single Responsibility Principle for cleaner, more maintainable code