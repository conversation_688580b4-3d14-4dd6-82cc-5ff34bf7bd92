/*
 * Copyright (c) 2022 Realsil.com.cn. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Authors: <AUTHORS>
 */

package com.realsil.sample.audioconnect.smartwear.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.TypedValueCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.realsil.sample.audioconnect.smartwear.R;
import com.realsil.sample.audioconnect.smartwear.entity.MessageInfo;
import com.realsil.sample.audioconnect.smartwear.view.TypewriterEffectUtil;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Chat Message Adapter.
 *
 * <AUTHOR>
 */
public class MsgListAdapter extends RecyclerView.Adapter<MsgListAdapter.ViewHolder> {

    private final List<MessageInfo> mMessageList;

    public static final int VIEW_TYPE_USER_SEND_MSG  = MessageInfo.MSG_TYPE_SEND_MESSAGE;
    public static final int VIEW_TYPE_RECEIVED_MSG   = MessageInfo.MSG_TYPE_RECEIVED_TEXT_MESSAGE;
    public static final int VIEW_TYPE_SYSTEM_MSG     = MessageInfo.MSG_TYPE_SYSTEM_MESSAGE;
    public static final int VIEW_TYPE_RECEIVED_IMG   = MessageInfo.MSG_TYPE_RECEIVED_IMG_MESSAGE;
    public static final int VIEW_TYPE_PROCESSING_MSG = MessageInfo.MSG_TYPE_PROCESSING_MSG;

    private final RequestOptions mGlideRequestOptions;

    private final TypewriterEffectUtil mTypewriterEffectUtil;

    public MsgListAdapter(Context context) {
        mMessageList = new ArrayList<MessageInfo>();
        mGlideRequestOptions = new RequestOptions()
                .transform(new CenterCrop(), new RoundedCorners(dp2Px(context, 6)))
                .placeholder(R.drawable.ic_chat_img_loading);

        mTypewriterEffectUtil = new TypewriterEffectUtil();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ViewHolder holder = null;
        if (viewType == VIEW_TYPE_USER_SEND_MSG) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.recycler_item_chat_send_msg, parent, false);
            holder = new SendMsgViewHolder(itemView);
        } else if (viewType == VIEW_TYPE_RECEIVED_MSG) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.recycler_item_chat_received_msg, parent, false);
            holder = new ReceivedMsgViewHolder(itemView);
        } else if (viewType == VIEW_TYPE_SYSTEM_MSG) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.recycler_item_chat_system_msg, parent, false);
            holder = new SystemMsgViewHolder(itemView);
        } else if (viewType == VIEW_TYPE_RECEIVED_IMG) {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.recycler_item_chat_received_img, parent, false);
            holder = new ReceivedImgViewHolder(itemView);
        } else {
            View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.recycler_item_chat_processing, parent, false);
            holder = new ProcessingMsgViewHolder(itemView);
        }
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        MessageInfo msgInfo = mMessageList.get(position);
        String msgContent = msgInfo.getMsgContent();
        String msgImgUrl = msgInfo.getMsgImgUrl();

        if (holder instanceof SendMsgViewHolder) {
            // Send Msg
            ((SendMsgViewHolder) holder).tv_user_message.setText(msgContent);
        } else if (holder instanceof ReceivedMsgViewHolder) {
            String typedText = msgInfo.getTypedText();
            if (msgInfo.isTyping()) {
                ((ReceivedMsgViewHolder) holder).tv_received_msg.setText(
                        String.format(Locale.getDefault(),
                                "%s%s",
                                typedText,
                                TypewriterEffectUtil.DEFAULT_PRINT_INDICATOR)
                );
            } else {
                ((ReceivedMsgViewHolder) holder).tv_received_msg.setText(typedText);
            }
        } else if (holder instanceof SystemMsgViewHolder) {
            // System Msg
            ((SystemMsgViewHolder) holder).tv_sys_message.setText(msgContent);
        } else if (holder instanceof ReceivedImgViewHolder) {
            // Received Img
            Glide.with(holder.itemView.getContext())
                    .load(msgImgUrl)
                    .apply(mGlideRequestOptions)
                    .into(((ReceivedImgViewHolder) holder).iv_received_img);
        } else {
            // Processing Msg
            ((ProcessingMsgViewHolder) holder).tv_processing_text.setText(msgContent);
        }
    }

    @Override
    public int getItemViewType(int position) {
        int msgType = mMessageList.get(position).getMsgType();
        if (msgType == MessageInfo.MSG_TYPE_SEND_MESSAGE) {
            return VIEW_TYPE_USER_SEND_MSG;
        } else if (msgType == MessageInfo.MSG_TYPE_RECEIVED_TEXT_MESSAGE) {
            return VIEW_TYPE_RECEIVED_MSG;
        } else if (msgType == MessageInfo.MSG_TYPE_SYSTEM_MESSAGE) {
            return VIEW_TYPE_SYSTEM_MSG;
        } else if (msgType == MessageInfo.MSG_TYPE_RECEIVED_IMG_MESSAGE) {
            return VIEW_TYPE_RECEIVED_IMG;
        } else {
            return VIEW_TYPE_PROCESSING_MSG;
        }
    }

    @Override
    public int getItemCount() {
        return mMessageList.size();
    }

    public void addSystemMsg(@NonNull String systemMsg) {
        MessageInfo msgInfo = new MessageInfo();
        msgInfo.setMsgType(MessageInfo.MSG_TYPE_SYSTEM_MESSAGE);
        msgInfo.setMsgContent(systemMsg);
        addMsg(msgInfo);
    }

    public void addUserMsg(@NotNull String userMsg) {
        MessageInfo msgInfo = new MessageInfo();
        msgInfo.setMsgType(MessageInfo.MSG_TYPE_SEND_MESSAGE);
        msgInfo.setMsgContent(userMsg);
        addMsg(msgInfo);

        addReceivedActionMsg(null);
    }

    public void addReceivedActionMsg(@Nullable String actionMsg) {
        MessageInfo msgInfo = new MessageInfo();
        msgInfo.setMsgType(MessageInfo.MSG_TYPE_PROCESSING_MSG);
        msgInfo.setMsgContent(actionMsg);

        // Remove the message being processed (if exists).
        if (replaceProcessingMsg(msgInfo)) return;

        addMsg(msgInfo);
    }

    public void addReceivedTextMsg(@NotNull MessageInfo receivedMsg) {
        // Remove the message being processed (if exists).
        if (replaceProcessingMsg(receivedMsg)) return;

        // If there is no Processing message, add it directly,
        addMsg(receivedMsg);
    }

    public void addReceivedImgMsg(@NotNull MessageInfo receivedMsg) {
        // Remove the message being processed (if exists).
        if (replaceProcessingMsg(receivedMsg)) return;

        // If there is no Processing message, add it directly,
        addMsg(receivedMsg);
    }

    private boolean replaceProcessingMsg(@NonNull MessageInfo newMsg) {
        if (!mMessageList.isEmpty()) {
            int lastIdx = mMessageList.size() - 1;
            MessageInfo lastMsg = mMessageList.get(lastIdx);
            if (lastMsg.getMsgType() == MessageInfo.MSG_TYPE_PROCESSING_MSG) {
                newMsg.setPositionInDisplayList(lastIdx);
                mMessageList.set(lastIdx, newMsg);
                notifyItemChanged(lastIdx);
                return true;
            }
        }
        return false;
    }

    private void addMsg(MessageInfo msgInfo) {
        msgInfo.setPositionInDisplayList(mMessageList.size());
        mMessageList.add(msgInfo);
        notifyItemInserted(mMessageList.size() - 1);
    }

    public void appendTextMsg(MessageInfo msgInfo) {
        mTypewriterEffectUtil.startTyping(msgInfo, this);
    }

    public static abstract class ViewHolder extends RecyclerView.ViewHolder {

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
        }

    }

    public static class SendMsgViewHolder extends ViewHolder {

        private final TextView tv_user_message;

        public SendMsgViewHolder(@NonNull View itemView) {
            super(itemView);
            tv_user_message = itemView.findViewById(R.id.tv_user_message);
        }

    }

    public static class ReceivedMsgViewHolder extends ViewHolder {

        private final TextView tv_received_msg;

        public ReceivedMsgViewHolder(@NonNull View itemView) {
            super(itemView);
            tv_received_msg = itemView.findViewById(R.id.tv_received_msg);
        }
    }

    public static class ReceivedImgViewHolder extends ViewHolder {

        private final ImageView iv_received_img;

        public ReceivedImgViewHolder(@NonNull View itemView) {
            super(itemView);
            iv_received_img = itemView.findViewById(R.id.iv_received_img);
        }
    }

    public static class SystemMsgViewHolder extends ViewHolder {
        private final TextView tv_sys_message;

        public SystemMsgViewHolder(@NonNull View itemView) {
            super(itemView);
            tv_sys_message = itemView.findViewById(R.id.tv_sys_message);
        }
    }

    public static class ProcessingMsgViewHolder extends ViewHolder {
        private final TextView tv_processing_text;

        public ProcessingMsgViewHolder(@NonNull View itemView) {
            super(itemView);
            tv_processing_text = itemView.findViewById(R.id.tv_processing_text);
        }
    }

    private static int dp2Px(Context context, int dpValue) {
        return (int) TypedValueCompat.dpToPx(dpValue, context.getResources().getDisplayMetrics());
    }

}
