package com.ggec.glasses.aimodelchat.aimodel.deepseek;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * DeepSeek API 响应体结构
 */
public class DeepSeekResponse {
    private String id;
    private String object;
    private long created;
    private String model;
    @SerializedName("choices")
    private List<Choice> choices;
    private Usage usage;

    // Getters (可选)
    public List<Choice> getChoices() {
        return choices;
    }

    /**
     * 选择项结构
     */
    public static class Choice {
        private int index;
        private Message message;
        @SerializedName("finish_reason")
        private String finishReason; // 完成原因，例如 "stop"

        // Getters (可选)
        public Message getMessage() {
            return message;
        }
    }

    /**
     * 消息结构 (通常是 AI 的回复)
     */
    public static class Message {
        private String role; // 角色 (通常是 "assistant")
        private String content; // AI 回复内容

        // Getters (可选)
        public String getContent() {
            return content;
        }
    }

    /**
     * Token 使用情况结构
     */
    public static class Usage {
        @SerializedName("prompt_tokens")
        private int promptTokens; // 输入 Token 数
        @SerializedName("completion_tokens")
        private int completionTokens; // 输出 Token 数
        @SerializedName("total_tokens")
        private int totalTokens; // 总 Token 数

        // Getters (可选)
    }
} 