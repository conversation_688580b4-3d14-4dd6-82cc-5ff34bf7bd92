package com.ggec.glasses.device.model;

/**
 * 设备模型类
 * 包含设备的基本信息
 */
public class Device {
    private String id;
    private String name;
    private String model;
    private String firmwareVersion;
    private boolean isConnected;
    private int batteryLevel;

    public Device() {
        // 默认构造函数
    }

    public Device(String id, String name, String model, String firmwareVersion, boolean isConnected, int batteryLevel) {
        this.id = id;
        this.name = name;
        this.model = model;
        this.firmwareVersion = firmwareVersion;
        this.isConnected = isConnected;
        this.batteryLevel = batteryLevel;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getFirmwareVersion() {
        return firmwareVersion;
    }

    public void setFirmwareVersion(String firmwareVersion) {
        this.firmwareVersion = firmwareVersion;
    }

    public boolean isConnected() {
        return isConnected;
    }

    public void setConnected(boolean connected) {
        isConnected = connected;
    }

    public int getBatteryLevel() {
        return batteryLevel;
    }

    public void setBatteryLevel(int batteryLevel) {
        this.batteryLevel = batteryLevel;
    }

    @Override
    public String toString() {
        return "Device{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", model='" + model + '\'' +
                ", firmwareVersion='" + firmwareVersion + '\'' +
                ", isConnected=" + isConnected +
                ", batteryLevel=" + batteryLevel +
                '}';
    }
} 