package com.ggec.glasses.ai.strategy;

/**
 * AI模型类型枚举
 * 定义系统支持的不同AI大模型类型
 */
public enum AIModelType {
    /**
     * DeepSeek AI模型
     * 处理流程: 语音输入 -> ASR -> DeepSeek API -> TTS -> 语音输出
     */
    DEEPSEEK,
    
    /**
     * 本地模型 (未实现)
     * 未来可能支持的本地AI模型
     */
    LOCAL_MODEL,
    
    /**
     * 其他云端模型 (未实现)
     * 未来可能支持的其他云端AI服务
     */
    OTHER_CLOUD;
    
    /**
     * 获取默认AI模型类型
     * @return 默认使用的AI模型类型
     */
    public static AIModelType getDefault() {
        return DEEPSEEK; // 当前默认使用DeepSeek模型
    }
} 